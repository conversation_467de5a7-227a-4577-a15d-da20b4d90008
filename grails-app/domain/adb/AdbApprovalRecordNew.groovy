package adb

class AdbApprovalRecordNew {

    Long id

    Long schoolId

    Long campusId

    Long approvalStepId

    Long approvalId//审批主体ID

    Long approvalTemplateId

    Byte approvalStepType

    Byte approvalStepUserType

    Long approvalStepUserId

    Byte userType//用于区分用户身份

    String suggestion//审批意见

    String urls//审批图片地址

    String autograph//手写签名

    Date dateCreated

    Date lastUpdated

    Byte status

    static constraints = {
    }

    static mapping = {
        datasource 'adb'
        table('oa_approval_record')
        id generator: 'identity'
        version false
        urls type: 'text'
    }
}

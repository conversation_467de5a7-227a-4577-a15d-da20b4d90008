package adb

class AdbApprovalNew {

    Long id

    Long campusId

    Long schoolId

    String valueJson//审批json

    String baseFormJson

    Long promoterId

    String promoterName//发起人姓名

    Byte promoterType//发起人类型

    String promoterAvatar//发起人头像

    String promoterMobile//发起人手机号

    Long approvalTemplateThemeId

    String approvalTemplateThemeName

    Long approvalFormId//表单id

    Long approvalTemplateId

    Long finalStepId//最终审批

    Integer kitType//请假套件类型

    String approvalNo//审批编号

    Byte autograph = 0 as byte //同意时必须手写签名

    Byte comments = 0 as byte //审批意见必填

    Byte promoterAutoApproval = 0 as byte //发起人自动通过

    Byte printTime = 0 as byte //是否打印审批/抄送时间

    /*
        套件字段，具体业务相关
     */
    /*
        请假
     */
    String studentId//请假学生,不限于请假，包括学生选择器

    Long leaveCategoryId//请假类型

    Long leaveDirectionId//请假去向

    Date leaveStartTime//请假开始时间

    Date leaveEndTime//请假结束时间

    String duration//时长

    Long teacherId//请假老师

    String leaveReason//请假理由

    String leavePeriod//请假频次

    Date leaveStartDate//开始日期

    Date leaveEndDate//结束日期

    Byte leaveUnitType//请假时间单位


    /*
        访客
     */
    Long respondentId//受访人id

    String respondentName//受访人

    String visitorName//访客姓名

    String visitorMobile//访客手机号码

    Date visitTime//预计到访时间

    String visitorIdCard//访客身份证号

    String visitorAvatar//访客人脸照片

    Boolean push//是否下发闸机权限

    Boolean single//是否仅一次通行有效

    String visitReason//来访事由

    /*
        荣誉申报
     */
    Byte targetType // 对象类型 1：学生， 2：班级

    String unitIds

    String awardName//奖励名称

    String awardType//奖励类型

    Long awardTypeId

    String awardLevel//奖励级别

    Long awardLevelId //奖励级别id

    String awardGrade//奖励等级

    Long awardGradeId // 奖励级别id

    String awardCategory//奖励类别

    Long awardCategoryId

    String classificationIndex//分类指标

    String awardReason//奖励原因

    String awardDocumentNo//奖励文号

    String awardingUnit//颁奖单位

    Date awardTime//获奖时间

    String proofPhoto//证明照片

    /*
        学生违纪
     */
    Date violateTime//违纪时间

    String punishmentResult//处分结果

    Long punishmentResultId //等第Id

    String memo//违纪描述

    /*
        临时调代课
     */
    String courseJson//调代课课程

    Integer courseNum//课程数量

    /**
     * 活动参与
     */
    String activityName // 活动名称

    Long activityLevelId // 活动级别

    String activityExperience // 活动经历

    /**
     * 任职履历
     */
    Long jobId

    /**
     * 物品借用
     */

    Long goodsId

    Integer borrowNum

    Date borrowStartTime

    Date borrowEndTime

    /**
     * 报修
     */
    //报修物品
    String propertyName

    //场地
    String siteName

    String fixReason

    String pics

    String teachingWeek

    /**
     * 复课返校
     */
    String files //附件

    Byte status

    Date dateCreated

    Date lastUpdated

    String formValueJson

    String updateRecordJson

    String businessId

    String themeId

    Date startTime

    Date endTime

    Integer total

    String childListJson

    static constraints = {
    }

    static mapping = {
        datasource 'adb'
        table('oa_approval_new')
        id generator: 'identity'
        version false
        baseFormJson type: 'text'
        valueJson type: 'text'
        visitReason type: 'text'
        studentId type: 'text'
        memo type: 'text'
        leaveReason type: 'text'
        awardReason type: 'text'
        files type: 'text'
        awardingUnit type: 'text'
        awardDocumentNo type: 'text'
        awardName type: 'text'
        formValueJson type: 'text'
    }
}

package timetabling.planTemplate

/**
 * 各组织结构和对接平台的关系对应
 */
class PlanTemplateRelated {
    public static final byte PLAN_TEMPLATE_TASK = 1 as byte
    public static final byte PLAN_TEMPLATE = 2 as byte
    public static final byte PLAN_TEMPLATE_GROUP = 3 as byte
    public static final byte PLAN_TEMPLATE_TIME = 4 as byte
    @Deprecated
    public static final byte PLAN_TEMPLATE_PERSON = 5 as byte

    //status  类型
    static transients = ["PLAN_TEMPLATE_TASK", "PLAN_TEMPLATE", "PLAN_TEMPLATE_GROUP", "PLAN_TEMPLATE_TIME", "PLAN_TEMPLATE_PERSON"]

    Long id

    Long aimId

    Byte relatedType

    Integer firm

    String relatedId

    Long version

//    Byte status

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
}

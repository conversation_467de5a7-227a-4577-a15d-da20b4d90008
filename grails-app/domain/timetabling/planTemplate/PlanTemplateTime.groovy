package timetabling.planTemplate

import java.sql.Time

class PlanTemplateTime {

    Long id

    Long planId

    Time startTime

    Time endTime

    Integer weekDayIdx

    //是否下发控制 默认不控制
    Boolean gated = false

    //时段序数
    Integer timeIdx

    Long schoolId

    Long campusId

    Byte status

    Date dateCreated

    Date lastUpdated

    Long version

    static constraints = {
    }

    static mapping = {
        startTime type: 'time'
        endTime type: 'time'
    }
}

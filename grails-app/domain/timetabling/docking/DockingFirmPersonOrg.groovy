package timetabling.docking

class DockingFirmPersonOrg {


    /**
     * 已绑定
     * */
    static final int BIND_SUCCESS = 1

    /**
     * 未绑定
     * */
    static final int BIND_FAILED = -1


    static transients = ["BIND_FAILED", "BIND_SUCCESS"]

    Long id

    Long schoolId

    Long campusId

    Integer firm

    Byte userType

    String orgName

    Long sectionId

    Integer schoolYear

    //对接平台组织主键
    String dockingFirmId


    //对接平台组织唯一标识
    String dockingFirmCode

    /**
     * 边缘一体机中需要
     * 绑定状态 1成功 -1失败
     * */
    Byte bindStatus

    Byte status

    Long version

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
}

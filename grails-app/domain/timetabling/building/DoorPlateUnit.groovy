package timetabling.building

/**
 * 场地班级关联
 */
class DoorPlateUnit implements Serializable {

    static final byte UNIT_DELETE_STATUS = -1
    static final byte DOOR_DELETE_STATUS = -2
    static transients = ["UNIT_DELETE_STATUS", "DOOR_DELETE_STATUS"]

    Long id

    Long schoolId

    Long campusId

    Long unitId

    Long gradeId

    Long majorId

    Long doorPlateId

    Date dateCreated

    Date lastUpdated

    Byte status

    Long version

    static constraints = {
    }
    static mapping = {
        table('door_plate_unit')
        id generator: 'identity'
        version false
    }
}

package timetabling

class Authority {

    Long id

    String name

    String description

    String appId

    Long campusId

    Byte status

    Date dateCreated

    Date lastUpdated

    boolean equals(o) {
        if (this.is(o)) return true
        if (getClass() != o.class) return false

        Authority authority = (Authority) o

        if (name != authority.name) return false

        return true
    }

    int hashCode() {
        return name.hashCode()
    }

    static constraints = {
        name nullable: false, maxSize: 64, blank: false
    }

//    static hasMany = [staffs: Staff]
//
//    static belongsTo = Staff


    static mapping = {
        table('hii_authority')
        version false

        id name: 'name'
        id generator: 'assigned'

//        staffs joinTable: [name: "hii_user_authority", key: 'authority_name', column: 'authority_name', type: 'String']
    }
}

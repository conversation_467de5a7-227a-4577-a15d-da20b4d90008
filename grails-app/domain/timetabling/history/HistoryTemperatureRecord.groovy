package timetabling.history

class HistoryTemperatureRecord {
    Long id

    Long submitterId

    String submitter

    //学生部分
    Long studentId

    Long unitId

    Long gradeId

    Long sectionId

    String code

    String name

    //老师部分
    Long teacherId

    //共有数据
    Long campusId

    Long schoolId

    Double temperature

    Boolean normal

    String eventPlace

    //1 人工登记 2 设备检测
    Byte mode

    Date startTime

    String avatar

    Long dayDatestamp

    Date dateCreated

    Date lastUpdated

    Byte status

    static constraints = {}

    static mapping = {
        datasource 'historicalData'
    }
}

package timetabling.history

class HistoryTrack implements Serializable {


    Long id

    Long version

    Date dateCreated

    Date lastUpdated

    Byte status

    Long deviceId

    Long unitId

    Long gradeId

    Long campusId

    Long schoolId

    Double temperature

    // 智慧校园字段
    /*
    人员Id
     */
    Long userId

    /*
    人员类型
     */
    Byte userType

    /*
    人员姓名
     */
    String userName

    /*

     */
    String userCode

    /*
    平台类型
    枚举类 @ConstantEnum
    */
    Integer firm

    // 对接系统字段

    /*
    对接系统告警设备（类似） 名称
     */
    String objName

    /*
    对接系统设备唯一值（Id、Code 等）
     */
    String objCode

    /**
     * 设备类型
     * */
    String objType

    /*
    告警事件位置
        默认设备名称
     */
    String location

    /*
    对接系统人员Id
     */
    String personId

    /*
    对接系统人员code
     */
    String personCode

    /*
    对接系统人员姓名
     */
    String personName

    /*
    对接系统的user类型根据平台不同
       大华  人员类型 1学生 2教师3保安 4宿管 5其他
     */
    String personUserType

    /*
    消息推送时间
        事件发生时间
     */
    Long pushTime

    /*
    抓拍开始时间
        富士进入设备
     */
    Date startTime
    /*
    抓拍结束时间
        富士出设备时间
     */
    Date endTime

    /*
    抓拍图
     */
    String faceImg

    /*
    抓拍热度图
        测温会用到
     */
    String thermalImg

    /*
    事件类型 海康已有枚举
     */
    String eventType

    /*
    大华添加冗余参数 通道Id
    */
    String channelId

    /*
    相似度
    */
    String similarity

    /**
     * 配对Id 富士
     * */
    String pairId

    String licensePlate

    //相关平台的原始参数
    String originalParameters

    Long dayTimeStamp

    static constraints = {
    }

    static mapping = {
        table('history_track')
        faceImg type: "text"
        thermalImg type: "text"
        datasource 'historicalData'
    }
}

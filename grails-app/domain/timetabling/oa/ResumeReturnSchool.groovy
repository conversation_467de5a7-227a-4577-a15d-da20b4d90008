package timetabling.oa

/**
 * 复课返校
 * <AUTHOR>
 * @Date 2023-07-04 10:07
 */
class ResumeReturnSchool {

    Long id

    Long studentId

    Long campusId

    String memo //要求

    String files //附件

    Long approvalId

    String promoterName

    Byte promoterType

    Byte status

    Byte approvalStatus

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        table("oa_resume_return_school")
        files type: 'text'
        memo type: 'text'
    }
}

package timetabling.oa

class ApprovalDefaultJson {
    
    Long id
    
    Byte userType
    
    String name
    
    String baseFormJson
    
    String baseConditionJson
    
    String baseProcessJson
    
    Integer status
    
    Date dateCreated
    
    Date lastUpdated
    
    static mapping = {
        baseFormJson type: 'text'
        baseConditionJson type: 'text'
        baseProcessJson type: 'text'
    }
}

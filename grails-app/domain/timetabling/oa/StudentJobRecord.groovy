package timetabling.oa

class StudentJobRecord {

    Long id

    Long campusId

    Long studentId

    Long jobId

    String jobName

    BigDecimal score = 0

    String memo

    Byte status

    Date dateCreated

    Date lastUpdated

    Long promoterId

    String promoterName

    Byte promoterType

    Date applyTime

    Long approvalId

    Long jobStartTime

    Long jobEndTime



    static constraints = {
    }

    static mapping = {
        table("oa_student_job_record")
        memo type: "text"
    }
}

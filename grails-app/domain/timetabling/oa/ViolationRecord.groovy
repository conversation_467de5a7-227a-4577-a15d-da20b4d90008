package timetabling.oa

class ViolationRecord {

    Long id

    Long campusId

    Long schoolId

    Long approvalId

    Long studentId
    
    Long pId

    // pId存在时为降级时间
    Date violateTime//违纪时间
    
    // pId存在时为降级Id
    Long punishmentResultId // 处分结果id(violation id)

    // pId存在时为降级结果
    String punishmentResult//处分结果
    
    BigDecimal score

    String memo//违纪描述

    Long publishTeacherId//登记人

    Date publishTime//登记时间

    @Deprecated
    String demotionResult//降级结果

    @Deprecated
    Date demoteTime//降级时间

    @Deprecated
    Long demoteTeacherId//降级登记人

    @Deprecated
    Date demoteRegisterTime//降级登记时间

    String proofPhoto

    Byte status

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        table('oa_violation_record')
        id generator: 'identity'
        version false
        memo type: 'text'
        proofPhoto type: 'text'
    }
}

package timetabling.oa

class LeaveRecord {

    Long id

    Long schoolId

    Long campusId

    Long studentId//请假学生,不限于请假，包括学生选择器

    Long leaveCategoryId//请假类型

    Long leaveDirectionId//请假去向

    Date leaveStartTime//请假开始时间

    Date leaveEndTime//请假结束时间

    String leaveReason//请假事由

    Byte leaveUnitType//请假时间单位

    Date dateCreated

    Date lastUpdated

    Byte status

    Long promoterId

    String promoterName//发起人姓名

    Byte promoterType//发起人类型

    Date applyTime//申请时间

    Date approvedTime//通过时间

    Long approvalId//审批id

    //冗余数据，后台查询记录有搜索
    Long unitId

    String unitName

    Long gradeId

    String gradeName

    String sectionName

    String avatar

    String studentName

    String studentCode

    String majorId

    String majorName

    String facultyId

    String facultyName
    
    Boolean gender
    
    Boolean nonresident
    
    String bed

    static constraints = {
    }

    static mapping = {
        table("oa_leave_record")
        id generator: 'identity'
        version false
        leaveReason type: 'text'
    }
}

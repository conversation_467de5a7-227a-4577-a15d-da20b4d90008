package timetabling.org

class Grade implements Serializable {

    Long id
    //code 年级1 一年级 2二年级。。。。。。
    String code

    Long schoolId

    Long campusId

    Long sectionId
    //仅用于创建时命名方式定义
    Byte sectionType

    String name

    String gradeName

    Integer schoolYear

    Long schoolYearId

    // 当campus为职校时，1：正常 0：停用 -1：删除
    Byte status

    Long gradeIndex
    
    String unionCode
    
    Integer syncType

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
    static mapping = {
        table('grade')
        id generator: 'identity'
        version false
//        datasource 'timetabling'
    }
}

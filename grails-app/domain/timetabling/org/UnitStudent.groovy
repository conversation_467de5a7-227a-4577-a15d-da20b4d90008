package timetabling.org


import static hiiadmin.ConstantEnum.RelationStatus

class UnitStudent implements Serializable {

    public static final byte NORMAL = RelationStatus.NORMAL.status
    public static final byte UPGRADE = RelationStatus.UPGRADE.status
    public static final byte CHANGE = RelationStatus.CHANGE.status
    public static final byte FINISH = RelationStatus.FINISH.status
    public static final byte DELETE = RelationStatus.DELETE.status
    public static final byte DELETE_STUDENT = -1
    public static final byte DELETE_UNIT = -2
    //status  类型
    static transients = ["NORMAL", "UPGRADE", "CHANGE", "FINISH", "DELETE", "DELETE_STUDENT", "DELETE_UNIT"]

    private static final long serialVersionUID = 6357851568194927018L

    Long id

    Long schoolYearId

    Long schoolId

    Long campusId

    Long gradeId
    
    Long facultyId
    
    Long majorId

    Long courseId

    Long sectionId

    Long unitId

    Byte unitType

    Long studentId

    Date dateCreated

    Date lastUpdated

    Byte status

    static constraints = {
    }
    static mapping = {
        table('unit_student')
        id generator: 'identity'
        version false
        unitId column: "class_unit_id"
        unitType column: "class_unit_type"
//        datasource 'timetabling'
    }
}

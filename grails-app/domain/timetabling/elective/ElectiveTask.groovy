package timetabling.elective

/**
 * 选课任务
 */
class ElectiveTask implements Serializable {

    Long id

    Long campusId

    Long schoolId

    String name

    String gradeIds

    Date startTime

    Date endTime

    Byte updateState  //能否修改

    /*
    预设可修改时 : 学生在点击重新选课时，预选名单不做释放；学生重新提交选课后取消预设标记
    预设不可修改时 ：学生任意选择不影响预设标记，预设课程每次必选
     */
    Byte changePreset //预设能否修改

    Integer maxNum

    Integer minNum

    String memo //注意事项

    Integer totalNum //总人数

    Integer selectNum //已选人数

    Byte status

    //文件处理状态
    //-1、导出失败 0、未导出  1、开始导出 2、数据处理中 3、处理完成、开始上传 5、处理完成、可以下载
    Byte fileStatus
    //导出失败信息
    String fileMessage
    //文件地址
    String fileUrl

    Integer jobId

    Long version

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        id generator: 'identity'
        memo type: 'text'
    }
}

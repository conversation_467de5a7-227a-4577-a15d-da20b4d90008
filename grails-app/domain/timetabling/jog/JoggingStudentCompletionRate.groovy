package timetabling.jog

/**
 * <AUTHOR>
 * @Date 2022-11-30 11:46
 */
class JoggingStudentCompletionRate implements Serializable {

    Long id

    Long campusId

    Long sectionId

    Long gradeId

    Long unitId

    Long studentId

    @Deprecated
    Integer completionCylinderNumber
    
    BigDecimal completionNumber

    Integer completionDay

    String rate

    Long dayDatestamp

    Long semesterId

    Byte status

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
}

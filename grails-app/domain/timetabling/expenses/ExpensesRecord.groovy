package timetabling.expenses

class ExpensesRecord {
    Long id

    Long schoolId

    Long campusId

    String studentName

    //每日消费时间戳
    Long dateStamp

    Long userId

    //用户类型
    Byte userType
    //消费时间
    Date expensesDate
    //交易类型中文
    String transactionType

    String transactionCode
    //1:消费 2 充值 0：其他
    Byte type
    //交易金额
    BigDecimal transactionAmount

    //余额
    BigDecimal transactionBalance

    //交易时间
    Date transactionDate

    //消费场所
    String consumerPlace
    //cardId
    String cardId
    //一卡通账号
    String cardNum

    Byte status

    Date dateCreated

    Date lastUpdated


    static constraints = {
    }
}

package timetabling

class YunMouAccount {

    Long id

    String account

    String clientId

    String clientSecret

    String schoolId

    Long campusId

    Byte status = 1 as Byte

    Byte projectType

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        table('yun_mou_account')
        id generator: 'identity'
//        datasource 'dataSource'
    }
}

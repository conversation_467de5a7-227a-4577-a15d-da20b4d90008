package timetabling.attendance.eventAttendance

import java.sql.Time

/**
 * 考勤座位事时间规则模板
 * */
class EventAttendanceConfigRule {
    Long id

    Long campusId

    Long eventConfigId

    Long pewTypeId

    Integer weekDay

    /**
     * 签到打开状态
     * */
    Boolean sinInOpenStatus

    /**
     * 开始签到时间
     * */
    Time signInTime

    /**
     * 开始时间
     * */
    Time startTime


    /**
     * 结束时间
     * */
    Time endTime

    Integer timeIdx

    String ignoreGroupIds

    Date dateCreated

    Date lastUpdated

    Byte status

    static constraints = {
    }
}

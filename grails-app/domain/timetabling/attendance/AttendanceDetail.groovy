package timetabling.attendance

import hiiadmin.ConstantEnum

class AttendanceDetail implements Serializable {

    static final byte ASK_FOR_LEAVE = 6,
                      NON = 5,
                      LATE = 2,
                      NORMAL = 1,
                      ABNORMAL = -1
    static final byte TEACHER_TYPE = ConstantEnum.UserTypeEnum.TEACHER.type,
                      STUDENT_TYPE = ConstantEnum.UserTypeEnum.STUDENT.type

    static transients = ["ASK_FOR_LEAVE", "NORMAL", "LATE", "NON", "ABNORMAL", "TEACHER_TYPE", "STUDENT_TYPE"]

    Long id

    Long allocationId

    Long rulesId

    Long groupId

    Long studentId

    String studentCode

    String studentName

    String avatar

    Long campusId

    Long schoolId

    Long unitId

    String unitName

    //班级别名
    String unitAlias

    Long gradeId

    String gradeName

    Long sectionId

    Long buildingId

    String buildingName

    Long layerId

    String layerName

    Long doorPlateId

    String doorPlateName

    Long bedId

    String bedName

    Long dayDatestamp

    Date startTime

    Date endTime

    //考勤截止时间
    Date closeTime

    // 5未考勤默认  2 迟到/晚归 1 正常
    Byte status = NON
    //迟到扣分
    Integer lateScored

    Integer eventType

    String indexCode

    Long happenedTime

    Double temperature

    Date dateCreated

    Date lastUpdated

    Long version

    //1 学生 6老师  老师时groupId == 0 (只支持全体老师)
    Byte userType

    Byte type

    Long factoryId

    String factoryName

    Long majorId

    String majorName


    static mapping = {
        id generator: 'identity'
        version true
    }
}
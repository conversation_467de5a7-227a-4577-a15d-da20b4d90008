package timetabling.option

class OptionRecord implements Serializable {

    Long id

    Long optionId

    Long optionUnitId

    Long studentId

    Integer course

    String choiceIds

    /**
     * 新加字段
     * 家长确认状态： 0 未确认 1已确认
     * */
    Byte confirmState

    String choiceName
    //创建/修改者Id
    Long submitId
    /**
     * @See ConstantEnum.UserTypeEnum
     */
    Byte submitType

    Long version

    Byte status

    //parentId
    Long confirmId

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
    static mapping = {
        table('option_record')
        id generator: 'identity'
        version true
//        datasource 'timetabling'
    }
}

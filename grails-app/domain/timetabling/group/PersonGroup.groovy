package timetabling.group

/**
 * 人员分组
 */
class PersonGroup {

    public static final Byte FIXED_TYPE = 1
    public static final Byte CONDITION_TYPE = 2

    Long id

    Long campusId

    String name

    Byte type

    Byte userType

    Integer userCount = 0

    //特征条件 使用json存储
    String conditionJson

    Byte status

    Long version

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        conditionJson type: 'text'
    }
}

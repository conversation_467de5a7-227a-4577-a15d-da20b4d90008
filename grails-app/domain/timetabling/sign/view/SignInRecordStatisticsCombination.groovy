package timetabling.sign.view

class SignInRecordStatisticsCombination {

    Long id

    Long campusId

    String statisticTeacherIds

    String statisticTeacherNames

    Date dateTime

    Integer dayOfWeek

    Integer timeslot
    
    String timeslotName

    String unitId

    String unitName

    String courseId

    String courseName

    Long subjectId

    String subjectName

    Long roomId

    String roomName

    Integer actualNum

    Integer shouldNum

    Integer weekIndex

    Date startTime

    Date endTime

    Date lastNoticeTime

    Long date//新课表新增

    String planId

    Long unitSignInId

    Boolean teacherSignIn

    Boolean studentSignIn

//######

    Long statisticsId

    String teacherId

    String teacherName

    Byte signInUserType

    Byte signInType

    Date signInTime

    String signInPics

    Integer signInWay

    String memo

    String padName
    
    Byte signInStatisticsStatus

    static constraints = {
    }
    static mapping = {
        version false
    }
}

package timetabling.track
/**
 * 告警事件
 * 记录不包含人员的信息
 */
class TrackAlarm {

    Long id

    Long version

    Date dateCreated

    Date lastUpdated

    Long deviceId

    Long campusId

    Long schoolId

    //事件id
    Long eventTypeId

    String eventId

    /*
    平台类型
    枚举类 @ConstantEnum
    */
    Integer firm

    /*
    事件类型映射
     */
    Integer event

    // 对接系统字段

    /*
    对接系统事件类型
     */
    String eventType
    /*
    对接系统事件类型名称
     */
    String eventName

    /*
    对接系统告警设备（类似） 名称
     */
    String objName

    /*
    对接系统设备唯一值（Id、Code 等）
     */
    String objCode

    /*
    消息推送时间
        事件发生时间
     */
    Long pushTime

    /*
    抓拍开始时间
     */
    Date startTime
    /*
    抓拍结束时间
     */
    Date endTime

    //标题
    String name
    /*
    抓拍图
     */
    String snapImg

    /*
    抓拍热度图
        测温会用到
     */
    String thermalImg

    /*
    当天开始时间的时间戳
     */
    Long dayTimeStamp

    String memo

    /**
     * 匹配的学生
     */
    String relatedPersonIds

    Byte relatedPersonType

    String areaPathIds

    //相关平台的原始参数
    String originalParameters

    String targetType
    String targetArea
    String targetScore

    static constraints = {
    }
    static mapping = {
        snapImg type: 'text'
    }

}

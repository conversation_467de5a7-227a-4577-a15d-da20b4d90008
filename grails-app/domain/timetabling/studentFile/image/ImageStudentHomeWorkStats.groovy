package timetabling.studentFile.image

/**
 * 学生档案作业情况统计(学期学年时间维度)
 */
class ImageStudentHomeWorkStats {
    Long id

    Long studentId

    Long campusId

    Long schoolId

    Long fileTaskId

    Long subjectId

    String subjectName

    //该次作业布置次数
    Integer homeworkCount

    //上交次数
    Integer submitCount

    //未上交次数
    Integer unSubmitCount

    Byte status

    Date dateCreated

    Date lastUpdated


    static constraints = {
    }
}

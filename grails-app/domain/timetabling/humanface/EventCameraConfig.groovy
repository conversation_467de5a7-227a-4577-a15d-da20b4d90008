package timetabling.humanface


/**
 * 事件配置
 * */
class EventCameraConfig {

    Long id

    /**
     * 关联事务id
     * */
    Long eventId

    /**
     * 需要参与监控的设备id
     * */
    Long deviceId

    /**
     * 设配名称
     * */
    String deviceName

    /**
     * 父设备id
     * */
    Long parentDeviceId

    /**
     * 拥挤/闯禁 表示告警时间间隔 单位:秒
     * 徘徊 表示徘徊时⻓
     * ⼈脸布控/陌⽣⼈/越界 不传
     * */
    Integer duration

    /**
     * 拥挤告警阈值，单位：⼈数
     * 其他告警类型不传
     * */
    Integer personCount

    /**
     * 布控告警配置，可以配置的⼈脸库id
     * */
    String faceGroups

    /**
     * ⼈脸布控阈值 其他告警不传
     * */
    Float score

    /**
     * 角度
     * */
    Integer direction

    /**
     * 布控/陌⽣⼈告警不传，摄像头拌线配置
     * json类型
     * */
    String config

    Date dateCreated

    Date lastUpdated
    //1成功    0删除
    Byte status

    static constraints = {
    }

    /*static mapping = {
        id generator: 'identity'
        config type: 'json'
    }*/

    static mapping = {
        version false
    }
}

package timetabling.moral

/**
 * 德育统计推送
 *
 * <AUTHOR>
 * @Date 2023-09-22 17:34
 */

class MoralPush implements Serializable {

    Long id

    Long campusId

    String name

    // 1:学生，2：宿舍，3：班级
    Byte targetType

    String targetIds

    Byte pushType

    Long publisherId

    // 1启用，0禁用，-1删除
    Byte status

    String times

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        table('m_moral_push')
        times type: 'text'
        targetIds type: 'text'
        id generator: 'identity'
        version true
    }
}

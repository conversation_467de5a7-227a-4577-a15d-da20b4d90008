package timetabling.moral

class MoralMenu implements Serializable {

    Long id
    //排序
    Integer idx
    //检查项目
    String name
    //备注
    String memo
    //所属类别   1、班级 2、宿舍  3、跑操
    Long type

    Long pewTypeId

    //学校
    Long schoolId

    Long campusId
    //多选单选。0、单选
    Byte multiple

    Byte status

    Long itemId //登记类型

    /*
    学生是否必选 0、1
     */
    Integer studentRequired

    Integer registrationOptBit

    Integer registerPermissions

    // 评分模式  0：自定义， 1：同步学生出入考勤
    Byte mode

    // 通知人员类型   1：班主任， 2：家长， 3：班主任+家长
    Byte noticeUserType

    //是否选择相册照片
    Boolean choseAlbumPic

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }
    static mapping = {
        table('m_moral_menu')
        id generator: 'identity'
        version false
    }
}
package timetabling.classPad

class PadDeviceTemplate {

    static transients = ["APP", "NOT_APP"]
    static final Byte APP = 1 as Byte

    static final Byte NOT_APP = -1 as Byte

    Long id

    Long campusId

    String name

    String dataJson

    String pic

    /**
     * 1:应用
     * -1 ：未应用
     * */
    Byte applicationStatus

    /**
     * 是否默认
     * */
    Boolean defaultStatus

    /**
     * 1: 横板  12：竖版
     */
    Byte templateType

    Integer status

    String deviceIds

    Date dateCreated

    Date lastUpdated


    static constraints = {
    }

    static mapping = {
        table('pad_device_template')
        id generator: 'identity'
        dataJson type: "text"
        version false

    }
}

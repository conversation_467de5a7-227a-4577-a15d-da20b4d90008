package timetabling

class OpRecord {

    Long id

    Long staffId

    String visit

    String controllerName

    String actionName

    String params

    String ip

    Date dateCreated

    Date lastUpdated

    static constraints = {
    }

    static mapping = {
        table('op_record')
        id generator: 'identity'
        params type: "text"
        version true
//        datasource 'timetabling'
    }
}

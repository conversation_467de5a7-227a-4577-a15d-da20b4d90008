import com.bugu.PhoenixCoder
import timetabling.share.ShareScreen

model {
    List<ShareScreen> list
    int total
    String baseUrl
}

json {
    code 0
    status 1
    result {
        total total
        list list,{
            ShareScreen shareScreen->
                id PhoenixCoder.encodeId(shareScreen.id)
                title shareScreen.title
                url baseUrl + "?id=" + PhoenixCoder.encodeId(shareScreen.id)
                pic shareScreen.pic
        }
    }
}
import timetabling.Menu

model {
    Menu menu
}

json {
    status 1
    result {
        id menu.id
        controllerName menu.controllerName
        title menu.title
        appId menu.appId
        link menu.link
        parentId menu.parentId
        level menu.level
        weight menu.weight
        customTitle menu.customTitle
        categoryWeight menu.categoryWeight
        actionMask menu.actionMask
        schoolId menu.schoolId
        category menu.category
        menuGroup menu.menuGroup
        type menu.type
        icon menu.icon
        dateCreated menu.dateCreated.getTime() ?: new Date().getTime()
    }
}

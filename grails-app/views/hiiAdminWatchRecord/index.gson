import timetabling.keepWatch.WatchRecord

model {
    List<WatchRecord> list
    Integer total
}

json {
    code 0
    status 1
    result {
        total total
        list list, {
            WatchRecord watchRecord ->
                id watchRecord.id
                name watchRecord.teacherName
                jobNum watchRecord.jobNum
                placeName watchRecord.placeName
                timeName watchRecord.timeName
                memo watchRecord.memo
                pic watchRecord.pic
                signInMethod watchRecord.signInMethod
                dateCreated watchRecord.dateCreated?.getTime()
        }
    }
}
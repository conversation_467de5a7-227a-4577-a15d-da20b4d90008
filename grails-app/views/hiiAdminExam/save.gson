import hiiadmin.utils.PhenixCoder
import timetabling.Exam

model {
    Exam exam
}

json {
    code 0
    status 1
    result {
        id exam.id
        schoolId PhenixCoder.encodeId(exam.schoolId)
        semesterId PhenixCoder.encodeId(exam.semesterId)
        semesterType exam.semesterType
        academicYear exam.academicYear
        startTime exam.startTime.getTime()
        endTime exam.endTime.getTime()
        name exam.name
        status exam.status
        classUnitIds exam.classUnitIds
        subjectIds exam.subjectIds
    }
}
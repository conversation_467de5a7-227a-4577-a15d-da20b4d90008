service8queen: "http://172.16.0.178"
servicedingone: "https://virus.yunzhiyuan100.com" #改成钉钉的
domain: "api.yunzhiyuan100.com"
hDomain: "www.yunzhiyuan100.com"
qiniuDomain: "http://res.yunzhiyuan100.com"
protocol: "https://"
buket: "bugu"
pcAppId: "rejmgk8s"
countServer: "http://121.40.187.92:7001"

---
dataSources:
  dataSource:
    dbCreate: "update"  # one of 'create', 'create-drop', 'update', 'validate', ''
    url: ***********************************************************************************************************************************************************************************************************
    username: bugu
    password: Im8nb1qaz2wsx
    loggingSql: true
    properties:
      jmxEnabled: true
      initialSize: 5
      maxActive: 200
      minIdle: 5
      maxIdle: 25
      maxWait: 10000
      maxAge: 600000
      timeBetweenEvictionRunsMillis: 5000
      minEvictableIdleTimeMillis: 60000
      validationQuery: SELECT 1
      validationQueryTimeout: 3
      validationInterval: 15000
      testOnBorrow: true
      testWhileIdle: true
      testOnReturn: false
      jdbcInterceptors: ConnectionState
      defaultTransactionIsolation: 2 # TRANSACTION_READ_COMMITTED
  historicalData:
    username: bugu
    password: Im8nb1qaz2wsx
    url: *******************************************************************************************************************************************************************************************************************
    logSql: true
    formatSql: true
    dbCreate: "update"
    driverClassName: com.mysql.cj.jdbc.Driver
    dialect: org.hibernate.dialect.MySQL8Dialect
    properties:
      jmxEnabled: true
      initialSize: 5
      maxActive: 200
      minIdle: 5
      maxIdle: 25
      maxWait: 10000
      maxAge: 600000
      timeBetweenEvictionRunsMillis: 5000
      minEvictableIdleTimeMillis: 60000
      validationQuery: SELECT 1
      validationQueryTimeout: 3
      validationInterval: 15000
      testOnBorrow: true
      testWhileIdle: true
      testOnReturn: false
      jdbcInterceptors: ConnectionState
      defaultTransactionIsolation: 2 # TRANSACTION_READ_COMMITTED
  adb: # one of 'create', 'create-drop', 'update', 'validate', ''
    url: ***********************************************************************************************************************************************************************************************************
    username: bugu
    password: Im8nb1qaz2wsx
    logSql: true
    formatSql: true
    driverClassName: com.mysql.cj.jdbc.Driver
    dialect: org.hibernate.dialect.MySQL55Dialect
    dbCreate: "" # one of 'create', 'create-drop', 'update', 'validate', ''
    properties:
      jmxEnabled: true
      initialSize: 5
      maxActive: 200
      minIdle: 5
      maxIdle: 25
      maxWait: 10000
      maxAge: 600000
      timeBetweenEvictionRunsMillis: 5000
      minEvictableIdleTimeMillis: 60000
      validationQuery: SELECT 1
      validationQueryTimeout: 3
      validationInterval: 15000
      testOnBorrow: true
      testWhileIdle: true
      testOnReturn: false
      jdbcInterceptors: ConnectionState
      defaultTransactionIsolation: 2 # TRANSACTION_READ_COMMITTED
---
redis:
  host: r-bp116a0cbed786f4.redis.rds.aliyuncs.com
  password: Bugu8888866666
  port: 6379
---
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${redis.host}
      password: ${redis.password}
      port: ${redis.port}
---
rocketmq:
  accessKey: LTAIvKscF8qNTVzZ
  secretKey: U23XZSEw4uWvgHdVMEp8HbJrW7dAwV
  nameSrvAddr: http://onsaddr.cn-hangzhou.mq-internal.aliyuncs.com:8080
  lectureTopic: bugu_lecture_online
  eventMsgTopic: BUGU_EVENT_ATTENDANCE_MESSAGE_ONLINE
  eventDelayedGroupId: GID_BUGU_HII_ADMIN_MESSAGE_ONLINE
  bgbBusGroupId: GID_BGB_BUS_CONSUMER_ONLINE
  yunRuiTopic: yunrui_auth_config_online
  gatingIssueTopic: gating_issue_task_online
  studentStatusTopic: online_bugu_student_change
  saasTopic: MESSAGE_SERVER_ONLINE
  auditLogsTopic: BUGU_AUDIT_LOGS_ONLINE
---
rocketmqV2:
  ak: qE0ZUThlWGDmsg
  sk: 4kcjCfVcUWxVfu5pzhSZ
  nameSrvAddr: rocketmq.yunzhiyuan100.com:8081
  topic:
    lecture: bugu_lecture_online
    auditLogsTopic: BUGU_AUDIT_LOGS_ONLINE
  groupId: GID_BUGU_HII_ADMIN_MESSAGE_ONLINE
---
nacos:
  host: mse-aee41480-nacos-ans.mse.aliyuncs.com
---
spring:
  config:
    activate:
      on-profile:
        - prod
    import-check:
      enabled: false
    import: "optional:configserver:"
  cloud:
    loadbalancer:
      nacos:
        enabled: true
    nacos:
      discovery:
        server-addr: ${nacos.host}:8848
      config:
        server-addr: ${nacos.host}:8848
        file-extension: yaml
  aop:
    auto: true
    proxy-target-class: true
---
dubbo:
  registry:
    address: nacos://${nacos.host}:8848
    register: false
  cloud:
    subscribed-services: ${spring.application.name}, queen9
  consumer:
    timeout: 60000
    retries: 0
    init: true
    check: false
  application:
    qos-enable: true
    qos-accept-foreign-ip: false
---
openSearch:
  host: https://opensearch-cn-hangzhou.aliyuncs.com
  studentInfo: online_student_info
---
share:
  baseUrl: https://smart-campus.yunzhiyuan100.com/share-screen
---
use:
  # 中策本地
  bg_host: http://*************:30000
  bg_access_token_url:  /api/auth/server/1.0/authorize/accessToken
  bg_access_token_get_user_url:  /api/auth/server/1.0/authorize/userInfo
  bg_auth_login_out:  /api/auth/server/1.0/authorize/authLoginOut
  bg_app_key: 5299479759
  bg_app_secret: YmMzMDZhMWFkNWFhM2FjMjNkMGIxN2E4YTVjYzI0NTE=
  bgbUrl: https://smart-campus.yunzhiyuan100.com
  bgAuthServerUrl: ""
---
user-module:
  loginTest: true
  url: https://smart-campus.yunzhiyuan100.com/api/user-module/1.0/login/exchange/token
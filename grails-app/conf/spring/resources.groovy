package spring

import com.alicp.jetcache.autoconfigure.AutoConfigureBeans
import hiiadmin.config.OpenSearchConfiguration
import hiiadmin.listen.listener.DeviceChangeListener
import hiiadmin.listen.listener.GatingIssueTaskListener
import hiiadmin.listen.listener.PersonDataChangeListener
import hiiadmin.listen.listener.SaasMsgListener

// Place your Spring DSL code here
beans = {
    AutoConfigureBeans(AutoConfigureBeans)
    personDataChangeListener(PersonDataChangeListener)
    gatingIssueTaskListener(GatingIssueTaskListener)
    deviceChangeListener(DeviceChangeListener)
    openSearchConfiguration(OpenSearchConfiguration)
    saasMsgListener(SaasMsgListener)

//    validator(){
//        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
//                .configure()
//        // 快速失败模式
//                .failFast(true)
//                .buildValidatorFactory()
//        return validatorFactory.getValidator()
//    }
}

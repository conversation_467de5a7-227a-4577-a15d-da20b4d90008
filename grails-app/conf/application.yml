---
grails:
  profile: rest-api
  codegen:
    defaultPackage: hiiadmin
  transaction:
    chainedTransactionManagerPostProcessor:
      enabled: true
  gorm:
    reactor:
      # Whether to translate GORM events into Reactor events
      # Disabled by default for performance reasons
      events: false
#        default:
#            constraints:
#                nullable: true
#                blank: true
#                failOnError: true
#                flush: true
#                version: true
info:
  app:
    name: '@info.app.name@'
    version: '@info.app.version@'
    grailsVersion: '@info.app.grailsVersion@'
spring:
  application:
    name: hiiadmin
  #  cloud:
  #    nacos:
  #      discovery:
  ##        server-addr: mse-09280d20-p.nacos-ans.mse.aliyuncs.com:8848
  #        server-addr: mse-09280d20-nacos-ans.mse.aliyuncs.com:8848
  #        enabled: true
  jmx:
    unique-names: true
  main:
    banner-mode: "off"
  groovy:
    template:
      check-template-location: false
  devtools:
    restart:
      additional-exclude:
        - '*.gsp'
        - '**/*.gsp'
        - '*.gson'
        - '**/*.gson'
        - 'logback.groovy'
        - '*.properties'
management:
  endpoints:
    enabled: true
    enabled-by-default: true
  endpoint:
    health:
      show-details: always
---
grails:
  mime:
    disable:
      accept:
        header:
          userAgents:
            - Gecko
            - WebKit
            - Presto
            - Trident
    types:
      json:
        - application/json
        - text/json
      hal:
        - application/hal+json
        - application/hal+xml
      xml:
        - text/xml
        - application/xml
      atom: application/atom+xml
      css: text/css
      csv: text/csv
      js: text/javascript
      rss: application/rss+xml
      text: text/plain
      all: '*/*'
  urlmapping:
    cache:
      maxsize: 1000
  controllers:
    defaultScope: singleton
  converters:
    encoding: UTF-8
  databinding:
    date-formats: yyyy-MM-dd HH:mm:ss
#grails:
#    env: local
---

---
dataSources:
  dataSource:
    pooled: true
    jmxExport: true
    driverClassName: com.mysql.cj.jdbc.Driver
    dialect: org.hibernate.dialect.MySQL55Dialect
    dbCreate: "validate" # one of 'create', 'create-drop', 'update', 'validate', ''
---
qiniu:
  domain: "http://res.yunzhiyuan100.com"
  bucket: "bugu"
---
# feign调用超时时间配置
feign:
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
  httpclient:
    connection-timeout: 80000
---
environments:
  development:
    spring:
      profiles:
        include: dev
  test:
    spring:
      profiles:
        include: test
  production:
    spring:
      profiles:
        include: prod
---
server:
  port: 25080
---
aliyun:
  accessKey: LTAIvKscF8qNTVzZ
  secretKey: U23XZSEw4uWvgHdVMEp8HbJrW7dAwV
package hiiadmin.store

import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.store.PropertyMapper
import hiiadmin.mapstruct.store.StorePropertyDtoMapper
import hiiadmin.messageControl.MessageControlCenterService
import hiiadmin.messageControl.MessageControlPersonService
import hiiadmin.module.messageControl.MessageControlPersonVO
import hiiadmin.module.store.StorePropertyVO
import hiiadmin.module.store.dto.StorePropertyDTO
import hiiadmin.msg.MessageService
import hiiadmin.school.TeacherService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.joda.time.DateTime
import org.joda.time.Months
import org.springframework.beans.factory.annotation.Autowired
import timetabling.messageControl.MessageControlCenter
import timetabling.store.StoreProperty
import timetabling.user.Teacher

@Transactional
class PropertyService {

    PropertyUpdateRecordService propertyUpdateRecordService

    TeacherService teacherService

    @Autowired
    PropertyMapper propertyMapper

    @Autowired
    StorePropertyDtoMapper propertyDtoMapper

    MessageControlCenterService messageControlCenterService

    MessageControlPersonService messageControlPersonService

    MessageService messageService

    StoreProperty save(StoreProperty property) {
        property.save(failOnError: true)
    }

    StoreProperty fetchPropertyById(Long id) {
        StoreProperty.findById(id)
    }

    StoreProperty fetchPropertyByCode(Long campusId, String code) {
        StoreProperty.findByCampusIdAndCodeAndStatusAndParentIdIsNull(campusId, code, 1)
    }

    List<StoreProperty> fetAllChildProperty(List<Long> parentIds) {
        StoreProperty.findAllByParentIdInListAndStatus(parentIds, 1)
    }

    List<StoreProperty> fetchAllPropertyByParentId(Long parentId) {
        StoreProperty.findAllByParentIdAndStatus(parentId, 1)
    }

    Map buildHQL(StringBuilder stringBuilder, Long campusId, Long parentId, Long useUserId, Long useDepartmentId, Long gainDate, String searchValue) {
        Map map = [:]
        stringBuilder.append(" FROM StoreProperty sp WHERE campusId = :campusId ")
        map.put("campusId", campusId)
        if (parentId) {
            stringBuilder.append(" AND parentId = :parentId")
            map.put("parentId", parentId)
        } else {
            stringBuilder.append(" AND parentId is null")
        }
        if (useUserId) {
            stringBuilder.append(" AND useUserId = :useUserId")
            map.put("useUserId", useUserId)
        }
        if (useDepartmentId) {
            stringBuilder.append(" AND useDepartmentId = :useDepartmentId")
            map.put("useDepartmentId", useDepartmentId)
        }
        if (gainDate) {
            stringBuilder.append(" AND gainDate BETWEEN :start AND :end")
            map.put("start", TimeUtils.getDateStartTime(gainDate))
            map.put("end", TimeUtils.getDateEndTime(gainDate))
        }
        if (searchValue) {
            stringBuilder.append(" AND (name like :searchValue OR code like :searchValue)")
            map.put("searchValue", "%" + searchValue + "%")

        }
        stringBuilder.append(" AND status = 1")

        map
    }

    List<StoreProperty> fetchPropertyLimit(Long campusId, Long parentId, Long useUserId, Long useDepartmentId, Long gainDate, String searchValue, Integer p, int s = 30) {
        StringBuilder stringBuilder = new StringBuilder("SELECT sp ")
        Map map = buildHQL(stringBuilder, campusId, parentId, useUserId, useDepartmentId, gainDate, searchValue)

        if (p) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        stringBuilder.append(" ORDER BY id desc")
        List<StoreProperty> propertyList = StoreProperty.executeQuery(stringBuilder.toString(), map)
        propertyList

    }

    Integer countPropertyLimit(Long campusId, Long parentId, Long useUserId, Long useDepartmentId, Long gainDate, String searchValue) {
        StringBuilder stringBuilder = new StringBuilder("SELECT COUNT(1) ")
        Map map = buildHQL(stringBuilder, campusId, parentId, useUserId, useDepartmentId, gainDate, searchValue)
        stringBuilder.append(" ORDER BY id desc")
        StoreProperty.executeQuery(stringBuilder.toString(), map)[0] as Integer ?: 0
    }

    StoreProperty createAndSaveProperty(Long campusId, Long userId, Long parentId, StorePropertyDTO propertyDTO) {

        if (!parentId && propertyDTO.code) {
            StoreProperty codeProperty = fetchPropertyByCode(campusId, propertyDTO.code)
            if (codeProperty) {
                throw new HiiAdminException("资产编号已存在，请重新输入！")
            }
        }
        StoreProperty property = new StoreProperty()
        propertyDtoMapper.dtoIntoDomain(propertyDTO, property)
        property.parentId = parentId
        property.status = 1
        property.campusId = campusId
        property.save(failOnError: true)
        propertyUpdateRecordService.createAndSaveUpdateRecord(campusId, property.id, userId, null, "新增", null)
        property
    }

    List<StoreProperty> fetchAllProperty(Long campusId) {
        StoreProperty.findAllByCampusIdAndStatus(campusId, 1)
    }

    List<StorePropertyVO> transformAllProperty(Long campusId, Long parentId, Long useUserId, Long useDepartmentId, Long gainDate, String searchValue) {
        List<StoreProperty> propertyList = fetchPropertyLimit(campusId, parentId, useUserId, useDepartmentId, gainDate, searchValue, null, 30)
        Multimap<Long, StoreProperty> parentIdPropertyMap = HashMultimap.create()

        List<StoreProperty> childPropertyList = fetAllChildProperty(propertyList*.id)
        childPropertyList.each { StoreProperty it ->
            parentIdPropertyMap.put(it.parentId, it)
        }
        List<StorePropertyVO> propertyVOList = transformPropertyVOByList(campusId, propertyList)
        propertyVOList.each {
            List<StoreProperty> child = parentIdPropertyMap.get(PhenixCoder.decodeId(it.id)).toList()
            if (child && child.size() > 0) {
                List<StorePropertyVO> children = transformPropertyVOByList(campusId, child)
                it.children = children
            }
        }

        propertyVOList
    }

    List<StorePropertyVO> transformPropertyVOByList(Long campusId, List<StoreProperty> propertyList) {


        List<Teacher> teacherList = teacherService.fetchAllTeacherByIdInList(propertyList*.useUserId)
        Map<Long, Teacher> teacherMap = [:]
        teacherList.each {
            teacherMap.put(it.id, it)
        }
        List<StorePropertyVO> propertyVOList = []
        propertyList.each {
            StorePropertyVO propertyVO = propertyMapper.convert2PropertyVO(it)
            propertyVO.useUserName = teacherMap.get(it.useUserId)?.name ?: ""
            propertyVO.usedMonth = Months.monthsBetween(new DateTime(propertyVO.serviceDate), new DateTime()).months
            propertyVOList << propertyVO
        }
        propertyVOList
    }


    def deleteProperty(Long id, Long campusId, Long userId) {
        StoreProperty property = fetchPropertyById(id)
        property.status = 0
        save(property)
        propertyUpdateRecordService.createAndSaveUpdateRecord(campusId, property.id, userId, null, "删除", null)
        List<StoreProperty> children = fetchAllPropertyByParentId(id)
        if (children && children.size() > 0) {
            children.each {
                it.status = 0
                propertyUpdateRecordService.createAndSaveUpdateRecord(campusId, property.id, userId, null, "删除", null)
            }
            children*.save(failOnError: true)
        }

    }


    /**
     * 定时任务 自动报废已过期资产
     * @param campusId
     * @return
     */
    def autoScrapProperty(long campusId) {
        log.info("updateUsefulProperty @campusId${campusId}")
        List<StoreProperty> propertyList = fetchAllProperty(campusId)
        propertyList.each {
            if ((it.autoScrap == null || it.autoScrap != false) && it.useStatus != ConstantEnum.PropertyStatus.SCRAP.status) {
                Date usefulDate = TimeUtils.getDateAfterMonth_D(it.serviceDate, it.serviceLifePeriod)
                if (usefulDate <= new Date()) {
                    it.useStatus = ConstantEnum.PropertyStatus.SCRAP.status
                    it.autoScrap = true
                    propertyUpdateRecordService.createAndSaveUpdateRecord(campusId, it.id, null, "useStatus", "使用状态", ConstantEnum.PropertyStatus.SCRAP.name)
                    MessageControlCenter messageControlCenter = messageControlCenterService.getMessageControlCenter(campusId, ConstantEnum.MessageControlCenterType.PROPERTY_SCRAP.type)
                    if (messageControlCenter.openStatus) {
                        def (List<MessageControlPersonVO> teacherVOList, List<MessageControlPersonVO> roleVOList, List<MessageControlPersonVO> departmentVOList) = messageControlPersonService.transformMessageControlPersonVO(messageControlCenter)
                        messageService.sendPropertyScrapMessage(campusId, teacherVOList*.personId, it)
                    }
                }

            }
        }
        propertyList*.save(failOnError: true)
    }
}

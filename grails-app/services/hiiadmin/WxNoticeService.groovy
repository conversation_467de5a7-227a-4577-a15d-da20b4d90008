package hiiadmin

import com.google.common.collect.Lists
import grails.gorm.transactions.Transactional
import hiiadmin.school.SchoolService
import hiiadmin.school.TermService
import hiiadmin.utils.TimeUtils
import me.chanjar.weixin.mp.api.WxMpService
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import timetabling.ExaminerEditor
import timetabling.ExaminerInfo
import timetabling.org.School
import timetabling.user.Teacher
import timetabling.user.User

@Transactional
class WxNoticeService {

    final static Logger logger = LoggerFactory.getLogger(WxNoticeService.class)

    final static String COLOR = "#743A3A"

    WxMpService wxMpService

    UserService userService

    TermService termService

    EnvService envService

    SchoolService schoolService

    /**
     * 发送试卷上传完毕微信通知
     * @param teacherList
     * @param examinerEditor
     * @return
     */
    def sendExaminationPaperUploadDoneMessage(List<Teacher> teacherList, ExaminerEditor examinerEditor, Long classId) {
        ExaminerInfo examinerInfo = ExaminerInfo.findByEditorId(examinerEditor.id)
        teacherList?.each {
            Teacher teacher ->
                User user = userService.fetchTeacherUserByUId(teacher.id)
                String openId = user?.openId
                Long schoolId = termService.getSchoolIdByUnitId(classId)
                School school = schoolService.fetchSchoolById(schoolId)
                if (StringUtils.isNotBlank(openId)) {
                    doSendExaminationPaperUploadedDoneMessage(school, examinerEditor, examinerInfo, openId, classId)
                    logger.info("试卷上传推送微信消息,teacherId:{},", teacher.id)
                } else {
                    logger.warn('试卷上传完毕，推送消息给老师，openId null. examinerEditorId:{}', examinerEditor.id)
                }
        }
    }

    /**
     * do发送试卷上传完毕微信通知
     * @param examinerEditor
     * @param openId
     * @return
     */
    @Async
    def doSendExaminationPaperUploadedDoneMessage(School school, ExaminerEditor examinerEditor, ExaminerInfo examinerInfo, String openId, Long classId) {
        WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage()
        if (envService.dev) {
            wxMpTemplateMessage.templateId = ConstantEnum.MessageCatesDaily.HOME_SCHOOL_CONNECT_DAILY.templateId
        } else if (envService.test) {
            wxMpTemplateMessage.templateId = ConstantEnum.MessageCatesTest.HOME_SCHOOL_CONNECT.templateId
        } else {
            wxMpTemplateMessage.templateId = ConstantEnum.MessageCates.HOME_SCHOOL_CONNECT.templateId
        }
        List<WxMpTemplateData> wxMpTemplateDataList = Lists.newArrayList()
        wxMpTemplateMessage.toUser = openId
        String result = ""
        WxMpTemplateData firstData = new WxMpTemplateData()
        firstData.name = "first"
        firstData.value = "试卷上传完毕通知"

        WxMpTemplateData keyword1 = new WxMpTemplateData()
        keyword1.name = "keyword1"
        keyword1.value = school?.name
        keyword1.color = COLOR

        WxMpTemplateData keyword2 = new WxMpTemplateData()
        keyword2.name = "keyword2"
        keyword2.value = "系统自动"
        keyword2.color = COLOR

        WxMpTemplateData keyword3 = new WxMpTemplateData()
        keyword3.name = "keyword3"
        keyword3.value = TimeUtils.getTimeStr(examinerEditor.lastUpdated.time, TimeUtils.TIME_FORMAT)
        keyword3.color = COLOR

        WxMpTemplateData keyword4 = new WxMpTemplateData()
        keyword4.name = "keyword4"
        keyword4.value = "试卷上传完成"
        keyword4.color = COLOR

        wxMpTemplateDataList.add(firstData)
        wxMpTemplateDataList.add(keyword1)
        wxMpTemplateDataList.add(keyword2)
        wxMpTemplateDataList.add(keyword3)
        wxMpTemplateDataList.add(keyword4)

        wxMpTemplateMessage.data = wxMpTemplateDataList

        //examiner/14?classId=23&gradeId=2&type=2
        if (envService.prod) {
            wxMpTemplateMessage.url = 'https://smart-campus-mp.yunzhiyuan100.com/#/6/examiner/' + examinerInfo.id + "?classId=" + classId + "&gradeId=" + examinerEditor.gradeId + "&type=" + examinerEditor.type
        } else if (envService.dev) {
            wxMpTemplateMessage.url = 'http://smart-campus-mp.yunzhiyuan100.com.cn/#/6/examiner/' + examinerInfo.id + "?classId=" + classId + "&gradeId=" + examinerEditor.gradeId + "&type=" + examinerEditor.type
        } else {
            wxMpTemplateMessage.url = 'http://smart-campus-mp-test.yunzhiyuan100.com.cn/#/6/examiner/' + examinerInfo.id + "?classId=" + classId + "&gradeId=" + examinerEditor.gradeId + "&type=" + examinerEditor.type
        }

        try {
            result = wxMpService.templateMsgService.sendTemplateMsg(wxMpTemplateMessage)
        } catch (Exception e) {
            logger.error("wx msg error ---  openId@${openId} ----  msg@${e.message}")
        }
        logger.info("wx mp message push. result:${result} --- openId@${openId}")
        return result
    }
}

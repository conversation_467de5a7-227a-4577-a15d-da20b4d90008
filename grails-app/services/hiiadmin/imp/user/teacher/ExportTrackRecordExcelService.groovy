package hiiadmin.imp.user.teacher

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.factory.imp.AbstractTransformExportRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.ferries.FerriesService
import hiiadmin.file.FileUpService
import hiiadmin.history.HistoryTrackService
import hiiadmin.imp.ImpRecordService
import hiiadmin.module.gated.TrackVO
import hiiadmin.school.CampusService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.track.TrackService
import hiiadmin.track.TrackVODOService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.SExcelUtil
import hiiadmin.utils.TimeUtils
import hiiadmin.utils.ToStringUnits
import hiiadmin.visitor.VisitorService
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.joda.time.DateTime
import timetabling.gated.Ferries
import timetabling.history.HistoryTrack
import timetabling.imp.ImpRecord
import timetabling.org.Campus
import timetabling.track.Track
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import static hiiadmin.ConstantEnum.*
import static hiiadmin.factory.imp.ExcelTips.TRACK_TIPS_DATE
import static hiiadmin.factory.imp.ExcelTips.TRACK_TIPS_SINGLE

@Slf4j
@Transactional
class ExportTrackRecordExcelService extends AbstractTransformExportRecordFileService {

    StudentService studentService

    TeacherService teacherService

    ParentService parentService

    FerriesService ferriesService

    VisitorService visitorService

    UserEncodeInfoService userEncodeInfoService

    CampusService campusService

    TrackService trackService

    TrackVODOService trackVODOService

    HistoryTrackService historyTrackService

    ImpRecordService impRecordService

    FileUpService fileUpService

//    def trackRecord(ImpRecord impRecord, Long startDate, Long endDate, Byte userType, Byte exportType, Long userId) {
    /**
     * trackRecord
     * @param impEntryDTO
     */
    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        Long startDate = impEntryDTO.startDate
        Long endDate = impEntryDTO.endDate
        Byte userType = impEntryDTO.userType
        Byte exportType = impEntryDTO.exportType
        Long userId = impEntryDTO.userId

        Long campusId = impRecord.campusId
        String startDateStr = TimeUtils.getTimeStr(startDate, "yyyy.MM.dd")
        String endDateStr = TimeUtils.getTimeStr(endDate, "yyyy.MM.dd")
        StringBuilder fileName = new StringBuilder()
        if (exportType == TrackExportType.SINGLE.type) {
            switch (userType) {
                case UserTypeEnum.STUDENT.type:
                    Student student = studentService.fetchStudentById(userId)
                    if (student) {
                        fileName.append("学生个人通行记录-").append(student.name).append(student.code)
                        fileName.append("-${System.currentTimeMillis()}.xlsx")
                    } else {
                        throw new HiiAdminException("未查找到学生，请重新输入学号")
                    }
                    break
                case UserTypeEnum.TEACHER.type:
                    Teacher teacher = teacherService.fetchTeacherById(userId)
                    if (teacher) {
                        fileName.append("教职工个人通行记录-").append(teacher.name).append("老师").append(userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id))
                        fileName.append("-${System.currentTimeMillis()}.xlsx")
                    } else {
                        throw new HiiAdminException("未查找到教师!")
                    }
                    break
                case UserTypeEnum.PARENT.type:
                    Parent parent = parentService.fetchParentById(userId, false)
                    if (parent) {
                        fileName.append("家长个人通行记录-").append(parent.name).append(parent.mobile)
                        fileName.append("-${System.currentTimeMillis()}.xlsx")
                    } else {
                        throw new HiiAdminException("未找到家长！")
                    }
                    break
                case UserTypeEnum.FERRIES.type:
                    Ferries ferries = ferriesService.fetchFerriesByFerriesId(userId)
                    if (ferries) {
                        fileName.append("接送人个人通行记录-").append(ferries.name).append(userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id))
                        fileName.append("-${System.currentTimeMillis()}.xlsx")
                    } else {
                        throw new HiiAdminException("未找到接送人！")
                    }
                    break
                case UserTypeEnum.VISITORS.type:
                    Visitor visitor = visitorService.fetchVisitorById(userId)
                    if (visitor) {
                        fileName.append("访客个人通行记录-").append(visitor.visitorName).append(userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitor.id))
                        fileName.append("-${System.currentTimeMillis()}.xlsx")
                    } else {
                        throw new HiiAdminException("未找到访客！")
                    }
            }
        }

        if (exportType == TrackExportType.USERTYPE.type) {
            String name = UserTypeEnum.getEnumByType(userType).name
            fileName.append("${name}人员通行记录-${startDateStr}~${endDateStr}-${System.currentTimeMillis()}.xlsx")
        }
        try {
            ServiceResult<String> serviceResult = exportTrackRecord(campusId, startDate, endDate, fileName.toString(), userType, exportType, userId)
            Date date = new Date()
            impRecord.dateCreated = date
            impRecord.lastUpdated = date
            if (serviceResult.success) {
                impRecord.fileName = fileName
                impRecord.fileUrl = serviceResult.result
                impRecordService.saveSuccessImpRecord(impRecord)
            } else {
                impRecord.fileName = fileName
                impRecordService.saveFailureImpRecord(serviceResult.message, impRecord)
            }
        } catch (Exception e) {
            log.error("导出失败{}", e)
            impRecordService.saveFailureImpRecord(e.message, impRecord)
        }
    }

    ServiceResult<String> exportTrackRecord(Long campusId, Long startDate, Long endDate, String fileName, Byte userType, Byte exportType, Long userId) {
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook()
        List<Long> timeStampList = TimeUtils.getDayTimeStampSection(startDate, endDate)
        List<Track> trackList = []
        List<HistoryTrack> historyTrackList = []
        List<TrackVO> vos = []
        String sheetName = ""
        if (exportType == TrackExportType.SINGLE.type) {
            if (startDate && endDate) {
                startDate = new DateTime(startDate).withMillisOfDay(0).millis
                endDate = new DateTime(endDate).withMillisOfDay(0).plusDays(1).millis
            }
            trackList = trackService.fetchAllTrackByCampusIdAndUserIdBetweenTime(campusId, userId, startDate, endDate, userType)
            historyTrackList = historyTrackService.fetchAllTrackByCampusIdAndUserIdBetweenTime(campusId, userId, startDate, endDate, userType)
            trackList?.each { track ->
                TrackVO trackVO = trackVODOService.transformTrackVO(track)
                vos << trackVO
            }
            historyTrackList?.each { track ->
                TrackVO trackVO = trackVODOService.transformHistoryTrackVOV2(track)
                vos << trackVO
            }
            sheetName = "通行记录"
            sxssfWorkbook = buildTrackRecordWorkbook(campus, sxssfWorkbook, sheetName, TRACK_TIPS_SINGLE, userType, vos)
        }

        if (exportType == TrackExportType.USERTYPE.type) {
            timeStampList?.each { Long timeStamp ->
                vos = []
                sheetName = TimeUtils.getTimeStr(timeStamp, "yyyy-MM-dd")
                if (!ToStringUnits.belongsToHistoricalData(timeStamp, -30)) {
                    trackList = trackService.getTrackRecordByStamp(campusId, userType, timeStamp)
                } else {
                    historyTrackList = historyTrackService.getHistoryTrackRecordByStamp(campusId, userType, timeStamp)
                }

                trackList?.each { track ->
                    TrackVO trackVO = trackVODOService.transformTrackVO(track)
                    vos << trackVO
                }

                historyTrackList?.each { track ->
                    TrackVO vo = trackVODOService.transformHistoryTrackVOV2(track)
                    vos << vo
                }
                sxssfWorkbook = buildTrackRecordWorkbook(campus, sxssfWorkbook, sheetName, TRACK_TIPS_DATE, userType, vos)
            }
        }
        return fileUpService.processFile(sxssfWorkbook, fileName)
    }

    SXSSFWorkbook buildTrackRecordWorkbook(Campus campus, SXSSFWorkbook workbook, String sheetName, String tips, Byte userType, List<TrackVO> trackVOList) {
        SXSSFSheet sheet = null
        String startTime = ""
        String pushTime = ""
        switch (userType) {
            case UserTypeEnum.PARENT.type:
            case UserTypeEnum.FERRIES.type:
                sheet = SExcelUtil.buildTitle(workbook, [0: "姓名",
                                                         1: "人员类型",
                                                         2: "手机号",
                                                         3: "学生关系",
                                                         4: "车牌号",
                                                         5: "通行设备",
                                                         6: "通行时间",
                                                         7: "设备上报时间"], sheetName, tips)
                trackVOList?.eachWithIndex { TrackVO entry, int i ->
                    if (entry.viaTime) {
                        startTime = TimeUtils.getTimeStr(entry.viaTime, "yyyy/MM/dd HH:mm")
                    }
                    if (entry.pushTime) {
                        pushTime = TimeUtils.getTimeStr(entry.pushTime, "yyyy/MM/dd HH:mm")
                    }
                    SExcelUtil.buildSheetRow(sheet, i + 2, [0: entry.name,
                                                            1: entry.userTypeName,
                                                            2: entry.mobile,
                                                            3: entry.organizeName,
                                                            4: entry.licensePlate,
                                                            5: entry.viaName,
                                                            6: startTime,
                                                            7: pushTime])
                }
                break
            case UserTypeEnum.STUDENT.type:

                Map<Integer, String> titleMap = [0: "姓名",
                                                 1: "人员类型",
                                                 2: "学号",
                                                 3: "学段",
                                                 4: "年级",
                                                 5: "班级",
                                                 6: "通行设备",
                                                 7: "通行时间",
                                                 8: "设备上报时间"]
                if (campus.type == 2) {
                    titleMap = [0: "姓名",
                                1: "人员类型",
                                2: "学号",
                                3: "院系",
                                4: "专业",
                                5: "年级",
                                6: "班级",
                                7: "通行设备",
                                8: "通行时间",
                                9: "设备上报时间"]
                }
                sheet = SExcelUtil.buildTitle(workbook, titleMap, sheetName, tips)
                trackVOList?.eachWithIndex { TrackVO entry, int i ->
                    if (entry.viaTime) {
                        startTime = TimeUtils.getTimeStr(entry.viaTime, "yyyy/MM/dd HH:mm")
                    }
                    if (entry.pushTime) {
                        pushTime = TimeUtils.getTimeStr(entry.pushTime, "yyyy/MM/dd HH:mm")
                    }
                    Map<Integer, Object> resultMap = [0: entry.name,
                                                      1: entry.userTypeName,
                                                      2: entry.code,
                                                      3: entry.sectionName,
                                                      4: entry.gradeName,
                                                      5: entry.unitName,
                                                      6: entry.viaName,
                                                      7: startTime,
                                                      8: pushTime]
                    if (campus.type == 2) {
                        resultMap = [0: entry.name,
                                     1: entry.userTypeName,
                                     2: entry.code,
                                     3: entry.facultyName,
                                     4: entry.majorName,
                                     5: entry.gradeName,
                                     6: entry.unitName,
                                     7: entry.viaName,
                                     8: startTime,
                                     9: pushTime]
                    }
                    SExcelUtil.buildSheetRow(sheet, i + 2, resultMap)
                }
                break
            case UserTypeEnum.TEACHER.type:
                Map<Integer, String> titleMap = [0: "姓名",
                                                 1: "人员类型",
                                                 2: "工号",
                                                 3: "手机号",
                                                 4: "部门",
                                                 5: "车牌号",
                                                 6: "通行设备",
                                                 7: "通行时间",
                                                 8: "设备上报时间"]
                if (campus.type == 2) {
                    titleMap.put(4, "院系部处")
                }
                sheet = SExcelUtil.buildTitle(workbook, titleMap, sheetName, tips)
                trackVOList?.eachWithIndex { TrackVO entry, int i ->
                    if (entry.viaTime) {
                        startTime = TimeUtils.getTimeStr(entry.viaTime, "yyyy/MM/dd HH:mm")
                    }
                    if (entry.pushTime) {
                        pushTime = TimeUtils.getTimeStr(entry.pushTime, "yyyy/MM/dd HH:mm")
                    }
                    SExcelUtil.buildSheetRow(sheet, i + 2, [0: entry.name,
                                                            1: entry.userTypeName,
                                                            2: entry.code,
                                                            3: entry.mobile,
                                                            4: entry.organizeName,
                                                            5: entry.licensePlate,
                                                            6: entry.viaName,
                                                            7: startTime,
                                                            8: pushTime])
                }
                break
            case UserTypeEnum.VISITORS.type:
                sheet = SExcelUtil.buildTitle(workbook, [0: "姓名",
                                                         1: "人员类型",
                                                         2: "手机号",
                                                         3: "车牌号",
                                                         4: "通行设备",
                                                         5: "通行时间",
                                                         6: "设备上报时间"], sheetName, tips)
                trackVOList?.eachWithIndex { TrackVO entry, int i ->
                    if (entry.viaTime) {
                        startTime = TimeUtils.getTimeStr(entry.viaTime, "yyyy/MM/dd HH:mm")
                    }
                    if (entry.pushTime) {
                        pushTime = TimeUtils.getTimeStr(entry.pushTime, "yyyy/MM/dd HH:mm")
                    }
                    SExcelUtil.buildSheetRow(sheet, i + 2, [0: entry.name,
                                                            1: entry.userTypeName,
                                                            2: entry.mobile,
                                                            3: entry.licensePlate,
                                                            4: entry.viaName,
                                                            5: startTime,
                                                            6: pushTime])
                }
                break
        }
        return workbook
    }
}

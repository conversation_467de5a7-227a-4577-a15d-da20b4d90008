package hiiadmin.imp.user.teacher

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.auth.BgbApiCore
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.factory.imp.AbstractTransformImpRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.imp.ImpRecordService
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.mapstruct.PersonGroupMapper
import hiiadmin.school.TeacherService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.apache.commons.lang3.StringUtils
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.xssf.usermodel.XSSFCell
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.annotation.Propagation
import timetabling.imp.ImpRecord
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class ImpTeacherInfoUpdateExcelService extends AbstractTransformImpRecordFileService {

    TeacherService teacherService

    PersonDataChangeService personDataChangeService

    @Resource
    BgbApiCore bgbApiCore

    UserEncodeInfoService userEncodeInfoService

    @Autowired
    PersonGroupMapper personGroupMapper

    ImpRecordService impRecordService

    /**
     * updateTeacherInfoExcelAndSync4Firm
     * @param impEntryDTO
     */
    @Override
    @Transactional(propagation = Propagation.NEVER)
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        String token = impEntryDTO.token
        ServiceResult result = updateTeacherExcel(impRecord, token)
        if (result.success) {
            List<Teacher> teacherList = result.result
            updateTeacherInfo(teacherList, impRecord.campusId, token)
        }
    }

    /**
     * 导入教职工*/
    ServiceResult<List<Teacher>> updateTeacherExcel(ImpRecord impRecord, String token) {
        int rowIndex = 0
        InputStream ips = null
        XSSFWorkbook wb = null
        List<Teacher> teachers = []
        try {
            URL u = new URL(impRecord.fileUrl)
            ips = u.openStream()

            wb = new XSSFWorkbook(ips)
            XSSFSheet sheet = wb.getSheetAt(0)
            String impRecordMsg
            if (!sheet) {
                impRecordService.saveFailureImpRecord("""未找到sheet""", impRecord)
                return ServiceResult.failure("100")
            }
            List<String> cardNumList = []
            List<String> jobNumList = []
            for (Iterator ite = sheet.rowIterator(); ite.hasNext();) {
                XSSFRow row = (XSSFRow) ite.next()
                rowIndex = row.getRowNum() + 1

                //从数据行开始忽略填写须知和表头
                if (rowIndex >= 3) {
                    String name = null
                    String mobile = null
                    String jobNum = null
                    String sex = null
                    String cardNum = null
                    for (int idx = 0; idx < 6; idx++) {
                        XSSFCell cell = row.getCell(idx)
                        cell?.setCellType(CellType.STRING)
                        switch (idx) {
                            case 0:
                                name = cell?.getStringCellValue()?.trim()
                                break
                            case 1:
                                mobile = cell?.getStringCellValue()
                                break
                            case 2:
                                jobNum = cell?.getStringCellValue()
                                break
                            case 3:
                                sex = cell?.getStringCellValue()
                                break
                            case 4:
                                cardNum = cell?.getStringCellValue()
                                break
                        }
                    }
                    
                    if (StringUtils.isBlank(name) && StringUtils.isBlank(mobile) && StringUtils.isBlank(jobNum) && StringUtils.isBlank(sex)) {
                        log.info("name,mobile,jobNum,sex都为空，跳出循环")
                        break
                    }

                    if (cardNumList.contains(cardNum)) {
                        throw new HiiAdminException("有卡号相同,第${rowIndex}行,卡号@${cardNum}")
                    }
                    if (StringUtils.isNotBlank(cardNum)) {
                        cardNumList.add(cardNum)
                    }

                    //工号是否已存在
                    if (jobNum?.startsWith("T")) {
                        jobNum = jobNum.substring(1)
                    }
                    TeacherSchoolCampus isJobNum = teacherService.fetchTeacherSchoolCampusViaCampusIdAndJobNum(impRecord.campusId, "T" + jobNum)
                    if (isJobNum) {
                        throw new HiiAdminException("该工号：" + jobNum + "在当前校区下已存在，请勿重复创建")
                    }

                    if (jobNumList.contains(jobNum)) {
                        throw new HiiAdminException("有工号相同,第${rowIndex}行,工号@${jobNum}")
                    }
                    if (StringUtils.isNotBlank(jobNum)) {
                        jobNumList.add(jobNum)
                    }

                    Boolean gender = true
                    if (sex == '女') {
                        gender = false
                    }
                    Teacher teacher = teacherService.createAndSaveNewTeacher(impRecord.schoolId, impRecord.campusId, name, jobNum, mobile, null, cardNum, gender, null)
                    teachers << teacher
                }
            }
            impRecord.msg = "导入成功"
            impRecord.count = teachers.size() ?: 0
            impRecordService.saveSuccessImpRecord(impRecord)
            return ServiceResult.success(teachers)
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id},已成功处理行号至${rowIndex},添加教师${teachers*.id}".toString(), e)
            return ServiceResult.success(teachers)
        } finally {
            wb.close()
            ips.close()
        }
    }


    /**
     * 导入教师后添加其他信息
     * @param teacherList
     * @param campusId
     * @param token
     * @return
     */
    def updateTeacherInfo(List<Teacher> teacherList, Long campusId, String token) {
        try {
            List<Long> teacherIdList = teacherList*.id
            int changeBit = PersonDataChangeEnum.userBaseChangeHandlerBit()
            personDataChangeService.teachersDataChange(campusId, teacherIdList, null, null, changeBit, OperateType.ADD.type)
        } catch (RuntimeException e) {
            log.error("批量导入教师同步对接平台失败${e.message}".toString())
        }
        teacherList.each { teacher ->
            try {
                BgbApiCore.StaffEntry staffEntry = new BgbApiCore.StaffEntry(token: token,
                        username: teacher?.name,
                        nick: teacher.name,
                        appId: AppIdEnum.PC.appId,
                        mobile: userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id),
                        teacherId: teacher.id)
                bgbApiCore.saveStaff(staffEntry)
            } catch (Exception e) {
                log.error("批量导入教师同步创建用户失败techerId@${teacher.id}, e${e.message}".toString())
            }
        }
    }

}

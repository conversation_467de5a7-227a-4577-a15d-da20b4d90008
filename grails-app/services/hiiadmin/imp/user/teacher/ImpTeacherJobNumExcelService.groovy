package hiiadmin.imp.user.teacher

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.factory.imp.AbstractTransformImpRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.imp.ImpRecordService
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.school.TeacherService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import org.apache.commons.lang3.StringUtils
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.xssf.usermodel.XSSFCell
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.imp.ImpRecord
import timetabling.user.TeacherSchoolCampus

@Transactional
@Slf4j
class ImpTeacherJobNumExcelService extends AbstractTransformImpRecordFileService {

    ImpRecordService impRecordService

    TeacherService teacherService

    PersonDataChangeService personDataChangeService

    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        ServiceResult serviceResult = updateTeacherJobNum(impRecord)
        if (serviceResult.success) {
            try {
                int changeBit = PersonDataChangeEnum.onSyncUserHandlerBit()
                personDataChangeService.teachersDataChange(impRecord.campusId, serviceResult.result, impRecord.teacherId, ConstantEnum.UserTypeEnum.TEACHER.type, changeBit, ConstantEnum.OperateType.UPDATE.type, ConstantEnum.ImpType.TEACHER_JOB_NUM_IMPORT.name)
            } catch (Exception e) {
                log.error("批量导入教职工卡号对接平台失败", e)
            }
        }
    }


    ServiceResult<List<Long>> updateTeacherJobNum(ImpRecord impRecord) {
        InputStream inputStream = null
        XSSFWorkbook wb = null
        try {
            URL url = new URL(impRecord.fileUrl)
            inputStream = url.openStream()
            wb = new XSSFWorkbook(inputStream)

            List<Long> teacherIdList = []

            XSSFSheet sheet = wb.getSheetAt(0)

            String impRecordMsg = ""
            if (!sheet) {
                impRecordService.saveFailureImpRecord("""未找到sheet""", impRecord)

                return ServiceResult.failure("100")
            }

            int idx1 = 1
            List<String> jobNumList = []

            List<TeacherSchoolCampus> saveTeacherSchoolCampus = []

            List<TeacherSchoolCampus> teacherSchoolCampuses = teacherService.fetchAllByCampusId(impRecord.campusId)
            Map<Long, TeacherSchoolCampus> teacherIdJobNumMap = [:]
            teacherSchoolCampuses?.each {
                teacherIdJobNumMap.put(it.teacherId, it)
            }

            for (Iterator iterator = sheet.rowIterator(); iterator.hasNext();) {
                XSSFRow row = (XSSFRow) iterator.next()
                if (idx1 >= 3) {
                    String jobNum
                    XSSFCell cell = row.getCell(4)
                    if(cell){
                        cell.setCellType(CellType.STRING)
                        jobNum = cell.getStringCellValue()
                    }

                    if(StringUtils.isEmpty(jobNum)){
                        impRecordMsg = "工号为必填项：第${idx1}行"
                        impRecordService.saveFailureImpRecord(impRecordMsg, impRecord)
                        return ServiceResult.failure("100")
                    }

                    String teacherEncodeId = row.getCell(0).getStringCellValue()


                    if (!PatternUtils.validCardNum(jobNum) || jobNum.length()>20) {
                        impRecordMsg = "工号格式错误：第${idx1}行"
                        impRecordService.saveFailureImpRecord(impRecordMsg, impRecord)
                        return ServiceResult.failure("100")
                    }

                    Long teacherId = PhenixCoder.decodeId(teacherEncodeId)
                    teacherIdList << teacherId

                    TeacherSchoolCampus oldTeacherSchool = TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(impRecord.campusId, jobNum, 1 as byte)
                    if (jobNum && oldTeacherSchool && teacherId != oldTeacherSchool.teacherId) {
                        impRecordMsg = "工号系统内已存在:第${idx1}行"
                        impRecordService.saveFailureImpRecord(impRecordMsg, impRecord)
                        return ServiceResult.failure("100")
                    }


                    if (jobNum && jobNumList?.contains(jobNum)) {
                        impRecordMsg = "教职工工号:${jobNum}存在重复,第${idx1}行"
                        impRecordService.saveFailureImpRecord(impRecordMsg, impRecord)
                        return ServiceResult.failure("100")
                    } else {
                        jobNumList.add(jobNum)
                    }

                    TeacherSchoolCampus teacherSchoolCampus = teacherIdJobNumMap.get(teacherId)

                    if(teacherSchoolCampus){
                        teacherSchoolCampus.jobNum = "T" + jobNum
                        saveTeacherSchoolCampus << teacherSchoolCampus
                    }
                }
                idx1++
            }


            saveTeacherSchoolCampus*.save(failOnError: true)
            impRecord.msg = "导入成功"
            impRecord.count = saveTeacherSchoolCampus.size() ?: 0
            impRecordService.saveSuccessImpRecord(impRecord)
            return ServiceResult.success(teacherIdList)
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id}".toString(), e)
        } finally {
            wb.close()
            inputStream.close()
        }

        return ServiceResult.failure("100")
    }
}

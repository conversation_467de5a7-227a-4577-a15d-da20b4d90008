package hiiadmin.imp.user.teacher

import com.bugu.ServiceResult
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.factory.imp.AbstractTransformExportRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.file.FileUpService
import hiiadmin.imp.ImpRecordService
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.Department
import timetabling.imp.ImpRecord
import timetabling.org.Campus
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus
import timetabling.vocationalSchool.Faculty

import static hiiadmin.utils.ExcelUtil.*

/**
 * <AUTHOR>
 * @Date 2023-12-06 17:45
 */
@Slf4j
@Transactional
class ExportTeacherDepartmentDataExcelService extends AbstractTransformExportRecordFileService {

    UserEncodeInfoService userEncodeInfoService

    FileUpService fileUpService

    ImpRecordService impRecordService

    CampusService campusService

    TeacherService teacherService

    DepartmentService departmentService

    FacultyService facultyService

    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        try {
            ServiceResult result = buildTeacherDepartmentDataExcel(impRecord)
            if (result?.success) {
                impRecord.fileUrl = result.result
                impRecordService.saveSuccessImpRecord(impRecord)
            } else {
                impRecordService.saveFailureImpRecord(result?.message, impRecord)
            }
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id}".toString(), e)
        }
    }

    ServiceResult<String> buildTeacherDepartmentDataExcel(ImpRecord impRecord) {
        Long campusId = impRecord.campusId
        String title = ""
        Campus campus = campusService.fetchCampusById(campusId)
        List<Teacher> teacherList = teacherService.fetchAllTeacherByCampusIdLimit(impRecord.campusId, false)
        List<Department> departments = []
        List<Faculty> faculties = []
        Map<Long, String> teacherIdDepartmentIdsMap = [:]
        if (campus.type == 1 as byte) {
            title = "部门"
            departments = departmentService.fetchAllDepartmentByCampusId(campusId)
            teacherIdDepartmentIdsMap = departmentService.fetchAllDepartmentIdsByCampusIdAndTeacherIdList(campusId, teacherList*.id)
        } else {
            title = "院系部处"
            faculties = facultyService.fetchAllFacultyByCampusId(campusId)
            Multimap<Long, Long> teacherIdFacultyIdMap = facultyService.transformTeacherIdFacultyIdsByCampusId(campusId)
            teacherIdFacultyIdMap?.keySet()?.each {
                List<Long> facultyIds = teacherIdFacultyIdMap.get(it).toList()
                teacherIdDepartmentIdsMap.put(it, facultyIds.join(","))
            }
        }
        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacherList*.id, false)
        List<TeacherVO> list = []
        teacherList.each { Teacher teacher ->
            TeacherSchoolCampus teacherSchool = teacherService.fetchTeacherSchoolBySchoolIdAndCampusIdAndTeacherId(impRecord.schoolId, campusId, teacher.id)
            TeacherVO teacherVO = new TeacherVO(
                    id: PhenixCoder.encodeId(teacher.id),
                    name: teacher.name,
                    gender: teacher.gender,
                    code: teacherSchool.jobNum?.startsWith("T") ? teacherSchool.jobNum.substring(1) : teacherSchool.jobNum,
                    mobile: teacherIdMobileMap.get(teacher.id),
                    departmentIds: teacherIdDepartmentIdsMap?.get(teacher.id))

            list << teacherVO
        }

        Workbook workbook = transformTeacherRoleDataExcel(list, departments, faculties, campus?.type, title)
        String upName = "修改教职工${title}信息模板-${System.currentTimeMillis()}.xlsx"
        return fileUpService.processFile(workbook, upName)
    }

    Workbook transformTeacherRoleDataExcel(List<TeacherVO> list, List<Department> departmentList, List<Faculty> facultyList, Byte campusType, String title) {
        XSSFWorkbook workbook = new XSSFWorkbook()
        CellStyle canEditStyle = workbook.createCellStyle()
        canEditStyle.setAlignment(HorizontalAlignment.LEFT)
        canEditStyle.setLocked(false)  //设置列的锁定状态为未锁定

        //设置单元格为文本格式时需加上
        DataFormat format = workbook.createDataFormat()
        canEditStyle.setDataFormat(format.getFormat("@"))

        String tips = """注意事项：
<1>表头：不能增加、删除、修改表头信息；
<2>字段：姓名、性别、工号、手机号无法增加、删除、修改，${title}字段支持输入系统已有的${title}id，多个${title}id用英文“,”隔开；
<3>上传：上传表格前，请勿删除/增加任一教职工信息；
<4>${title}信息表：请勿修改${title}信息表。"""

        XSSFSheet sheet = buildTitle(workbook, [0: "ID",
                                                1: "姓名",
                                                2: "性别",
                                                3: "工号",
                                                4: "手机号",
                                                5: title], "教师信息表", tips)

        sheet.setColumnWidth(3, 40 * 256)

        XSSFSheet sheet2 = buildTitle(workbook, [0: "${title}id",
                                                                1: "${title}名称"], "${title}信息表")

        if (campusType == 1 as byte) {
            departmentList.eachWithIndex { Department department, int i ->
                buildSheetRow(sheet2, i + 1, [0: department.id,
                                              1: department.name])
            }
        } else {
            facultyList.eachWithIndex { Faculty faculty, int i ->
                buildSheetRow(sheet2, i + 1, [0: faculty.id,
                                              1: faculty.name])
            }
        }

        if (list.size() < 1) {
            return workbook
        }

        CellStyle cantEditStyle = workbook.createCellStyle()
        cantEditStyle.setAlignment(HorizontalAlignment.LEFT)
        cantEditStyle.setLocked(true)  //设置列的锁定状态为未锁定

        list.eachWithIndex { TeacherVO teacherVO, int i ->

            buildSheetRow(sheet, i + 2, [0: teacherVO.id,
                                         1: teacherVO.name,
                                         2: teacherVO.gender ? "男" : "女",
                                         3: teacherVO.code,
                                         4: teacherVO.mobile])
            buildSheetRow(sheet, i + 2, [5: teacherVO.departmentIds], CellType.STRING, canEditStyle)
        }
        lockingSheet(sheet)

        workbook
    }
}

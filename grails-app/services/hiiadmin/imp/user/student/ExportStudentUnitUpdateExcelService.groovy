package hiiadmin.imp.user.student

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.factory.imp.AbstractTransformExportRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.file.FileUpService
import hiiadmin.imp.ImpRecordService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.imp.ImpRecord
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Student

import static com.bugu.PhoenixCoder.encodeId
import static hiiadmin.factory.imp.ExcelTips.STUDENT_UNIT
import static hiiadmin.utils.ExcelUtil.*

@Slf4j
@Transactional
class ExportStudentUnitUpdateExcelService extends AbstractTransformExportRecordFileService {

    StudentService studentService

    GradeService gradeService

    UnitService unitService

    FileUpService fileUpService

    ImpRecordService impRecordService

    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        try {
            ServiceResult result = createAndUploadStudentUnit(impRecord.gradeId)
            if (result?.success) {
                impRecord.fileUrl = result.result
                impRecordService.saveSuccessImpRecord(impRecord)
            } else {
                impRecordService.saveFailureImpRecord(result?.message, impRecord)
            }
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id}".toString(), e)
        }
    }
    /**
     * 重新分班模板
     * @param gradeId
     * @return
     */
    ServiceResult<String> createAndUploadStudentUnit(Long gradeId) {
        Grade grade = gradeService.fetchGradeById(gradeId)
        List<Student> studentList = studentService.fetchAllStudentByGradeId(gradeId)
        Map<Long, Unit> longUnitMap = unitService.getStudentIdUnitMapByGradeId(gradeId)
        Workbook workbook = transformStudentUnit(studentList, longUnitMap, STUDENT_UNIT)

        String upName = "重新分班模板/${ConstantEnum.SectionType.getEnumByType(grade.sectionId).name}-${grade.name}-重新分班模板-${System.currentTimeMillis()}.xlsx"
        return fileUpService.processFile(workbook, upName)
    }

    Workbook transformStudentUnit(List<Student> studentList, Map<Long, Unit> longUnitMap, String tips) {
        XSSFWorkbook workbook = new XSSFWorkbook()
        String sheetName = "学生信息表"
        CellStyle canEditStyle = workbook.createCellStyle()
        canEditStyle.setAlignment(HorizontalAlignment.LEFT)

        canEditStyle.setLocked(false)  //设置列的锁定状态为未锁定
        XSSFSheet sheet = buildTitle(workbook, [0: "学生ID",
                                                1: "学号",
                                                2: "学生姓名",
                                                3: "性别",
                                                4: "*班级"], sheetName, tips)
        if (studentList.size() < 1) {
            return workbook
        }
        studentList.eachWithIndex { Student student, int i ->
            Unit unit = longUnitMap.get(student.id)
            String unitCode = unit.code
            int idx = i + 2
            buildSheetRow(sheet, idx, [0: encodeId(student.id),
                                       1: student.code,
                                       2: student.name,
                                       3: student.gender ? "男" : "女",])
            buildSheetRow(sheet, idx, [4: unitCode], canEditStyle)
        }
        lockingSheet(sheet)

        workbook
    }

}

package hiiadmin.imp.user.student

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.factory.imp.AbstractTransformExportRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.file.FileUpService
import hiiadmin.imp.ImpRecordService
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.utils.PhenixCoder
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.imp.ImpRecord
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.user.Student

import static com.bugu.PhoenixCoder.encodeId
import static hiiadmin.ConstantEnum.SectionType
import static hiiadmin.factory.imp.ExcelTips.STUDENT_CODE
import static hiiadmin.utils.ExcelUtil.*

@Slf4j
@Transactional
class ExportStudentCodeUpdateExcelService extends AbstractTransformExportRecordFileService {

    StudentService studentService

    GradeService gradeService

    UnitService unitService

    FileUpService fileUpService

    ImpRecordService impRecordService

    UnitStudentService unitStudentService

    CampusService campusService

    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        try {
            Campus campus = campusService.fetchCampusById(impRecord.campusId)
            ServiceResult result = null
            if (campus.type == ConstantEnum.CampusType.K12.type) {
                result = createAndUploadStudentCode(impRecord.gradeId)
            } else {
                result = createAndUploadStudentCode4v(impRecord.campusId, impEntryDTO.unitIds)
            }
            if (result?.success) {
                impRecord.fileUrl = result.result
                impRecordService.saveSuccessImpRecord(impRecord)
            } else {
                impRecordService.saveFailureImpRecord(result?.message, impRecord)
            }
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id}".toString(), e)
        }
    }


    /**
     * 修改学生学号信息模板
     * @param gradeId
     * @return
     */
    ServiceResult<String> createAndUploadStudentCode(Long gradeId) {
        Grade grade = gradeService.fetchGradeById(gradeId)
        List<Student> studentList = studentService.fetchAllStudentByGradeId(gradeId)
        List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId, false)
        Map<Long, String> longUnitMap = [:]
        unitList.each { unit ->
            List<UnitStudent> unitStudentList = unitStudentService.fetchAllNormalUnitStudentByUnitId(unit.id)
            unitStudentList.each {
                longUnitMap.put(it.studentId, unit.name)
            }
        }
        Workbook workbook = transformStudentCode(studentList, longUnitMap, grade?.name, STUDENT_CODE)

        String upName = "修改学生学号信息模板/${SectionType.getEnumByType(grade.sectionId).name}-${grade.name}-修改学生学号信息模板-${System.currentTimeMillis()}.xlsx"
        return fileUpService.processFile(workbook, upName)
    }


    ServiceResult<String> createAndUploadStudentCode4v(Long campusId, String unitIds) {
        List<Long> unitIdList = unitIds.split(",").collect { PhenixCoder.decodeId(it) }
        List<Student> studentList = studentService.fetchAllStudentByUnitIdInListLimit(unitIdList, 0, 0)
        List<Unit> unitList = unitService.fetchAllAdminUnitByCampusId(campusId)

        Map<Long, String> longUnitMap = [:]
        unitList.each { unit ->
            if (unitIdList.remove(unit.id)) {
                List<UnitStudent> unitStudentList = unitStudentService.fetchAllNormalUnitStudentByUnitId(unit.id)
                unitStudentList.each {
                    longUnitMap.put(it.studentId, unit.name)
                }
            }
        }

        Workbook workbook = transformStudentCode(studentList, longUnitMap, null, STUDENT_CODE)

        String upName = "修改学生学号信息模板/修改学生学号信息模板-${System.currentTimeMillis()}.xlsx"
        return fileUpService.processFile(workbook, upName)
    }


    Workbook transformStudentCode(List<Student> studentList, Map<Long, String> longUnitMap, String gradeName, String tips) {
        XSSFWorkbook workbook = new XSSFWorkbook()
        String sheetName = "学生信息表"
        CellStyle canEditStyle = workbook.createCellStyle()
        canEditStyle.setAlignment(HorizontalAlignment.LEFT)

        canEditStyle.setLocked(false)  //设置列的锁定状态为未锁定
        XSSFSheet sheet = buildTitle(workbook, [0: "学生ID",
                                                1: "班级",
                                                2: "学生姓名",
                                                3: "性别",
                                                4: "*学号",], sheetName, tips)
        if (studentList.size() < 1) {
            return workbook
        }
        studentList.eachWithIndex { Student student, int i ->
            String unitName = longUnitMap.get(student.id)
            if (gradeName) {
                unitName = gradeName + unitName
            }
            buildSheetRow(sheet, i + 2, [0: encodeId(student.id),
                                         1: unitName,
                                         2: student.name,
                                         3: student.gender ? "男" : "女",])
            buildSheetRow(sheet, i + 2, [4: student.code], canEditStyle)
        }
        lockingSheet(sheet)

        workbook
    }

}

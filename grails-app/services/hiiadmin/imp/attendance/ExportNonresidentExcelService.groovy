package hiiadmin.imp.attendance

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.attendance.NonresidentService
import hiiadmin.factory.imp.AbstractTransformExportRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.file.FileUpService
import hiiadmin.imp.ImpRecordService
import hiiadmin.module.attendance.NonresidentVO
import hiiadmin.school.CampusService
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.joda.time.DateTime
import timetabling.imp.ImpRecord
import timetabling.org.Campus

import static hiiadmin.utils.ExcelUtil.buildSheetRow
import static hiiadmin.utils.ExcelUtil.buildTitle

@Slf4j
@Transactional
class ExportNonresidentExcelService extends AbstractTransformExportRecordFileService {

    CampusService campusService

    NonresidentService nonresidentService

    FileUpService fileUpService

    ImpRecordService impRecordService

    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        try {
            ServiceResult result = createNonresident(impRecord.campusId)
            if (result?.success) {
                impRecord.fileUrl = result.result
                impRecordService.saveSuccessImpRecord(impRecord)
            } else {
                impRecordService.saveFailureImpRecord(result?.message, impRecord)
            }
        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("导入出错,impRecordId:${impRecord.id}".toString(), e)
        }
    }


    ServiceResult<String> createNonresident(Long campusId) {
        Campus campus = campusService.fetchCampusById(campusId)
        List<NonresidentVO> nonresidentVOList = nonresidentService.transformTodayNonresidentVOByCampusId(campusId, null)

        Workbook workbook = null
        if (campus.type == 1) {
            workbook = getNonresidentExcel(nonresidentVOList)
        } else {
            workbook = getNonresidentExcel4Vocational(nonresidentVOList)
        }
//        String fileName = "导出通校生名单.xlsx"
        String upName = "通校生名单/导出通校生名单-${System.currentTimeMillis()}.xlsx"
        return fileUpService.processFile(workbook, upName)
    }

    /**
     * 获取通校生
     * @param list
     * @return
     */
    XSSFWorkbook getNonresidentExcel(List<NonresidentVO> list) {
        Workbook workbook = new XSSFWorkbook()
        Sheet sheet = buildTitle(workbook, [0: "姓名",
                                            1: "学号",
                                            2: "班级",
                                            3: "寝室",
                                            4: "通校时间",
                                            5: "备注"], "通校生导出")
        list.eachWithIndex { nonresidentVO, i ->
            String date = null
            if (nonresidentVO.endTime) {
                date = new DateTime(nonresidentVO.endTime).toString("yyyy/MM/dd")
            }
            buildSheetRow(sheet, i + 1, [0: nonresidentVO.name,
                                         1: nonresidentVO.code,
                                         2: """${nonresidentVO.sectionName ?: ""}${nonresidentVO.gradeName}${nonresidentVO.unitName}""",
                                         3: nonresidentVO.building2BedName,
                                         4: date,
                                         5: nonresidentVO.memo])
        }
        workbook
    }

    /**
     * 获取通校生(职校版)
     * @param list
     * @return
     */
    XSSFWorkbook getNonresidentExcel4Vocational(List<NonresidentVO> list) {
        Workbook workbook = new XSSFWorkbook()
        Sheet sheet = buildTitle(workbook, [0: "姓名",
                                            1: "学号",
                                            2: "院系",
                                            3: "专业",
                                            4: "年级",
                                            5: "班级",
                                            6: "寝室",
                                            7: "通校时间",
                                            8: "备注"], "通校生导出")
        list.eachWithIndex { nonresidentVO, i ->
            String date = null
            if (nonresidentVO.endTime) {
                date = new DateTime(nonresidentVO.endTime).toString("yyyy/MM/dd")
            }
            String facultyName = nonresidentVO.facultyStatus == -1 ? nonresidentVO.facultyName + "[已删除]" : nonresidentVO.facultyName
            String majorName = nonresidentVO.majorStatus == -1 ? nonresidentVO.majorName + "[已删除]" : nonresidentVO.majorName

            buildSheetRow(sheet, i + 1, [0: nonresidentVO.name,
                                         1: nonresidentVO.code,
                                         2: facultyName,
                                         3: majorName,
                                         4: nonresidentVO.gradeName,
                                         5: nonresidentVO.unitName,
                                         6: nonresidentVO.building2BedName,
                                         7: date,
                                         8: nonresidentVO.memo])
        }
        workbook
    }
}

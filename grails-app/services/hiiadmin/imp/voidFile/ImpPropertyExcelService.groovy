package hiiadmin.imp.voidFile

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.factory.imp.AbstractTransformImpRecordFileService
import hiiadmin.factory.imp.ImpEntryDTO
import hiiadmin.imp.ImpRecordService
import hiiadmin.store.PropertyService
import hiiadmin.store.PropertyUpdateRecordService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.ExcelUtil
import org.apache.commons.lang3.StringUtils
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.imp.ImpRecord
import timetabling.store.StoreProperty
import timetabling.user.Teacher

import static hiiadmin.utils.PatternUtils.isNumeric
import static hiiadmin.utils.PatternUtils.isSpecial

@Slf4j
@Transactional
class ImpPropertyExcelService extends AbstractTransformImpRecordFileService {

    ImpRecordService impRecordService

    UserEncodeInfoService userEncodeInfoService

    PropertyService propertyService

    PropertyUpdateRecordService propertyUpdateRecordService

    /**
     * readPropertyExcel
     * @param impEntryDTO
     */
    @Override
    void transformExcel(ImpEntryDTO impEntryDTO) {
        ImpRecord impRecord = impEntryDTO.impRecord
        Long classId = impEntryDTO.classId
        InputStream ips = null
        XSSFWorkbook workbook = null
        try {
            URL url = new URL(impRecord.fileUrl)
            ips = url.openStream()
            workbook = new XSSFWorkbook(ips)

            XSSFSheet sheet = workbook.getSheet("sheet0")

            if (!sheet) {
                impRecordService.saveFailureImpRecord("未找到sheet", impRecord)
                return
            }

            List<String> codeList =[]
            List<StoreProperty> propertyList = []
            for (Iterator iteRow = sheet.iterator(); iteRow.hasNext();) {
                XSSFRow row = (XSSFRow) iteRow.next()
                int rowIndex = row.getRowNum() + 1

                if (rowIndex >= 3) {
                    StoreProperty property = new StoreProperty()
                    String idx = ExcelUtil.getStringCellValue(row.getCell(0))
                    if(idx && !isNumeric(idx)){
                        impRecordService.saveFailureImpRecord("格式错误：A${rowIndex}单元格", impRecord)
                        return
                    }
                    if(idx && idx.size()>20){
                        impRecordService.saveFailureImpRecord("超出字符限制：A${rowIndex}单元格", impRecord)
                        return
                    }
                    property.idx = idx

                    String code = ExcelUtil.getStringCellValue(row.getCell(1))

                    if (code && code.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：B${rowIndex}单元格", impRecord)
                        return
                    }
                    if (StringUtils.isNotEmpty(code)) {
                        StoreProperty codeProperty = propertyService.fetchPropertyByCode(impRecord.campusId,code)
                        if (codeProperty || codeList.contains(code)) {
                            impRecordService.saveFailureImpRecord("字段内容系统内已存在：B${rowIndex}单元格", impRecord)
                            return
                        }
                    }
                    property.code = code
                    codeList << code

                    String lockStatus = ExcelUtil.getStringCellValue(row.getCell(2))
                    if (lockStatus && lockStatus.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：C${rowIndex}单元格", impRecord)
                        return
                    }
                    property.lockStatus = lockStatus

                    String name = ExcelUtil.getStringCellValue(row.getCell(3))
                    if (StringUtils.isEmpty(name)) {
                        impRecordService.saveFailureImpRecord("表格信息不完整：D${rowIndex}单元格", impRecord)
                        return
                    }
                    if (name.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：D${rowIndex}单元格", impRecord)
                        return
                    }
                    if(!isSpecial(name)){
                        impRecordService.saveFailureImpRecord("格式错误：D${rowIndex}单元格", impRecord)
                        return
                    }

                    property.name = name

                    String GS1Code = ExcelUtil.getStringCellValue(row.getCell(4))
                    if (GS1Code && GS1Code.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：E${rowIndex}单元格", impRecord)
                        return
                    }
                    property.gS1Code = GS1Code

                    BigDecimal initialValue = ExcelUtil.getBigDecimalCellValue(row.getCell(5))
                    if (initialValue && initialValue.scale() > 2) {
                        impRecordService.saveFailureImpRecord("格式错误：F${rowIndex}单元格", impRecord)
                        return
                    }
                    property.initialValue = initialValue

                    BigDecimal cumulativeDiscount = ExcelUtil.getBigDecimalCellValue(row.getCell(6))
                    if (cumulativeDiscount && cumulativeDiscount.scale() > 2) {
                        impRecordService.saveFailureImpRecord("格式错误：G${rowIndex}单元格", impRecord)
                        return
                    }
                    property.cumulativeDiscount = cumulativeDiscount

                    BigDecimal netValue = ExcelUtil.getBigDecimalCellValue(row.getCell(7))
                    if (netValue && netValue.scale() > 2) {
                        impRecordService.saveFailureImpRecord("格式错误：H${rowIndex}单元格", impRecord)
                        return
                    }
                    property.netValue = netValue

                    BigDecimal monthlyDiscountAmount = ExcelUtil.getBigDecimalCellValue(row.getCell(8))
                    if (monthlyDiscountAmount && monthlyDiscountAmount.scale() > 3) {
                        throw new HiiAdminException("格式错误：I${rowIndex}单元格")
                    }
                    property.monthlyDiscountAmount = monthlyDiscountAmount


                    Date gainDate = ExcelUtil.getDateCellValue(row.getCell(9))
                    if (!gainDate) {
                        impRecordService.saveFailureImpRecord("表格信息不完整：J${rowIndex}单元格", impRecord)
                        return
                    }
                    property.gainDate = gainDate

                    String num = ExcelUtil.getStringCellValue(row.getCell(10))
                    if(num && !isNumeric(num)){
                        impRecordService.saveFailureImpRecord("格式错误：K${rowIndex}单元格", impRecord)
                        return
                    }
                    if(num && num.size()>20){
                        impRecordService.saveFailureImpRecord("超出字符限制：K${rowIndex}单元格", impRecord)
                        return
                    }
                    property.num = num

                    String unitOfMeasurement = ExcelUtil.getStringCellValue(row.getCell(11))
                    if (unitOfMeasurement && unitOfMeasurement.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：L${rowIndex}单元格", impRecord)
                        return
                    }
                    property.unitOfMeasurement = unitOfMeasurement

                    String area = ExcelUtil.getStringCellValue(row.getCell(12))
                    if(area && !isNumeric(area)){
                        impRecordService.saveFailureImpRecord("格式错误：M${rowIndex}单元格", impRecord)
                        return
                    }
                    if(area && area.size()>20){
                        impRecordService.saveFailureImpRecord("超出字符限制：M${rowIndex}单元格", impRecord)
                        return
                    }
                    property.area = area

                    Optional<Integer> serviceLifePeriodOpt = ExcelUtil.getIntegerCellValue(row.getCell(13))
                    if (!serviceLifePeriodOpt.isPresent()) {
                        impRecordService.saveFailureImpRecord("表格信息不完整：N${rowIndex}单元格", impRecord)
                        return
                    }
                    property.serviceLifePeriod = serviceLifePeriodOpt.get()

                    String place = ExcelUtil.getStringCellValue(row.getCell(14))
                    if (place && place.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：O${rowIndex}单元格", impRecord)
                        return
                    }
                    property.place = place

                    String brand = ExcelUtil.getStringCellValue(row.getCell(15))
                    if (brand && brand.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：P${rowIndex}单元格", impRecord)
                        return
                    }
                    property.brand = brand

                    String specificationsModels = ExcelUtil.getStringCellValue(row.getCell(16))
                    if (specificationsModels && specificationsModels.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：Q${rowIndex}单元格", impRecord)
                        return
                    }
                    property.specificationsModels = specificationsModels

                    String accountingDocumentNumber = ExcelUtil.getStringCellValue(row.getCell(17))
                    if (accountingDocumentNumber && accountingDocumentNumber.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：R${rowIndex}单元格", impRecord)
                        return
                    }
                    property.accountingDocumentNumber = accountingDocumentNumber

                    String remark = ExcelUtil.getStringCellValue(row.getCell(18))
                    if (remark && remark.size() > 100) {
                        impRecordService.saveFailureImpRecord("超出字符限制：S${rowIndex}单元格", impRecord)
                        return
                    }
                    property.remark = remark

                    String useUser = ExcelUtil.getStringCellValue(row.getCell(19))
                    if (useUser) {
                        String[] t = useUser.split(",")
                        if (t.size() == 2 && t[1]) {
                            Teacher teacher = userEncodeInfoService.fetchTeacherByTrueMobile(t[1])
                            if (!teacher) {
                                impRecordService.saveFailureImpRecord("使用人在系统内不存在：T${rowIndex}单元格", impRecord)
                                return
                            }
                            property.useUserId = teacher.id
                        } else {
                            impRecordService.saveFailureImpRecord("格式错误：T${rowIndex}单元格", impRecord)
                            return
                        }
                    }

                    String useDepartment = ExcelUtil.getStringCellValue(row.getCell(20))
                    if (useDepartment) {
                        property.useDepartmentName = useDepartment
                    }

                    String propertyClass = ExcelUtil.getStringCellValue(row.getCell(21))
                    if (propertyClass && propertyClass.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：V${rowIndex}单元格", impRecord)
                        return
                    }
                    property.propertyClass = propertyClass


                    String nationalStandardCategory = ExcelUtil.getStringCellValue(row.getCell(22))
                    if (nationalStandardCategory && nationalStandardCategory.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：W${rowIndex}单元格", impRecord)
                        return
                    }
                    property.nationalStandardCategory = nationalStandardCategory

                    String useDirection = ExcelUtil.getStringCellValue(row.getCell(23))
                    if (useDirection && useDirection.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：X${rowIndex}单元格", impRecord)
                        return
                    }
                    property.useDirection = useDirection

                    String useStatus = ExcelUtil.getStringCellValue(row.getCell(24))
                    if (useStatus) {
                        property.useStatus = ConstantEnum.PropertyStatus.getEnumByName(useStatus).status
                    }


                    String licensePlateNumber = ExcelUtil.getStringCellValue(row.getCell(25))
                    if (licensePlateNumber && licensePlateNumber.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：Z${rowIndex}单元格", impRecord)
                        return
                    }
                    property.licensePlateNumber = licensePlateNumber

                    String assetClassificationCode = ExcelUtil.getStringCellValue(row.getCell(26))
                    if(assetClassificationCode && !isNumeric(assetClassificationCode)){
                        impRecordService.saveFailureImpRecord("格式错误：AA${rowIndex}单元格", impRecord)
                        return
                    }
                    if(assetClassificationCode && assetClassificationCode.size()>20){
                        impRecordService.saveFailureImpRecord("超出字符限制：AA${rowIndex}单元格", impRecord)
                        return
                    }
                    property.assetClassificationCode = assetClassificationCode


                    String gainingMethod = ExcelUtil.getStringCellValue(row.getCell(27))
                    if (gainingMethod && gainingMethod.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：AB${rowIndex}单元格", impRecord)
                        return
                    }
                    property.gainingMethod = gainingMethod

                    String seatingPosition = ExcelUtil.getStringCellValue(row.getCell(28))
                    if (seatingPosition && seatingPosition.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：AC${rowIndex}单元格", impRecord)
                        return
                    }
                    property.seatingPosition = seatingPosition

                    String discountedMonths = ExcelUtil.getStringCellValue(row.getCell(29))
                    if(discountedMonths && !isNumeric(discountedMonths)){
                        impRecordService.saveFailureImpRecord("格式错误：AD${rowIndex}单元格", impRecord)
                        return
                    }
                    if(discountedMonths && discountedMonths.size()>20){
                        impRecordService.saveFailureImpRecord("超出字符限制：AD${rowIndex}单元格", impRecord)
                        return
                    }
                    property.discountedMonths = discountedMonths


                    Date serviceDate = ExcelUtil.getDateCellValue(row.getCell(30))
                    if (!serviceDate) {
                        impRecordService.saveFailureImpRecord("表格信息不完整：AE${rowIndex}单元格", impRecord)
                        return
                    }
                    property.serviceDate = serviceDate

                    Date accountingDate = ExcelUtil.getDateCellValue(row.getCell(31))
                    property.accountingDate = accountingDate

                    String capitalSource = ExcelUtil.getStringCellValue(row.getCell(32))
                    if (capitalSource && capitalSource.size() > 30) {
                        impRecordService.saveFailureImpRecord("超出字符限制：AG${rowIndex}单元格", impRecord)
                        return
                    }
                    property.capitalSource = capitalSource


                    property.campusId = impRecord.campusId
                    property.status =1

                    propertyList << property
                }

            }
            propertyList*.save(failOnError: true)
            propertyList.each {
                propertyUpdateRecordService.createAndSaveUpdateRecord(impRecord.campusId, it.id, impRecord.teacherId, null, "新增", null)
            }
            impRecord.msg = "导入成功"
            impRecord.count = propertyList ? propertyList.size() : 0
            impRecordService.saveSuccessImpRecord(impRecord)

        } catch (Exception e) {
            impRecordService.saveFailureImpRecord(e.message, impRecord)
            log.error("批量导入资产出错,recordId: ${impRecord.id}, error:", e)
        }
    }

}

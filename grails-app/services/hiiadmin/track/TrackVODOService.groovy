package hiiadmin.track

import com.alibaba.fastjson.JSON
import com.google.common.base.Joiner
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.biz.DepartmentService
import hiiadmin.docking.RelatedUserService
import hiiadmin.ferries.FerriesService
import hiiadmin.ferries.FerriesStudentService
import hiiadmin.module.gated.TrackVO
import hiiadmin.module.humanface.TrackImageVO
import hiiadmin.oss.OssUtils
import hiiadmin.school.*
import hiiadmin.school.device.DeviceSpaceService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import org.apache.commons.lang3.StringUtils
import timetabling.Department
import timetabling.ParentStudent
import timetabling.gated.Ferries
import timetabling.gated.FerriesStudent
import timetabling.history.HistoryTrack
import timetabling.iot.DeviceSpace
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.track.Track
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major

import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class TrackVODOService {

    GradeService gradeService

    UnitService unitService

    ParentService parentService

    StudentService studentService

    TeacherService teacherService

    DepartmentService departmentService

    FerriesService ferriesService

    FerriesStudentService ferriesStudentService

    RelatedUserService relatedUserService

    UserEncodeInfoService userEncodeInfoService

    UnitStudentService unitStudentService

    CampusService campusService

    FacultyService facultyService

    MajorService majorService

    DeviceSpaceService deviceSpaceService

    TrackVO transformTrackVO(Track track) {
        Campus campus = campusService.fetchCampusByCampusId(track.campusId)
        TrackVO vo = new TrackVO()
        vo.id = track.id
        vo.name = track.userName
        vo.code = track.userCode
        vo.faceImg = track.faceImg
        vo.licensePlate = track.licensePlate
        vo.startTime = track.startTime

        if (StringUtils.isNotBlank(track?.originalParameters)) {
            vo.originalParameters = track?.originalParameters
            try {
                List<TrackImageVO> trackImageVOList = JSON.parseArray(track?.originalParameters, TrackImageVO.class)
                vo.trackImageVOList = trackImageVOList
            } catch (Exception e) {
                log.info("json解析异常，trackId#${track.id}".toString())
            }
        }

        // String firmName = DeviceFirm.getEnumByFirm(track.firm)?.name
        //vo.viaName = firmName ? "${firmName}-${track.objName}" : track.objName
        vo.viaName = track?.objName
        vo.viaTime = track?.startTime?.getTime()
        vo.pushTime = track.pushTime
        vo.userTypeName = UserTypeEnum.getEnumByType(track.userType).name
        String organizeName = ""
        String mobile = ""
        String identities = ""
        switch (track.userType) {
            case UserTypeEnum.STUDENT.type:
                Student student = studentService.fetchStudentById(track.userId)
                UnitStudent unitStudent = unitStudentService.fetchAdministrativeUnitStudentByStudentId(student?.id, false)
                vo.name = studentService.appendStudentStatus(student)
                if (unitStudent) {
                    Unit unit = unitService.fetchUnitById(unitStudent.unitId)
                    Grade grade = gradeService.fetchGradeById(unitStudent.gradeId)
                    if (campus.type == 1) {

                        if (grade?.name) {
                            vo.sectionName = SectionType.getEnumByType(grade?.sectionId).name
                            vo.gradeName = grade.name
                            organizeName += SectionType.getEnumByType(grade?.sectionId).name + grade.name
                        }
                        if (unit?.name) {
                            vo.unitName = unit?.alias ?: unit.name
                            organizeName += unit?.alias ?: unit.name
                        }
                    } else {
                        Faculty faculty = facultyService.fetchFacultyById(unitStudent.facultyId)
                        Major major = majorService.fetchMajorById(unitStudent.majorId)
                        vo.gradeName = grade?.name
                        vo.facultyName = faculty?.name
                        vo.majorName = major?.name
                        if (unit?.name) {
                            vo.unitName = unit?.name
                            organizeName += unit?.name
                        }
                    }

                }
                identities = "学生"
                vo.avatar = student?.pic
                break
            case UserTypeEnum.TEACHER.type:
                Teacher teacher = teacherService.fetchTeacherById(track.userId)
                if (teacher) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id)

                    if (campus.type == 1) {
                        List<Department> departmentList = departmentService.fetchAllDepartmentByCampusIdAndTeacherId(track.campusId, track.userId)
                        if (departmentList != null && departmentList.size() > 0) {
                            departmentList?.removeAll([null])
                            organizeName = Joiner.on(",").join(departmentList*.name)
                        }
                    } else {
                        List<Faculty> facultyList = facultyService.fetchAllFacultyByUser(track.campusId, track.userId)
                        if (facultyList != null && facultyList.size() > 0) {
                            facultyList?.removeAll([null])
                            organizeName = Joiner.on(",").join(facultyList*.name)
                        }
                    }
                    vo.avatar = teacher.avatar
                    vo.mobile = mobile
                }
                break
            case UserTypeEnum.FERRIES.type:
                Ferries ferries = ferriesService.fetchFerriesByFerriesId(track.userId)
                if (ferries) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id)
                    List<FerriesStudent> ferriesStudentList = ferriesStudentService.fetchAllFerriesStudentByFerriesIdAndCampusId(track.campusId, ferries.id)
                    List<String> relationList = []
                    ferriesStudentList.each { FerriesStudent ferriesStudent ->
                        Student student = studentService.fetchStudentByIdViaCache(ferriesStudent.studentId)
                        if (student && student.status == 1) {
                            StringBuilder relation = new StringBuilder()
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }
                            if (StringUtils.isNotBlank(ferriesStudent.relation)) {
                                relation.append(ferriesStudent.relation)
                            }
                            if (StringUtils.isNotBlank(relation.toString())) {
                                relationList << relation.toString()
                            }
                        }
                    }
                    vo.avatar = ferries.pics
                    vo.mobile = mobile
                    organizeName = Joiner.on("/").join(relationList)
                }
                break
            case UserTypeEnum.VISITORS.type:
                Visitor visitor = Visitor.get(track.userId)
                if (visitor) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitor.id)
                    String url = visitor.pic
                    if (!url.startsWith("http")) {
                        url = OssUtils.transformFilePath(url, 300)
                    }
                    vo.avatar = url
                    vo.mobile = mobile
                }
                break
            case UserTypeEnum.PARENT.type:
                Parent parent = parentService.fetchParentById(track.userId)
                if (parent) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parent.id)
                    List<ParentStudent> parentStudentList = ParentStudent.findAllByParentIdAndStatus(parent.id, 1 as Byte)
                    vo.avatar = parent?.avatar
                    vo.mobile = mobile
                    List<String> relationList = []
                    parentStudentList?.each { parentStudent ->
                        Student student = studentService.fetchStudentByIdViaCache(parentStudent.studentId)
                        if (student && student.status == 1) {
                            StringBuilder relation = new StringBuilder()
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }

                            if (StringUtils.isNotBlank(parentStudent.memo)) {
                                relation.append(parentStudent.memo)
                            }

                            if (StringUtils.isNotBlank(relation)) {
                                relationList << relation.toString()
                            }
                        }
                        organizeName = Joiner.on("/").join(relationList)
                    }
                }
                break
        }
        if (mobile) {
            if (identities) {
                identities += ","
            }
            identities += relatedUserService.getAllIdentitiesByTrueMobile(mobile)
        }

        vo.userType = track.userType
        vo.identities = identities
        vo.organizeName = organizeName
        vo
    }

    TrackVO transformHistoryTrackVOV2(HistoryTrack track) {
        Campus campus = campusService.fetchCampusByCampusId(track.campusId)
        TrackVO vo = new TrackVO()
        vo.id = track.id
        vo.name = track.userName
        vo.code = track.userCode
        vo.faceImg = track.faceImg
        vo.licensePlate = track.licensePlate
        vo.startTime = track.startTime
        if (StringUtils.isNotBlank(track?.originalParameters)) {
            vo.originalParameters = track?.originalParameters
            try {
                List<TrackImageVO> trackImageVOList = JSON.parseArray(track?.originalParameters, TrackImageVO.class)
                vo.trackImageVOList = trackImageVOList
            } catch (Exception e) {
                log.info("json解析异常，trackId#${track.id}".toString())
            }
        }
        vo.viaName = track?.objName
        vo.viaTime = track.pushTime
        vo.pushTime = track.pushTime
        vo.userTypeName = UserTypeEnum.getEnumByType(track.userType)?.name
        String organizeName = ""
        String mobile = ""
        String identities = ""
        switch (track.userType) {
            case UserTypeEnum.STUDENT.type:
                Student student = studentService.fetchStudentById(track.userId)
                UnitStudent unitStudent = unitStudentService.fetchAdministrativeUnitStudentByStudentId(student?.id, false)
                vo.name = studentService.appendStudentStatus(student)
                if (unitStudent) {
                    Unit unit = unitService.fetchUnitById(unitStudent.unitId)
                    Grade grade = gradeService.fetchGradeById(unitStudent.gradeId)
                    if (campus.type == 1) {

                        if (grade?.name) {
                            vo.sectionName = SectionType.getEnumByType(grade?.sectionId).name
                            vo.gradeName = grade.name
                            organizeName += SectionType.getEnumByType(grade?.sectionId).name + grade.name
                        }
                        if (unit?.name) {
                            vo.unitName = unit?.alias ?: unit.name
                            organizeName += unit?.alias ?: unit.name
                        }
                    } else {
                        Faculty faculty = facultyService.fetchFacultyById(unitStudent.facultyId)
                        Major major = majorService.fetchMajorById(unitStudent.majorId)
                        vo.gradeName = grade?.name
                        vo.facultyName = faculty?.name
                        vo.majorName = major?.name
                        if (unit?.name) {
                            vo.unitName = unit?.name
                            organizeName += unit?.name
                        }
                    }

                }
                identities = "学生"
                vo.avatar = student?.pic
                break
            case UserTypeEnum.TEACHER.type:
                Teacher teacher = teacherService.fetchTeacherById(track.userId)
                if (teacher) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id)
                    if (campus.type == 1) {
                        List<Department> departmentList = departmentService.fetchAllDepartmentByCampusIdAndTeacherId(track.campusId, track.userId)
                        if (departmentList) {
                            organizeName = Joiner.on(",").join(departmentList*.name)
                        }
                    } else {
                        List<Faculty> facultyList = facultyService.fetchAllFacultyByUser(track.campusId, track.userId)
                        if (facultyList) {
                            organizeName = Joiner.on(",").join(facultyList*.name)
                        }
                    }
                    vo.avatar = teacher.avatar
                    vo.mobile = mobile
                    vo.realMobile = mobile
                }
                break
            case UserTypeEnum.FERRIES.type:
                Ferries ferries = ferriesService.fetchFerriesByFerriesId(track.userId)
                if (ferries) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id)
                    List<FerriesStudent> ferriesStudentList = ferriesStudentService.fetchAllFerriesStudentByFerriesIdAndCampusId(track.campusId, ferries.id)
                    List<String> relationList = []
                    ferriesStudentList.each { FerriesStudent ferriesStudent ->
                        Student student = studentService.fetchStudentByIdViaCache(ferriesStudent.studentId)
                        if (student && student.status == 1) {
                            StringBuilder relation = new StringBuilder()
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }
                            if (StringUtils.isNotBlank(ferriesStudent.relation)) {
                                relation.append(ferriesStudent.relation)
                            }
                            if (StringUtils.isNotBlank(relation.toString())) {
                                relationList << relation.toString()
                            }
                        }
                    }
                    vo.avatar = ferries.pics
                    vo.mobile = mobile
                    organizeName = Joiner.on("/").join(relationList)
                }
                break
            case UserTypeEnum.VISITORS.type:
                Visitor visitor = Visitor.get(track.userId)
                if (visitor) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitor.id)
                    String url = visitor.pic
                    if (!url.startsWith("http")) {
                        url = OssUtils.transformFilePath(url, 300)
                    }
                    vo.avatar = url
                    vo.mobile = mobile
                }
                break
            case UserTypeEnum.PARENT.type:
                Parent parent = parentService.fetchParentById(track.userId)
                if (parent) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parent.id)
                    List<ParentStudent> parentStudentList = ParentStudent.findAllByParentIdAndStatus(parent.id, 1 as Byte)
                    vo.avatar = parent?.avatar
                    vo.mobile = mobile
                    List<String> relationList = []
                    parentStudentList?.each { parentStudent ->
                        Student student = studentService.fetchStudentByIdViaCache(parentStudent.studentId)
                        if (student && student.status == 1) {
                            StringBuilder relation = new StringBuilder()
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }

                            if (StringUtils.isNotBlank(parentStudent.memo)) {
                                relation.append(parentStudent.memo)
                            }

                            if (StringUtils.isNotBlank(relation)) {
                                relationList << relation.toString()
                            }
                        }
                        organizeName = Joiner.on("/").join(relationList)
                    }
                }
                break
        }
        if (mobile) {
            if (identities) {
                identities += ","
            }
            identities += relatedUserService.getAllIdentitiesByTrueMobile(mobile)
        }

        vo.userType = track.userType
        vo.identities = identities
        vo.organizeName = organizeName
        vo
    }


    void trackVOAddBuilding(List<TrackVO> vos, Long campusId) {
        List<Long> deviceIdList = []
        if (vos) {
            vos.each {
                if (it.deviceId) {
                    deviceIdList << it.deviceId
                }
            }
            if (deviceIdList) {
                List<DeviceSpace> deviceSpaceList = deviceSpaceService.fetchAllDeviceSpace4device(campusId, deviceIdList)
                Map<Long, DeviceSpace> deviceSpaceMap = deviceSpaceList.collectEntries { [(it.deviceId): it] }
                vos.each {
                    if (it.deviceId) {
                        DeviceSpace space = deviceSpaceMap.get(it.deviceId)
                        if (space?.areaIdPath) {
                            String[] ids = space.areaIdPath.split("_")
                            if (ids.length > 0) {
                                it.buildingId = PhenixCoder.encodeId(ids[0])
                            }
                            if (ids.length > 1) {
                                it.layerId = PhenixCoder.encodeId(ids[1])
                            }
                            if (ids.length > 2) {
                                it.doorplateId = PhenixCoder.encodeId(ids[2])
                            }

                        }
                        if (space) {
                            it.rowIndex = space.rowIndex
                            it.colIndex = space.colIndex
                        }
                    }
                }
            }
        }
    }


    TrackVO transformHistoryTrackVO(TrackVO track) {
        Campus campus = campusService.fetchCampusByCampusId(PhenixCoder.decodeId(track.campusId))
        if (StringUtils.isNotBlank(track?.originalParameters)) {
            try {
                List<TrackImageVO> trackImageVOList = JSON.parseArray(track?.originalParameters, TrackImageVO.class)
                track.trackImageVOList = trackImageVOList
            } catch (Exception e) {
                log.info("json解析异常，trackId#${track.id}".toString())
            }
        }
        String organizeName = ""
        String mobile = ""
        String identities = ""
        switch (track.userType) {
            case UserTypeEnum.STUDENT.type:
                Student student = studentService.fetchStudentById(PhenixCoder.decodeId(track.userId))
                UnitStudent unitStudent = unitStudentService.fetchUnitStudentByStudentId(student?.id)
                if (unitStudent) {
                    Unit unit = unitService.fetchUnitById(unitStudent.unitId)
                    Grade grade = gradeService.fetchGradeById(unitStudent.gradeId)
                    if (campus.type == 1) {

                        if (grade?.name) {
                            organizeName += SectionType.getEnumByType(grade?.sectionId).name + grade.name
                        }
                        if (unit?.name) {
                            organizeName += unit?.alias ?: unit.name
                        }
                    } else {
                        organizeName += unit?.name
                    }

                }
                identities = "学生"
                track.avatar = student?.pic
                break
            case UserTypeEnum.TEACHER.type:
                Teacher teacher = teacherService.fetchTeacherById(PhenixCoder.decodeId(track.userId))
                if (teacher) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id)
                    track.avatar = teacher.avatar
                    track.mobile = mobile
                    if (campus.type == 1) {
                        List<Department> departmentList = departmentService.fetchAllDepartmentByCampusIdAndTeacherId(PhenixCoder.decodeId(track.campusId), PhenixCoder.decodeId(track.userId))
                        if (departmentList) {
                            organizeName = Joiner.on(",").join(departmentList*.name)
                        }
                    } else {
                        List<Faculty> facultyList = facultyService.fetchAllFacultyByUser(PhenixCoder.decodeId(track.campusId), PhenixCoder.decodeId(track.userId))
                        if (facultyList) {
                            organizeName = Joiner.on(",").join(facultyList*.name)
                        }
                    }
                }
                break
            case UserTypeEnum.FERRIES.type:
                Ferries ferries = ferriesService.fetchFerriesByFerriesId(PhenixCoder.decodeId(track.userId))
                if (ferries) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id)
                    track.avatar = ferries.pics
                    track.mobile = mobile
                }
                break
            case UserTypeEnum.VISITORS.type:
                Visitor visitor = Visitor.get(track.userId)
                if (visitor) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitor.id)
                    track.avatar = visitor.pic
                    track.mobile = mobile
                }
                break
            case UserTypeEnum.PARENT.type:
                Parent parent = parentService.fetchParentById(PhenixCoder.decodeId(track.userId))
                if (parent) {
                    mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parent.id)
                    track.avatar = parent?.avatar
                    track.mobile = mobile
                }
                break
        }
        if (mobile) {
            if (identities) {
                identities += ","
            }
            identities += relatedUserService.getAllIdentitiesByTrueMobile(mobile)
        }
        track
    }
}

package hiiadmin.track

import com.alibaba.fastjson.JSONObject
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import hiiadmin.apiCloud.AntennaDhYrApi
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.PositionService
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.ferries.FerriesService
import hiiadmin.history.HistoryTrackService
import hiiadmin.module.bugu.PositionCountVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.oss.OssPathService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import hiiadmin.utils.ToStringUnits
import hiiadmin.vocationalSchool.FacultyUserService
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import timetabling.Position
import timetabling.StaffDepartment
import timetabling.history.HistoryTrack
import timetabling.org.Campus
import timetabling.track.Track
import timetabling.vocationalSchool.FacultyUser

import java.util.concurrent.TimeUnit

import static hiiadmin.ConstantEnum.DeviceFirm
import static hiiadmin.ConstantEnum.UserTypeEnum

@Transactional
class TrackService {
    PositionService positionService

    HistoryTrackService historyTrackService

    FerriesService ferriesService

    ParentService parentService

    DepartmentService departmentService

    FacultyUserService facultyUserService

    @Autowired
    AntennaDhYrApi antennaDhYrApi

    DockingPlatformCampusService dockingPlatformCampusService

    OssPathService ossPathService

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS,
            name = "hiiadmin_trackS_fetchAllTrackLimit", key = "#campusId  + '_' + #deviceIds + '_' + #searchValue + '_' + #userType + '_' + #startDate + '_' + #endDate + '_' + #departmentId + '_' + #unitId + '_' + #history + '_' + #studentName + '_' + #p + '_' + #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Track> fetchAllTrackByCampusId(Long campusId, String deviceIds, String searchValue, String userType, Long startDate, Long endDate, Long departmentId, Long unitId, boolean history, String studentName, int p, int s) {
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT t ")
        Map<String, Object> map = buildHQL(sb, campusId, startDate, endDate, deviceIds, searchValue, userType, departmentId, unitId, history, studentName)
        sb.append(" ORDER BY t.pushTime DESC")
        if (s > 0) {
            map["max"] = s
            map["offset"] = (p - 1) * s
        }
        Track.findAll(sb.toString(), map)
    }


    List<Track> fetchAllTrackByCampusIdAndDeviceIdsBetweenTime(Long campusId, List<Long> deviceIds, String searchValue, Byte userType, Long startTime, Long endTime, Long unitId, int p, int s, Boolean isAsc) {
        if (startTime == null && endTime == null && deviceIds == null) {
            return []
        }
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT t ")
        Map<String, Object> map = buildHQLV2(sb, campusId, startTime, endTime, deviceIds, searchValue, userType, unitId)
        if (isAsc) {
            sb.append(" ORDER BY t.pushTime ASC")
        } else {
            sb.append(" ORDER BY t.pushTime DESC")
        }
        if (s > 0) {
            map["max"] = s
            map["offset"] = (p - 1) * s
        }

        Track.findAll(sb.toString(), map)
    }

    Integer countTrackByCampusId(Long campusId, List<Long> deviceIds, String searchValue, Byte userType, Long startTime, Long endTime, Long unitId) {
        StringBuffer sb = new StringBuffer()
        sb.append("SELECT COUNT(1) ")
        Map<String, Object> map = buildHQLV2(sb, campusId, startTime, endTime, deviceIds, searchValue, userType, unitId)
        Track.executeQuery(sb.toString(), map)[0] as Integer
    }

    Map buildHQLV2(StringBuffer sb, Long campusId, Long startDate, Long endDate, List<Long> deviceIds, String searchValue, Byte userType, Long unitId) {
        Map<String, Object> map = [:]

        sb.append(" FROM Track t WHERE t.campusId = :campusId AND t.status = :status ")

        map["campusId"] = campusId
        map["status"] = 1 as byte
        if (userType) {
            sb.append(" AND t.userType in :userType ")
            map["userType"] = userType
        }

        if (deviceIds) {
            List<Long> deviceIdList = ToStringUnits.idsString2LongList(deviceIds)
            sb.append(" AND t.deviceId IN :deviceIdList ")
            map["deviceIdList"] = deviceIdList
        }

        if (unitId) {
            sb.append(" AND t.unitId =:unitId ")
            map["unitId"] = unitId
        }
        if (searchValue) {
            sb.append(" AND (t.personName LIKE :name or t.userName LIKE :userName or t.userCode LIKE :userCode) ")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("userName", value)
            map.put("userCode", value)
        }

        if (startDate && endDate) {
            Long dayStartDate = new DateTime(startDate).withMillisOfDay(0).millis
            Long dayEndDate = new DateTime(endDate).withMillisOfDay(0).plusDays(1).millis
            map["startDate"] = dayStartDate
            map["endDate"] = dayEndDate
            sb.append(" AND (t.pushTime BETWEEN :startDate AND :endDate) ")
        }
        map
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS,
            name = "hiiadmin_historyTrackS_fetchAllHistoryTrackLimit", key = "#campusId + '_'  + #deviceIds + '_' + #searchValue + '_' + #userType + '_' + #startDate + '_' + #endDate + '_' + #departmentId + '_' + #unitId + '_' + #history + '_' +#studentName + '_' + #p + '_' + #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<HistoryTrack> fetchAllHistoryTrackByCampusId(Long campusId, String deviceIds, String searchValue, String userType, Long startDate, Long endDate, Long departmentId, Long unitId, boolean history, String studentName, int p, int s) {
        StringBuffer sb = new StringBuffer()
        List<HistoryTrack> historyTrackList = []
        sb.append(" SELECT t ")
        Map<String, Object> map = buildHQL(sb, campusId, startDate, endDate, deviceIds, searchValue, userType, departmentId, unitId, history, studentName)
        sb.append(" ORDER BY t.pushTime DESC")
        if (s > 0) {
            map["max"] = s
            map["offset"] = (p - 1) * s
        }
        historyTrackList = historyTrackService.fetchHistoryTrack(sb, map)
        return historyTrackList
    }

    List<Track> fetchAllTrackByCampusIdAndUserId(Long campusId, Long userId, Byte userType, Long startTime, Long endTime,Boolean isAsc) {
        Long startDate = TimeUtils.getDateStartTime(startTime).time
        Long endDate = TimeUtils.getDateEndTime(endTime).time
        return fetchAllTrackByCampusIdAndUserIdBetweenTime(campusId, userId, startDate, endDate, userType,isAsc)
    }

    List<Track> fetchAllByLoCampusIdAndUserIdAndUserType(Long campusId, Long userId, Byte userType) {
        Track.findAllByCampusIdAndUserIdAndUserTypeAndStatus(campusId, userId, userType, 1 as Byte)
    }

    def saveTrackList(List<Track> trackList) {
        trackList*.save(failOnError: true, flush: true)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS,
            name = "hiiadmin_trackS_countTrackSLimit", key = "#campusId  + '_' + #deviceIds + '_' + #searchValue + '_' + #userType + '_' + #startDate + '_' + #endDate + '_' + #departmentId + '_' + #unitId + '_' + #history + '_' + #studentName",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Integer countHistoryTrackByCampusId(Long campusId, String deviceIds, String searchValue, String userType, Long startDate, Long endDate, Long departmentId, Long unitId, boolean history, String studentName) {
        Integer count = 0
        StringBuffer sb = new StringBuffer()
        sb.append("SELECT COUNT(1) ")
        Map<String, Object> map = buildHQL(sb, campusId, startDate, endDate, deviceIds, searchValue, userType, departmentId, unitId, history, studentName)
        count = historyTrackService.fetchHistoryTrackCount(sb, map)
        return count
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS,
            name = "hiiadmin_historyTrackS_countHistoryTrackSLimit", key = "#campusId  + '_' + #deviceIds + '_' + #searchValue + '_' + #userType + '_' + #startDate + '_' + #endDate + '_' + #departmentId + '_' + #unitId + '_' + #history + '_' + #studentName",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Integer countTrackByCampusId(Long campusId, String deviceIds, String searchValue, String userType, Long startDate, Long endDate, Long departmentId, Long unitId, boolean history, String studentName) {
        StringBuffer sb = new StringBuffer()
        sb.append("SELECT COUNT(1) ")
        Map<String, Object> map = buildHQL(sb, campusId, startDate, endDate, deviceIds, searchValue, userType, departmentId, unitId, history, studentName)
        Track.executeQuery(sb.toString(), map)[0] as Integer
    }

    Map buildHQL(StringBuffer sb, long campusId, Long startDate, Long endDate, String deviceIds, String searchValue, String userType, Long departmentId, Long unitId, boolean history, String studentName) {
        Map<String, Object> map = [:]
        Campus campus = Campus.get(campusId)
        List<Byte> userTypeList = ToStringUnits.userTypeString2ByteList(userType)
        if (history) {
            sb.append(" FROM HistoryTrack t WHERE t.campusId = :campusId AND t.status = :status ")
        } else {
            sb.append(" FROM Track t WHERE t.campusId = :campusId AND t.status = :status ")
        }
        map["campusId"] = campusId
        map["status"] = 1 as byte
        if (userType) {
            sb.append(" AND t.userType in :userType ")
            map["userType"] = userTypeList
        }

        if (deviceIds) {
            List<Long> deviceIdList = ToStringUnits.idsString2LongList(deviceIds)
            sb.append(" AND t.deviceId IN :deviceIdList ")
            map["deviceIdList"] = deviceIdList
        }

        if (unitId) {
            sb.append(" AND t.unitId =:unitId ")
            map["unitId"] = unitId
        }
        if (studentName) {
            if (userTypeList.contains(UserTypeEnum.PARENT.type)) {
                List<Long> parentIds = fetchParentIds(campusId, studentName)
                if (parentIds) {
                    sb.append(""" AND (t.userId IN :parentIds) """)
                    map.put("parentIds", parentIds)
                } else {
                    //让结果查询为空
                    sb.append(" AND t.userId = -1 ")
                }
            } else if (userTypeList.contains(UserTypeEnum.FERRIES.type)) {
                List<Long> ferriesIds = fetchFerriesIds(campusId, studentName)
                if (ferriesIds) {
                    sb.append(""" AND (t.userId IN :ferriesIds) """)
                    map.put("ferriesIds", ferriesIds)
                } else {
                    //让结果查询为空
                    sb.append(" AND t.userId = -1 ")
                }
            } else {
                //让结果查询为空
                sb.append(" AND t.userId = -1 ")
            }
        } else if (searchValue) {
            sb.append(" AND (t.personName LIKE :name or t.userName LIKE :userName or t.userCode LIKE :userCode) ")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("userName", value)
            map.put("userCode", value)
        }

        if (departmentId) {
            if (history) {
                List<Long> teacherIds = []
                if (campus.type == 1) {
                    List<StaffDepartment> staffDepartmentList = departmentService.fetchAllStaffDepartmentByDepartmentId(departmentId)
                    teacherIds = staffDepartmentList*.teacherId.unique()

                } else {
                    List<FacultyUser> facultyUserList = facultyUserService.fetchAllUserByFaculty(campusId, departmentId)
                    teacherIds = facultyUserList*.userId.unique()
                }
                if (teacherIds && teacherIds.size() > 0) {
                    sb.append(" AND (t.userId IN :teacherIds)")
                    map.put("teacherIds", teacherIds)
                } else {
                    sb.append(" AND t.userId = -1 ")
                }

            } else {
                if (campus.type == 1) {
                    if (departmentId == -1) {
                        sb.append(""" AND NOT EXISTS ( SELECT 1 from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.teacherId = t.userId AND s_d.status = :status ) """)
                    } else if (departmentId) {
                        sb.append(""" AND t.userId IN ( SELECT s_d.teacherId from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.departmentId = :departmentId AND s_d.status = :status ) """)
                        map.put("departmentId", departmentId)
                    }
                } else {
                    if (departmentId) {
                        sb.append(""" AND t.userId IN ( SELECT fu.userId from FacultyUser fu WHERE fu.campusId = :campusId AND fu.facultyId = :departmentId AND fu.userType = :userType AND fu.status = :status ) """)
                        map.put("userType", UserTypeEnum.TEACHER.type)
                        map.put("departmentId", departmentId)
                    }
                }
            }
        }

        if (startDate && endDate) {
            Long dayStartDate = new DateTime(startDate).withMillisOfDay(0).millis
            Long dayEndDate = new DateTime(endDate).withMillisOfDay(0).plusDays(1).millis
            map["startDate"] = dayStartDate
            map["endDate"] = dayEndDate
            sb.append(" AND (t.pushTime BETWEEN :startDate AND :endDate) ")
        }
        map
    }

    Integer getMaxPersonIdByFirmAndObjType(Integer firm, String objType) {

        String HQL = """SELECT MAX(personId) FROM Track WHERE
                                             firm = :firm
                                             AND objType = :objType"""
        (Track.executeQuery(HQL, [firm: firm, objType: objType])[0] as Integer) ?: 0
    }

    //根据学生姓名/学号查找关联接送人ids
    List<Long> fetchFerriesIds(Long campusId, String searchValue) {
        List<Long> ids = []
        ids = ferriesService.findAllFerriesByCampusIdAndSearchValue(campusId, searchValue)
        return ids
    }

    //根据学生姓名/学号查找家长ids
    List<Long> fetchParentIds(Long campusId, String searchValue) {
        List<Long> ids = []
        ids = parentService.fetchAllParentByStudentNameOrCode(campusId, searchValue)
        return ids
    }

    //导出使用
    List<Track> getTrackRecordByStamp(Long campusId, Byte userType, Long dayTimeStamp) {
        List<Track> trackList = []
        Long endStamp = dayTimeStamp + 86400000
        String hql = """SELECT t FROM Track t WHERE t.campusId = :campusId AND t.userType = :userType AND t.status = 1 AND t.pushTime >= :dayTimeStamp AND t.pushTime < :endStamp """
        trackList = Track.executeQuery(hql, [campusId: campusId, userType: userType, dayTimeStamp: dayTimeStamp, endStamp: endStamp])
        return trackList
    }

    //导出使用
    List<Track> getTrackRecordByStampRange(Long campusId, Byte userType, Long startDate, Long endDate) {
        List<Track> trackList = []
        String hql = """SELECT t FROM Track t WHERE t.campusId = :campusId AND t.userType = :userType AND t.status = 1 AND (t.dayTimeStamp BETWEEN :startDate AND :endDate)"""
        trackList = Track.executeQuery(hql, [campusId: campusId, userType: userType, startDate: startDate, endDate: endDate])
        return trackList
    }

    List<Track> fetchAllTrackByCampusIdAndUserIdInDeviceIdsBetweenTime(Long campusId, Long userId, Byte userType, Long startTime, Long endTime, List<Long> deviceIds) {
        def c = Track.createCriteria()
        def list = c.list {
            eq("campusId", campusId)
            eq("userId", userId)
            eq("userType", userType)
            'in'("deviceId", deviceIds)
            between("pushTime", startTime, endTime)
            order("pushTime", "desc")
        }
        list as List<Track>
    }

    List<Track> fetchAllTrackByPositionIdAndCampusIdAndUserIdBetweenTime(Long campusId, Long userId, Byte userType, Long startTime, Long endTime, Long positionId) {
        List<Long> deviceIds = positionService.fetchAllPositionDeviceByPositionId(positionId)*.deviceId
        if (deviceIds) {
            return fetchAllTrackByCampusIdAndUserIdInDeviceIdsBetweenTime(campusId, userId, userType, startTime, endTime, deviceIds)
        }
        return null
    }

    Track fetchById(Long id) {
        return Track.get(id)
    }

    List<Track> fetchAllTrackByCampusIdAndUserIdBetweenTime(Long campusId, Long userId, Long startTime, Long endTime, Byte userType,Boolean isAsc) {
        def c = Track.createCriteria()
        def list = c.list {
            eq("campusId", campusId)
            eq("userId", userId)
            eq("userType", userType)
            between("pushTime", startTime, endTime)
            if (isAsc){
                order("pushTime", "asc")
            }else {
                order("pushTime", "desc")
            }
        }
        list as List<Track>
    }


    Map<Long, Integer> countTrackByCampusIdAndUserIdBetweenTimeOrderDeviceIdMap(Long campusId, Long userId, Long startTime, Long endTime, Byte userType) {
        String HQL = """SELECT deviceId,COUNT(id) FROM Track WHERE campusId =:campusId 
                       AND userId = :userId AND userType=:userType 
                       AND pushTime between :s AND :e GROUP BY deviceId"""
        List list = Track.executeQuery(HQL, [campusId: campusId, userId: userId, userType: userType, s: startTime, e: endTime])

        Map<Long, Integer> map = [:]
        list.each {
            Long deviceId = it[0] as Long
            Integer count = it[1] as Integer
            map[deviceId] = count
        }
        map
    }


    List<PositionCountVO> statisticsStudentPlaceTrack(Long campusId, Long userId, Long startTime, Long endTime, Byte userType) {
        Map<Long, Integer> countTrackMap = countTrackByCampusIdAndUserIdBetweenTimeOrderDeviceIdMap(campusId, userId, startTime, endTime, userType)
        List<PositionCountVO> countVOList = []
        countTrackMap.each {
            Map.Entry<Long, Integer> entry ->
                List<Position> positionList = positionService.fetchAllPositionByDeviceId(campusId, entry.key)
                if (positionList) {
                    positionList.each {
                        Position position ->
                            PositionCountVO countVO = countVOList.find { it.positionId == position.id }
                            if (countVO) {
                                countVO.number += entry.value
                            } else {
                                countVOList << new PositionCountVO(
                                        positionId: position.id,
                                        number: entry.value,
                                        positionName: position.name
                                )
                            }
                    }
                }
        }
        countVOList
    }


    TrackVO transformNewTrackVO(Track track) {
        TrackVO vo = new TrackVO()
        vo.id = track.id
        vo.name = track.userName
        vo.code = track.userCode
        vo.faceImg = track.faceImg
        vo.licensePlate = track.licensePlate
        vo.originalParameters = track?.originalParameters
        vo.viaName = track?.objName
        vo.viaTime = track.pushTime
        vo.pushTime = track.pushTime
        vo.userId = PhenixCoder.encodeId(track.userId)
        vo.campusId = PhenixCoder.encodeId(track.campusId)
        vo.userType = track.userType
        vo.location = track.location ? track.location : track.objName
        vo.deviceId = track.deviceId
        vo
    }


    @Cached(expire = 1, timeUnit = TimeUnit.HOURS,
            name = "Admin_aS_getTrackPic", key = "#trackId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "result ne null")
    ResultVO getTrackPic(Long trackId) {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Track track = Track.get(trackId)
        if (!track.faceImg) {
            return ResultVO.success()
        }
        switch (track.firm) {
            case DeviceFirm.DH_YR.firm:
                Long dateTime = new DateTime().withMillisOfDay(0).minusDays(7).millis
                if (dateTime < track.pushTime) {
                    if (track.faceImg?.startsWith("http")) {
                        resultVO.result.put("faceImg", track.faceImg)
                    } else if (track.faceImg) {
                        JSONObject jsonObject = new JSONObject([photoPath: track.faceImg])
                        resultVO = antennaDhYrApi.faceRivers(jsonObject)
                        String url = resultVO.result.photoUrl
                        resultVO.result.put("faceImg", url)
                    }
//                resultVO.result.put("faceImgSmall", url)
                }
                break
            case DeviceFirm.ALY_DEVICE.firm:
                String pic = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, track.campusId)
                resultVO.result.put("faceImg", pic)
                resultVO.result.put("photoUrl", pic)
                resultVO.result.put("photoPath", pic)
                break
            case DeviceFirm.HK_EDU.firm:
                switch (track.schoolId) {
                    case 101L:
                    case 112L:
                        String url = "https://locals.yunzhiyuan100.com/" + track.campusId.toString() + "/" + DeviceFirm.HK_EDU.firm.toString() + track.faceImg
                        resultVO.result.put("faceImg", url)
                        break
                    case 16L:
                        String baseUrl = "https://locals.yunzhiyuan100.com/" + track.campusId.toString() + "/" + DeviceFirm.HK_EDU.firm.toString()
                        String url = null
                        if (track.faceImg.startsWith("https://192.168.26.199:6113")) {
                            url = track.faceImg.replace("https://192.168.26.199:6113", baseUrl)
                        } else if (track.faceImg) {
                            url = baseUrl + track.faceImg
                        }
                        resultVO.result.put("faceImg", url)
                        break

                }
                break
            case DeviceFirm.ANDROID_DEVICE.firm:
            case DeviceFirm.YS_DEVICE.firm:
            case DeviceFirm.HK_ISUP.firm:
            case DeviceFirm.MEGVII.firm:
            case DeviceFirm.DH_SDK.firm:
                String url = ossPathService.getFilePath(track.faceImg, 3600, "bugu-temp")
                resultVO.result.put("faceImg", url)
                break
            default:
                resultVO.result.put("faceImg", track.faceImg)
        }
        resultVO
    }


}

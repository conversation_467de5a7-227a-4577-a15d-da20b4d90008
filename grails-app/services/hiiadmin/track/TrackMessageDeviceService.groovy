package hiiadmin.track

import com.google.common.base.Joiner
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import hiiadmin.biz.PositionDeviceService
import hiiadmin.biz.PositionService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.TrackMessageMapper
import hiiadmin.module.track.TrackMessageDeviceVO
import hiiadmin.school.device.DeviceService
import org.springframework.beans.factory.annotation.Autowired
import timetabling.Position
import timetabling.device.Device
import timetabling.track.TrackMessageDevice

import static hiiadmin.ConstantEnum.DeviceType

@Transactional
class TrackMessageDeviceService {

    PositionService positionService

    DeviceService deviceService

    PositionDeviceService positionDeviceService

    @Autowired
    TrackMessageMapper trackMessageMapper

    List<Device> fetchAllUnExistTrackMessageDevice(long campusId, Integer firm, Byte type, Long positionId, String searchValue, int p, int s) {
        Map<String, Object> map = [:]
        String HQL = """ SELECT d FROM Device d WHERE d.campusId = :campusId AND d.status = 1 """
        map.put("campusId", campusId)
        if (firm) {
            HQL += """ AND d.firm = :firm """
            map.put("firm", firm)
        }
        if (type) {
            HQL += """ AND d.type = :deviceType """
            map.put("deviceType", type)
        } else {
//            HQL += """ AND d.type != :deviceType """
//            map.put("deviceType", DeviceType.EDGE_BOX.type)
            HQL += """ AND d.type IN :deviceType """
            map.put("deviceType", [DeviceType.FACE_CAMERA.type, DeviceType.GATE.type, DeviceType.CAR_GATE.type])
        }
        if (searchValue) {
            HQL += """ AND d.name LIKE '%${searchValue}%' """
        }
        map.put("max", s)
        map.put("offset", (p - 1) * s)
        HQL += """ AND NOT EXISTS (SELECT 1 FROM TrackMessageDevice tmd WHERE d.id = tmd.deviceId AND tmd.status = 1 )"""
        if (positionId) {
            HQL += """ AND d.id IN ( SELECT pd.deviceId FROM PositionDevice pd WHERE pd.positionId = :positionId AND pd.status = 1 )"""
            map.put("positionId", positionId)
        }
        List<Device> deviceList = Device.executeQuery(HQL, map)
        deviceList
    }

    Integer countUnExistTrackMessageDevice(long campusId, Integer firm, Byte type, Long positionId, String searchValue) {
        Map<String, Object> map = [:]
        String HQL = """ SELECT COUNT(1) FROM Device d WHERE d.campusId = :campusId AND d.status = 1 """
        map.put("campusId", campusId)
        if (firm) {
            HQL += """ AND d.firm = :firm """
            map.put("firm", firm)
        }
        if (type) {
            HQL += """ AND d.type = :deviceType """
            map.put("deviceType", type)
        } else {
//            HQL += """ AND d.type != :deviceType """
//            map.put("deviceType", DeviceType.EDGE_BOX.type)
            HQL += """ AND d.type IN :deviceType """
            map.put("deviceType", [DeviceType.FACE_CAMERA.type, DeviceType.GATE.type, DeviceType.CAR_GATE.type])
        }
        if (searchValue) {
            HQL += """ AND d.name LIKE '%${searchValue}%' """
        }
        HQL += """ AND NOT EXISTS (SELECT 1 FROM TrackMessageDevice tmd WHERE d.id = tmd.deviceId AND tmd.status = 1 )"""
        if (positionId) {
            HQL += """ AND d.id IN ( SELECT pd.deviceId FROM PositionDevice pd WHERE pd.positionId = :positionId AND pd.status = 1 )"""
            map.put("positionId", positionId)
        }
        Device.executeQuery(HQL, map)[0] as Integer
    }

    List<TrackMessageDeviceVO> transformUnExistTrackMessageDeviceVO(long campusId, List<Device> deviceList) {
        Map<Long, Position> idPositionMap = positionService.transformPositionIdMap(campusId)
        List<Long> deviceIdList = deviceList*.id
        Multimap<Long, Long> deviceIdPositionIdMultimap = positionDeviceService.transformDeviceIdPositionIdMultimap(deviceIdList)
        List<TrackMessageDeviceVO> deviceVOList = deviceList.collect {
            String placeNames = null
            Set<Long> positionIdSet = deviceIdPositionIdMultimap.get(it.id)
            if (positionIdSet.size() > 0) {
                List<String> positionNameList = positionIdSet.collect { positionId ->
                    idPositionMap.get(positionId)?.name
                }
                placeNames = Joiner.on(",").join(positionNameList)
            }
            trackMessageMapper.convert2TrackMessageDeviceVO4device(it, placeNames)
        }
        deviceVOList
    }

    List<TrackMessageDevice> fetchAllTrackMessageDevice(long featureId, String searchValue, int p, int s) {
        String search_HQL = """ SELECT tmd FROM TrackMessageDevice tmd WHERE tmd.featureId = :featureId AND tmd.status = 1  """

        if (searchValue) {
            search_HQL += """ AND tmd.deviceId IN ( SELECT d.id FROM Device d WHERE d.name LIKE '%${searchValue}%' AND d.status = 1 ) """
        }
        List<TrackMessageDevice> deviceList = TrackMessageDevice.executeQuery(search_HQL, [featureId: featureId, max: s, offset: (p - 1) * s])
        deviceList
    }

    Integer countTrackMessageDevice(long featureId, String searchValue) {
        String search_HQL = """ SELECT COUNT(1) FROM TrackMessageDevice tmd WHERE tmd.featureId = :featureId AND tmd.status = 1  """

        if (searchValue) {
            search_HQL += """ AND tmd.deviceId IN ( SELECT d.id FROM Device d WHERE d.name LIKE '%${searchValue}%' AND d.status = 1 ) """
        }
        TrackMessageDevice.executeQuery(search_HQL, [featureId: featureId])[0] as Integer
    }

    List<TrackMessageDeviceVO> transformTrackMessageDeviceVO(long campusId, List<TrackMessageDevice> deviceList) {
        Map<Long, Position> idPositionMap = positionService.transformPositionIdMap(campusId)
        List<Long> deviceIdList = deviceList*.deviceId
        Multimap<Long, Long> deviceIdPositionIdMultimap = positionDeviceService.transformDeviceIdPositionIdMultimap(deviceIdList)
        Map<Long, Device> deviceIdMap = deviceService.transformDeviceIdMap(deviceIdList)
        List<TrackMessageDeviceVO> deviceVOList = deviceList.collect {
            Device device = deviceIdMap.get(it.deviceId)
            String placeNames = null
            Set<Long> positionIdSet = deviceIdPositionIdMultimap.get(it.deviceId)
            if (positionIdSet.size() > 0) {
                List<String> positionNameList = positionIdSet.collect { positionId ->
                    idPositionMap.get(positionId).name
                }
                placeNames = Joiner.on(",").join(positionNameList)
            }
            trackMessageMapper.convert2TrackMessageDeviceVO(it, device, placeNames)
        }
        deviceVOList
    }

    static String REMOVE_TRACK_MESSAGE_DEVICE_4_ID_HQL = """UPDATE TrackMessageDevice SET status = 0, lastUpdated = NOW(), version = version+1 WHERE id = :id"""

    void removeTrackMessageDevice(long featureDeviceId) {
        TrackMessageDevice.executeUpdate(REMOVE_TRACK_MESSAGE_DEVICE_4_ID_HQL, [id: featureDeviceId])
    }
    static String REMOVE_TRACK_MESSAGE_DEVICE_4_DEVICE_ID_HQL = """UPDATE TrackMessageDevice SET status = 0, lastUpdated = NOW(), version = version+1 WHERE deviceId = :deviceId"""

    void removeTrackMessageDevice4deviceId(long deviceId) {
        TrackMessageDevice.executeUpdate(REMOVE_TRACK_MESSAGE_DEVICE_4_DEVICE_ID_HQL, [deviceId: deviceId])
    }

    void addTrackMessageDevice(long campusId, long featureId, String deviceIds) {
        List<TrackMessageDevice> trackMessageDeviceList = []

        deviceIds.split(",").each { deviceId ->
            if (checkDeviceExistMessage(deviceId as Long, featureId)) {
                Device device = deviceService.fetchDeviceById(deviceId as Long)
                throw new HiiAdminException("设备“${device.name}”已经配置".toString())
            }

            TrackMessageDevice trackMessageDevice = TrackMessageDevice.findOrCreateByFeatureIdAndDeviceId(featureId, deviceId as Long)
            if (trackMessageDevice.status != 1) {
                trackMessageDevice.campusId = campusId
                trackMessageDevice.status = 1
                trackMessageDeviceList << trackMessageDevice
            }
        }
        trackMessageDeviceList*.save(failOnError: true, flush: true)
    }

    boolean checkDeviceExistMessage(long deviceId, Long exFeatureId) {
        if (exFeatureId) {
            return TrackMessageDevice.findByDeviceIdAndFeatureIdNotEqualAndStatus(deviceId, exFeatureId, 1) ? true : false
        } else {
            return TrackMessageDevice.findByDeviceIdAndStatus(deviceId, 1) ? true : false
        }
    }
}

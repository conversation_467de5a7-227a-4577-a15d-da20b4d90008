package hiiadmin.signIn.repository

import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import timetabling.sign.SignInRecord
import timetabling.sign.SignInRecordRepair
import timetabling.sign.SignInStatistics

@Transactional
@Slf4j
class RepositorySignInRecordService {

    def fetchSignInRecordList(Long statisticsId, String studentName, Byte signInType, byte signInUserType, int p, int s) {
        def c = SignInRecord.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            eq("statisticsId", statisticsId)
            eq("signInUserType", signInUserType)
            if (studentName) {
                like("studentName", studentName + "%")
            }
            if (null != signInType) {
                eq("signInType", signInType)
            }
            order("id", "asc")
        }
        [list: list as List<SignInRecord>, total: list?.totalCount ?: 0]
    }

    Integer countSignInRecordByStudentIdAndStartTimeByStatus(Long campusId, Long studentId, Long startDate, Long endDate, Byte status) {
        SignInRecord.countByCampusIdAndStudentIdAndDateTimeBetweenAndStatus(campusId, studentId, new Date(startDate), new Date(endDate), status)
    }

    List<SignInRecord> fetchSignInRecordViaStudentIdAndDateTime(Long studentId, Date dateTime) {
        SignInRecord.findAllByDateTimeAndStudentId(dateTime, studentId)
    }

    SignInRecord fetchSignInRecordById(Long id) {
        SignInRecord.findById(id)
    }
    
    List<SignInRecord> fetchAllSignInRecordByStatisticsIdInList(List<Long> statisticsIdList) {
        if (!statisticsIdList) {
            return []
        }
        return SignInRecord.findAllByStatisticsIdInListAndStatus(statisticsIdList, 1 as byte)
    }

    List<SignInRecord> fetchAllSignInRecordByStatisticsIdInList4Teacher(List<Long> statisticsIdList) {
        return SignInRecord.findAllByStatisticsIdInListAndSignInUserType(statisticsIdList, ConstantEnum.UserTypeEnum.TEACHER.type)
    }

    void deleteSignInRecords(List<Long> statisticsIdList) {
        List<SignInRecord> signInRecordList = SignInRecord.findAllByStatisticsIdInList(statisticsIdList)
        signInRecordList*.delete(failOnError: true)
    }


    SignInRecord fetchTeacherSignInRecord(Long statisticsId) {
        return SignInRecord.findByStatisticsIdAndSignInUserType(statisticsId, ConstantEnum.UserTypeEnum.TEACHER.type)
    }
    
    SignInRecord fetchTeacherSignInRecordByTeacherId(Long statisticsId, String teacherId) {
        return SignInRecord.findByStatisticsIdAndTeacherIdAndSignInUserType(statisticsId, teacherId, ConstantEnum.UserTypeEnum.TEACHER.type)
    }
    
    List<SignInRecord> fetchAllBySignInStatisticId(Long statisticId, Byte signInUserType) {
        return SignInRecord.findAllByStatisticsIdAndStatusAndSignInUserType(statisticId, 1 as byte, signInUserType)
    }
    
    List<SignInRecord> fetchAllBySignInStatisticId(Long statisticId) {
        return SignInRecord.findAllByStatisticsIdAndStatus(statisticId, 1 as byte)
    }

    void updateBatchRecordByStatisticId(SignInStatistics signInStatistics) {
        List<SignInRecord> signInRecordList = fetchAllBySignInStatisticId(signInStatistics.id, ConstantEnum.UserTypeEnum.STUDENT.type)
        if (signInRecordList) {
            signInRecordList*.status = signInStatistics.status
            signInRecordList*.teacherId = signInStatistics.teacherId
            signInRecordList*.teacherName = signInStatistics.teacherName
            signInRecordList*.startTime = signInStatistics.startTime
            signInRecordList*.endTime = signInStatistics.endTime
            signInRecordList*.timeslot = signInStatistics.timeslot
            signInRecordList*.timeslotName = signInStatistics.timeslotName
            signInRecordList*.dayOfWeek = signInStatistics.dayOfWeek
            saveList(signInRecordList)
        }
    }
    
    void deleteSignInRecordByStatisticsId(Long statisticId) {
        List<SignInRecord> signInRecordList = fetchAllBySignInStatisticId(statisticId)
        if (signInRecordList) {
            signInRecordList*.status = 0 as byte
            signInRecordList*.save(flush: true, failOnError: true)
        }
    }
    
    void deleteSignInRecordByStatisticsIdList(List<Long> statisticIdList) {
        log.info("deleteSignInRecordByStatisticsIdList statisticIdList: ${statisticIdList}")
        
        if (!statisticIdList) {
            return
        }
        
        String sql = 'UPDATE SignInRecord SET status = 0, lastUpdated = NOW() WHERE statisticsId IN :statisticIdList'
        SignInRecord.executeUpdate(sql, [statisticIdList: statisticIdList])
    }
    
    def saveList(List<SignInRecord> signInRecordList) {
        signInRecordList*.save(flush: true, failOnError: true)
    }

    def saveSignInRecord(SignInRecord signInRecord) {
        signInRecord.save(flush: true, failOnError: true)
    }


    SignInRecordRepair fetchSignInRecordRepairByRecordId(Long recordId) {
        return SignInRecordRepair.findBySignInRecordIdAndStatus(recordId,1)
    }

    def saveSignInRecordRepair(SignInRecordRepair signInRecordRepair) {
        signInRecordRepair.save(flush: true, failOnError: true)
    }

    List<SignInRecordRepair> fetchSignInRecordRepairAllByRecordIdList(List<Long> recordIdList){
        return SignInRecordRepair.findAllBySignInRecordIdInListAndStatus(recordIdList, 1)
    }
}

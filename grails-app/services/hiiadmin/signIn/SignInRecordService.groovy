package hiiadmin.signIn

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantSignInEnum
import hiiadmin.approval.LeaveRecordService
import hiiadmin.signIn.repository.RepositorySignInRecordService
import hiiadmin.signIn.repository.RepositorySignInStatisticsService
import hiiadmin.utils.TimeUtils
import timetabling.oa.LeaveRecord
import timetabling.sign.SignInRecord
import timetabling.sign.SignInStatistics
import timetabling.sign.UnitSignIn

@Slf4j
@Transactional
class SignInRecordService {

    RepositorySignInRecordService repositorySignInRecordService

    RepositorySignInStatisticsService repositorySignInStatisticsService

    LeaveRecordService leaveRecordService

    void updateSignIn4CancelLeaveRecord(Long studentId) {
        Date datetime = TimeUtils.getDateStartTime(new Date())
        List<SignInRecord> signInRecordList = repositorySignInRecordService.fetchSignInRecordViaStudentIdAndDateTime(studentId, datetime)
        signInRecordList.each {
            if (it.signInType == ConstantSignInEnum.SignInType.Leave.type) {
                List<LeaveRecord> leaveList = leaveRecordService.isLeaveRecordHasCreated(studentId, it.startTime, it.endTime)
                if (!leaveList) {
                    it.signInType = ConstantSignInEnum.SignInType.NOSIGNIN.type
                    SignInStatistics signInStatistics1 = repositorySignInStatisticsService.getSignInStatistics(it.statisticsId)
                    signInStatistics1.shouldNum = signInStatistics1.shouldNum + 1
                    repositorySignInStatisticsService.saveSignInStatistics(signInStatistics1)
                }
            }
        }
        signInRecordList*.save(failOnError: true)
    }

    def deleteSignInStatistics(List<UnitSignIn> unitSignInList) {
        List<Long> unitSignInIdList = unitSignInList*.id
        if (unitSignInIdList && unitSignInIdList.size() > 0) {
            //TODO 考勤数据量很大，所以物理删除，无法恢复，关闭按钮需加确认提示
            List<SignInStatistics> statisticsList = SignInStatistics.findAllByUnitSignInIdInListAndDateTime(unitSignInIdList, TimeUtils.getDateStartTime(new Date()))
            if (statisticsList && statisticsList.size() > 0) {
                List<Long> statisticsIdList = statisticsList*.id
                repositorySignInRecordService.deleteSignInRecords(statisticsIdList)
            }
            statisticsList*.delete(failOnError: true)
            log.info("删除上课考勤数据，size:${statisticsList?.size() ?: 0}")
        } else {
            log.info("未删除上课考勤数据")
        }
    }

}

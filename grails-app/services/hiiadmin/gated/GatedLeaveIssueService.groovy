package hiiadmin.gated

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.apiCloud.BgbDockingApi
import hiiadmin.apiCloud.XJobApi
import hiiadmin.approval.LeaveCategoryService
import hiiadmin.docking.DockingPlatformService
import hiiadmin.utils.ToStringUnits
import org.joda.time.DateTime
import timetabling.gated.GatedLeaveIssue
import timetabling.oa.LeaveDirection
import timetabling.oa.LeaveDirectionDevice
import timetabling.oa.LeaveRecord

import javax.annotation.Resource

@Transactional
class GatedLeaveIssueService {

    @Resource
    XJobApi xJobApi

    @Resource
    AntennaApi antennaApi

    LeaveCategoryService leaveCategoryService

    @Resource
    BgbDockingApi bgbDockingApi

    /**
     * 撤销请假后处理权限相关
     */
    def cancelLeaveRecordUpdateGateIssue(Long leaveRecordId) {
        LeaveRecord leaveRecord = LeaveRecord.get(leaveRecordId)
        DateTime dateTime = new DateTime()
        List<GatedLeaveIssue> leaveGateIssueList = GatedLeaveIssue.findAllByLeaveIdAndStatus(leaveRecordId, GatedLeaveIssue.NORMAL_STATUS)
        leaveGateIssueList.each { leaveGateIssue ->
            switch (leaveGateIssue.executedFirm) {
                case GatedLeaveIssue.FIRM_XJOB_LEAVE:
                    xJobApi.removeJob(leaveGateIssue.relatedId.toInteger())
                    leaveGateIssue.status = GatedLeaveIssue.DELETE_STATUS
                    break
                case GatedLeaveIssue.FIRM_XJOB_ISSUE:
                    break
                case GatedLeaveIssue.FIRM_XJOB_REVOKE:
                    xJobApi.triggerAndStopJob(leaveGateIssue.relatedId.toInteger())
                    leaveGateIssue.status = GatedLeaveIssue.DELETE_STATUS
                    break
                case GatedLeaveIssue.FIRM_DH_LEAVE:
                    antennaApi.leave2eliminate([leaveIds: leaveGateIssue.relatedId, campusId: leaveRecord.campusId])
                    leaveGateIssue.status = GatedLeaveIssue.DELETE_STATUS
                    break
            }
        }


        LeaveDirection leaveDirection = LeaveDirection.findById(leaveRecord.leaveDirectionId)
        List<LeaveDirectionDevice> leaveDirectionDeviceList = leaveCategoryService.fetchAllLeaveDirectionDeviceByLeaveDirectionId(leaveDirection?.id)
        bgbDockingApi.updateUserIssueBackFormLeave([
                campusId : leaveRecord.campusId,
                userId   : leaveRecord.studentId,
                userType : ConstantEnum.UserTypeEnum.STUDENT.type,
                deviceIds: ToStringUnits.longList2String(leaveDirectionDeviceList*.deviceId),
                foreignIds: ToStringUnits.longList2String([leaveRecord.id]),
        ])

        leaveGateIssueList*.save(failOnError: true, flush: true)
    }
}

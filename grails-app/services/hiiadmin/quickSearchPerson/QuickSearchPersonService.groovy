package hiiadmin.quickSearch<PERSON>erson

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.module.quickSearchPerson.QuickSearchPersonVO
import hiiadmin.pic.CheckFaceService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.BedService
import hiiadmin.school.repository.RepositoryStaffDepartmentService
import hiiadmin.track.TrackService
import hiiadmin.track.TrackVODOService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import org.apache.commons.lang3.StringUtils
import timetabling.StaffDepartment
import timetabling.building.Bed
import timetabling.humanface.pic.CheckFace
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.track.Track
import timetabling.user.Student
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major


/**
 * @Author: sjl
 * @CreateTime: 2025-08-25
 * @Description: ${description}
 * @Version: 1.0
 */
@Transactional
@Slf4j
class QuickSearchPersonService {
    StudentService studentService

    UnitService unitService

    BedService bedService

    GradeService gradeService

    FacultyService facultyService

    MajorService majorService

    CheckFaceService checkFaceService

    TrackService trackService

    TeacherService teacherService

    UserEncodeInfoService userEncodeInfoService

    DepartmentService departmentService

    RepositoryStaffDepartmentService repositoryStaffDepartmentService



    def findStudentByCampusIdAndSearchValuePage(Long campusId, String searchValue, Long unitId, int p, int s) {
        Campus campus = Campus.get(campusId)

        List<Student> studentList = studentService.findAllStudentByCampusIdAndSearchValueAndUnitIdPage(campusId, searchValue, unitId, p, s)
        int total = studentService.countStudentByCampusIdAndSearchValueV2(campusId, searchValue, unitId)
        List<QuickSearchPersonVO> quickSearchPersonVOS = []
        if (studentList != null && studentList.size() > 0) {
            List<Long> studentIdList = studentList*.id
            Map<Long, Unit> studentIdUnitMap = unitService.getStudentIdUnitMapByStudentIdList(campusId, studentIdList)
            List<Bed> bedList = bedService.fetchAllBedByStudentIdInList(studentIdList)
            Map<Long, Bed> studentIdBedMap = [:]
            bedList.each {
                bed ->
                    studentIdBedMap.put(bed.studentId, bed)
            }
            List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)
            Map<Long, Grade> gradeIdGradeMap = [:]
            gradeList.each {
                grade ->
                    gradeIdGradeMap.put(grade.id, grade)
            }

            Map<Long, Faculty> facultyMap = [:]
            Map<Long, Major> majorMap = [:]

            if (campus && campus.type == 2 as byte) {
                List<Faculty> facultyList = facultyService.fetchAllFacultyByCampus(campusId)
                facultyMap = facultyList?.collectEntries { [it.id, it] }
                List<Major> majorList = majorService.fetchAllMajorByCampus(campusId)
                majorMap = majorList?.collectEntries { [it.id, it] }
            }


            studentList.each {
                Student student ->
                    Unit unit = studentIdUnitMap.get(student.id)
                    String gradeName = ""
                    String sectionName = ""
                    String facultyName = ""
                    String majorName = ""
                    if (unit) {
                        Grade grade = gradeIdGradeMap.get(unit.gradeId)
                        if (grade) {
                            gradeName = gradeService.appendGradeName(grade)
                            if (grade.sectionId) {
                                sectionName = ConstantEnum.SectionType.getEnumByType(grade?.sectionId).name
                            }
                        }

                        Faculty faculty = facultyMap?.get(unit.facultyId)
                        if (faculty) {
                            facultyName = facultyService.appendFacultyName(faculty)
                        }

                        Major major = majorMap?.get(unit.majorId)
                        if (major) {
                            majorName = majorService.appendMajorName(major)
                        }
                    }
                    Bed bed = studentIdBedMap.get(student.id)
                    String dormRoom = ""
                    if (bed) {
                        if (StringUtils.isNotBlank(bed?.buildingName)) {
                            dormRoom = bed?.buildingName
                        }

                        if (StringUtils.isNotBlank(bed?.buildingLayerName)) {
                            dormRoom = dormRoom + bed?.buildingLayerName
                        }

                        if (StringUtils.isNotBlank(bed?.doorPlateName)) {
                            dormRoom = dormRoom + bed?.doorPlateName
                        }
                    }
                    int status = 3
                    String checkMessage = null
                    if (student.pic) {
                        CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUserIdAndUserType(campusId, student.id, ConstantEnum.UserTypeEnum.STUDENT.type)
                        if (checkFace != null) {
                            status = checkFace.resultStatus
                            if (checkFace.checkMessage.equals("success")) {
                                checkMessage = "-"
                            } else {
                                checkMessage = checkFace.checkMessage
                            }
                        }
                    }

                    quickSearchPersonVOS << QuickSearchPersonVO.setFaceGroupVoToStudent(student, gradeName, unit?.alias ?: unit?.name, sectionName, dormRoom, facultyName, majorName, campus?.type, status, checkMessage)
            }
        }
        [list: quickSearchPersonVOS, total: total]
    }

    List<Track> findTrackByCampusIdAndSearchValuePage(Long campusId, List<Long> deviceIds, Long startTime, Long endTime, Byte userType, int p, int s,Boolean isAsc) {
        List<Track> trackList = trackService.fetchAllTrackByCampusIdAndDeviceIdsBetweenTime(campusId, deviceIds, userType, startTime, endTime, p, s,isAsc)
        return trackList
    }

    int countTrackByCampusId(Long campusId, List<Long> deviceIds, Long startTime, Long endTime, Byte userType) {
        Integer count = trackService.countTrackByCampusId(campusId, deviceIds, userType, startTime, endTime)
        return count
    }

    def findTeacherFaceByCampusIdAndSearchValuePage(long campusId, String searchValue, int p, int s) {
        List<TeacherVO> teacherList = teacherService.findTeacherByCampusIdAndSearchValue(campusId, searchValue,  p, s)
        List<QuickSearchPersonVO> quickSearchPersonVOList = []
        if (teacherList != null && teacherList.size() > 0) {
            List<Long> teacherIds = teacherList.collect { PhenixCoder.decodeId(it.id) }
            Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacherIds)
            List<StaffDepartment> staffDepartmentList = repositoryStaffDepartmentService.fetchAllByCampusIdAndStatus(campusId)
            Map<Long,List<StaffDepartment>> staffDepartmentMap = staffDepartmentList.groupBy { it.teacherId }
             departmentService.fetchAllDepartmentByCampusId(campusId).groupBy { it.id}
            teacherList.each {
                TeacherVO teacher ->
                    QuickSearchPersonVO vo = QuickSearchPersonVO.setFaceGroupVoToTeacherVO(teacher)



                    vo.mobile = teacherIdMobileMap.get(PhenixCoder.decodeId(teacher.id))
                    quickSearchPersonVOList << vo
            } as Map<Long, List<StaffDepartment>>
        }
        //如果有搜索名称，就只放回当前页面
        int total = (searchValue ? teacherList.size() : teacherService.countTeacherBySchoolIdAndGender(campusId, searchValue))
        [list: quickSearchPersonVOList, total: total]

    }
}

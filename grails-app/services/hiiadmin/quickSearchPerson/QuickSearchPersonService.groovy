package hiiadmin.quickSearchPerson

import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.module.quickSearchPerson.QuickSearchPersonVO
import hiiadmin.pic.CheckFaceService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.BedService
import hiiadmin.school.repository.RepositoryStaffDepartmentService
import hiiadmin.school.user.ParentService
import hiiadmin.track.TrackService
import hiiadmin.track.TrackVODOService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.visitor.VisitorService
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import org.apache.commons.lang3.StringUtils
import timetabling.ParentStudent
import timetabling.StaffDepartment
import timetabling.building.Bed
import timetabling.humanface.pic.CheckFace
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.track.Track
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.visitor.Visitor
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major


/**
 * @Author: sjl
 * @CreateTime: 2025-08-25
 * @Description: ${description}
 * @Version: 1.0
 */
@Transactional
@Slf4j
class QuickSearchPersonService {
    StudentService studentService

    UnitService unitService

    BedService bedService

    GradeService gradeService

    FacultyService facultyService

    MajorService majorService

    CheckFaceService checkFaceService

    TrackService trackService

    TeacherService teacherService

    UserEncodeInfoService userEncodeInfoService

    DepartmentService departmentService

    RepositoryStaffDepartmentService repositoryStaffDepartmentService

    ParentService parentService

    VisitorService visitorService


    def findStudentByCampusIdAndSearchValuePage(Long campusId, String searchValue, Long unitId, int p, int s) {
        Campus campus = Campus.get(campusId)

        List<Student> studentList = studentService.findAllStudentByCampusIdAndSearchValueAndUnitIdPage(campusId, searchValue, unitId, p, s)
        int total = studentService.countStudentByCampusIdAndSearchValueV2(campusId, searchValue, unitId)
        List<QuickSearchPersonVO> quickSearchPersonVOS = []
        if (studentList != null && studentList.size() > 0) {
            List<Long> studentIdList = studentList*.id
            Map<Long, Unit> studentIdUnitMap = unitService.getStudentIdUnitMapByStudentIdList(campusId, studentIdList)
            List<Bed> bedList = bedService.fetchAllBedByStudentIdInList(studentIdList)
            Map<Long, Bed> studentIdBedMap = [:]
            bedList.each {
                bed ->
                    studentIdBedMap.put(bed.studentId, bed)
            }
            List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)
            Map<Long, Grade> gradeIdGradeMap = [:]
            gradeList.each {
                grade ->
                    gradeIdGradeMap.put(grade.id, grade)
            }

            Map<Long, Faculty> facultyMap = [:]
            Map<Long, Major> majorMap = [:]

            if (campus && campus.type == 2 as byte) {
                List<Faculty> facultyList = facultyService.fetchAllFacultyByCampus(campusId)
                facultyMap = facultyList?.collectEntries { [it.id, it] }
                List<Major> majorList = majorService.fetchAllMajorByCampus(campusId)
                majorMap = majorList?.collectEntries { [it.id, it] }
            }


            studentList.each {
                Student student ->
                    Unit unit = studentIdUnitMap.get(student.id)
                    String gradeName = ""
                    String sectionName = ""
                    String facultyName = ""
                    String majorName = ""
                    if (unit) {
                        Grade grade = gradeIdGradeMap.get(unit.gradeId)
                        if (grade) {
                            gradeName = gradeService.appendGradeName(grade)
                            if (grade.sectionId) {
                                sectionName = ConstantEnum.SectionType.getEnumByType(grade?.sectionId).name
                            }
                        }

                        Faculty faculty = facultyMap?.get(unit.facultyId)
                        if (faculty) {
                            facultyName = facultyService.appendFacultyName(faculty)
                        }

                        Major major = majorMap?.get(unit.majorId)
                        if (major) {
                            majorName = majorService.appendMajorName(major)
                        }
                    }
                    Bed bed = studentIdBedMap.get(student.id)
                    String dormRoom = ""
                    if (bed) {
                        if (StringUtils.isNotBlank(bed?.buildingName)) {
                            dormRoom = bed?.buildingName
                        }

                        if (StringUtils.isNotBlank(bed?.buildingLayerName)) {
                            dormRoom = dormRoom + bed?.buildingLayerName
                        }

                        if (StringUtils.isNotBlank(bed?.doorPlateName)) {
                            dormRoom = dormRoom + bed?.doorPlateName
                        }
                    }
                    int status = 3
                    String checkMessage = null
                    if (student.pic) {
                        CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUserIdAndUserType(campusId, student.id, ConstantEnum.UserTypeEnum.STUDENT.type)
                        if (checkFace != null) {
                            status = checkFace.resultStatus
                            if (checkFace.checkMessage.equals("success")) {
                                checkMessage = "-"
                            } else {
                                checkMessage = checkFace.checkMessage
                            }
                        }
                    }

                    quickSearchPersonVOS << QuickSearchPersonVO.setFaceGroupVoToStudent(student, gradeName, unit?.alias ?: unit?.name, sectionName, dormRoom, facultyName, majorName, campus?.type, status, checkMessage)
            }
        }
        [list: quickSearchPersonVOS, total: total]
    }

    List<Track> findTrackByCampusIdAndSearchValuePage(Long campusId, List<Long> deviceIds, String searchValue, Byte userType, Long startTime, Long endTime, Long unitId,  int p, int s, Boolean isAsc) {
        List<Track> trackList = trackService.fetchAllTrackByCampusIdAndDeviceIdsBetweenTime(campusId, deviceIds,searchValue,userType, startTime, endTime,unitId, p, s, isAsc)
        return trackList
    }

    int countTrackByCampusId(Long campusId, List<Long> deviceIds, String searchValue, Byte userType, Long startTime, Long endTime, Long unitId) {
        Integer count = trackService.countTrackByCampusId(campusId, deviceIds,searchValue,userType, startTime, endTime,unitId)
        return count
    }

    def findTeacherFaceByCampusIdAndSearchValuePage(long campusId, String searchValue, int p, int s) {
        List<TeacherVO> teacherList = teacherService.findTeacherByCampusIdAndSearchValue(campusId, searchValue, p, s)
        List<QuickSearchPersonVO> quickSearchPersonVOList = []
        if (teacherList != null && teacherList.size() > 0) {
            List<Long> teacherIds = teacherList.collect { PhenixCoder.decodeId(it.id) }
            Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(
                    ConstantEnum.UserEncodeInfoType.MOBILE.type,
                    ConstantEnum.UserTypeEnum.TEACHER.type,
                    teacherIds
            )

            // 构建教师ID到部门名称的映射
            Map<Long, String> teacherIdDepartmentNameMap = buildTeacherDepartmentMapping(campusId, teacherIds)

            teacherList.each { TeacherVO teacher ->
                QuickSearchPersonVO vo = QuickSearchPersonVO.setFaceGroupVoToTeacherVO(teacher)
                Long teacherId = PhenixCoder.decodeId(teacher.id)

                // 设置手机号
                vo.mobile = teacherIdMobileMap.get(teacherId)

                // 设置部门信息
                vo.departmentName = teacherIdDepartmentNameMap.get(teacherId)

                quickSearchPersonVOList << vo
            }
        }
        //如果有搜索名称，就只放回当前页面
        int total = teacherService.countTeacherBySchoolIdAndGender(campusId, searchValue)
        [list: quickSearchPersonVOList, total: total]
    }

    /**
     * 构建教师ID到部门名称的映射
     * @param campusId 校区ID
     * @param teacherIds 教师ID列表
     * @return 教师ID到部门名称的映射（多个部门用逗号分隔）
     */
    private Map<Long, String> buildTeacherDepartmentMapping(Long campusId, List<Long> teacherIds) {
        if (!teacherIds) {
            return [:]
        }

        // 获取教师部门关联信息（只查询指定教师的部门关联）
        List<StaffDepartment> staffDepartmentList = repositoryStaffDepartmentService.fetchAllByCampusIdAndStatus(campusId)
        Map<Long, List<StaffDepartment>> teacherIdStaffDepartmentMap = staffDepartmentList
                .findAll { it.teacherId in teacherIds } // 只保留指定教师的记录
                .groupBy { it.teacherId }

        if (!teacherIdStaffDepartmentMap) {
            return [:]
        }

        // 获取相关的部门ID
        Set<Long> departmentIds = staffDepartmentList.collect { it.departmentId }.toSet()

        // 获取部门信息（只查询需要的部门）
        List<Object> departments = departmentService.fetchAllDepartmentByCampusId(campusId)
        Map<Long, Object> departmentIdMap = departments
                .findAll { it.id in departmentIds } // 只保留需要的部门
                .collectEntries { [it.id, it] }

        // 构建教师ID到部门名称的映射
        Map<Long, String> teacherIdDepartmentNameMap = [:]
        teacherIdStaffDepartmentMap.each { teacherId, staffDepartments ->
            List<String> departmentNames = staffDepartments
                    .collect { staffDepartment ->
                        def department = departmentIdMap[staffDepartment.departmentId]
                        return department?.name
                    }
                    .findAll { it != null } // 过滤掉null值
                    .unique() // 去重

            if (departmentNames) {
                teacherIdDepartmentNameMap[teacherId] = departmentNames.join(", ")
            }
        }

        return teacherIdDepartmentNameMap
    }

    def findParentFaceByCampusIdAndSearchValuePage(Long campusId, String name, Long unitId = null, int s, int p) {
        List<Parent> parentList = parentService.findParentPageByCampusIdAndSearchValueV2(campusId, name, unitId, s, p)
        Integer total = parentService.countParentPageByCampusIdAndSearchValueV2(campusId, name, unitId)
        List<QuickSearchPersonVO> quickSearchPersonVOS = []
        if (parentList != null && parentList.size() > 0) {
            Map<Long, String> parentIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.PARENT.type, parentList*.id)
            Multimap<Long, ParentStudent> parentIdParentStudentMap = HashMultimap.create()
            List<ParentStudent> parentStudents = parentService.fetchAllParentStudentByParentIdInListAndCampusId(parentList*.id, campusId)
            parentStudents?.each {
                parentIdParentStudentMap.put(it.parentId, it)
            }
            List<Student> studentList = studentService.fetchAllStudentByStudentIdInList(parentStudents*.studentId)
            Map<Long, Student> studentMap = studentList?.collectEntries { [it.id, it] }
            Map<Long, Unit> unitMap = unitService.getStudentIdUnitMapByStudentIdList(campusId, studentList*.id)
            Map<Long, String> unitIdNameMap = unitService.createGradeUnitNameViaUnitIdInList(unitMap.keySet().toList())
            parentList.each {
                Parent parent ->
                    Set<ParentStudent> parentStudentList = parentIdParentStudentMap.get(parent.id)
                    List<String> relationList = []
                    List<String> unitNameList = []
                    parentStudentList.each {
                        ParentStudent parentStudent ->
                            String relation = "家长"
                            Student student = studentMap.get(parentStudent.studentId)
                            Unit unit = unitMap.get(student.id)
                            unitNameList << unitIdNameMap.get(unit?.id)
                            if (StringUtils.isEmpty(parentStudent.studentName)) {
                                relation = student?.name
                            } else {
                                relation = parentStudent.studentName
                            }
                            if (parentStudent.appellation != null) {
                                String memo = "家长"
                                if (parentStudent.appellation == ConstantEnum.AppellationType.OTHER.type && parentStudent.memo) {
                                    memo = parentStudent.memo
                                    relation = (relation + "的" + memo) ?: "家长"
                                } else {
                                    relation = (relation + "的" + ConstantEnum.AppellationType.getEnumByType(parentStudent.appellation).name ?: memo) ?: "家长"
                                }
                            }
                            if (relation == null || relation.equals("null")) {
                                relation = "家长"
                            }
                            relationList << relation
                    }
                    QuickSearchPersonVO vo = QuickSearchPersonVO.setFaceGroupVOToParent(parent)
                    vo.mobile = parentIdMobileMap.get(parent.id)
                    vo.relation = relationList
                    vo.unitNameList = unitNameList


                    quickSearchPersonVOS << vo
            }
        }
        [list: quickSearchPersonVOS, total: total]

    }

    def findVisitorFaceByCampusIdAndSearchValuePage(long campusId, String searchValue, int p, int s) {
        def resultMap = visitorService.fetchAllVisitorByCampusIdAndSearchValue(campusId, searchValue, s, p)
        List<QuickSearchPersonVO> quickSearchPersonVOList = []
        if (resultMap.list != null && resultMap.list.size() > 0) {
            List<Long> teacherIds = resultMap.list.collect { it.id }
            Map<Long, String> visitorIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(
                    ConstantEnum.UserEncodeInfoType.MOBILE.type,
                    ConstantEnum.UserTypeEnum.VISITORS.type,
                    teacherIds
            )
            resultMap.list.each { visitor ->

                QuickSearchPersonVO vo = QuickSearchPersonVO.setFaceGroupVOToVisitor(visitor)
                vo.mobile = visitorIdMobileMap.get(visitor.id)
                quickSearchPersonVOList << vo
            }

        }
        [list: quickSearchPersonVOList, total: resultMap.total]
    }
}

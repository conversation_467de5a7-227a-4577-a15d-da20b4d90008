package hiiadmin.history

import cn.hutool.core.bean.BeanUtil
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.PCTemperatureRecordVO
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.utils.PhenixCoder
import timetabling.Department
import timetabling.history.HistoryTemperatureRecord
import timetabling.org.Grade
import timetabling.org.Unit

@Transactional
class HistoryTemperatureRecordService {

    GradeService gradeService

    UnitService unitService

    DepartmentService departmentService

    StudentService studentService

    def transformHistoryPCTemperatureRecordVO(Long campusId, Byte type, Long startTime, Long endTime, String name, String temperatures, Byte status) {

        def re = HistoryTemperatureRecord.createCriteria().list {
            eq("campusId", campusId)
            if (startTime & endTime) {
                gte("dayDatestamp", min)
                lte("dayDatestamp", max)
            }
            if (temperatures) {
                String[] ts = temperatures.split(",")
                Double minT = ts[0] as Double
                Double maxT = ts[1] as Double
                gte("temperature", minT)
                lte("temperature", maxT)
            }
            if (status == -1) {
                or {
                    gte("temperature", ConstantEnum.Temperature.UNUSUAL.min)
                    lt("temperature", ConstantEnum.Temperature.NORMAL.min)
                }
            } else if (status == 1) {
                or {
                    and {
                        lt("temperature", ConstantEnum.Temperature.NORMAL.max)
                        gte("temperature", ConstantEnum.Temperature.NORMAL.min)
                    }
                    eq("normal", true)
                }
            }
            switch (type) {
                case ConstantEnum.UserTypeEnum.STUDENT.type:
                    isNotNull("studentId")
                    break
                case ConstantEnum.UserTypeEnum.TEACHER.type:
                    isNotNull("teacherId")
                    break
                case -1:
                    and {
                        isNull("studentId")
                        isNull("teacherId")
                    }
                    break
            }
            if (name) {
                or {
                    eq("name", name)
                }
            }
            order("startTime", "desc")
        }
        List<PCTemperatureRecordVO> vos = []
        re.each {
            HistoryTemperatureRecord historyTemperatureRecord ->
                PCTemperatureRecordVO vo = BeanUtil.copyProperties(historyTemperatureRecord, PCTemperatureRecordVO)
                if (vo.type == ConstantEnum.UserTypeEnum.STUDENT.type) {
                    Unit unit = unitService.fetchUnitById(historyTemperatureRecord.unitId)
                    Grade grade = gradeService.fetchGradeById(unit?.gradeId)
                    vo.departmentName = ConstantEnum.SectionType.getEnumByType(grade?.sectionId)?.name + grade?.name + (unit?.alias ?: unit?.name)
                } else if (vo.type == ConstantEnum.UserTypeEnum.TEACHER.type) {
                    List<Department> departmentList = departmentService.findDepartmentListByTeacherIdAndCampusId(historyTemperatureRecord.teacherId, historyTemperatureRecord.campusId)
                    vo.departmentName = departmentList*.name.join(",")
                }
                vos << vo
        }
        [list: vos, total: vos.size() ?: 0, msg: null]
    }


    /**
     * pc端测温记录
     * @param campusId
     * @param type
     * @param dateTimes
     * @param name
     * @param temperatures
     * @param status
     * @param p
     * @param s
     * @return
     */
    def transformPCHistoryTemperatureRecordVO(Long campusId, Byte type, Long startTime, Long endTime, String name, String temperatures, Byte status, int p, int s) {
        def re = HistoryTemperatureRecord.createCriteria().list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            if (startTime & endTime) {
                gte("dayDatestamp", startTime)
                lte("dayDatestamp", endTime)
            }
            if (temperatures) {
                String[] ts = temperatures.split(",")
                Double min = ts[0] as Double
                Double max = ts[1] as Double
                gte("temperature", min)
                lte("temperature", max)
            }
            if (status == -1) {
                or {
                    gte("temperature", ConstantEnum.Temperature.UNUSUAL.min)
                    lt("temperature", ConstantEnum.Temperature.NORMAL.min)
                }
            } else if (status == 1) {
                or {
                    and {
                        lt("temperature", ConstantEnum.Temperature.NORMAL.max)
                        gte("temperature", ConstantEnum.Temperature.NORMAL.min)
                    }
                    eq("normal", true)
                }
            }
            switch (type) {
                case ConstantEnum.UserTypeEnum.STUDENT.type:
                    isNotNull("studentId")
                    break
                case ConstantEnum.UserTypeEnum.TEACHER.type:
                    isNotNull("teacherId")
                    break
                case -1:
                    and {
                        isNull("studentId")
                        isNull("teacherId")
                    }
                    break
            }
            if (name) {
                or {
                    eq("name", name)
                }
            }
            order("startTime", "desc")
        }
        Integer total = re.totalCount
        List<PCTemperatureRecordVO> vos = []
        re.each {
            HistoryTemperatureRecord temperatureRecord ->
                PCTemperatureRecordVO vo = historyTemperatureRecordConversion(temperatureRecord)
                vos << vo
        }
        [list: vos, total: total]
    }

    PCTemperatureRecordVO historyTemperatureRecordConversion(HistoryTemperatureRecord temperatureRecord) {
        PCTemperatureRecordVO temperatureRecordVO = new PCTemperatureRecordVO()
        temperatureRecordVO.setTemperature(temperatureRecord.getTemperature())

        temperatureRecordVO.setId(temperatureRecord.getId())
        temperatureRecordVO.setSubmitterId(PhenixCoder.encodeId(temperatureRecord.getSubmitterId()))
        temperatureRecordVO.setSubmitter(temperatureRecord.getSubmitter())
        if (temperatureRecord.studentId) {
            temperatureRecordVO.name = studentService.appendStudentStatusByStudentId(temperatureRecord.studentId)
        } else {
            temperatureRecordVO.name = temperatureRecord.name
        }

        temperatureRecordVO.setCampusId(PhenixCoder.encodeId(temperatureRecord.getCampusId()))
        temperatureRecordVO.setSchoolId(PhenixCoder.encodeId(temperatureRecord.getSchoolId()))
        temperatureRecordVO.setEventPlace(temperatureRecord.getEventPlace())
        temperatureRecordVO.setMode(temperatureRecord.getMode())
        temperatureRecordVO.setStartTime(temperatureRecord.getStartTime().getTime())
        temperatureRecordVO.setAvatar(temperatureRecord.getAvatar())
        temperatureRecordVO.setDayDatestamp(temperatureRecord.getDayDatestamp())
        temperatureRecordVO.dateCreated = temperatureRecord.dateCreated?.time
        temperatureRecordVO.normal = temperatureRecord.normal
        Byte status = -1
        if (temperatureRecord.normal || ConstantEnum.Temperature.isNormal(temperatureRecord.temperature)) {
            status = 1
        }
        temperatureRecordVO.status = status
        temperatureRecordVO.code = temperatureRecord.code
        if (temperatureRecord.studentId) {
            temperatureRecordVO.type = 1
        } else if (temperatureRecord.teacherId) {
            temperatureRecordVO.type = 6
        } else {
            temperatureRecordVO.type = -1
        }
        return temperatureRecordVO
    }

}

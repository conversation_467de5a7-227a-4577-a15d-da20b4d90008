package hiiadmin.history

import grails.gorm.transactions.Transactional
import hiiadmin.module.gated.TrackVO
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import timetabling.history.HistoryTrack

@Transactional("historicalData")
class HistoryTrackService {

    /**
     * 获取历史数据
     * */
    List<HistoryTrack> fetchHistoryTrackByCampusIdAndUserId(Long campusId, Long userId, Byte userType, Long startTime, Long endTime) {
        Long startDate = TimeUtils.getDateStartTime(startTime).time
        Long endDate = TimeUtils.getDateEndTime(endTime).time
        return fetchAllTrackByCampusIdAndUserIdBetweenTime(campusId, userId, startDate, endDate, userType)
    }


    List<HistoryTrack> fetchAllTrackByCampusIdAndUserIdBetweenTime(Long campusId, Long userId, Long startTime, Long endTime, Byte userType) {
        def c = HistoryTrack.createCriteria()
        def list = c.list {
            eq("campusId", campusId)
            eq("userId", userId)
            eq("userType", userType)
            between("pushTime", startTime, endTime)
            order("pushTime", "asc")
        }
        list as List<HistoryTrack>
    }

    TrackVO transformHistoryTrackVO(HistoryTrack track) {
        TrackVO vo = new TrackVO()
        vo.id = track.id
        vo.name = track.userName
        vo.code = track.userCode
        vo.faceImg = track.faceImg
        vo.licensePlate = track.licensePlate
        vo.originalParameters = track?.originalParameters
        vo.viaName = track?.objName
        vo.viaTime = track.pushTime
        vo.pushTime = track.pushTime
        vo.userId = PhenixCoder.encodeId(track.userId)
        vo.campusId = PhenixCoder.encodeId(track.campusId)
        vo.userType = track.userType
        vo.location = track.location ? track.location : track.objName
        vo.deviceId = track.deviceId
        vo
    }

    List<HistoryTrack> fetchHistoryTrack(StringBuffer stringBuffer, Map<String, Object> map) {
        return HistoryTrack.executeQuery(stringBuffer.toString(), map) as List<HistoryTrack>
    }

    Integer fetchHistoryTrackCount(StringBuffer stringBuffer, Map<String, Object> map) {
        return HistoryTrack.executeQuery(stringBuffer.toString(), map)[0] as Integer
    }

    //导出
    List<HistoryTrack> getHistoryTrackRecordByStamp(Long campusId, Byte userType, Long dayTimeStamp) {
        List<HistoryTrack> trackList = []
        Long endStamp = dayTimeStamp + 86400000
        String hql = """SELECT t FROM HistoryTrack t WHERE t.campusId = :campusId AND t.userType = :userType AND t.status = 1 AND t.pushTime >= :dayTimeStamp AND t.pushTime < :endStamp """
        trackList = HistoryTrack.executeQuery(hql, [campusId: campusId, userType: userType, dayTimeStamp: dayTimeStamp, endStamp: endStamp])
        return trackList
    }

    //导出
    List<HistoryTrack> getHistoryTrackRecordByStampRange(Long campusId, List<Byte> typeList, Long startDate, Long endDate) {
        List<HistoryTrack> trackList = []
        String hql = """SELECT t FROM HistoryTrack t WHERE t.campusId = :campusId AND t.userType in :typeList AND t.status = 1 AND (t.dayTimeStamp BETWEEN :startDate AND :endDate)"""
        trackList = HistoryTrack.executeQuery(hql, [campusId: campusId, userType: typeList, startDate: startDate, endDate: endDate])
        return trackList
    }
}

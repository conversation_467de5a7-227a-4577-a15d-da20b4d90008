package hiiadmin.history

import grails.gorm.transactions.Transactional
import hiiadmin.school.TemperatureRecordService
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.time.DateUtils

import static hiiadmin.utils.TimeUtils.getDateTimeOfDay

@Transactional
class AppTemperatureRecordService {
    TemperatureRecordService temperatureRecordService

    HistoryTemperatureRecordService historyTemperatureRecordService

    def transformAllPCTemperatureRecordVO(Long campusId, Byte type, String dateTimes, String searchName, String temperatures, Byte status, int p, int s) {
        Long startTime
        Long endTime
        if (dateTimes) {
            String[] dates = dateTimes.split(",")
            startTime = getDateTimeOfDay(dates[0] as Long).millis
            endTime = getDateTimeOfDay(dates[1] as Long).millis
        } else {
            endTime = getDateTimeOfDay(new Date()).millis
            startTime = getDateTimeOfDay(DateUtils.addDays(new Date(), -6)).millis
        }

        def re
        if (ToStringUnits.belongsToHistoricalData(startTime, -30)) {
            re = historyTemperatureRecordService.transformPCHistoryTemperatureRecordVO(campusId, type, startTime, endTime, searchName, temperatures, status, p, s)
        } else {
            re = temperatureRecordService.transformPCTemperatureRecordVO(campusId, type, dateTimes, searchName, temperatures, status, p, s)
        }


        return re
    }
}

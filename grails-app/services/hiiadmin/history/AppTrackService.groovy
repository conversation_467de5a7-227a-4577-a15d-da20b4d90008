package hiiadmin.history

import grails.gorm.transactions.Transactional
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.gated.TrackVO
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.DeviceSpaceService
import hiiadmin.track.TrackService
import hiiadmin.track.TrackVODOService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import timetabling.history.HistoryTrack
import timetabling.iot.DeviceSpace
import timetabling.track.Track

import static hiiadmin.ConstantEnum.DeviceFirm

@Transactional
class AppTrackService {

    TrackService trackService

    HistoryTrackService historyTrackService

    TrackVODOService trackVODOService

    DockingPlatformCampusService dockingPlatformCampusService

    DeviceService deviceService

    DeviceSpaceService deviceSpaceService


    /**
     * 更加pushTime选择查询近期数据或历史数据
     * @param campusId
     * @param userId
     * @param userType
     * @param pushTime
     * @return
     */
    List<TrackVO> getAllTrackVORecord(Long campusId, Long userId, Byte userType, Long startTime, Long endTime,Boolean isAsc) {
        List<Long> deviceIdList = []

        List<TrackVO> vos = []
        if (ToStringUnits.belongsToHistoricalData(startTime, -30)) {
            List<HistoryTrack> historyTrackList = historyTrackService.fetchHistoryTrackByCampusIdAndUserId(campusId, userId, userType, startTime, endTime,isAsc)
            if (historyTrackList != null && historyTrackList.size() > 0) {
                historyTrackList.each { HistoryTrack track ->
                    TrackVO vo = historyTrackService.transformHistoryTrackVO(track)
                    trackVODOService.transformHistoryTrackVO(vo)
                    if (track.deviceId) {
                        deviceIdList << track.deviceId
                        vo.coordinate = deviceService.fetchDeviceById(track.deviceId)?.coordinate
                    } else {
                        vo.coordinate = deviceService.fetchDeviceByIndexCodeAndCampusId(campusId, track.objCode)?.coordinate

                    }
                    if (track.firm == DeviceFirm.ALY_DEVICE.firm) {
                        vo.faceImg = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, campusId)
                    }
                    vos << vo
                }
            }
        } else {
            List<Track> trackList = trackService.fetchAllTrackByCampusIdAndUserId(campusId, userId, userType, startTime, endTime,isAsc)
            if (trackList != null && trackList.size() > 0) {
                trackList.each { track ->
                    TrackVO vo = trackService.transformNewTrackVO(track)
                    trackVODOService.transformHistoryTrackVO(vo)
                    if (track.deviceId) {
                        deviceIdList << track.deviceId
                        vo.coordinate = deviceService.fetchDeviceById(track.deviceId)?.coordinate
                    } else {
                        vo.coordinate = deviceService.fetchDeviceByIndexCodeAndCampusId(campusId, track.objCode)?.coordinate
                    }
                    if (track.firm == DeviceFirm.ALY_DEVICE.firm) {
                        vo.faceImg = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, campusId)
                    }
                    vos << vo
                }
            }
        }
        if (deviceIdList) {
            List<DeviceSpace> deviceSpaceList = deviceSpaceService.fetchAllDeviceSpace4device(campusId, deviceIdList)
            Map<Long, DeviceSpace> deviceSpaceMap = deviceSpaceList.collectEntries { [(it.deviceId): it] }
            vos.each {
                if (it.deviceId) {
                    DeviceSpace space = deviceSpaceMap.get(it.deviceId)
                    if (space?.areaIdPath) {
                        String[] ids = space.areaIdPath.split("_")
                        if (ids.length > 0) {
                            it.buildingId = PhenixCoder.encodeId(ids[0])
                        }
                        if (ids.length > 1) {
                            it.layerId = PhenixCoder.encodeId(ids[1])
                        }
                        if (ids.length > 2) {
                            it.doorplateId = PhenixCoder.encodeId(ids[2])
                        }
                    }
                    if (space) {
                        it.rowIndex = space.rowIndex
                        it.colIndex = space.colIndex
                    }
                }
            }
        }
        return vos
    }
}


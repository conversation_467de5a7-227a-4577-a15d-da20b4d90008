package hiiadmin.moral

import com.alibaba.fastjson.JSON
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.bugu.ServiceResult
import com.google.common.collect.*
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.module.moral.*
import hiiadmin.module.vo.DayTimeVO
import hiiadmin.school.GradeService
import hiiadmin.school.UnitService
import hiiadmin.school.building.BuildingService
import hiiadmin.utils.TimeUtils
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.StringUtils
import timetabling.building.DoorPlate
import timetabling.moral.*
import timetabling.org.Grade
import timetabling.org.Unit

import java.util.concurrent.TimeUnit

import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class MoralService {
    
    UnitService unitService
    
    BuildingService buildingService
    
    GradeService gradeService

    final static byte OF_All = MoralScoreType.OF_All.type,
                      OF_UNIT = MoralScoreType.OF_UNIT.type,
                      OF_DORMITORY = MoralScoreType.OF_DORMITORY.type,
                      OF_STUDENT = MoralScoreType.OF_STUDENT.type,
                      OF_UNIT_OPTION = MoralScoreType.OF_UNIT_OPTION.type,
                      OF_DORMITORY_OPTION = MoralScoreType.OF_DORMITORY_OPTION.type,
                      OF_PUBLISHER_OPTION = MoralScoreType.OF_PUBLISHER_OPTION.type,

                      BY_DAY = MoralCountType.BY_DAY.type,
                      BY_WEEK = MoralCountType.BY_WEEK.type,
                      BY_MONTH = MoralCountType.BY_MONTH.type

    final static long CLASS_SCORE = MoralMenuType.CLASS_SCORE.id,
                      DORMITORY_SCORE = MoralMenuType.DORMITORY_SCORE.id

    final static List<Byte> DEFAULT_COUNT_TYPE = [BY_DAY, BY_WEEK, BY_MONTH]

    def createMoralList(Long schoolId, Long campusId, Long moralId, String json) {
        List<MoralVO> moralVOList = JSON.parseArray(json, MoralVO.class)
//        int maxi = moralVOList.size()
        Integer maxIdx = maxMoralIdxByPId(moralId) ?: 1
        moralVOList.eachWithIndex { vo, i ->
            Moral moral = new MoralVO().createMoral(vo)
            moral.idx = maxIdx + i
            moral.status = 1 as byte
            moral.schoolId = schoolId
            moral.campusId = campusId
            moral.pId = moralId
            saveMoral(moral)
        }
    }

    def createMoralList4Campus(Long schoolId, Long campusId, Long moralId, Long type) {
        Integer[] nameType = [2, 1, 6, -1, 5]
        for (i in 0..< 5) {
            Moral moral = new Moral()
            moral.idx = i + 1
            moral.status = 1 as byte
            moral.name = MoralOptionType.getValueByType(nameType[i] as byte)
            moral.nameType = nameType[i] as byte
            moral.schoolId = schoolId
            moral.campusId = campusId
            moral.pId = moralId
            moral.defaultScore = -1 as Double
            moral.classScore = 1 as Double
            if (type == CLASS_SCORE) {
                moral.studentScore = 1 as Double
                moral.dormitoryScore = 0 as Double
            } else if (type == DORMITORY_SCORE) {
                moral.studentScore = 0 as Double
                moral.dormitoryScore = 1 as Double
            }
            saveMoral(moral)
        }
    }

    List<MoralItem> fetchAllMoralItemByCampusId(Long campusId) {
        MoralItem.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    List<MoralItem> fetchAllMoralItemByCampusIdNotStatus(Long campusId) {
        MoralItem.findAllByCampusId(campusId)
    }

    Integer maxMoralIdxByPId(Long pId) {
        String HQL = """ SELECT MAX(idx) FROM Moral WHERE pId =: pId AND status = 1 """
        Moral.executeQuery(HQL, [pId: pId])[0] as Integer
    }

    List<MoralResult> getAllMoralResult(Collection<Long> resultIds) {
        MoralResult.getAll(resultIds)
    }

    MoralResult getMoralResult(Long resultId) {
        MoralResult.get(resultId)
    }

    Map<Long, MoralResult> getMoralResultIdMap(Collection<Long> resultIds) {
        List<MoralResult> moralResultList = getAllMoralResult(resultIds)
        moralResultList.collectEntries { moralResult ->
            [moralResult.id, moralResult]
        }
    }

    List<MoralMenu> fetchAllMoralMenuByCampusId(Long campusId, Long itemId, Long type, int p = 1, Integer s = null) {
        Map map = [campusId: campusId, "status": 1 as byte]
        if (itemId) {
            map.put("itemId", itemId)
        }
        if (type) {
            map.put("type", type)
        }
        if (s) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        MoralMenu.findAllWhere(map)
    }

    @Cached(
            expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_moralS_fetchAllMoralMenuByCampusId", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    List<MoralMenu> fetchAllMoralMenuByCampusId(Long campusId) {
        return MoralMenu.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    @Cached(
            expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_moralS_fetchAllMoralMenuByCampusIdNotStatus", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    List<MoralMenu> fetchAllMoralMenuByCampusIdNotStatus(Long campusId) {
        return MoralMenu.findAllByCampusId(campusId)
    }

    /**
     * 按照年级获取所有的德育评分详情，不剔除非详情非本年级数据
     * @param gradeId
     * @param dateTimes
     * @param moralMenuIds
     * @param scoreType
     * @return
     */
    ServiceResult<List<MoralDetail>> fetchAllMoralDetail(Long gradeId, String dateTimes, String moralMenuIds, byte scoreType) {
        String[] dates = dateTimes.split(",")
        Date startDate = TimeUtils.getDateTimeOfDay(dates[0] as Long).toDate()
        Date endDate = TimeUtils.getDateTimeOfDay(dates[1] as Long).toDate()
        StringBuilder sb = new StringBuilder(150)
        sb.append("FROM MoralDetail WHERE ")
        String startDateStr = TimeUtils.dateToStr(startDate, TimeUtils.DATE_FORMAT)
        String endDateStr = TimeUtils.dateToStr(endDate, TimeUtils.DATE_FORMAT)
        if (gradeId == null) {
            return ServiceResult.failure("100", "年级找不到")
        }
        switch (scoreType) {
            case OF_UNIT_OPTION:
                List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
                List<Long> unitHasDormitoryDoorPlateIdList = []
                unitList.each { unit ->
                    unitHasDormitoryDoorPlateIdList.addAll(buildingService.fetchAllHasDormitoryDoorPlateIdByUnitId(unit.id))
                }
                if (unitHasDormitoryDoorPlateIdList) {
                    String ids = ToStringUnits.list2String(unitHasDormitoryDoorPlateIdList)
                    sb.append(" moralMenuId IN (").append(moralMenuIds)
                            .append(") AND ( gradeId = ").append(gradeId)
                            .append(" OR doorPlateId IN (").append(ids)
                            .append(") ) AND ")
                } else {
                    log.warn("gradeId@{} not find dormitory", gradeId)
                    sb.append(" moralMenuId IN (").append(moralMenuIds)
                            .append(") AND gradeId = ").append(gradeId)
                            .append(" AND ")
                }
                break
            case OF_DORMITORY_OPTION:
                List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
                List<Long> unitHasDormitoryDoorPlateIdList = []
                unitList.each { unit ->
                    unitHasDormitoryDoorPlateIdList.addAll(buildingService.fetchAllHasDormitoryDoorPlateIdByUnitId(unit.id))
                }
                String ids = ToStringUnits.list2String(unitHasDormitoryDoorPlateIdList)
                if (StringUtils.isEmpty(ids)) {
                    return ServiceResult.failure("100", "当前年级没有宿舍信息")
                }
                sb.append(" moralMenuId IN (").append(moralMenuIds)
                        .append(") AND doorPlateId IN ( ").append(ids).append(" ) ")
                        .append(" AND ")
                break
            case OF_STUDENT:
                sb.append(" moralMenuId IN (").append(moralMenuIds).append(") AND studentId IS NOT NULL AND ")
                break
        }
        sb.append(" dateDay >= '").append(startDateStr)
                .append("' AND dateDay <= '").append(endDateStr)
                .append("' AND status = 1")
        String HQL = sb.toString()

        log.info(">>>>>> HQL@{}", HQL)
        List<MoralDetail> moralDetailList = MoralDetail.findAll(HQL)
        return ServiceResult.success(moralDetailList)
    }

    ServiceResult<List<MoralDetail>> fetchAllMoralDetail(Long gradeId, Date startDate, Date endDate, String moralMenuIds, byte scoreType) {
        StringBuilder sb = new StringBuilder(150)
        sb.append("FROM MoralDetail WHERE ")
        String startDateStr = TimeUtils.dateToStr(startDate, TimeUtils.DATE_FORMAT)
        String endDateStr = TimeUtils.dateToStr(endDate, TimeUtils.DATE_FORMAT)
        if (gradeId == null) {
            return ServiceResult.failure("100", "年级找不到")
        }
        switch (scoreType) {
            case OF_UNIT_OPTION:
                List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
                List<Long> unitHasDormitoryDoorPlateIdList = []
                unitList.each { unit ->
                    unitHasDormitoryDoorPlateIdList.addAll(buildingService.fetchAllHasDormitoryDoorPlateIdByUnitId(unit.id))
                }
                if (unitHasDormitoryDoorPlateIdList) {
                    String ids = ToStringUnits.list2String(unitHasDormitoryDoorPlateIdList)
                    sb.append(" moralMenuId IN (").append(moralMenuIds)
                            .append(") AND ( unitId IN (").append(ToStringUnits.list2String(unitList*.id)).append(") ")
                            .append(" OR doorPlateId IN (").append(ids)
                            .append(") ) AND ")
                } else {
                    log.warn("gradeId@{} not find dormitory", gradeId)
                    sb.append(" moralMenuId IN (").append(moralMenuIds)
                            .append(") AND unitId IN (").append(ToStringUnits.list2String(unitList*.id)).append(") ")
                            .append(" AND ")
                }
                break
            case OF_DORMITORY_OPTION:
                List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
                List<Long> unitHasDormitoryDoorPlateIdList = []
                unitList.each { unit ->
                    unitHasDormitoryDoorPlateIdList.addAll(buildingService.fetchAllHasDormitoryDoorPlateIdByUnitId(unit.id))
                }
                String ids = ToStringUnits.list2String(unitHasDormitoryDoorPlateIdList)
                if (StringUtils.isEmpty(ids)) {
                    return ServiceResult.failure("100", "当前年级没有宿舍信息")
                }
                sb.append(" moralMenuId IN (").append(moralMenuIds)
                        .append(") AND doorPlateId IN ( ").append(ids).append(" ) ")
                        .append(" AND ")
                break
            case OF_STUDENT:
                sb.append(" moralMenuId IN (").append(moralMenuIds).append(") AND studentId IS NOT NULL AND ")
                break
        }
        sb.append(" dateDay >= '").append(startDateStr)
                .append("' AND dateDay <= '").append(endDateStr)
                .append("' AND status = 1")
        String HQL = sb.toString()

        log.info(">>>>>> HQL@{}", HQL)
        List<MoralDetail> moralDetailList = MoralDetail.findAll(HQL)
        if (moralDetailList.size() > 0) {
            return ServiceResult.success(moralDetailList)
        } else {
            return ServiceResult.failure("200018", "德育记录不存在")
        }
    }

    Multimap<Long, Moral> fetchAllMoralListMapByCampusId(Long campusId) {
        List<Moral> menuList = fetchAllMoralByCampusId(campusId)
        Multimap<Long, Moral> multimap = HashMultimap.create()
        menuList.each {
            multimap.put(it.pId, it)
        }
        multimap
    }

    static String UPDATE_MORAL_INDEX_HQL = """UPDATE Moral SET idx = :idx, lastUpdated = NOW() WHERE id = :id AND campusId = :campusId"""

    void fixMoralIndex(Long campusId, String indexJson) {
        indexJson.split(",").eachWithIndex { id, i ->
            Moral.executeUpdate(UPDATE_MORAL_INDEX_HQL, [idx: (i + 1), id: id as Long, campusId: campusId])
        }
    }
    static String UPDATE_MORAL_MENU_INDEX_HQL = """UPDATE MoralMenu SET idx = :idx, lastUpdated = NOW() WHERE id = :id AND campusId = :campusId"""

    void fixMoralMenuIndex(Long campusId, String indexJson) {
        indexJson.split(",").eachWithIndex { id, i ->
            MoralMenu.executeUpdate(UPDATE_MORAL_MENU_INDEX_HQL, [idx: (i + 1), id: id as Long, campusId: campusId])
        }
    }

    Moral fetchMoralById(Long moralId) {
        Moral.get(moralId)
    }

    MoralItem fetchMoralItemByCampusIdAndItemId(Long campusId, Integer moralItemId) {
        MoralItem.findByCampusIdAndItemIdAndStatus(campusId, moralItemId, 1 as byte)
    }

    MoralDetail fetchMoralDetailById(Long moralDetailId) {
        MoralDetail.get(moralDetailId)
    }

    MoralResult fetchMoralResultById(Long id) {
        MoralResult.get(id)
    }

    void deleteMoral(Long moralId) {
        Moral.executeUpdate(DELETE_MORAL_4_ID_HQL, [id: moralId])
    }

    static String DELETE_MORAL_MENU_4_ID_HQL = """ UPDATE MoralMenu SET status = 0, lastUpdated = NOW() WHERE id = :menuId """
    static String DELETE_MORAL_4_ID_HQL = """ UPDATE Moral SET status = 0, lastUpdated = NOW() WHERE id = :id """
    static String DELETE_ALL_MORAL_4_MENU_HQL = """ UPDATE Moral SET status = 0, lastUpdated = NOW() WHERE pId = :menuId """

    void deleteMoralMenu(Long menuId) {
        MoralMenu.executeUpdate(DELETE_MORAL_MENU_4_ID_HQL, [menuId: menuId])
        Moral.executeUpdate(DELETE_ALL_MORAL_4_MENU_HQL, [menuId: menuId])
    }

    Moral saveMoral(Moral moral) {
        moral.save(failOnError: true, flush: true)
    }

    List<Moral> fetchAllMoralByMenuIdInList(List<Long> moralMenuIdList) {
        Moral.findAllByPIdInListAndStatus(moralMenuIdList, 1 as byte)
    }

    MoralMenu fetchMoralMenuById(Long menuId) {
        MoralMenu.get(menuId)
    }

    MoralMenu saveMoralMenu(MoralMenu menu) {
        menu.save(failOnError: true, flush: true)
    }

    def saveAllMoralMenu(List<MoralMenu> menuList) {
        menuList*.save(failOnError: true, flush: true)
    }

    List<MoralMenu> fetchAllMoralMenuByIds(List<Long> moralMenuIdList) {
        MoralMenu.getAll(moralMenuIdList)
    }

    List<Moral> fetchAllMoralByMenuId(Long menuId) {
        Moral.findAllByPIdAndStatus(menuId, 1 as byte)
    }


    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_moralS_fetchAllMoralByCampusId", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Moral> fetchAllMoralByCampusId(Long campusId) {
        Moral.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_moralS_fetchAllMoralByCampusIdNoStatus", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Moral> fetchAllMoralByCampusIdNoStatus(Long campusId) {
        Moral.findAllByCampusId(campusId)
    }

    List<MoralDetail> fetchAllMoralDetailByMoralResultId(Long moralResultId) {
        if (!moralResultId) {
            return []
        }
        MoralDetail.findAllByResultIdAndStatus(moralResultId, 1 as byte)
    }

    List<MoralDetail> fetchAllMoralDetailByMoralResultIdNoStatus(Long moralResultId) {
        if (!moralResultId) {
            return []
        }
        MoralDetail.findAllByResultId(moralResultId)
    }

    List<MoralDetail> fetchAllMoralDetailByMoralResultIdInListNoStatus(List<Long> moralResultIds) {
        if (!moralResultIds) {
            return []
        }
        MoralDetail.findAllByResultIdInList(moralResultIds)
    }

    List<VocationalMoralDetail> fetchAllVocationalMoralDetailByMoralResultIdInListNoStatus(List<Long> moralResultIds) {
        if (!moralResultIds) {
            return []
        }
        VocationalMoralDetail.findAllByResultIdInList(moralResultIds)
    }

    Table<Long, Long, Table<Long, Long, Integer>> transformDetailTable(List<MoralDetail> detailList, byte scoreType) {
        //班级/宿舍Id-时间戳-<moralMenuId,resultId,detailCount>
        Table<Long, Long, Table<Long, Long, Integer>> detailTable = HashBasedTable.create()
        detailList.each { moralDetail ->
            //事件提交时间
            Long time = moralDetail.dateDay.time
            //事件对应德育一级菜单
            Long moralMenuId = moralDetail.moralMenuId
            //事件对应提交Id
            Long resultId = moralDetail.resultId
            switch (scoreType) {
                case OF_UNIT_OPTION:
                    //因为目前的需求问题不会出现这种问题，该判断作为过滤异常数据
                    //同时造成了查询统计的班级数据缺失宿舍部分
                    if (moralDetail.unitId) {
                        if (!detailTable.get(moralDetail.unitId, time)) {
                            detailTable.put(moralDetail.unitId, time, HashBasedTable.create())
                            detailTable.get(moralDetail.unitId, time).put(moralMenuId, resultId, 1)
                        } else {
                            int total = detailTable.get(moralDetail.unitId, time).get(moralMenuId, resultId) ?: 0
                            detailTable.get(moralDetail.unitId, time).put(moralMenuId, resultId, total + 1)
                        }
                    } else {
                        log.info("detailId {}", moralDetail?.id)
                    }
                    break
                case OF_DORMITORY_OPTION:
                    if (!detailTable.get(moralDetail.doorPlateId, time)) {
                        detailTable.put(moralDetail.doorPlateId, time, HashBasedTable.create())
                        detailTable.get(moralDetail.doorPlateId, time).put(moralMenuId, resultId, 1)
                    } else {
                        int total = detailTable.get(moralDetail.doorPlateId, time).get(moralMenuId, resultId) ?: 0
                        detailTable.get(moralDetail.doorPlateId, time).put(moralMenuId, resultId, total + 1)
                    }
                    break
                case OF_STUDENT:
                    if (!detailTable.get(moralDetail.studentId, time)) {
                        detailTable.put(moralDetail.studentId, time, HashBasedTable.create())
                        detailTable.get(moralDetail.studentId, time).put(moralMenuId, resultId, 1)
                    } else {
                        int total = detailTable.get(moralDetail.studentId, time).get(moralMenuId, resultId) ?: 0
                        detailTable.get(moralDetail.studentId, time).put(moralMenuId, resultId, total + 1)
                    }
                    break
            }
        }
        detailTable
    }

    Map<Long, Moral> fetchALlMoralMapByCampusId(Long campusId) {
        fetchAllMoralByCampusId(campusId).collectEntries { moral ->
            [moral.id, moral]
        }
    }

    Map<Long, MoralMenu> fetchAllMoralMenuMapByCampusId(Long campusId) {
        fetchAllMoralMenuByCampusId(campusId).collectEntries { moralMenu ->
            [moralMenu.id, moralMenu]
        }
    }

    List<MoralExcelUnifiedBaseExportVO> transformUnitUnifiedExcel(Long gradeId, Table<Long, Long, Table<Long, Long, Integer>> detailTable, Map<Long, Moral> moralMap, Map<Long, MoralMenu> moralMenuMap) {
        Grade grade = gradeService.fetchGradeById(gradeId)
        //年级
        String gradeName = grade.name
        List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
        Multimap<Double, MoralExcelUnifiedUnitExportVO> scoreMultimap = HashMultimap.create()

        unitList.each { Unit unit ->
            Long unitId = unit.id
            //班级
            String unitName = unit.alias ?: unit?.name

            String name = gradeName + unitName
            Map<Long, Table<Long, Long, Integer>> timeTableMap = detailTable.row(unitId)

            MoralExcelUnifiedUnitExportVO moralExcelUnitVO = new MoralExcelUnifiedUnitExportVO(
                    name: name,
                    timeDatas: []
            )
            moralExcelUnitVO.code = unit.code
            //班级总得分
            double unitScore = 0
            timeTableMap.each { timeEntry ->
                //登记日期
                Long time = timeEntry.key

                MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO timeBaseVO = new MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO(
                        dateTime: time,
                        moralMenus: []
                )
                Table<Long, Long, Integer> moralMenuIdResultIdTotal = timeEntry.value

                Set<Long> moralMenuIdSet = moralMenuIdResultIdTotal.rowKeySet()
                Set<Long> resultIdSet = moralMenuIdResultIdTotal.columnKeySet()
                Map<Long, MoralResult> moralResultMap = getMoralResultIdMap(resultIdSet)
                //时间总分统计
                double timeScore = 0
                moralMenuIdSet.each { moralMenuId ->
                    MoralMenu moralMenu = moralMenuMap.get(moralMenuId)
                    //登记项
                    String moralMenuName = moralMenu.name
                    MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO.MoralMenuExcelBaseVO menuExcelBaseVO = new MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO.MoralMenuExcelBaseVO(
                            id: moralMenuId,
                            name: moralMenuName
                    )
                    //登记项得分
                    double timeMoralScore = 0
                    Map<Long, Integer> resultIdTotalMap = moralMenuIdResultIdTotal.row(moralMenuId)
                    resultIdTotalMap.each { resultIdTotalEntry ->
                        Long resultId = resultIdTotalEntry.key
                        int total = resultIdTotalEntry.value
                        MoralResult moralResult = moralResultMap.get(resultId)
                        Moral moral = moralMap.get(moralResult.moralId)
                        if (moral) {
                            Double classScore = moral.classScore == 0D ? 0D : moralResult.score
                            if (moral.scoreLimit == 0D) {
                                classScore = moral.classScore
                            }
                            double score = total * classScore
                            timeMoralScore += score
                        } else {
                            log.error("记录登记项Id 异常 ，造成原因可能是多学校时前端缓存提交的，moralResultId@{}", moralResult.id)
                        }
                    }
                    timeScore += timeMoralScore
                    menuExcelBaseVO.score = timeMoralScore
                    timeBaseVO.moralMenus.add(menuExcelBaseVO)
                }
                unitScore += timeScore
                timeBaseVO.score = timeScore
                moralExcelUnitVO.timeDatas.add(timeBaseVO)
            }
            moralExcelUnitVO.score = unitScore
            scoreMultimap.put(unitScore, moralExcelUnitVO)
        }
        addUnifiedRange(scoreMultimap)
    }

    List<MoralExcelUnifiedBaseExportVO> transformDoorPlateUnifiedExcel(Table<Long, Long, Table<Long, Long, Integer>> detailTable, Map<Long, Moral> moralMap, Map<Long, MoralMenu> moralMenuMap) {
        Set<Long> doorPlateIdSet = detailTable.rowKeySet()
        List<DoorPlate> doorPlateList = DoorPlate.getAll(doorPlateIdSet)

        Multimap<Double, MoralExcelUnifiedDoorPlateExportVO> scoreMultimap = HashMultimap.create()

        //宿舍总评分
        doorPlateList.each { doorPlate ->
            Map<Long, Table<Long, Long, Integer>> timeTableMap = detailTable.row(doorPlate.id)
            //房间名
            MoralExcelUnifiedDoorPlateExportVO moralExcelDoorPlateExportVO = new MoralExcelUnifiedDoorPlateExportVO(
                    name: doorPlate.memo,
                    timeDatas: []
            )
            moralExcelDoorPlateExportVO.code = doorPlate.code
            double doorPlateScore = 0
            timeTableMap.each { timeEntry ->
                long time = timeEntry.key
                MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO timeBaseVO = new MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO(
                        dateTime: time,
                        moralMenus: []
                )
                Table<Long, Long, Integer> moralMenuIdResultIdTotal = timeEntry.value

                Set<Long> moralMenuIdSet = moralMenuIdResultIdTotal.rowKeySet()
                Set<Long> resultIdSet = moralMenuIdResultIdTotal.columnKeySet()
                Map<Long, MoralResult> moralResultMap = getMoralResultIdMap(resultIdSet)
                double doorPlateTimeScore = 0
                moralMenuIdSet.each { moralMenuId ->
                    MoralMenu moralMenu = moralMenuMap.get(moralMenuId)
                    //登记项
                    String moralMenuName = moralMenu.name
                    MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO.MoralMenuExcelBaseVO menuExcelBaseVO = new MoralExcelUnifiedBaseExportVO.MoralExcelTimeBaseVO.MoralMenuExcelBaseVO(
                            id: moralMenuId,
                            name: moralMenuName
                    )
                    Map<Long, Integer> resultIdTotalMap = moralMenuIdResultIdTotal.row(moralMenuId)
                    //单项日评分
                    double doorPlateTimeMoralMenuScore = 0
                    resultIdTotalMap.each { resultIdTotalEntry ->
                        Long resultId = resultIdTotalEntry.key
                        int total = resultIdTotalEntry.value
                        MoralResult moralResult = moralResultMap.get(resultId)
                        Moral moral = moralMap.get(moralResult.moralId)
                        if (moral) {
                            Double dormitoryScore = moral.dormitoryScore == 0D ? 0D : moralResult.score
                            if (moral.scoreLimit == 0D) {
                                dormitoryScore = moral.dormitoryScore
                            }
                            double score = total * dormitoryScore
                            doorPlateTimeMoralMenuScore += score
                        } else {
                            log.error("记录登记项Id 异常 ，造成原因可能是多学校时前端缓存提交的，moralResultId@{}", moralResult.id)
                        }
                    }
                    doorPlateTimeScore += doorPlateTimeMoralMenuScore
                    menuExcelBaseVO.score = doorPlateTimeMoralMenuScore
                    timeBaseVO.moralMenus.add(menuExcelBaseVO)
                }
                doorPlateScore += doorPlateTimeScore
                timeBaseVO.score = doorPlateTimeScore
                moralExcelDoorPlateExportVO.timeDatas.add(timeBaseVO)
            }
            moralExcelDoorPlateExportVO.score = doorPlateScore
            scoreMultimap.put(doorPlateScore, moralExcelDoorPlateExportVO)
        }
        addUnifiedRange(scoreMultimap)
    }

    List<MoralExcelUnifiedBaseExportVO> addUnifiedRange(Multimap<Double, MoralExcelUnifiedBaseExportVO> scoreMultimap) {
        Set<Double> scoreList = scoreMultimap.keySet()
        int i = 1
        scoreList.sort {
            -it
        }.each { score ->
            Set<MoralExcelUnifiedBaseExportVO> vos = scoreMultimap.get(score)
            int size = vos.size()
            vos*.rank = i
            i += size
        }
        List<MoralExcelUnifiedBaseExportVO> list = scoreMultimap.values().toList()
        list.sort {
            it?.code
        }
        list
    }

    List<MoralExcelSingleBaseExportVO> transformUnitSingleExcel(Long gradeId, Table<Long, Long, Table<Long, Long, Integer>> detailTable, Map<Long, Moral> moralMap) {
        Grade grade = gradeService.fetchGradeById(gradeId)
        //年级
        String gradeName = grade.name
        List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(gradeId)
        Map<Long, Unit> longUnitMap = unitList.collectEntries { unit ->
            [unit.id, unit]
        }
        List<MoralExcelSingleBaseExportVO> resultList = Lists.newArrayList()
        Set<Long> timeSet = detailTable.columnKeySet()
        timeSet.each { time ->
            MoralExcelSingleUnitExportVO unitExportVO = new MoralExcelSingleUnitExportVO(
                    dateTime: time
            )
            Map<Long, Table<Long, Long, Integer>> unitTableMap = detailTable.column(time)

            Multimap<Double, MoralExcelSingleBaseExportVO.MoralUDVO> scoreMultimap = HashMultimap.create()

            unitTableMap.each { unitEntry ->
                //登记日期
                Long unitId = unitEntry.key
                Unit unit = longUnitMap.get(unitId)
                //班级
                String unitName = unit.alias ?: unit?.name

                String name = gradeName + unitName

                MoralExcelSingleBaseExportVO.MoralUDVO moralUDVO = new MoralExcelSingleBaseExportVO.MoralUDVO(
                        name: name,
                        morals: []
                )
                moralUDVO.code = unit.code

                double timeUnitScore = 0
                Table<Long, Long, Integer> moralMenuIdResultIdTotal = unitEntry.value

                Set<Long> moralMenuIdSet = moralMenuIdResultIdTotal.rowKeySet()
                Set<Long> resultIdSet = moralMenuIdResultIdTotal.columnKeySet()
                Map<Long, MoralResult> moralResultMap = getMoralResultIdMap(resultIdSet)
                moralMenuIdSet.each { moralMenuId ->
                    Map<Long, Integer> resultIdTotalMap = moralMenuIdResultIdTotal.row(moralMenuId)
                    Map<Long, Double> moralIdScoreMap = [:]
                    resultIdTotalMap.each { resultIdTotalEntry ->
                        Long resultId = resultIdTotalEntry.key
                        int total = resultIdTotalEntry.value
                        MoralResult moralResult = moralResultMap.get(resultId)
                        Moral moral = moralMap.get(moralResult.moralId)
                        if (moral) {
                            Double classScore = moral.classScore == 0D ? 0D : moralResult.score
                            if (moral.scoreLimit == 0D) {
                                classScore = moral.classScore
                            }
                            double score = total * classScore
                            Double s = moralIdScoreMap[moral.id] ?: 0D
                            moralIdScoreMap[moral.id] = s + score
                            timeUnitScore += score
                        } else {
                            log.error("记录登记项Id 异常 ，造成原因可能是多学校时前端缓存提交的，moralResultId@{}", moralResult.id)
                        }
                    }
                    moralIdScoreMap.each { moralIdScoreEntry ->
                        Long moralId = moralIdScoreEntry.key
                        Moral moral = moralMap.get(moralId)
                        MoralExcelSingleBaseExportVO.MoralExcelBaseVO moralExcelBaseVO = new MoralExcelSingleBaseExportVO.MoralExcelBaseVO(
                                id: moralId,
                                name: moral.name,
                                score: moralIdScoreEntry.value
                        )
                        moralUDVO.morals.add(moralExcelBaseVO)
                    }
                }
                moralUDVO.score = timeUnitScore
                scoreMultimap.put(timeUnitScore, moralUDVO)
            }
            unitExportVO.list = addSingleRange(scoreMultimap)
            resultList.add(unitExportVO)
        }
        resultList
    }

    List<MoralExcelSingleBaseExportVO> transformDoorPlateSingleExcel(Table<Long, Long, Table<Long, Long, Integer>> detailTable, Map<Long, Moral> moralMap) {
        Set<Long> doorPlateIdSet = detailTable.rowKeySet()
        List<DoorPlate> doorPlateList = DoorPlate.getAll(doorPlateIdSet)
        Map<Long, DoorPlate> doorPlateMap = doorPlateList.collectEntries { doorPlate ->
            [doorPlate.id, doorPlate]
        }
        List<MoralExcelSingleDoorPlateExportVO> resultList = Lists.newArrayList()
        Set<Long> timeSet = detailTable.columnKeySet()
        timeSet.each { time ->
            MoralExcelSingleDoorPlateExportVO doorPlateExportVO = new MoralExcelSingleDoorPlateExportVO(
                    dateTime: time
            )
            Map<Long, Table<Long, Long, Integer>> doorPlateTableMap = detailTable.column(time)

            Multimap<Double, MoralExcelSingleBaseExportVO.MoralUDVO> scoreMultimap = HashMultimap.create()

            doorPlateTableMap?.each { doorPlateEntry ->
                //登记日期
                Long doorPlateId = doorPlateEntry.key
                DoorPlate doorPlate = doorPlateMap.get(doorPlateId)

                MoralExcelSingleBaseExportVO.MoralUDVO moralUDVO = new MoralExcelSingleBaseExportVO.MoralUDVO(
                        name: doorPlate.memo,
                        morals: []
                )
                moralUDVO.code = doorPlate.code
                double timeDoorPlateScore = 0
                Table<Long, Long, Integer> moralMenuIdResultIdTotal = doorPlateEntry.value

                Set<Long> moralMenuIdSet = moralMenuIdResultIdTotal.rowKeySet()
                Set<Long> resultIdSet = moralMenuIdResultIdTotal.columnKeySet()

                Map<Long, MoralResult> moralResultMap = getMoralResultIdMap(resultIdSet)

                moralMenuIdSet.each { moralMenuId ->
                    Map<Long, Integer> resultIdTotalMap = moralMenuIdResultIdTotal.row(moralMenuId)
                    Map<Long, Double> moralIdScoreMap = [:]
                    resultIdTotalMap.each { resultIdTotalEntry ->
                        Long resultId = resultIdTotalEntry.key
                        int total = resultIdTotalEntry.value
                        MoralResult moralResult = moralResultMap.get(resultId)
                        Moral moral = moralMap.get(moralResult.moralId)
                        if (moral) {
                            Double dormitoryScore = moral.dormitoryScore == 0D ? 0D : moralResult.score
                            if (moral.scoreLimit == 0D) {
                                dormitoryScore = moral.dormitoryScore
                            }
                            double score = total * dormitoryScore
                            Double s = moralIdScoreMap[moral.id] ?: 0D
                            moralIdScoreMap[moral.id] = s + score
                            timeDoorPlateScore += score
                        } else {
                            log.error("记录登记项Id 异常 ，造成原因可能是多学校时前端缓存提交的，moralResultId@{}", moralResult.id)
                        }
                    }
                    moralIdScoreMap.each { moralIdScoreEntry ->
                        Long moralId = moralIdScoreEntry.key
                        Moral moral = moralMap.get(moralId)
                        MoralExcelSingleBaseExportVO.MoralExcelBaseVO moralExcelBaseVO = new MoralExcelSingleBaseExportVO.MoralExcelBaseVO(
                                id: moralId,
                                name: moral.name,
                                score: moralIdScoreEntry.value
                        )
                        moralUDVO.morals.add(moralExcelBaseVO)
                    }
                }
                moralUDVO.score = timeDoorPlateScore
                scoreMultimap.put(timeDoorPlateScore, moralUDVO)
            }
            doorPlateExportVO.list = addSingleRange(scoreMultimap)
            resultList.add(doorPlateExportVO)
        }
        resultList
    }

    List<MoralExcelSingleBaseExportVO.MoralUDVO> addSingleRange(Multimap<Double, MoralExcelSingleBaseExportVO.MoralUDVO> scoreMultimap) {
        Set<Double> scoreList = scoreMultimap.keySet()
        int i = 1
        scoreList.sort {
            -it
        }.each { score ->
            Set<MoralExcelSingleBaseExportVO.MoralUDVO> vos = scoreMultimap.get(score)
            int size = vos.size()
            vos*.rank = i
            i += size
        }
        List<MoralExcelSingleBaseExportVO.MoralUDVO> list = scoreMultimap.values().toList()
        list?.sort {
            it.code
        }
        list
    }

    @Cached(
            expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_moralS_fetchAllMoralBaseScoreByCampusId", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    List<MoralBaseScore> fetchAllMoralBaseScoreByCampusId(Long campusId) {
        return MoralBaseScore.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    List<VocationalMoralDetail> fetchAllVocationalMoralDetailByMoralResultId(Long moralResultId) {
        if (!moralResultId) {
            return []
        }
        VocationalMoralDetail.findAllByResultIdAndStatus(moralResultId, 1 as byte)
    }

    List<VocationalMoralDetail> fetchAllVocationalMoralDetailByMoralResultIdNoStatus(Long moralResultId) {
        if (!moralResultId) {
            return []
        }
        VocationalMoralDetail.findAllByResultId(moralResultId)
    }

    /**
     * 生成日期查询条件
     * @param countType
     * @param timeVO
     * @return
     */
    private static String buildSelDataHQL(byte countType, DayTimeVO timeVO) {
        StringBuilder sb = new StringBuilder(50)
        if (countType == BY_MONTH) {
            sb.append(" monthIndex = ").append(timeVO.monthIndex)
        } else if (countType == BY_WEEK) {
            sb.append(" weekIndex = ").append(timeVO.weekIndex)
        } else if (countType == BY_DAY) {
            sb.append(" weekIndex = ").append(timeVO.weekIndex)
                    .append(" AND dayOfWeek = ").append(timeVO.dayOfWeek)
        }
        sb.toString()
    }

    MoralScore fetchScoreMoralScoreByIdOfScoreTypeAndCountTypeAndTime(long id, Long moralMenuId, byte scoreType, byte countType, DayTimeVO timeVO) {
        String selWeek = buildSelDataHQL(countType, timeVO)

        StringBuilder sb = new StringBuilder(255)
        sb.append("FROM MoralScore WHERE aimId = ").append(id)
                .append(" AND ").append(selWeek)
                .append(" AND countType = ").append(countType)
                .append(" AND ").append(" scoreType = ")
                .append(scoreType).append(" AND status = 1")

        if (scoreType == OF_UNIT_OPTION || scoreType == OF_DORMITORY_OPTION) {
            sb.append(" AND moralMenuId = ").append(moralMenuId)
        }
        String HQL = sb.toString()
//        log.info(">>>>>> HQL@{}", HQL)
        MoralScore moralScore = MoralScore.find(HQL)
        return moralScore
    }

    Double sumScore4MoralScoreByIdOfDayBetween(long id, Long moralMenuId, byte scoreType, Date start, Date end) {
        StringBuffer sb = transformAimIdMoralScoreHQLByDay(id)
        sb.insert(0, "SELECT SUM(score)")
        if (scoreType == OF_UNIT_OPTION || scoreType == OF_DORMITORY_OPTION) {
            sb.append(" AND moralMenuId = ").append(moralMenuId)
        }
        return (MoralScore.executeQuery(sb.toString(), [start: start, end: end, scoreType: scoreType])[0] as Double) ?: 0D
    }

    private static StringBuffer transformAimIdMoralScoreHQLByDay(long aimId) {
        StringBuffer sb = new StringBuffer(255)
        sb.append(" FROM MoralScore WHERE aimId = ").append(aimId)
                .append(" AND dateDay BETWEEN :start AND :end")
                .append(" AND countType = ").append(BY_DAY)
                .append(" AND scoreType = :scoreType")
                .append(" AND status = 1 ")
    }
}

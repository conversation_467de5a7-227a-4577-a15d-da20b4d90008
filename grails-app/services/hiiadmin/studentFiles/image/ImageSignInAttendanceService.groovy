package hiiadmin.studentFiles.image

import grails.gorm.transactions.Transactional
import hiiadmin.signIn.repository.RepositorySignInRecordService
import timetabling.studentFile.image.ImageSignInAttendance

import static hiiadmin.ConstantSignInEnum.SignInType

@Transactional
class ImageSignInAttendanceService {

    RepositorySignInRecordService repositorySignInRecordService

    ImageSignInAttendance fetchImageImageSignInAttendanceByCampusIdAndStudentIdAndFileTaskId(Long fileTaskId, Long campusId, Long studentId) {
        ImageSignInAttendance.findOrCreateByCampusIdAndFileTaskIdAndStudentIdAndStatus(campusId, fileTaskId, studentId, 1 as Byte)
    }

    def statisticsStudentSignInByStudentAndTimeType(Long studentId, Long campusId, Long fileTaskId, Long startDate, Long endDate) {
        ImageSignInAttendance attendance = fetchImageImageSignInAttendanceByCampusIdAndStudentIdAndFileTaskId(fileTaskId, campusId, studentId)
        attendance.campusId = campusId
        attendance.fileTaskId = fileTaskId
        attendance.studentId = studentId
        attendance.status = 1 as Byte
        //获取开学时间
        attendance.noSignInNum = repositorySignInRecordService.countSignInRecordByStudentIdAndStartTimeByStatus(campusId, studentId, startDate, endDate, SignInType.NOSIGNIN.type)
        attendance.normalNum = repositorySignInRecordService.countSignInRecordByStudentIdAndStartTimeByStatus(campusId, studentId, startDate, endDate, SignInType.NORMAL.type)
        attendance.lateNum = repositorySignInRecordService.countSignInRecordByStudentIdAndStartTimeByStatus(campusId, studentId, startDate, endDate, SignInType.LATE.type)
        attendance.absentNum = repositorySignInRecordService.countSignInRecordByStudentIdAndStartTimeByStatus(campusId, studentId, startDate, endDate, SignInType.ABSENT.type)
        attendance.leaveNum = repositorySignInRecordService.countSignInRecordByStudentIdAndStartTimeByStatus(campusId, studentId, startDate, endDate, SignInType.Leave.type)
        saveImageSignInAttendance(attendance)
    }

    def saveImageSignInAttendance(ImageSignInAttendance imageSignInAttendance) {
        imageSignInAttendance.save(failOnError: true, flush: true)
    }

}

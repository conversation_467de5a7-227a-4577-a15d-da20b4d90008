package hiiadmin

import com.google.common.base.Preconditions
import grails.gorm.transactions.Transactional
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.core.env.Environment

import javax.annotation.PostConstruct

@Transactional
class EnvService {
    static Logger logger = LoggerFactory.getLogger(EnvService.class)

    static lazyInit = false

    Environment environment

    private boolean online

    private boolean dev

    private boolean test


    boolean isProd() {
        return online
    }

    boolean isDev() {
        return dev
    }

    boolean isTest() {
        return test
    }

    @PostConstruct
    void init() {
        String[] activeProfiles = environment.getActiveProfiles()
        activeProfiles.each {
            logger.info("-----act:{}", it)
        }

        Preconditions.checkArgument(activeProfiles != null && activeProfiles.length > 0)

        online = "prod".equals(activeProfiles[0])
        if ("production".equalsIgnoreCase(activeProfiles[0]) || "prod".equalsIgnoreCase(activeProfiles[0])) {
            online = true
        }
        if ("dev".equalsIgnoreCase(activeProfiles[0]) || "development".equalsIgnoreCase(activeProfiles[0])) {
            dev = true
        }
        test = "test".equals(activeProfiles[0])
        logger.info("====profile online:{},dev:{},test:{},active:{}".toString(), online, dev, test, activeProfiles[0])
    }

    String getMsgPath() {
        long time = System.currentTimeMillis()
        if (this.online) {
            return """https://smart-campus-mp.yunzhiyuan100.com?t=${time}/#"""
        }
        if (this.dev) {
            return """http://smart-campus-mp.yunzhiyuan100.com.cn?t=${time}/#"""
        }
        if (this.test) {
            return """http://smart-campus-mp-test.yunzhiyuan100.com.cn?t=${time}/#"""
        }
        return msgPath
    }

    String getDingMsgPath() {
        long time = System.currentTimeMillis()
        if (this.online) {
            return """https://smart-ding.yunzhiyuan100.com?t=${time}/#"""
        }

        if (this.dev) {
            return """http://smart-ding.yunzhiyuan100.com.cn?t=${time}/#"""
        }

        if (this.test) {
            return """http://smart-ding.yunzhiyuan100.com.cn?t=${time}/#"""
        }

        return dingMsgPath
    }
}

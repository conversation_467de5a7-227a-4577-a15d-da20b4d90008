package hiiadmin.reactive.observer.user

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.reactive.meta.orgUser.DataStaffDepartmentMeta
import hiiadmin.reactive.meta.orgUser.DataStaffRoleMeta
import hiiadmin.reactive.meta.orgUser.DataTeacherSchoolMeta
import hiiadmin.reactive.meta.orgUser.DataUnitTeacherMeta
import hiiadmin.reactive.meta.orgUser.DataUserEncodeMeta
import hiiadmin.reactive.meta.user.DataStaffMeta
import hiiadmin.reactive.meta.user.DataTeacherMeta
import hiiadmin.reactive.meta.user.DataUserMeta
import hiiadmin.reactive.modle.PdDataDTO
import hiiadmin.reactive.modle.PdDataPlatform
import hiiadmin.reactive.modle.orgUser.PdDataTeacherSchoolDTO
import hiiadmin.reactive.modle.orgUser.PdDataUnitTeacherDTO
import hiiadmin.reactive.modle.orgUser.PdDataUserEncodeInfoDTO
import hiiadmin.reactive.modle.user.PdDataStaffDTO
import hiiadmin.reactive.modle.user.PdDataTeacherDTO
import hiiadmin.reactive.DataMetaObserverService
import hiiadmin.reactive.modle.user.PdDataUserDTO
import hiiadmin.school.SchoolService
import hiiadmin.school.repository.RepositoryCampusService
import hiiadmin.school.repository.RepositoryTeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import io.reactivex.rxjava3.annotations.NonNull
import org.apache.commons.lang3.StringUtils

import javax.annotation.PostConstruct
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.Teacher

@Transactional
class DataTeacherMetaObserverService extends DataMetaObserverService<DataTeacherMeta> {
    
    RepositoryTeacherService repositoryTeacherService

    RepositoryCampusService repositoryCampusService

    SchoolService schoolService

    UserEncodeInfoService userEncodeInfoService
    
    
    @Override
    void update(@NonNull DataTeacherMeta dataTeacherMeta) {
        PdDataTeacherDTO pdDataTeacherDTO = dataTeacherMeta.response
        
        Teacher realTeacher = null
        
        Teacher teacher = repositoryTeacherService.fetchOrCreateTeacherByUnionCode(pdDataTeacherDTO.id, ConstantEnum.SyncType.User_Module_Pd.type)
        teacher.name = pdDataTeacherDTO.name
        teacher.avatar = pdDataTeacherDTO.avatar
        teacher.gender = pdDataTeacherDTO.gender == 1
        teacher.status = 1
        repositoryTeacherService.saveTeacher(teacher)

        School school = schoolService.findOrCreateSchoolByOrgId(pdDataTeacherDTO.organizationId, ConstantEnum.SyncType.User_Module_Pd.type)
        List<Campus> campusList = repositoryCampusService.findCampusListBySchoolId(school.id)

        if (StringUtils.isNotBlank(pdDataTeacherDTO.mobile)) {
            Teacher mobileTeacher = userEncodeInfoService.fetchTeacherByTrueMobile(pdDataTeacherDTO.mobile)
            if (mobileTeacher) {
                
                if (mobileTeacher.id != teacher.id) {
                    teacher.status = 0 as byte
                    repositoryTeacherService.saveTeacher(teacher)
                    mobileTeacher.unionCode = pdDataTeacherDTO.id
                    mobileTeacher.syncType = ConstantEnum.SyncType.User_Module_Pd.type
                    repositoryTeacherService.saveTeacher(mobileTeacher)
                }
                
                realTeacher = mobileTeacher
            } else {
                realTeacher = teacher
            }
        } else {
            realTeacher = teacher
        }
        DataUserEncodeMeta dataUserEncodeMeta = new DataUserEncodeMeta(new PdDataUserEncodeInfoDTO(userId: realTeacher.id, userType: ConstantEnum.UserTypeEnum.TEACHER.type, mobile: pdDataTeacherDTO.mobile, idCard: pdDataTeacherDTO.idCard), true, true)
        dataMetaEventbusService.onNext(dataUserEncodeMeta)


        String dingUserId = ''
        String dingUnionId = ''
        if (pdDataTeacherDTO.platforms != null && pdDataTeacherDTO.platforms.size() > 0) {
            PdDataPlatform ding = pdDataTeacherDTO.platforms.find {
                it.platform == 2
            }
            if (ding) {
                DataUserMeta dataUserMeta = new DataUserMeta(new PdDataUserDTO(), true, true)
                dataUserMeta.teacher = realTeacher
                dataUserMeta.dingUnionId = ding.platformUnionId
                dataUserMeta.dingUserId = ding.platformUserId
                dingUserId = ding.platformUserId
                dingUnionId = ding.platformUnionId
            }
        }
        DataUserMeta dataUserMeta = new DataUserMeta(new PdDataUserDTO(), true, true)
        dataUserMeta.campuses = campusList
        dataUserMeta.teacher = realTeacher
        dataUserMeta.dingUnionId = dingUnionId
        dataUserMeta.dingUserId = dingUserId
        dataMetaEventbusService.onNext(dataUserMeta)
        
        DataStaffMeta dataStaffMeta = new DataStaffMeta(new PdDataStaffDTO(), true, true)
        dataStaffMeta.campusList = campusList
        dataStaffMeta.teacher = realTeacher
        dataStaffMeta.roleIds = pdDataTeacherDTO.roleIds
        dataStaffMeta.mobile = pdDataTeacherDTO.mobile
        dataMetaEventbusService.onNext(dataStaffMeta)
        
        DataTeacherSchoolMeta dataTeacherSchoolMeta = new DataTeacherSchoolMeta(new PdDataTeacherSchoolDTO(), true, true)
        dataTeacherSchoolMeta.teacher = realTeacher
        dataTeacherSchoolMeta.campusList = campusList
        dataTeacherSchoolMeta.jobNum = pdDataTeacherDTO.code
        dataMetaEventbusService.onNext(dataTeacherSchoolMeta)
        
        
        DataStaffDepartmentMeta dataStaffDepartmentMeta = new DataStaffDepartmentMeta(new PdDataDTO(), true, true)
        dataStaffDepartmentMeta.campusList = campusList
        dataStaffDepartmentMeta.teacher = realTeacher
        dataStaffDepartmentMeta.departments = pdDataTeacherDTO.departments
        dataMetaEventbusService.onNext(dataStaffDepartmentMeta)

        DataUnitTeacherMeta dataUnitTeacherMeta = new DataUnitTeacherMeta(new PdDataUnitTeacherDTO(), true, true)
        dataUnitTeacherMeta.teacher = realTeacher
        dataUnitTeacherMeta.campusList = campusList
        dataUnitTeacherMeta.units = pdDataTeacherDTO.units
        dataMetaEventbusService.onNext(dataUnitTeacherMeta)
    }

    @Override
    void delete(@NonNull DataTeacherMeta dataTeacherMeta) {
        PdDataTeacherDTO pdDataTeacherDTO = dataTeacherMeta.response
        Teacher teacher = repositoryTeacherService.fetchOrCreateTeacherByUnionCode(pdDataTeacherDTO.id, ConstantEnum.SyncType.User_Module_Pd.type)
        repositoryTeacherService.saveTeacher(teacher)
        
        School school = schoolService.findOrCreateSchoolByOrgId(pdDataTeacherDTO.organizationId, ConstantEnum.SyncType.User_Module_Pd.type)
        List<Campus> campusList = repositoryCampusService.findCampusListBySchoolId(school?.id)
        
        if (campusList) {
            DataTeacherSchoolMeta dataTeacherSchoolMeta = new DataTeacherSchoolMeta(new PdDataTeacherSchoolDTO(), true, false)
            dataTeacherSchoolMeta.teacher = teacher
            dataTeacherSchoolMeta.campusList = campusList
            dataMetaEventbusService.onNext(dataTeacherSchoolMeta)

            DataUnitTeacherMeta dataUnitTeacherMeta = new DataUnitTeacherMeta(new PdDataUnitTeacherDTO(), true, false)
            dataUnitTeacherMeta.teacher = teacher
            dataUnitTeacherMeta.campusList = campusList
            dataMetaEventbusService.onNext(dataUnitTeacherMeta)

            DataStaffMeta dataStaffMeta = new DataStaffMeta(new PdDataStaffDTO(), true, false)
            dataStaffMeta.campusList = campusList
            dataStaffMeta.teacher = teacher

            DataUserMeta dataUserMeta = new DataUserMeta(new PdDataUserDTO(), true, false)
            dataUserMeta.teacher = teacher
            dataUserMeta.campuses = campusList
        }
    }
    
    @PostConstruct
    void init() {
        dataMetaEventbusService.subscribe(DataTeacherMeta.class, this)
    }
}

package hiiadmin.reactive.observer.user

import com.google.common.collect.HashBasedTable
import com.google.common.collect.Table
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.UserWxDingService
import hiiadmin.reactive.DataMetaObserverService
import hiiadmin.reactive.meta.user.DataUserMeta
import hiiadmin.school.repository.RepositoryUserService
import io.reactivex.rxjava3.annotations.NonNull
import timetabling.org.Campus
import timetabling.user.Teacher
import timetabling.user.User
import timetabling.user.UserWxDing

import javax.annotation.PostConstruct

@Transactional
class DataUserMetaObserverService extends DataMetaObserverService<DataUserMeta> {
    
    RepositoryUserService repositoryUserService

    UserWxDingService userWxDingService
    
    @Override
    void update(@NonNull DataUserMeta dataUserMeta) {
        Teacher teacher = dataUserMeta.teacher
        List<Campus> campusList = dataUserMeta.campuses
        String dingUserId = dataUserMeta.dingUserId
        String dingUnionId = dataUserMeta.dingUnionId

        User user = repositoryUserService.fetchOrCreateByUid(teacher.id, ConstantEnum.UserTypeEnum.TEACHER.type)
        user.status = 1 as byte
        repositoryUserService.save(user)
        
        
        List<UserWxDing> userWxDingList = userWxDingService.fetchAllByUserIdAndCampusIdList(user.id, ConstantEnum.UserTypeEnum.TEACHER.type, campusList?.collect {it.id})
        Table<Long, Long, UserWxDing> userWxDingTable = HashBasedTable.create()
        userWxDingList.each {
            UserWxDing userWxDing ->
                userWxDingTable.put(userWxDing.userId, userWxDing.campusId, userWxDing)
        }
        campusList.each {
            Campus campus ->
                UserWxDing userWxDing = userWxDingService.fetchOrCreateByUserIdAndCampusId(user.id, ConstantEnum.UserTypeEnum.TEACHER.type, campus.id)
                userWxDing.status = 1 as byte
                userWxDing.dingUserIdStr = dingUserId
                userWxDing.dingUnionId = dingUnionId
                userWxDing.dingCorpId = campus.corpId
                userWxDingService.saveUserWxDing(userWxDing)
                
                userWxDingTable.remove(user.id, campus.id)
        }
        
        List<UserWxDing> needDeleteUserWxDingList = userWxDingTable.values().toList()
        if (needDeleteUserWxDingList) {
            needDeleteUserWxDingList*.status = 0 as byte
            userWxDingService.saveList(needDeleteUserWxDingList)
        }
    }

    @Override
    void delete(@NonNull DataUserMeta dataUserMeta) {
        Teacher teacher = dataUserMeta.teacher
        List<Campus> campusList = dataUserMeta.campuses
        if (teacher) {
            User user = repositoryUserService.fetchByUid(teacher.id, ConstantEnum.UserTypeEnum.TEACHER.type)
            if (user) {
                List<UserWxDing> userWxDingList = userWxDingService.fetchAllByUserIdAndCampusIdList(user.id, ConstantEnum.UserTypeEnum.TEACHER.type, campusList?.collect {it.id})
                if (userWxDingList) {
                    userWxDingList*.status = 0 as byte
                    userWxDingService.saveList(userWxDingList)
                }
            }
        }
    }

    @PostConstruct
    void init() {
        dataMetaEventbusService.subscribe(DataUserMeta.class, this)
    }
}

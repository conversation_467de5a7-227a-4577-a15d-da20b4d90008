package hiiadmin.reactive.observer.user

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.reactive.meta.orgUser.DataStaffRoleMeta
import hiiadmin.reactive.meta.user.DataRoleMeta
import hiiadmin.reactive.modle.PdDataDTO
import hiiadmin.reactive.modle.user.PdDataRoleDTO
import hiiadmin.reactive.DataMetaObserverService
import hiiadmin.school.SchoolService
import hiiadmin.school.repository.RepositoryBgbCampusRoleService
import hiiadmin.school.repository.RepositoryCampusService
import io.reactivex.rxjava3.annotations.NonNull
import javax.annotation.PostConstruct
import timetabling.newMenu.BgbCampusRole
import timetabling.org.Campus
import timetabling.org.School

@Transactional
class DataRoleMetaObserverService extends DataMetaObserverService<DataRoleMeta> {
    
    SchoolService schoolService

    RepositoryCampusService repositoryCampusService

    RepositoryBgbCampusRoleService repositoryBgbCampusRoleService
    
    @Override
    void update(@NonNull DataRoleMeta dataRoleMeta) {
        PdDataRoleDTO response = dataRoleMeta.response
        School school = schoolService.findOrCreateSchoolByOrgId(response.organizationId, ConstantEnum.SyncType.User_Module_Pd.type)
        List<Campus> campusList = repositoryCampusService.findCampusListBySchoolId(school.id)
        campusList.each {
            Campus campus ->
                BgbCampusRole bgbCampusRole = repositoryBgbCampusRoleService.fetchOrCreateByUnionCode(campus.id, response.id, ConstantEnum.SyncType.User_Module_Pd.type)
                if (bgbCampusRole.status == null || bgbCampusRole.status == 0) {
                    bgbCampusRole.status = 1
                }
                bgbCampusRole.name = response.name
                repositoryBgbCampusRoleService.save(bgbCampusRole)
        }
    }

    @Override
    void delete(@NonNull DataRoleMeta dataRoleMeta) {
        PdDataRoleDTO response = dataRoleMeta.response
        List<BgbCampusRole> bgbCampusRoleList = repositoryBgbCampusRoleService.fetchAllByUnionCode(response.id, ConstantEnum.SyncType.User_Module_Pd.type)
        
        if (bgbCampusRoleList) {
            bgbCampusRoleList*.status = 0
            repositoryBgbCampusRoleService.saveAll(bgbCampusRoleList)
            DataStaffRoleMeta dataStaffRoleMeta = new DataStaffRoleMeta(new PdDataDTO(), true, false)
            dataStaffRoleMeta.bgbCampusRoleList = bgbCampusRoleList
            dataMetaEventbusService.onNext(dataStaffRoleMeta)
        }
    }

    @PostConstruct
    void init() {
        dataMetaEventbusService.subscribe(DataRoleMeta.class, this)
    }
}

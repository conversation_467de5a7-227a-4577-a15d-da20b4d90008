package hiiadmin.reactive.observer.orgUser

import com.google.common.collect.HashBasedTable
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.UserService
import hiiadmin.UserWxDingService
import hiiadmin.reactive.meta.orgUser.DataParentStudentMeta
import hiiadmin.reactive.modle.PdDataPlatform
import hiiadmin.reactive.modle.orgUser.PdDataParentStudentDTO
import hiiadmin.reactive.DataMetaObserverService
import hiiadmin.school.repository.RepositoryParentService
import hiiadmin.school.repository.RepositoryUserService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import io.reactivex.rxjava3.annotations.NonNull
import org.apache.commons.lang3.StringUtils
import timetabling.user.User
import timetabling.user.UserWxDing

import javax.annotation.PostConstruct
import timetabling.ParentStudent
import timetabling.user.Parent
import timetabling.user.Student

@Transactional
@Slf4j
class DataParentStudentMetaObserverService extends DataMetaObserverService<DataParentStudentMeta> {

    RepositoryParentService repositoryParentService
    
    ParentStudentService parentStudentService

    UserEncodeInfoService userEncodeInfoService

    RepositoryUserService repositoryUserService

    UserWxDingService userWxDingService

    @Override
    void update(@NonNull DataParentStudentMeta dataParentStudentMeta) {
        
        Student student = dataParentStudentMeta.student
        Long campusId = dataParentStudentMeta.campusId

        List<ParentStudent> parentStudentList = parentStudentService.fetchAllParentStudentByStudentId(student.id)
        HashBasedTable<Long, Long, ParentStudent> parentStudentTable = HashBasedTable.create()
        parentStudentList.each {
            parentStudentTable.put(it.parentId, it.studentId, it)
        }

        List<Long> parentIdList = parentStudentList.collect { it.parentId }
        Map<String, Parent> parentMap = [:]
        if (parentIdList) {
            List<Parent> parentList = repositoryParentService.fetchAllParentByIdList(parentIdList)
            parentMap = parentList.collectEntries { [it.unionCode, it] }
        }
        
        dataParentStudentMeta?.parentStudentDTOList?.each {
            PdDataParentStudentDTO parentStudentDTO ->
                
                Parent realParent = null
                
                Parent parent = parentMap.get(parentStudentDTO.parentId)
                if (!parent) {
                    parent = repositoryParentService.fetchOrCreateByUnionCode(parentStudentDTO.parentId, ConstantEnum.SyncType.User_Module_Pd.type)
                    parent.name = student.name + parentStudentDTO.relationName
                    parent.status = 1 as byte
                    repositoryParentService.save(parent)
                }
                
                if (StringUtils.isNotBlank(parentStudentDTO.mobile)) {
                    Parent mobileParent = userEncodeInfoService.fetchParentByTrueMobile(parentStudentDTO.mobile)
                    if (mobileParent) {
                        if (mobileParent.id != parent.id) {
                            parent.status = 0 as byte
                            repositoryParentService.save(parent)
                        }

                        mobileParent.unionCode = parentStudentDTO.parentId
                        mobileParent.syncType = ConstantEnum.SyncType.User_Module_Pd.type
                        mobileParent.name = student.name + parentStudentDTO.relationName
                        repositoryParentService.save(mobileParent)
                        
                        realParent = mobileParent
                    } else {
                        realParent = parent
                        userEncodeInfoService.saveUserEncodeInfo(parentStudentDTO.mobile, ConstantEnum.UserTypeEnum.PARENT.type, parent.id, ConstantEnum.UserEncodeInfoType.MOBILE.type)
                    }
                } else {
                    realParent = parent
                }
                
                if (parentStudentDTO.platforms != null && parentStudentDTO.platforms.size() > 0) {
                    PdDataPlatform ding = parentStudentDTO.platforms.find {
                        it.platform == 2
                    }
                    
                    if (ding) {
                        User parentUser = repositoryUserService.fetchOrCreateByUid(realParent.id, ConstantEnum.UserTypeEnum.PARENT.type)
                        parentUser.status = 1 as byte
                        repositoryUserService.save(parentUser)

                        UserWxDing userWxDing = userWxDingService.fetchOrCreateByUserIdAndCampusId(parentUser.id, ConstantEnum.UserTypeEnum.PARENT.type, campusId)
                        userWxDing.status = 1
                        userWxDing.dingUserIdStr = ding.platformUserId
                        userWxDing.dingUnionId = ding.platformUnionId
                        userWxDingService.saveUserWxDing(userWxDing)
                    }
                }

                ParentStudent parentStudent = parentStudentTable.get(realParent.id, student.id)
                Byte appellation = ConstantEnum.DingRelationCode.getEnumByCode(parentStudentDTO.relationCode)?.type
                if (!parentStudent) {
                    parentStudent = new ParentStudent(
                            parentId: realParent.id,
                            studentId: student.id,
                            studentName: student.name,
                            parentName: realParent.name,
                            parentType: 1,
                            status: 1 as byte,
                            appellation: appellation
                    )
                } else {
                    parentStudent.parentName = realParent.name
                    parentStudent.appellation = appellation
                    parentStudent.status = 1 as byte
                }
                
                if (appellation == null) {
                    parentStudent.memo = parentStudentDTO.relationName
                }
                
                parentStudentService.saveParentStudent(parentStudent)
                parentStudentTable.remove(parent.id, student.id)
        }
        
        
        parentStudentTable.values().toList().each {
            it.status = 0 as byte
            parentStudentService.saveParentStudent(it)
        }
    }

    @Override
    void delete(@NonNull DataParentStudentMeta dataParentStudentMeta) {
        Student student = dataParentStudentMeta.student
        List<ParentStudent> parentStudentList = parentStudentService.fetchAllParentStudentByStudentId(student?.id)
        if (parentStudentList) {
            parentStudentList*.status = 0 as byte
            parentStudentService.saveParentStudentList(parentStudentList)
        }
    }
    
    @PostConstruct
    void init() {
        dataMetaEventbusService.subscribe(DataParentStudentMeta.class, this)
    }
}

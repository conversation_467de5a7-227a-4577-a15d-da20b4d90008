package hiiadmin.reactive.observer.orgUser

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.reactive.DataMetaObserverService
import hiiadmin.reactive.meta.orgUser.DataUserEncodeMeta
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import io.reactivex.rxjava3.annotations.NonNull

import javax.annotation.PostConstruct

@Transactional
class DataUserEncodeMetaObserverService extends DataMetaObserverService<DataUserEncodeMeta> {

    UserEncodeInfoService userEncodeInfoService


    @Override
    void update(@NonNull DataUserEncodeMeta dataUserEncodeMeta) {
        Long userId = dataUserEncodeMeta.response.userId
        Byte userType = dataUserEncodeMeta.response.userType
        String mobile = dataUserEncodeMeta.response.mobile
        String idCard = dataUserEncodeMeta.response.idCard

        if (userId) {
            userEncodeInfoService.saveUserEncodeInfo(mobile, userType, userId, ConstantEnum.UserEncodeInfoType.MOBILE.type)
            userEncodeInfoService.saveUserEncodeInfo(idCard, userType, userId, ConstantEnum.UserEncodeInfoType.ID_CARD.type)
        }
    }

    @Override
    void delete(@NonNull DataUserEncodeMeta dataUserEncodeMeta) {
        Long userId = dataUserEncodeMeta.response.userId
        Byte userType = dataUserEncodeMeta.response.userType
        userEncodeInfoService.deleteUserEncodeInfoByUserTypeAndUserId(userType, userId)
    }

    @PostConstruct
    void init() {
        dataMetaEventbusService.subscribe(DataUserEncodeMeta.class, this)
    }
}

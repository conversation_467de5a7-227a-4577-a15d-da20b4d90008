package hiiadmin.syn

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.bugu.DockingFirmUtil
import com.bugu.MobileUtil
import com.bugu.ResultVO
import com.bugu.ServiceResult
import com.google.common.collect.HashBasedTable
import com.google.common.collect.Lists
import com.google.common.collect.Table
import grails.async.Promise
import grails.async.Promises
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.CacheService
import hiiadmin.UserInfoChangeEnum
import hiiadmin.apiCloud.ALiYunApiInvoker
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.apiCloud.AntennaDhYrApi
import hiiadmin.apiCloud.EdgeALiYunApiInvoker
import hiiadmin.docking.*
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.ferries.FerriesService
import hiiadmin.gated.GatedIssueService
import hiiadmin.module.docking.DockJsonParseAliYunVO
import hiiadmin.oss.OssUtils
import hiiadmin.planTemplate.PlanTemplatePersonService
import hiiadmin.school.CampusService
import hiiadmin.school.CardUserService
import hiiadmin.school.GradeService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.IdUtils
import hiiadmin.visitor.VisitorService
import org.apache.commons.lang3.StringUtils
import org.springframework.transaction.annotation.Propagation
import timetabling.ParentStudent
import timetabling.docking.*
import timetabling.gated.Ferries
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.UnitStudent
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus
import timetabling.visitor.Visitor

import javax.annotation.Resource
import java.util.concurrent.TimeUnit

import static com.bugu.DockingFirmUtil.compareFieldBit
import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class SynUserV2Service {

    DockingPlatformCampusService dockingPlatformCampusService

    @Resource
    ALiYunApiInvoker aLiYunApiInvoker

    @Resource
    EdgeALiYunApiInvoker edgeALiYunApiInvoker

    @Lazy
    RelatedUserService relatedUserService

    @Lazy
    DockingFirmPersonConfigService dockingFirmPersonConfigService

    @Lazy
    GradeService gradeService

    @Resource
    AntennaApi antennaApi

    @Resource
    AntennaDhYrApi antennaDhYrApi

    @Lazy
    CardUserService cardUserService

    @Lazy
    FerriesService ferriesService

    @Lazy
    ParentService parentService

    VisitorService visitorService

    @Lazy
    DockingFirmPersonOrgService dockingFirmPersonOrgService

    @Lazy
    DockingFirmSyncTaskService dockingFirmSyncTaskService
    
    PlanTemplatePersonService planTemplatePersonService

    GatedIssueService gatedIssueService

    DockingIssueService dockingIssueService

    CacheService cacheService

    UserEncodeInfoService userEncodeInfoService

    CampusService campusService

    void syncRetrySync2FirmByTaskId(Long taskId, Long userId, Byte userType) {
        DockingFirmSyncTask synTask = dockingFirmSyncTaskService.fetchDockingFirmSyncTaskById(taskId)
        if (synTask.hasRetry) {
            throw new HiiAdminException("该任务已经重新同步")
        }
        Promise task = Promises.task {
            retrySync2FirmByTaskId(taskId, userId, userType)
        }
        task.onError { Throwable throwable ->
            log.warn("sync retry user 2 firm fail taskId${taskId}, userType@${userType}".toString(), throwable)
        }
        task.onComplete {
            log.info("sync retry user 2 firm success taskId${taskId}, userType@${userType}".toString())
        }
    }

    void retrySync2FirmByTaskId(Long taskId, Long userId, Byte userType) {
        DockingFirmSyncTask task = dockingFirmSyncTaskService.fetchDockingFirmSyncTaskById(taskId)
        if (task.hasRetry) {
            throw new HiiAdminException("该任务已经重新同步")
        }
        task.hasRetry = true
        dockingFirmSyncTaskService.saveDockingFirmSyncTask(task)
        List<DockingFirmSyncTaskInfo> dockingFirmSyncTaskInfoList = dockingFirmSyncTaskService.fetchAllDockingFirmSyncTaskInfoByTaskIdAndOperatingStatus(taskId, 0 as byte)
        List<Long> syncUserIdList = dockingFirmSyncTaskInfoList*.userId
        Byte syncUserType = dockingFirmSyncTaskInfoList[0].userType
        if (task.operationPath == 1) {
            String operationContent = task.operationContent
            if (!task.operationContent.startsWith("重新删除")) {
                operationContent = "重新删除-${task.operationContent}"
            }
            syncDeleteUser2Firm(task.campusId, syncUserIdList, syncUserType, userId, userType, task.operationType, [content: operationContent])
        } else if (task.operationPath == 2) {
            String operationContent = task.operationContent
            if (!task.operationContent.startsWith("重新同步")) {
                operationContent = "重新同步-${task.operationContent}"
            }
            syncUpdateOrCreateUser2Firm(task.campusId, syncUserIdList, syncUserType, userId, userType, task.operationType, operationContent, task.firm)
        }
    }

    void syncDefaultSync4UpdateOrCreateUserToFirm(Long campusId, Integer firm, Long operationUserId, Byte operationUserType, String option = "批量同步人员") {
        Promise task = Promises.task {
            defaultSync4UpdateOrCreateUserToFirm(campusId, firm, operationUserId, operationUserType, option)
        }
        task.onError { Throwable throwable ->
            log.warn("sync default sync user 2 firm fail campusId${campusId}, firm@${firm}".toString(), throwable)
        }
        task.onComplete {
            log.info("sync default sync user 2 firm success campusId${campusId}, firm@${firm}".toString())
        }
    }

    void defaultSync4UpdateOrCreateUserToFirm(Long campusId, Integer firm, Long operationUserId, Byte operationUserType, String option = "批量同步人员") {
        Campus campus = Campus.get(campusId)
        List<DockingFirmPersonConfig> configList = dockingFirmPersonConfigService.fetchAllDockingFirmPersonConfigByCampusIdAndFirm(campusId, firm)
        configList.each { config ->
            if (config.needSync) {
                List<DockingFirmPersonOrg> dockingFirmPersonOrgList = dockingFirmPersonOrgService.fetchAllDockingFirmPersonOrgByCampusIdAndFirmAndUserType(campusId, config.firm, config.userType)
                dockingFirmPersonOrgList.each { org ->
                    log.info("[defaultSync4UpdateOrCreateUserToFirm]orgId@${org.id},name@${org.orgName},userType@${org.userType},firm@${org.firm}".toString())
                    if (!org.dockingFirmId) {
                        return
                    }
                    switch (org.userType) {
                        case UserTypeEnum.STUDENT.type:
                            option = "批量同步人员至分组-${org.orgName}"
                            List<Long> studentIdList =[]
                            if(campus.type ==1){
                                Grade grade = gradeService.fetchGradeByCampusIdSectionIdAndSchoolYear(campusId, org.sectionId, org.schoolYear)
                                if (grade) {
                                    studentIdList = fetchAllStudentIdByGradeId(grade?.id)
                                }
                            }else{
                                studentIdList = fetchAllStudentIdByCampusId(campusId)
                            }
                            if (studentIdList && studentIdList.size() > 0) {
                                studentIdList.unique()
                                syncUpdateOrCreateUser2Firm(campusId, studentIdList, org.userType, operationUserId, operationUserType, DockingFirmSyncTask.OPERATION_TYPE_MANY, option, firm)
                            }
                            break
                        case UserTypeEnum.PARENT.type:
                            option = "批量同步人员至分组-${org.orgName}"
                            List<Long> parentIdList = parentService.fetchAllByCampusId(campusId)*.id
                            if (parentIdList && parentIdList.size() > 0) {
                                syncUpdateOrCreateUser2Firm(campusId, parentIdList, org.userType, operationUserId, operationUserType, DockingFirmSyncTask.OPERATION_TYPE_MANY, option, firm)
                            }
                            break
                        case UserTypeEnum.TEACHER.type:
                            List<TeacherSchoolCampus> schoolCampusList = fetchAllTeacherSchoolByCampusId(campusId)
                            option = "批量同步人员至分组-${org.orgName}"
                            List<Long> teacherIdList = schoolCampusList*.teacherId.toSet().toList()
                            if (teacherIdList && teacherIdList.size() > 0) {
                                syncUpdateOrCreateUser2Firm(campusId, teacherIdList, org.userType, operationUserId, operationUserType, DockingFirmSyncTask.OPERATION_TYPE_MANY, option, firm)
                            }
                            break
                        case UserTypeEnum.FERRIES.type:
                            option = "批量同步人员至分组-${org.orgName}"
                            List<Ferries> ferriesList = ferriesService.fetchAllFerriesLimit(campusId, null, null, null, null, 99)
                            if (ferriesList && ferriesList.size() > 0) {
                                syncUpdateOrCreateUser2Firm(campusId, ferriesList*.id, org.userType, operationUserId, operationUserType, DockingFirmSyncTask.OPERATION_TYPE_MANY, option, firm)
                            }
                            break
                        case UserTypeEnum.VISITORS.type:
                            option = "批量同步人员至分组-${org.orgName}"
                            List<Visitor> visitors = visitorService.fetchAllVisitorByCampusIdAndDay(campusId, System.currentTimeMillis())
                            if (visitors && visitors.size() > 0) {
                                syncUpdateOrCreateUser2Firm(campusId, visitors*.id, org.userType, operationUserId, operationUserType, DockingFirmSyncTask.OPERATION_TYPE_MANY, option, firm)
                            }
                            break
                    }
                }
            }
        }
    }

    void syncDeleteUser2Firm(long campusId, List<Long> syncUserIdList, byte syncUserType, Long operationUserId, Byte operationUserType, Byte promoterType, Map option = [content: "删除人员"]) {
        Promise task = Promises.task {
            deleteUser2Firm(campusId, syncUserIdList, syncUserType, operationUserId, operationUserType, promoterType, option)
        }
        task.onError { Throwable throwable ->
            log.warn("sync del user 2 firm fail userType@${syncUserType}".toString(), throwable)
        }
        task.onComplete {
            log.info("sync del user 2 firm success userIdList${JSON.toJSONString(syncUserIdList)}, userType@${syncUserType}".toString())
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void deleteUser2Firm(long campusId, List<Long> syncUserIdList, byte syncUserType, Long operationUserId, Byte operationUserType, Byte promoterType, Map option = [content: "删除人员"]) {
        String operationContent = option.content
        List<DockingFirmPersonConfig> personConfigList = dockingFirmPersonConfigService.fetchAllNeedSyncDockingFirmPersonConfigByCampusIdAndUserType(campusId, syncUserType)

        personConfigList?.each { personConfig ->
            DockingFirmSyncTask task = dockingFirmSyncTaskService.saveDockingFirmSyncTask(
                    personConfig.schoolId, personConfig.campusId, operationUserId, operationUserType, promoterType, personConfig.firm, operationContent, 1)
            task.operatingStatus = 2
            task = dockingFirmSyncTaskService.saveDockingFirmSyncTask(task)
            Boolean successStatus = false
            Boolean failureStatus = false
            Boolean operatingStatus = false
            switch (personConfig.firm) {
                case DeviceFirm.HK_EDU.firm:
                    syncUserIdList.each { Long syncUserId ->
                        ServiceResult serviceResult = deletePerson4HKEDU(campusId, syncUserId, syncUserType)
                        successStatus ?: (successStatus = serviceResult.success)
                        failureStatus ?: (failureStatus = !serviceResult.success)
                        Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
                        saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
                    }
                    break
                case DeviceFirm.HK_YM.firm:
                    throw new HiiAdminException("平台参数异常，或平台不支持，firm@${personConfig.firm}")
                    break
                case DeviceFirm.DH_DSS.firm:
                    Long studentId = option.studentId as Long
                    syncUserIdList.each { Long syncUserId ->
                        ServiceResult serviceResult = deletePerson4DH(campusId, syncUserId, syncUserType, studentId)
                        successStatus ?: (successStatus = serviceResult.success)
                        failureStatus ?: (failureStatus = !serviceResult.success)
                        Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
                        saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
                    }
                    break
                case DeviceFirm.DH_YR.firm:
                    syncUserIdList.each { Long syncUserId ->
                        ServiceResult serviceResult = deletePerson4DHYR(campusId, syncUserId, syncUserType)
                        successStatus ?: (successStatus = serviceResult.success)
                        failureStatus ?: (failureStatus = !serviceResult.success)
                        Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
                        saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
                    }
                    break
                case DeviceFirm.FS_CAR.firm:
//                    SchoolSynFirm schoolSynFirm = schoolSynFirmService.fetchSchoolSynFirmByCampusIdAndFirm(campusId, personConfig.firm)
//                    syncUserIdList.each { Long syncUserId ->
//                        Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
//                        ServiceResult serviceResult = synDeleteUserToFsCar(syncUserId, syncUserType, paramMap.licensePlate as String, schoolSynFirm)
//                        successStatus ?: (successStatus = serviceResult.success)
//                        failureStatus ?: (failureStatus = !serviceResult.success)
//                        saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
//                    }
                    break
                case DeviceFirm.ALY_DEVICE.firm:
                    try {
                        DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, personConfig.firm)
                        syncUserIdList.each { Long syncUserId ->
                            ServiceResult serviceResult = deleteUserFaceToAliV3(campusId, syncUserId, syncUserType, platform.addressLink)
                            successStatus ?: (successStatus = serviceResult.success)
                            failureStatus ?: (failureStatus = !serviceResult.success)
                            Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
                            saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
                        }
                    } catch (Exception e) {
                        log.info("用户删除失败,msg@${e.message}")
                    }
                    break
                case DeviceFirm.ALY_EDGE_DEVICE.firm:
                    try {
                        syncUserIdList.each { Long syncUserId ->
                            ServiceResult serviceResult = deleteUserFaceToYunV3(campusId, syncUserId, syncUserType)
                            successStatus ?: (successStatus = serviceResult.success)
                            failureStatus ?: (failureStatus = !serviceResult.success)
                            Map paramMap = getUserInfoMapByUserType(syncUserId, syncUserType, campusId)
                            saveDockingFirmSyncTask(serviceResult, paramMap, campusId, task.id, personConfig.firm)
                        }
                    } catch (Exception e) {
                        log.info("用户删除失败,msg@${e.message}")
                    }
                    break
                default:
                    throw new HiiAdminException("平台参数异常，或平台不支持，firm@${personConfig.firm}")
            }

            operatingStatus = successStatus && failureStatus
            task.operatingStatus = operatingStatus ? -1 : successStatus ? 1 : 0
            task.operationCompletionTime = new Date()
            dockingFirmSyncTaskService.saveDockingFirmSyncTask(task)
        }
    }


    /**
     *
     * @param userId
     * @param userType
     * @param campusId
     * @return [userId:userId,userType:userType,name:user.name,code:user.code,mobile:user.mobile]
     */
    Map getUserInfoMapByUserType(long userId, long userType, long campusId) {
        Map map = [:]
        map.put("userId", userId)
        map.put("userType", userType)
        switch (userType) {
            case UserTypeEnum.STUDENT.type:
                Student student = fetchStudentById(userId)
                map.put("name", student?.name)
                map.put("code", student?.code)
                map.put("mobile", userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.STUDENT.type, student.id))
                break
//            TODO 逻辑有问题
            case UserTypeEnum.TEACHER.type:
                Teacher teacher = fetchTeacherById(userId)
                TeacherSchoolCampus teacherSchool = fetchTeacherSchoolCampusByTeacherIdAndCampusId(campusId, teacher.id)
                map.put("name", teacher?.name)
                map.put("code", teacherSchool?.jobNum)
                map.put("mobile", userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id))
                break
            case UserTypeEnum.PARENT.type:
                Parent parent = parentService.fetchParentById(userId)
                map.put("name", parent?.name)
                map.put("mobile", userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parent.id))
                break
            case UserTypeEnum.FERRIES.type:
                Ferries ferries = ferriesService.fetchFerriesByFerriesId(userId)
                map.put("name", ferries?.name)
                map.put("mobile", userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id))
                map.put("licensePlate", ferries?.licensePlate)
                break
            case UserTypeEnum.VISITORS.type:
                Visitor visitor = visitorService.fetchVisitorById(userId)
                map.put("name", visitor?.visitorName)
                map.put("mobile", userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitor.id))
                break
        }
        return map
    }

    /**
     * 处理人员同步需要的基础数据
     * @param campusId
     * @param syncUserIdList
     * @param syncUserType
     * @return
     */
    Table<Long, String, Object> transformSyncUserInfoTable(long campusId, List<Long> syncUserIdList, byte syncUserType) {
        Table<Long, String, Object> table = HashBasedTable.create()

        if (!syncUserIdList) {
            throw new HiiAdminException("请选择需要同步的人员")
        }
        switch (syncUserType) {
            case UserTypeEnum.STUDENT.type:
                List<Student> studentList = fetchAllStudentByStudentIdInList(syncUserIdList)
                Map<Long, Grade> studentIdGradeMap = gradeService.getStudentIdGradeMapByStudentIdList(syncUserIdList)
                Map<Long, String> studentIdCardNum = cardUserService.fetchAllCardUserByUserIdListAndType(syncUserIdList, syncUserType)
                Map<Long, String> studentIdIdCardMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.ID_CARD.type, UserTypeEnum.STUDENT.type, studentList*.id, false)
                studentList.each { student ->
//                    table.put(student.id, "exist", student.status == 1 as byte)
                    table.put(student.id, "name", student?.name)
                    if (student?.gender != null) {
                        table.put(student.id, "gender", student?.gender)
                    } else {
                        table.put(student.id, "gender", true)
                    }
                    table.put(student.id, "code", student?.code)
//                    table.put(student.id, "mobile", "")
                    if (student?.pic) {
                        String url
                        if (student.pic.startsWith("http")) {
                            url = student.pic
                        } else {
                            url = OssUtils.transformFilePath(student.pic + "!face", 12000)
                        }
                        table.put(student.id, "avatarUrl", url)
                    }
                    if (studentIdIdCardMap.get(student.id)) {
                        table.put(student.id, "idCard", studentIdIdCardMap.get(student.id))
                    }
                    if (studentIdCardNum.get(student.id)) {
                        table.put(student.id, "cardNum", studentIdCardNum.get(student.id))
                    }
//                    table.put(student.id, "numberPlate", "")
                    table.put(student.id, "gradeId", studentIdGradeMap.get(student.id).id)
                }
                break
            case UserTypeEnum.PARENT.type:
                List<Parent> parentList = parentService.fetchAllParentByIds(syncUserIdList)
                List<ParentStudent> parentStudentList = parentService.fetchAllParentStudentByParentIdInListAndCampusId(syncUserIdList, campusId)
                List<Long> studentIdList = parentStudentList*.studentId
                
                Map<Long, String> parentIdNameMap = [:]
                if (studentIdList) {
                    List<Student> studentList = fetchAllStudentByStudentIdInList(studentIdList)
                    Map<Long, Student> studentMap = studentList.collectEntries{[it.id, it]}
                    
                    parentStudentList.each {
                        Student student = studentMap.get(it.studentId)
                        String application = AppellationType.getEnumByType(it.appellation)?.name ?: ''
                        if (it.appellation == 0 as byte) {
                            application = it.memo
                        }
                        
                        parentIdNameMap.put(it.parentId, "${student?.name}${application}".toString())
                    }
                }
                Map<Long, String> parentIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parentList*.id, false)
                parentList.each { Parent parent ->
                    if (parent?.name){
//                    table.put(parent.id, "exist", parent.status == 1 as byte)
                        table.put(parent.id, "name", parent?.name)
                    } else {
                        String name = parentIdNameMap.get(parent.id)
                        if (name) {
                            table.put(parent.id, 'name', name)
                        }
                    }

                    if (parent.gender != null) {
                        table.put(parent.id, "gender", parent?.gender)
                    } else {
                        table.put(parent.id, "gender", true)
                    }
                    if (parentIdMobileMap.get(parent.id)) {
                        String mobile = parentIdMobileMap.get(parent.id)
                        table.put(parent.id, "mobile", mobile)
                        table.put(parent.id, "code", "P${mobile}".toString())
                    }
                    if (parent?.avatar) {
                        String url = parent.avatar+ "!face"
                        if (!url.startsWith("http")) {
                            url = OssUtils.transformFilePath(url, 1200)
                        }
                        table.put(parent.id, "avatarUrl", url)
                    }
                }
                break
            case UserTypeEnum.TEACHER.type:
                List<Teacher> teacherList = fetchAllTeacherByIdInList(syncUserIdList)
                Map<Long, String> teacherIdCardNum = cardUserService.fetchAllCardUserByUserIdListAndType(syncUserIdList, syncUserType)
                Map<Long, String> teacherIdJobNum = getTeacherNumByTeacherId(syncUserIdList, campusId)
                Map<Long, String> teacherIdIdCardMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.ID_CARD.type, UserTypeEnum.TEACHER.type, teacherList*.id, false)
                Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacherList*.id, false)
                teacherList.each { teacher ->
//                    table.put(teacher.id, "exist", (teacher.status == 1 && teacherIdJobNum[teacher.id] != null))
                    table.put(teacher.id, "name", teacher?.name)
                    if (teacher?.gender != null) {
                        table.put(teacher.id, "gender", teacher?.gender)
                    } else {
                        table.put(teacher.id, "gender", true)
                    }
                    if (teacherIdJobNum.get(teacher.id)) {
                        table.put(teacher.id, "code", teacherIdJobNum.get(teacher.id))
                    }
                    if (teacherIdMobileMap.get(teacher.id)) {
                        table.put(teacher.id, "mobile", teacherIdMobileMap.get(teacher.id))
                    }
                    if (teacher?.avatar) {
                        String url = teacher.avatar
                        if (!url.startsWith("http")) {
                            url = OssUtils.transformFilePath(teacher.avatar + "!face", 12000)
                        }
                        table.put(teacher.id, "avatarUrl", url)
                    }
                    if (teacherIdIdCardMap.get(teacher.id)) {
                        table.put(teacher.id, "idCard", teacherIdIdCardMap.get(teacher.id))
                    }
                    if (teacherIdCardNum.get(teacher.id)) {
                        table.put(teacher.id, "cardNum", teacherIdCardNum.get(teacher.id))
                    }
//                    table.put(teacher.id, "numberPlate", "")
                }
                break
            case UserTypeEnum.FERRIES.type:
                List<Ferries> ferriesList = ferriesService.fetchAllByFerriesIdList(syncUserIdList)
                Map<Long, String> ferriesIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferriesList*.id, false)
                ferriesList.each { Ferries ferries ->
                    table.put(ferries.id, "name", ferries?.name)
                    if (ferries.gender != null) {
                        table.put(ferries.id, "gender", ferries?.gender)
                    } else {
                        table.put(ferries.id, "gender", true)
                    }
                    if (ferriesIdMobileMap.get(ferries.id)) {
                        String mobile = ferriesIdMobileMap.get(ferries.id)
                        table.put(ferries.id, "mobile", mobile)
                        table.put(ferries.id, "code", "F${mobile}".toString())
                    }
                    if (ferries?.pics) {
                        String url = ferries.pics+ "!face"
                        if (!url.startsWith("http")) {
                            url = OssUtils.transformFilePath(url, 1200)
                        }
                        table.put(ferries.id, "avatarUrl", url)
                    }
                    if (StringUtils.isNotBlank(ferries?.licensePlate)) {
                        table.put(ferries.id, "numberPlate", ferries?.licensePlate)
                    }
                }
                break
            case UserTypeEnum.VISITORS.type:
                List<Visitor> visitorList = visitorService.fetchVisitorByIds(syncUserIdList)
                Map<Long, String> visitorIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitorList*.id, false)
                Map<Long, String> visitorIdIdCardMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.ID_CARD.type, UserTypeEnum.VISITORS.type, visitorList*.id, false)
                visitorList.each { Visitor visitor ->
                    table.put(visitor.id, "name", visitor?.visitorName)
                    if (StringUtils.isNotBlank(visitorIdIdCardMap.get(visitor.id))) {
                        table.put(visitor.id, "idCard", visitorIdIdCardMap.get(visitor.id))
                    }
                    if (StringUtils.isNotBlank(visitorIdMobileMap.get(visitor.id))) {
                        String mobile = visitorIdMobileMap.get(visitor.id)
                        table.put(visitor.id, "mobile", mobile)
                        table.put(visitor.id, "code", "V${mobile}".toString())
                    }
                    if (visitor.gender != null) {
                        table.put(visitor.id, "gender", visitor?.gender)
                    } else {
                        table.put(visitor.id, "gender", true)
                    }
                    if (StringUtils.isNotBlank(visitor?.pic)) {
                        String url = visitor.pic+ "!face"
                        if (!url.startsWith("http")) {
                            url = OssUtils.transformFilePath(url, 1200)
                        }
                        table.put(visitor.id, "avatarUrl", url)
                    }
                }
                break
        }
        table
    }

    void syncUpdateOrCreateUser2Firm(long campusId, List<Long> syncUserIdList, byte syncUserType, Long operationUserId, Byte operationUserType, Byte promoterType, String option = "同步人员", Integer firm = null) {
        Promise task = Promises.task {
            updateOrCreateUser2Firm(campusId, syncUserIdList, syncUserType, operationUserId, operationUserType, promoterType, option, firm,UserInfoChangeEnum.onInitBit())
        }
        task.onError { Throwable throwable ->
            log.warn("sync update user 2 firm fail userType@${syncUserType}".toString(), throwable)
        }
        task.onComplete {
            log.info("sync update user 2 firm success userIdList${JSON.toJSONString(syncUserIdList)}, userType@${syncUserType}".toString())
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void updateOrCreateUser2Firm(long campusId, List<Long> syncUserIdList, byte syncUserType, Long operationUserId, Byte operationUserType, Byte promoterType, String option = "同步人员", Integer firm = null,Integer userInfoChange=0) {
        if (!syncUserIdList) {
            throw new HiiAdminException("请选择需要同步的人员")
        }
        final Table<Long, String, Object> table = transformSyncUserInfoTable(campusId, syncUserIdList, syncUserType)
        List<DockingFirmPersonConfig> personConfigList = dockingFirmPersonConfigService.fetchAllNeedSyncDockingFirmPersonConfigByCampusIdAndUserType(campusId, syncUserType)

        if (personConfigList != null && personConfigList.size() > 0) {
            personConfigList.each { personConfig ->
                if (firm && personConfig.firm != firm) {
                    return
                }

                DockingFirmSyncTask task = dockingFirmSyncTaskService.saveDockingFirmSyncTask(
                        personConfig.schoolId, personConfig.campusId, operationUserId, operationUserType, promoterType, personConfig.firm, option, 2)
                task.operatingStatus = 2
                task = dockingFirmSyncTaskService.saveDockingFirmSyncTask(task)
                Boolean successStatus = false
                Boolean failureStatus = false
                Boolean operatingStatus = false
                switch (personConfig.firm) {
                    case DeviceFirm.HK_EDU.firm:
                        syncUserIdList.each { syncUserId ->
                            Map<String, Object> parameter = table.row(syncUserId).collectEntries {
                                [it.key, it.value]
                            }

                            try {
                                if (parameter){
                                    defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)
                                    String key = "syncPerson2HKEDU${campusId}_${syncUserId}_${personConfig.firm}_${syncUserType}".toString()
                                    cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES,
                                            {
                                                ServiceResult serviceResult = syncPerson2HKEDU(parameter)
                                                successStatus ?: (successStatus = serviceResult.success)
                                                failureStatus ?: (failureStatus = !serviceResult.success)
                                                saveDockingFirmSyncTask(serviceResult, serviceResult.result, campusId, task.id, personConfig.firm)
                                            })
                                }

                            } catch (Exception e) {
                                log.warn("""同步人员到海康失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                        parameter@${JSON.toJSONString(parameter)}""".toString(), e.getMessage())
                                failureStatus = true
                            }
                        }
                        break
                    case DeviceFirm.HK_YM.firm:
                        break
                    case DeviceFirm.DH_DSS.firm:
                        syncUserIdList.each { syncUserId ->
                            Map<String, Object> parameter = table.row(syncUserId).collectEntries {
                                [it.key, it.value]
                            }
                            try {
                                defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)
                                String key = "syncPerson2DH${campusId}_${syncUserId}_${personConfig.firm}_${syncUserType}".toString()
                                cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES,
                                        {
                                            ServiceResult serviceResult = syncPerson2DH(parameter)
                                            successStatus ?: (successStatus = serviceResult.success)
                                            failureStatus ?: (failureStatus = !serviceResult.success)
                                            saveDockingFirmSyncTask(serviceResult, serviceResult.result, campusId, task.id, personConfig.firm)
                                        })
                            } catch (Exception e) {
                                failureStatus = true
                                log.warn("""同步人员到海康失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                        parameter@${JSON.toJSONString(parameter)}-- e@${e.message}""".toString())
                            }
                        }
                        break
                    case DeviceFirm.DH_YR.firm:
                        syncUserIdList.each { syncUserId ->
                            Map<String, Object> parameter = table.row(syncUserId).collectEntries {
                                [it.key, it.value]
                            }
                            parameter.put("userInfoChange",userInfoChange)
                            try {
                                defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)
                                String key = "syncPerson2DHYR${campusId}_${syncUserId}_${personConfig.firm}_${syncUserType}".toString()
                                cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES,
                                        {
                                            ServiceResult serviceResult = syncPerson2DHYR(parameter)
                                            successStatus ?: (successStatus = serviceResult.success)
                                            failureStatus ?: (failureStatus = !serviceResult.success)
                                            saveDockingFirmSyncTask(serviceResult, serviceResult.result, campusId, task.id, personConfig.firm)
                                        })
                            } catch (Exception e) {
                                failureStatus = true
                                log.warn("""同步人员到海康失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                        parameter@${JSON.toJSONString(parameter)}-- e@${e.message}""".toString())
                            }
                        }
                        break
                    case DeviceFirm.FS_CAR.firm:
                        //todo 后台暂时不同步富士的
                        /*syncUserIdList.each {
                            Long syncUserId ->
                                Map<String, Object> parameter = table.row(syncUserId)
                                try {
                                    defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)

                                    ServiceResult serviceResult = synUserFaceToFujiResult(parameter, synFirm)
                                    successStatus ?: (successStatus = serviceResult.success)
                                    failureStatus ?: (failureStatus = !serviceResult.success)
                                    saveDockingFirmSyncTask(serviceResult, parameter, task.id, synFirm.firm)
                                } catch (Exception e) {
                                    failureStatus = true
                                    log.warn("""同步人员到富士失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                    parameter@${JSON.toJSONString(parameter)}-- e@${e.message}""".toString())
                                }
                        }*/
                        break
                    case DeviceFirm.ALY_DEVICE.firm:
                        syncUserIdList.each { Long syncUserId ->
                            Map parameter = table.row(syncUserId).collectEntries {
                                [it.key, it.value]
                            }
                            try {
                                DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, personConfig.firm)
                                defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)
                                parameter.put("platformId", platform.id)
                                parameter.put("dockingJson", platform.dockingJson)
                                String key = "synUserFaceToAliDeviceResult${campusId}_${syncUserId}_${personConfig.firm}_${syncUserType}".toString()
                                cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES,
                                        {
                                            ServiceResult serviceResult
                                            try {
                                                serviceResult = synUserFaceToAliDeviceResult(parameter, platform?.addressLink)
                                            } catch (Exception e) {
                                                log.info("syncUpdateOrCreateUser2Firm,msg@${e.message}".toString())
                                                serviceResult = ServiceResult.failure("100", e.message)
                                            }
                                            successStatus ?: (successStatus = serviceResult.success)
                                            failureStatus ?: (failureStatus = !serviceResult.success)
                                            saveDockingFirmSyncTask(serviceResult, parameter, campusId, task.id, personConfig.firm)
                                        })
                            } catch (Exception e) {
                                failureStatus = true
                                log.warn("""同步人员到阿里一体机失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                        parameter@${JSON.toJSONString(parameter)}-- e@${e.message}""".toString())
                            }
                        }
                        break
                    case DeviceFirm.ALY_EDGE_DEVICE.firm:
                        DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, personConfig.firm)
                        syncUserIdList.each { syncUserId ->
                            Map parameter = table.row(syncUserId).collectEntries {
                                [it.key, it.value]
                            }
                            try {
                                defaultParameterMap(campusId, syncUserId, syncUserType, personConfig.dockingFieldKey, parameter)
                                parameter.put("platformId", platform.id)
                                parameter.put("dockingJson", platform.dockingJson)
                                String key = "synUserFaceToYunV2${campusId}_${syncUserId}_${personConfig.firm}_${syncUserType}".toString()
                                cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES,
                                        {
                                            ServiceResult serviceResult
                                            try {
                                                serviceResult = synUserFaceToYunV2(parameter, personConfig.schoolId)
                                            } catch (Exception e) {
                                                log.info("syncUpdateOrCreateUser2Firm,msg@${e.message}".toString())
                                                serviceResult = ServiceResult.failure("100", e.message)
                                            }
                                            successStatus ?: (successStatus = serviceResult.success)
                                            failureStatus ?: (failureStatus = !serviceResult.success)
                                            saveDockingFirmSyncTask(serviceResult, parameter, campusId, task.id, personConfig.firm)
                                        })
                            } catch (Exception e) {
                                failureStatus = true
                                log.warn("""同步人员到边缘一体机失败campusId@${campusId}, syncUserId@${syncUserId}, syncUserType@${syncUserType}, promoterId@${operationUserId}, promoterType@${promoterType}
                                        parameter@${JSON.toJSONString(parameter)}-- e@${e.message}""".toString())
                            }
                        }
                        break
                }
                operatingStatus = successStatus && failureStatus
                task.operatingStatus = operatingStatus ? -1 : successStatus ? 1 : 0
                task.operationCompletionTime = new Date()
                dockingFirmSyncTaskService.saveDockingFirmSyncTask(task)
            }
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult deletePerson4DHYR(long campusId, Long syncUserId, byte syncUserType) {
        JSONObject jsonObject = new JSONObject()
        jsonObject.put("campusId", campusId)
        jsonObject.put("userType", syncUserType)
        jsonObject.put("userId", syncUserId)
        ResultVO resultVO = antennaDhYrApi.deletePerson(jsonObject)
        if (resultVO.status == 1) {
            ServiceResult.success()
        } else {
            ServiceResult.failure(resultVO.code.toString(), resultVO.message)
        }
    }

    /**
     *
     * @param campusId
     * @param syncUserId
     * @param syncUserType
     * @return
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult deletePerson4HKEDU(long campusId, Long syncUserId, byte syncUserType) {
        //TODO 删除权限管控，管控目前只支持老师、学生
        try {
            planTemplatePersonService.minusAllPersonPlan4campus(campusId, syncUserId, syncUserType)
        } catch (RuntimeException e) {
           log.warn("删除权限管控"+e.getMessage())
            return ServiceResult.failure("100", e.message)
        }
        ResultVO resultVO = antennaApi.deleteHKPerson([
                campusId: campusId,
                userType: syncUserType,
                userId  : syncUserId])
        if (resultVO.status == 1) {
            ServiceResult.success()
        } else {
            ServiceResult.failure(resultVO.code.toString(), resultVO.message)
        }
    }

    /**
     * @param campusId
     * @param syncUserId
     * @param syncUserType
     * @param studentId 仅当syncUserType = ConstantEnum.UserTypeEnum.FERRIES.type 时存在该值
     * @return
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult deletePerson4DH(long campusId, Long syncUserId, byte syncUserType, Long studentId = null) {
        try {
            gatedIssueService.minusAllPersonGate4campus(campusId, syncUserId, syncUserType, studentId)
        } catch (RuntimeException e) {
            return ServiceResult.failure("100", e.message)
        }
        ResultVO resultVO = antennaApi.deleteDHPersonV2([
                campusId: campusId,
                userType: syncUserType,
                userIds : syncUserId])
        if (resultVO.status == 1) {
            ServiceResult.success()
        } else {
            ServiceResult.failure(resultVO.code.toString(), resultVO.message)
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult syncPerson2HKEDU(Map<String, Object> parameter) {
        long campusId = parameter.campusId as long
        long syncUserId = parameter.userId as long
        byte syncUserType = parameter.userType as byte
        boolean codePrefix = dockingPlatformCampusService.hasMannyDockingPlatformCampusByCampus4Platform(campusId, DeviceFirm.HK_EDU.firm)
        Campus campus =campusService.fetchCampusByCampusId(campusId)
        String code = parameter.code
        if (code && codePrefix) {
            parameter.put("code", "${campusId}${code}".toString())
        }
        ResultVO resultVO
        try {
            switch (syncUserType) {
                case UserTypeEnum.STUDENT.type:
                    DockingFirmPersonOrg org = null
                    if(campus.type==1){
                        Long gradeId = parameter.gradeId as Long

                        Grade grade = gradeService.fetchGradeById(gradeId)
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndSectionIdAndSchoolYear4student(campusId, DeviceFirm.HK_EDU.firm, grade.sectionId, grade.schoolYear)
                    }else {
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.HK_EDU.firm, syncUserType)
                    }

                    if (!org?.dockingFirmId) {
                        return ServiceResult.failure("100", "组织未同步海康系统：${org.orgName}", parameter)
                    }
                    parameter.put("orgIndexCode", org.dockingFirmId)
                    resultVO = antennaApi.hikUpdateOrCreatePersonV2(parameter)
                    break
                case UserTypeEnum.PARENT.type:
                    String mobile = parameter.mobile
                    if (!mobile) {
                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
                    }
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.HK_EDU.firm, syncUserType)
                    if (!org?.dockingFirmId) {
                        return ServiceResult.failure("100", "组织未同步海康系统：${org.orgName}", parameter)
                    }
                    parameter.put("orgIndexCode", org.dockingFirmId)
                    resultVO = antennaApi.hikUpdateOrCreatePersonV2(parameter)
                    break
                case UserTypeEnum.FERRIES.type:
                    String mobile = parameter.mobile
                    if (!mobile) {
                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
                    }
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.HK_EDU.firm, syncUserType)
                    if (!org?.dockingFirmId) {
                        return ServiceResult.failure("100", "组织未同步海康系统：${org.orgName}", parameter)
                    }
                    parameter.put("orgIndexCode", org.dockingFirmId)
                    resultVO = antennaApi.hikUpdateOrCreatePersonV2(parameter)
                    break
                case UserTypeEnum.TEACHER.type:
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.HK_EDU.firm, syncUserType)
                    if (!org?.dockingFirmId) {
                        return ServiceResult.failure("100", "组织未同步海康系统：${org.orgName}", parameter)
                    }
                    parameter.put("orgIndexCode", org.dockingFirmId)
                    resultVO = antennaApi.hikUpdateOrCreatePersonV2(parameter)
                    break
//            海康访客不同步到平台
                case UserTypeEnum.VISITORS.type:
                    break
            }
        } catch (Exception e) {
            log.error("同步人员到海康失败"+e.getMessage())
            return ServiceResult.failure("100", e?.message, parameter)
        }
        if (resultVO?.status == 1 && parameter.cardNum) {
            try {
                ResultVO re = antennaApi.bindOrUpdatePersonCardV2([
                        campusId: campusId,
                        userId  : syncUserId,
                        userType: syncUserType,
                        cardNum : parameter.cardNum
                ])
                if (re?.status != 1) {
                    return ServiceResult.failure("100", re?.message, parameter)
                }
            } catch (Exception e) {
                log.error("同步卡号到海康失败"+ e.getMessage())
                return ServiceResult.failure("100", e?.message, parameter)
            }

            return ServiceResult.success(parameter)
        } else if (resultVO?.status == 1) {
            return ServiceResult.success(parameter)
        } else {
            return ServiceResult.failure(resultVO?.code as String, resultVO?.message, parameter)
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    def syncPerson2DH(Map<String, Object> parameter) {
        ResultVO resultVO
        long campusId = parameter.campusId as long
        byte syncUserType = parameter.userType as byte
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        try {
            switch (syncUserType) {
                case UserTypeEnum.STUDENT.type:
                    DockingFirmPersonOrg org = null
                    if(campus.type ==1){
                        Long gradeId = parameter.gradeId as Long
                        Grade grade = gradeService.fetchGradeById(gradeId)
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndSectionIdAndSchoolYear4student(campusId, DeviceFirm.DH_DSS.firm, grade.sectionId, grade.schoolYear)
                        if (!org) {
                            return ServiceResult.failure("100", "组织未同步大华系统：学年${grade.schoolYear}, 学段${SectionType.getEnumByType(grade.sectionId)?.name}, 不支持同步", parameter)
                        }
                    }else{
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.DH_DSS.firm, syncUserType)
                        if (!org) {
                            return ServiceResult.failure("100", "组织未同步大华系统：不支持同步", parameter)
                        }
                    }

                    if (!org?.dockingFirmId) {
                        return ServiceResult.failure("100", "组织未同步大华系统：${org?.orgName}, 不支持同步", parameter)
                    }
                    parameter.put("orgIndexCode", org.dockingFirmId)
                    parameter.put("roleId", "student001")
                    resultVO = antennaApi.updateOrCreatePersonV2(parameter)
                    break
                case UserTypeEnum.PARENT.type:
                    parameter.put("roleId", "other001")
                    String mobile = parameter.mobile
                    if (!mobile) {
                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
                    }
                    if (!parameter.code) {
                        parameter.put("code", "P${mobile}".toString())
                    }
                    resultVO = antennaApi.updateOrCreatePersonV2(parameter)
                    long syncUserId = parameter.userId as long
                    gatedIssueService.synchronousParent2Gated(campusId, syncUserId)
                    break
                case UserTypeEnum.TEACHER.type:
                    parameter.put("roleId", "teacher001")
                    resultVO = antennaApi.updateOrCreatePersonV2(parameter)
                    break
                case UserTypeEnum.FERRIES.type:
                    parameter.put("roleId", "other001")
                    String mobile = parameter.mobile
                    if (!mobile) {
                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
                    }
                    if (!parameter.code) {
                        parameter.put("code", "F${mobile}".toString())
                    }
                    resultVO = antennaApi.updateOrCreatePersonV2(parameter)
                    long syncUserId = parameter.userId as long
                    gatedIssueService.updateFerries2DaHuaV2(campusId, syncUserId)
                    break
                case UserTypeEnum.VISITORS.type:
                    String mobile = parameter.mobile
                    if (!mobile) {
                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
                    }
                    if (!parameter.code) {
                        parameter.put("code", "V${mobile}".toString())
                    }
                    parameter.put("roleId", "other001")
                    resultVO = antennaApi.updateOrCreatePersonV2(parameter)
                    long syncUserId = parameter.userId as long
                    gatedIssueService.updateVisitor2DaHuaV2(campusId, syncUserId)
                    break
            }
            if (resultVO?.status != 1) {
                return ServiceResult.failure("100", resultVO?.message, parameter)
            }
        } catch (Exception e) {
            log.error("同步人员到大华失败"+e.getMessage())
            return ServiceResult.failure("100", e.message, parameter)
        }
        return ServiceResult.success(parameter)
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    def syncPerson2DHYR(Map<String, Object> parameter) {
        ResultVO resultVO
        long campusId = parameter.campusId as long
        long syncUserId = parameter.userId as long
        byte syncUserType = parameter.userType as byte
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        try {
            switch (syncUserType) {
                case UserTypeEnum.STUDENT.type:
                    DockingFirmPersonOrg org =null
                    if(campus.type ==1){
                        Long gradeId = parameter.gradeId as Long
                        Grade grade = gradeService.fetchGradeById(gradeId)
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndSectionIdAndSchoolYear4student(campusId, DeviceFirm.DH_YR.firm, grade.sectionId, grade.schoolYear)
                        if (!org) {
                            return ServiceResult.failure("100", "组织未同步云睿系统：学年${grade.schoolYear}, 学段${SectionType.getEnumByType(grade.sectionId)?.name}, 不支持同步", parameter)
                        }
                    }else{
                        org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.DH_YR.firm, syncUserType)
                        if (!org) {
                            return ServiceResult.failure("100", "组织未同步云睿系统：不支持同步", parameter)
                        }
                    }


                    if (!org?.dockingFirmId && !org?.dockingFirmCode) {
                        return ServiceResult.failure("100", "组织未同步云睿系统：${org?.orgName}, 不支持同步", parameter)
                    }
                    parameter.put("storeId", org.dockingFirmId)
                    parameter.put("orgCode", org.dockingFirmCode)
                    JSONObject jsonObject = new JSONObject(parameter)
                    resultVO = antennaDhYrApi.updateOrCreatePerson(jsonObject)
                    break
                case UserTypeEnum.PARENT.type:
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.DH_YR.firm, syncUserType)
                    if (!org?.dockingFirmId && !org?.dockingFirmCode) {
                        return ServiceResult.failure("100", "组织未同步云睿系统：${org.orgName}", parameter)
                    }
                    parameter.put("storeId", org.dockingFirmId)
                    parameter.put("orgCode", org.dockingFirmCode)
                    JSONObject jsonObject = new JSONObject(parameter)
                    resultVO = antennaDhYrApi.updateOrCreatePerson(jsonObject)
                    break
                case UserTypeEnum.TEACHER.type:
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.DH_YR.firm, syncUserType)
                    if (!org?.dockingFirmId && !org?.dockingFirmCode) {
                        return ServiceResult.failure("100", "组织未同步云睿系统：${org.orgName}", parameter)
                    }
                    parameter.put("storeId", org.dockingFirmId)
                    parameter.put("orgCode", org.dockingFirmCode)
                    JSONObject jsonObject = new JSONObject(parameter)
                    resultVO = antennaDhYrApi.updateOrCreatePerson(jsonObject)
                    break
                case UserTypeEnum.FERRIES.type:
//                    parameter.put("roleId", "other001")
//                    String mobile = parameter.mobile
//                    if (!mobile) {
//                        return ServiceResult.failure("100", "人员不存在手机号，同步失败", parameter)
//                    }
//                    if (!parameter.code) {
//                        parameter.put("code", "F${mobile}".toString())
//                    }
//                    resultVO = antennaDhYrApi.updateOrCreatePerson(parameter)
                    break
                case UserTypeEnum.VISITORS.type:
                    if (!parameter.avatarUrl) {
                        return ServiceResult.failure("100", "人员无照片不同步", parameter)
                    }
                    parameter.remove("mobile")
                    DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserType4unStudent(campusId, DeviceFirm.DH_YR.firm, syncUserType)
                    if (!org?.dockingFirmId && !org?.dockingFirmCode) {
                        return ServiceResult.failure("100", "组织未同步云睿系统：${org.orgName}", parameter)
                    }
                    parameter.put("storeId", org.dockingFirmId)
                    parameter.put("orgCode", org.dockingFirmCode)
                    JSONObject jsonObject = new JSONObject(parameter)
                    resultVO = antennaDhYrApi.updateOrCreatePerson(jsonObject)
                    try {
                        if (resultVO?.status == 1 && resultVO.result.personId) {
                            String personId = resultVO.result.personId
                            dockingIssueService.issueYrPerson4visitor(personId, campusId)
                        }
                    } catch (Exception e) {
                        log.error("云睿 issueNowadaysVisitor userId${syncUserId}".toString(), e)
                    }
                    break
            }
            if (resultVO?.status == 1 && parameter.cardNum) {
                try {
                    JSONObject jsonObject = new JSONObject()
                    jsonObject.put("campusId", campusId)
                    jsonObject.put("userId", syncUserId)
                    jsonObject.put("userType", syncUserType)
                    jsonObject.put("cardNum", parameter.cardNum)
                    ResultVO re = antennaDhYrApi.crateOrUpdateCard(jsonObject)
                    if (re?.status != 1) {
                        return ServiceResult.failure("100", re?.message, parameter)
                    }
                } catch (Exception e) {
                    log.error("同步卡号到海康失败"+e.getMessage())
                    return ServiceResult.failure("100", e?.message, parameter)
                }
                return ServiceResult.success(parameter)
            } else if (resultVO?.status == 1) {
                return ServiceResult.success(parameter)
            } else {
                return ServiceResult.failure(resultVO.code as String, resultVO?.message, parameter)
            }
        } catch (Exception e) {
            log.error("同步人员到大华失败"+ e.getMessage())
            return ServiceResult.failure("100", e.message, parameter)
        }
    }

    private static void defaultParameterMap(long campusId, long syncUserId, byte syncUserType, int dockingFieldKey, Map<String, Object> parameterMap) {
        parameterMap.put("campusId", campusId)
        parameterMap.put("userId", syncUserId)
        parameterMap.put("userType", syncUserType)
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_NAME.field, dockingFieldKey)) {
            parameterMap.remove("name")
        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_GENDER.field, dockingFieldKey)) {
            parameterMap.remove("gender")
        }
//       工号必须同步
//        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_CODE.field, dockingFieldKey)) {
//            parameterMap.remove("code")
//        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_MOBILE.field, dockingFieldKey)) {
            parameterMap.remove("mobile")
        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_AVATAR.field, dockingFieldKey)) {
            parameterMap.remove("avatarUrl")
        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_ID_CARD.field, dockingFieldKey)) {
            parameterMap.remove("idCard")
        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_CARD_NUM.field, dockingFieldKey)) {
            parameterMap.remove("cardNum")
        }
        if (!compareFieldBit(DockingFirmUtil.DockingFirmPersonField.PERSON_NUMBER_PLATE.field, dockingFieldKey)) {
            parameterMap.remove("numberPlate")
        }
    }

    /**
     * 同步到边缘一体机中
     * @param map
     * @param campusId
     * @param schoolId
     * @return
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult synUserFaceToYunV2(Map parameter, Long schoolId) {
        Long platformId = parameter.platformId as Long

        long campusId = parameter.campusId as long
        long userId = parameter.userId as long
        byte userType = parameter.userType as byte
        ResultVO resultVO = ResultVO.success()
        String avatarUrl = parameter.get("avatarUrl")
        //图片为空且不是学生
        if (StringUtils.isEmpty(avatarUrl)) {
            return ServiceResult.failure("100", " 用户图片为空，添加同步失败")
        }
        DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserIdAndUserType(campusId, DeviceFirm.ALY_EDGE_DEVICE.firm, userId, userType)
        if (org == null || StringUtils.isEmpty(org.dockingFirmId) || org.bindStatus != 1) {
            log.info("【SynUserService】synUserFaceToYun未同步用户组或用户组未成功设备绑定,userId@${userId},userType@${userType}".toString())
            return ServiceResult.failure("100", "未同步用户组或用户组未成功于设备绑定，跟新用户信息失败！")
        }
        RelatedUser relatedUser = relatedUserService.fetchRelateUserByUserIdAndUserTypeAndFirm(campusId, userId, userType, DeviceFirm.ALY_EDGE_DEVICE.firm)
        Map remarksMap = [:]
        remarksMap.put("sex", parameter.get("gender"))
        remarksMap.put("userType", userType)
        remarksMap.put("code", parameter.get("code"))
        String params = JSONObject.toJSONString(remarksMap)
        Map mapParam = [:]
        mapParam.put("name", parameter.get("name"))
        mapParam.put("faceUrl", parameter.get("avatarUrl"))
        mapParam.put("code", userId)
        mapParam.put("params", params)
        mapParam.put("campusId", platformId)

        if (relatedUser == null) {
            resultVO = aLiYunApiInvoker.addUserToYun(mapParam)
            if (resultVO.status != 1) {
                log.info("【SynUserService】synUserFaceToYun添加用户失败userId@${userId},userType@${userType}".toString())
                return ServiceResult.failure("100", " 添加用户失败，msg@${resultVO.message}")
            }
            //将用户加入用户组
            String synCode = resultVO.result.get("userId")

            relatedUser = relatedUserService.synSaveUser(schoolId, campusId, userId, userType, synCode, parameter.get("userCode") as String, DeviceFirm.ALY_EDGE_DEVICE.firm, null)
            log.info("synUserFaceToAliDeviceResult 保存成功relatedId@${relatedUser?.id}".toString())

            if (org == null && StringUtils.isEmpty(org.dockingFirmId)) {
                log.info("【SynUserService】synUserFaceToYun未查到有用户组campusId@${campusId},userId@${userId},userType@${userType}".toString())
                relatedUser.bindStatus = 0
                relatedUserService.saveRelatedUser(relatedUser)
                return ServiceResult.failure("100", "未同步用户组，绑定用户组失败")
            }
            return synUserJoinUserGroupToYunV2(platformId, org.dockingFirmId, synCode)
        } else {

            mapParam.put("userId", relatedUser.personId)
            resultVO = aLiYunApiInvoker.updateUserToYun(mapParam)

            if (resultVO.status != 1) {
                log.info("【SynUserService】synUserFaceToYun更新用户失败campusId@${campusId},userType@${userType}，firm@${DeviceFirm.ALY_EDGE_DEVICE.firm}，msg@${resultVO.message}".toString())
                return ServiceResult.failure("100", "修改用户失败,msg@${resultVO.message}")
            }

            if (relatedUser.bindStatus != ResultVOStatus.NORMAL.type) {
                return synUserJoinUserGroupToYunV2(platformId, org.dockingFirmId, relatedUser.personId)
            }
        }
        return ServiceResult.success()
    }


    ServiceResult synUserJoinUserGroupToYunV2(Long platformId, String userGroupId, String userIdAtYun) {
        Map joinUserGroupMap = [:]
        joinUserGroupMap.put("campusId", platformId)
        joinUserGroupMap.put("userGroupId", userGroupId)
        joinUserGroupMap.put("userId", userIdAtYun)
        ResultVO joinGroupResult = new ResultVO()
        try {
            joinGroupResult = aLiYunApiInvoker.userJoinUserGroupToYun(joinUserGroupMap)
        } catch (Exception e) {
            log.info("【SynUserService】synUserJoinUserGroupToYun，加入用户组异常msg@${e.message}".toString())
            return ServiceResult.failure("100", "加入用户组异常msg@${e.message}")
        }
        if (joinGroupResult == null && joinGroupResult.status != 1) {
            log.info("【SynUserService】synUserJoinUserGroupToYun人脸组和用户绑定失败platformId@${platformId},userGroupId@${userGroupId},userIdAtYun@${userIdAtYun}".toString())
            return ServiceResult.failure("100", "人脸组和用户绑定失败userGroupId@${userGroupId},userIdAtYun@${userIdAtYun}")
        }
        return ServiceResult.success()
    }

    /**
     * todo
     * 删除边缘一体机用户
     * @param campusId
     * @param userId
     * @param userType
     * @return
     */
    ServiceResult deleteUserFaceToYunV3(long campusId, Long userId, byte userType) {
        RelatedUser relatedUser = relatedUserService.fetchRelateUserByUserIdAndUserTypeAndFirm(campusId, userId, userType, DeviceFirm.ALY_EDGE_DEVICE.firm)
        if (relatedUser == null) {
            log.info("【SynUserService】deleteUserFaceToYunV2 删除用户失败，未找到用户关联表@${campusId},userId@${userId},userType@${userType}".toString())
            return ServiceResult.failure("100", "人员未关联边缘一体机平台，平台人员删除失败")
        }
        DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, DeviceFirm.ALY_EDGE_DEVICE.firm)
        Map delMap = [:]
        delMap.put("campusId", platform.id)
        delMap.put("userId", relatedUser.personId)
        ResultVO resultVO = aLiYunApiInvoker.deleteUserToYun(delMap)
        if (resultVO.status != 1) {
            log.info("【SynUserService】deleteUserFaceToYunV2 删除用户失败，@${campusId},userId@${userId},userType@${userType},msg@${resultVO.message}".toString())
            return ServiceResult.failure("100", "删除用户失败，msg@${resultVO.message}")
        } else {
            relatedUser.status = 0
            relatedUserService.saveRelatedUser(relatedUser)
        }
        return ServiceResult.success("删除成功")
    }


    /**
     * 删除阿里云一体机用户
     * @param campusId
     * @param userId
     * @param userType
     * @param addressLink
     * @return
     */
    ServiceResult deleteUserFaceToAliV3(long campusId, long userId, byte userType, String addressLink) {

        RelatedUser relatedUser = relatedUserService.fetchRelateUserByUserIdAndUserTypeAndFirm(campusId, userId, userType, DeviceFirm.ALY_DEVICE.firm)
        if (!relatedUser) {
            log.info("不存在同步数据，可以直接删除".toString())
            return ServiceResult.failure("100", "人员未关联阿里一体机平台，平台人员删除失败")
        }

        if (!relatedUser.personId) {
            log.info("阿里平台主键未能获取到".toString())
            return ServiceResult.failure("100", "阿里平台主键未能获取到relatedId@${relatedUser.id}")
        }

        Map paramMap = [:]
        paramMap.put("id", relatedUser.personId)
        paramMap.put("addressLink", addressLink)
        paramMap.put("campusId", campusId)

        ResultVO resultVO = aLiYunApiInvoker.synDeleteFaceToALiYun(paramMap)
        if (resultVO.status == 1) {
            log.info("阿里一体机删除成功！msg@:${resultVO.message},userId:${userId},userType@${userType}")
            relatedUser.status = 0
            relatedUserService.saveRelatedUser(relatedUser)
            return ServiceResult.success("删除成功")
        } else {
            log.info("阿里一体机删除失败！msg@:${resultVO.message},userId:${userId},userType@${userType}")
            return ServiceResult.failure("100", "阿里一体机删除失败！msg@:${resultVO.message}")
        }

    }


    Boolean deleteUserFaceToAli(Map map, Long campusId, String addressLink) {

        Long userId = map.get("userId") as Long

        Byte userType = map.get("userType") as Byte

        RelatedUser relatedUser = relatedUserService.fetchRelateUserByUserIdAndUserTypeAndFirm(campusId, userId, userType, DeviceFirm.ALY_DEVICE.firm)
        if (!relatedUser) {
            log.info("不存在同步数据，可以直接删除".toString())
            return true
        }

        if (!relatedUser.personId) {
            log.info("阿里平台主键未能获取到".toString())
            return true
        }

        Map paramMap = [:]
        paramMap.put("id", relatedUser.personId)
        paramMap.put("addressLink", addressLink)

        ResultVO resultVO = aLiYunApiInvoker.synDeleteFaceToALiYun(paramMap)
        if (resultVO.status == 1) {
            log.info("阿里一体机删除成功！msg@:${resultVO.message},userId:${userId},userType@${userType}")
            relatedUser.status = 0
            relatedUserService.saveRelatedUser(relatedUser)
            return true
        } else {
            log.info("阿里一体机删除失败！msg@:${resultVO.message},userId:${userId},userType@${userType}")
            return false
        }

    }

    /**
     //     * 同步人员到富士中
     //     * */
//    @Transactional(propagation = Propagation.NOT_SUPPORTED)
//    ServiceResult<String> synUserFaceToFujiResult(Map<String, Object> parameter, SchoolSynFirm synFirm) throws RuntimeException {
//        Long userId = parameter.userId as long
//        Byte userType = parameter.userType as byte
//
//        DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserIdAndUserType(synFirm.campusId, synFirm.firm, userId, userType)
//
//        String licensePlate = parameter.get("numberPlate")
//        String name = parameter.get("name")
//        String mobile = parameter.get("mobile")
//        if (StringUtils.isEmpty(licensePlate)) {
//            return ServiceResult.failure("100", "车牌号为空，同步失败userType:${userType},userId@${userId}")
//        }
//        fujiApiService.synFerriesToFsStaff(userId, licensePlate, name, mobile, synFirm, org.dockingFirmCode)
//    }

    /**
     * 同步人脸到阿里一体机中
     * */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    ServiceResult<String> synUserFaceToAliDeviceResult(Map<String, Object> parameter, String addressLink) throws RuntimeException {
        String dockingJson = parameter.dockingJson
        long campusId = parameter.campusId as long
        long userId = parameter.userId as long
        byte userType = parameter.userType as byte
        ResultVO resultVO = ResultVO.success()
        String avatarUrl = parameter.get("avatarUrl") as String
        if (StringUtils.isEmpty(avatarUrl)) {
            log.info("【阿里同步】图片为空，同步失败userType:${userType},userId@${userId}".toString())
            return ServiceResult.failure("100", "阿里同步图片为空，同步失败userType:${userType},userId@${userId}")
        }
        DockingFirmPersonOrg org = dockingFirmPersonOrgService.fetchDockingFirmPersonOrgByCampusIdAndFirmAndUserIdAndUserType(campusId, DeviceFirm.ALY_DEVICE.firm, userId, userType)
        if (org == null) {
            return ServiceResult.failure("100", "未同步用户组")
        }
        DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(dockingJson)

        String phoneNumber = parameter.get("mobile")
        RelatedUser relatedUser = null
        if (userType) {
            if (userType == UserTypeEnum.VISITORS.type) {
                if (StringUtils.isNotBlank(phoneNumber)) {
                    String encodeMobile = MobileUtil.encodeMobile(phoneNumber)
                    relatedUser = relatedUserService.fetchRelatedUserByCampusIdAndUserIdAndUserTypeAndEncodeMobile(encodeMobile, campusId, userType, DeviceFirm.ALY_DEVICE.firm)
                    if (relatedUser && relatedUser.userId != userId) {
                        Map paramMap = [:]
                        paramMap.put("id", relatedUser.personId)
                        paramMap.put("addressLink", addressLink)
                        paramMap.put("version", aliYunVO.version)
                        ResultVO deleteResultVO
                        if (aliYunVO.version) {
                            deleteResultVO = edgeALiYunApiInvoker.synDeleteFaceToALiYun(paramMap)
                        } else {
                            deleteResultVO = aLiYunApiInvoker.synDeleteFaceToALiYun(paramMap)
                        }


                        if (deleteResultVO.status != 1) {
                            return ServiceResult.failure("100", "平台已存在该手机号的访客且删除失败")
                        }
                        relatedUser.status = 0
                        relatedUserService.saveRelatedUser(relatedUser)
                        relatedUser = new RelatedUser()
                    }
                }
            } else {
                relatedUser = relatedUserService.fetchRelateUserByUserIdAndUserTypeAndFirm(campusId, userId, userType, DeviceFirm.ALY_DEVICE.firm)

            }
        }
        String idCard = parameter.get("idCard")
        if (StringUtils.isEmpty(idCard)) {
            idCard = IdUtils.snowflakeId()
        }
        Map mapParam = [:]
        mapParam.put("userId", userId)
        mapParam.put("userType", userType)
        mapParam.put("name", parameter.get("name"))
        mapParam.put("groupId", org.dockingFirmId)
        mapParam.put("idcard", idCard)
        mapParam.put("pic", avatarUrl)
        mapParam.put("phoneNumber", parameter.get("mobile"))
        mapParam.put("userCode", parameter.get("code"))
        //阿里平台0：男 1：女
        mapParam.put("sex", parameter.get("gender") ? 0 : 1)
        mapParam.put("campusId", campusId)
        mapParam.put("addressLink", addressLink)
        mapParam.put("job", UserTypeEnum.getEnumByType(userType).name)
        mapParam.put("organization", UserTypeEnum.getEnumByType(userType).name)
        mapParam.put("campusId", parameter.get("campusId"))
        mapParam.put("version", aliYunVO.version)
        try {
            if (relatedUser) {
                mapParam.put("personId", relatedUser.personId)
                if (aliYunVO.version) {
                    resultVO = edgeALiYunApiInvoker.synUpdateFaceToALiYun(mapParam)
                } else {
                    resultVO = aLiYunApiInvoker.synUpdateFaceToALiYun(mapParam)
                }

            } else {
                if (aliYunVO.version) {
                    resultVO = edgeALiYunApiInvoker.synCreateFaceToALiYun(mapParam)
                } else {
                    resultVO = aLiYunApiInvoker.synCreateFaceToALiYun(mapParam)
                }
                if (resultVO.status == 1) {
                    String synId = resultVO.result.get("synId")
                    relatedUser = relatedUserService.synSaveUser(org.schoolId, campusId, userId, userType, synId, mapParam.get("userCode") as String, DeviceFirm.ALY_DEVICE.firm, phoneNumber)
                    log.info("synUserFaceToAliDeviceResult 保存成功relatedId@${relatedUser?.id}".toString())
                }
            }
        } catch (Exception e) {
            return ServiceResult.failure("100", "同步失败,网络异常!msg@${e.message}")
        }
        if (resultVO.status != 1) {
            return ServiceResult.failure("100", resultVO.message)
        }
        return ServiceResult.success("同步人员成功")
    }

    def saveDockingFirmSyncTask(ServiceResult serviceResult, Map mapParam, long campusId, long taskId, int firm) {
        Long userId = mapParam.get("userId") as Long
        Byte userType = mapParam.get("userType") as Byte
        Byte operatingStatus = serviceResult.success ? 1 as byte : 0 as byte
        String jsonParam = JSON.toJSONString(mapParam)
        dockingFirmSyncTaskService.saveDockingFirmSyncTaskInfo(campusId, taskId, userId, userType, firm, mapParam.name as String, mapParam.code as String, mapParam.mobile as String, operatingStatus, serviceResult?.code, serviceResult?.message, jsonParam)
    }
    //删除用户在富士平台
//    def synDeleteUserToFsCar(Long userId, Byte userType, String licensePlate, SchoolSynFirm synFirm) {
//        RelatedUser relatedUser = relatedUserService.fetchOrCreateRelatedUserByUserIdAndUserType(userId, userType, synFirm.firm)
//        if (relatedUser) {
//            return fujiApiService.deleteFerriesToStaff(licensePlate, relatedUser.personId, synFirm)
//        }
//
//        return ServiceResult.failure("100", "人员未关联富士平台,平台人员删除失败")
//    }

    List<Long> fetchAllStudentIdByGradeId(Long gradeId) {
        List<UnitStudent> unitStudentList = UnitStudent.findAllByGradeIdAndUnitTypeAndStatus(gradeId, UnitType.ADMINISTRATIVE_CLASS.type, 1 as byte)
        unitStudentList*.studentId
    }

    List<Long> fetchAllStudentIdByCampusId(Long campusId) {
        List<UnitStudent> unitStudentList = UnitStudent.findAllByCampusIdAndUnitTypeAndStatus(campusId, UnitType.ADMINISTRATIVE_CLASS.type, 1 as byte)
        unitStudentList*.studentId
    }

    Student fetchStudentById(Long studentId) {
        return Student.get(studentId)
    }

    List<Student> fetchAllStudentByStudentIdInList(List<Long> studentIdList) {
        if (!studentIdList || studentIdList.size() < 1) {
            return Lists.newArrayList()
        }
        return Student.findAllByIdInList(studentIdList)
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolByCampusId(Long campusId) {
        TeacherSchoolCampus.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    @Cached(expire = 1, cacheNullValue = false, timeUnit = TimeUnit.DAYS, name = "teacherService_fetchTeacherByid",
            key = "#teacherId", cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Teacher fetchTeacherById(Long teacherId, boolean cache = true) {
        Teacher.findByIdAndStatus(teacherId, 1 as byte)
    }

    TeacherSchoolCampus fetchTeacherSchoolCampusByTeacherIdAndCampusId(Long campusId, Long teacherId) {
        TeacherSchoolCampus.findByCampusIdAndTeacherIdAndStatus(campusId, teacherId, 1 as Byte)
    }

    List<Teacher> fetchAllTeacherByIdInList(List<Long> teacherIdList) {
        if (!teacherIdList) {
            return []
        }
        Teacher.findAllByIdInListAndStatus(teacherIdList, 1 as byte)
    }

    Map<Long, String> getTeacherNumByTeacherId(List<Long> teacherIdList, Long campusId) {
        fetchTeacherSchoolByTeacherIdListAndCampusId(teacherIdList, campusId)?.collectEntries {
            [it.teacherId, it.jobNum]
        }

    }

    List<TeacherSchoolCampus> fetchTeacherSchoolByTeacherIdListAndCampusId(List<Long> teacherIdList, Long campusId) {
        TeacherSchoolCampus.findAllByCampusIdAndTeacherIdInListAndStatus(campusId, teacherIdList, 1 as Byte)
    }

}

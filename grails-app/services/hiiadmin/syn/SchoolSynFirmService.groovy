package hiiadmin.syn

import com.alibaba.fastjson.JSON
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.module.docking.DockJsonParseAliYunVO
import org.apache.commons.lang3.StringUtils
import timetabling.docking.DockingPlatform
import timetabling.sync.SchoolSynFirm

import java.util.concurrent.TimeUnit

@Transactional
@Slf4j
class SchoolSynFirmService {

    String getAliYunImgLink(String img, String nodeMac, DockingPlatform synFirm) {
        String addressLink = getAliYunAddressLink(nodeMac, synFirm)
        return getAliYunImgByAddressLinkAndImg(addressLink, img)
    }
    /**
     * 根据nodeMac获取地址
     * @param nodeMac
     * @param synFirm
     * @return
     */
    @Cached(expire = 1, timeUnit = TimeUnit.HOURS, name = "hiiadmin_getAliYunAddressLink", key = "#nodeMac",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    String getAliYunAddressLink(String nodeMac, DockingPlatform synFirm) {
        String addressLink = ""
        if (synFirm && StringUtils.isNotBlank(nodeMac)) {
            List<DockJsonParseAliYunVO> aliYunVOList = JSON.parseArray(synFirm.dockingJson, DockJsonParseAliYunVO.class)
            aliYunVOList.each { DockJsonParseAliYunVO it ->
                if (it.nodeMac == nodeMac) {
                    addressLink = it.addressLink
                }
            }
            if (StringUtils.isEmpty(addressLink)) {
                addressLink = synFirm?.addressLink
            }
        }
        return addressLink
    }

    @Cached(expire = 1, timeUnit = TimeUnit.HOURS, name = "hiiadmin_getAliYunAddressLink", key = "#nodeIp+'_'+#campusId+'_'+firm",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    String getAliYunNodeMacByNodeIp(String nodeIp, Long campusId, Integer firm) {
        SchoolSynFirm synFirm = fetchSchoolSynFirmByCampusIdAndFirm(campusId, firm)
        String nodeMac = ""
        if (synFirm && StringUtils.isNotBlank(nodeIp)) {
            List<DockJsonParseAliYunVO> aliYunVOList = JSON.parseArray(synFirm.dockingJson, DockJsonParseAliYunVO.class)
            aliYunVOList.each { DockJsonParseAliYunVO it ->
                if (it.ip == nodeIp) {
                    nodeMac = it.nodeMac
                }
            }

        }
        return nodeMac
    }

    String getAliYunImgByAddressLinkAndImg(String addressLink, String img) {
        return """${addressLink}/extend/api/face/image/showImage?imageUrl=${img}"""
    }

    @Cached(expire = 1, timeUnit = TimeUnit.HOURS, name = "hiiadmin_fetchSchoolSynFirmByCampusIdAndFirm", key = "#campusId + '_' + #firm",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    SchoolSynFirm fetchSchoolSynFirmByCampusIdAndFirm(Long campusId, Integer firm) {
        SchoolSynFirm.findByCampusIdAndFirmAndStatus(campusId, firm, 1 as Byte)
    }
}

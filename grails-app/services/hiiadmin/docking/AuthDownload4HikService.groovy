package hiiadmin.docking

import grails.gorm.transactions.Transactional
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.ferries.FerriesService
import hiiadmin.module.docking.hk.DownloadRecordDetailVO
import hiiadmin.module.docking.vo.AuthDownloadDetailVO
import hiiadmin.school.*
import hiiadmin.school.device.DockingDeviceService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.joda.time.DateTime
import timetabling.docking.DockingDevice
import timetabling.docking.RelatedUser
import timetabling.gated.Ferries
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*

@Transactional
class AuthDownload4HikService {

    @Resource
    AntennaApi antennaApi

    DockingDeviceService dockingDeviceService

    RelatedUserService relatedUserService

    StudentService studentService

    TeacherService teacherService

    FerriesService ferriesService

    ParentService parentService

    UnitService unitService

    UnitStudentService unitStudentService

    GradeService gradeService

    UserEncodeInfoService userEncodeInfoService

    CampusService campusService

    AuthDownloadDetailVO transformDetailVO(long campusId, String parentResourceIndexCode, DownloadRecordDetailVO recordDetail) {
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        AuthDownloadDetailVO downloadDetailVO = new AuthDownloadDetailVO()
        if (recordDetail.downloadTime) {
            downloadDetailVO.downloadTime = new DateTime(recordDetail.downloadTime).millis
        }
        downloadDetailVO.downloadResult = recordDetail.persondownloadResult
        if (parentResourceIndexCode) {
            DockingDevice dockingDevice = dockingDeviceService.fetchDockingDeviceByParentIndexCodeAndChannelAndFirm(
                    parentResourceIndexCode, recordDetail.personDownloadDetail.channelNo as String, DeviceFirm.HK_EDU.firm)
            downloadDetailVO.parentResourceIndexCode = parentResourceIndexCode
            if (dockingDevice) {
                downloadDetailVO.parentResourceName = dockingDevice.parentResourceName
                downloadDetailVO.resourceIndexCode = dockingDevice.resourceIndexCode
                downloadDetailVO.resourceName = dockingDevice.resourceName
            }
        }
        String personId = recordDetail.personId
        RelatedUser relatedUser = relatedUserService.fetchByCampusIdAndPersonIdAndFirm(campusId, personId, DeviceFirm.HK_EDU.firm)
        if (relatedUser) {
            downloadDetailVO.userId = relatedUser.userId
            downloadDetailVO.userType = relatedUser.userType
            downloadDetailVO.personId = relatedUser.personId
            switch (relatedUser.userType) {
                case UserTypeEnum.TEACHER.type:
                    Teacher teacher = teacherService.fetchTeacherById(relatedUser.userId)
                    downloadDetailVO.userName = teacher?.name
                    downloadDetailVO.mobile =  userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id)
                    break
                case UserTypeEnum.STUDENT.type:
                    Student student = studentService.fetchStudentById(relatedUser.userId)
                    downloadDetailVO.userName = studentService.appendStudentStatus(student)
                    downloadDetailVO.userCode = student?.code
                    UnitStudent unitStudent = unitStudentService.fetchAdministrativeUnitStudentByStudentId(relatedUser.userId, false)
                    if (unitStudent) {
                        Unit unit = unitService.fetchUnitById(unitStudent.unitId)
                        Grade grade = gradeService.fetchGradeById(unitStudent.gradeId)
                        if(campus.type ==1){
                            String unitName = unit.alias ? unit.alias : unit.name
                            downloadDetailVO.memo = """${SectionType.getEnumByType(grade?.sectionId)?.name}${grade.name}${unitName}"""
                        }else {
                            downloadDetailVO.memo = unit.name
                        }

                    }
                    break
                case UserTypeEnum.FERRIES.type:
                    Ferries ferries = ferriesService.fetchFerriesByFerriesId(relatedUser.userId)
                    downloadDetailVO.userName = ferries?.name
                    downloadDetailVO.mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferries.id)
                    break
                case UserTypeEnum.PARENT.type:
                    Parent parent = parentService.fetchParentById(relatedUser.userId)
                    downloadDetailVO.userName = parent?.name
                    downloadDetailVO.mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parent.id)
                    break
            }
        }
        downloadDetailVO.cards = recordDetail.personDownloadDetail.cards
        downloadDetailVO.faces = recordDetail.personDownloadDetail.faces
        downloadDetailVO.fingers = recordDetail.personDownloadDetail.fingers
        downloadDetailVO.person = recordDetail.personDownloadDetail.person

        downloadDetailVO
    }
}

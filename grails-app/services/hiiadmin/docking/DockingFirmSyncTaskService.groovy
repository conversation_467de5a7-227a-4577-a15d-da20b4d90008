package hiiadmin.docking

import grails.gorm.transactions.Transactional
import org.springframework.transaction.annotation.Propagation
import timetabling.docking.DockingFirmSyncTask
import timetabling.docking.DockingFirmSyncTaskInfo

import static hiiadmin.ConstantEnum.OperationTerminal

@Transactional
class DockingFirmSyncTaskService {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    DockingFirmSyncTaskInfo saveDockingFirmSyncTaskInfo(long campusId, Long taskId, Long userId, Byte userType, Integer firm, String userName, String userCode, String mobile, Byte operatingStatus, String errorCode, String failureMessage, String jsonParam) {
        DockingFirmSyncTaskInfo taskInfo = new DockingFirmSyncTaskInfo(
                taskId: taskId,
                userId: userId,
                campusId: campusId,
                userType: userType,
                firm: firm,
                userName: userName,
                userCode: userCode,
                mobile: mobile,
                operatingStatus: operatingStatus,
                errorCode: errorCode,
                failureMessage: failureMessage,
                status: 1 as byte,
                jsonParam: jsonParam
        )
        saveDockingFirmSyncTaskInfo(taskInfo)
    }

    DockingFirmSyncTaskInfo saveDockingFirmSyncTaskInfo(DockingFirmSyncTaskInfo taskInfo) {
        taskInfo.save(failOnError: true, flush: true)
    }

    DockingFirmSyncTask fetchDockingFirmSyncTaskById(Long taskId) {
        DockingFirmSyncTask.findById(taskId)
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    DockingFirmSyncTask saveDockingFirmSyncTask(DockingFirmSyncTask syncTask) {
        syncTask.save(failOnError: true, flush: true)
    }

    DockingFirmSyncTask saveDockingFirmSyncTask(Long schoolId, Long campusId, Long operationUserId, Byte operationUserType, Byte operationType, Integer firm, String operationContent, Integer operationPath) {

        DockingFirmSyncTask task = new DockingFirmSyncTask(
                schoolId: schoolId,
                campusId: campusId,
                operationUserId: operationUserId,
                operationUserType: operationUserType,
                operationType: operationType,
                firm: firm,
                operationContent: operationContent,
                operationTerminal: OperationTerminal.PC.type,
                operatingTime: new Date(),
                status: 1 as Byte,
                operatingStatus: 3 as Byte,
                operationPath: operationPath
        )
        saveDockingFirmSyncTask(task)
    }

    List<DockingFirmSyncTaskInfo> fetchAllDockingFirmSyncTaskInfoByTaskIdAndOperatingStatus(Long taskId, Byte operatingStatus) {
        DockingFirmSyncTaskInfo.findAllByTaskIdAndOperatingStatusAndStatus(taskId, operatingStatus, 1 as byte)
    }

    DockingFirmSyncTaskInfo fetchLastDockingFirmSyncTaskInfoByUserIdAndUserTypeAndFirm(long campusId, long userId, byte userType, int firm) {
        DockingFirmSyncTaskInfo.findByCampusIdAndUserIdAndUserTypeAndFirmAndStatus(campusId, userId, userType, firm, 1 as byte, [sort: "id", order: "desc"])
    }
}

package hiiadmin.docking

import com.alibaba.fastjson.JSONObject
import grails.gorm.transactions.Transactional
import hiiadmin.apiCloud.AntennaDhYrApi
import timetabling.device.Device

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*

@Transactional
class DockingIssueService {

    @Resource
    AntennaDhYrApi antennaDhYrApi

    /**
     * TODO SynUserV2Service 中有些乱的相关懒加载引起注入null该方法直接调用domain
     * @param yrPersonId
     * @param campusId
     */
    void issueYrPerson4visitor(String yrPersonId, long campusId) {
        List<Device> deviceList = Device.findAllByCampusIdAndFirmAndTypeAndStatus(campusId, DeviceFirm.DH_YR.firm, DeviceType.GATE.type, 1 as byte)
        List<String> serialNumberList = deviceList*.serialNumber
        JSONObject jsonObject = new JSONObject()
        jsonObject.put("deviceIdList", serialNumberList)
        jsonObject.put("operateType", OperateType.ADD.type)
        antennaDhYrApi.authConfig4person(yrPersonId, jsonObject)
    }
}

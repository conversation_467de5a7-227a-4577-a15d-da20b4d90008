package hiiadmin.docking


import grails.gorm.transactions.Transactional
import org.joda.time.DateTime
import timetabling.docking.DockingPlatformStatusHistory

@Transactional
class DockingPlatformHistoryService {

    List<DockingPlatformStatusHistory> fetchAllDockingPlatformHistory(Long platformId, Long time, Integer firmStatus, int p, int s) {
        StringBuilder sb = new StringBuilder()
        Map<String, Object> map = buildHQL(sb, platformId, time, firmStatus)
        sb.append(" ORDER BY id DESC")
        map.put("max", s)
        map.put("offset", (p - 1) * s)
        DockingPlatformStatusHistory.findAll(sb.toString(), map)
    }

    Integer countDockingPlatformHistory(Long platformId, Long time, Integer firmStatus) {
        StringBuilder sb = new StringBuilder()
        sb.append("SELECT COUNT(1)")
        Map<String, Object> map = buildHQL(sb, platformId, time, firmStatus)
        DockingPlatformStatusHistory.executeQuery(sb.toString(), map)[0] as Integer
    }

    /**
     * 获取平台状态统计数据
     * @param platformId 平台ID
     * @param time 查询日期时间戳
     * @return 统计数据Map
     */
    Map<String, Object> getPlatformStatusStatistics(Long platformId, Long time) {
        DateTime dateTime = new DateTime(time)
        Date startDate = dateTime.withMillisOfDay(0).toDate()
        Date endDate = dateTime.millisOfDay().withMaximumValue().toDate()

        // 查询当天所有状态记录
        String hql = """FROM DockingPlatformStatusHistory
                       WHERE platformId = :platformId
                       AND dateCreated BETWEEN :start AND :end
                       ORDER BY dateCreated ASC"""

        List<DockingPlatformStatusHistory> historyList = DockingPlatformStatusHistory.findAll(hql, [
            platformId: platformId,
            start: startDate,
            end: endDate
        ])

        return calculateStatistics(historyList, startDate, endDate)
    }

    /**
     * 获取时间轴数据
     * @param platformId 平台ID
     * @param time 查询日期时间戳
     * @return 时间轴数据列表
     */
    List<Map<String, Object>> getTimelineData(Long platformId, Long time) {
        DateTime dateTime = new DateTime(time)
        Date startDate = dateTime.withMillisOfDay(0).toDate()
        Date endDate = dateTime.millisOfDay().withMaximumValue().toDate()

        String hql = """FROM DockingPlatformStatusHistory
                       WHERE platformId = :platformId
                       AND dateCreated BETWEEN :start AND :end
                       ORDER BY dateCreated ASC"""

        List<DockingPlatformStatusHistory> historyList = DockingPlatformStatusHistory.findAll(hql, [
            platformId: platformId,
            start: startDate,
            end: endDate
        ])

        return buildTimelineData(historyList, startDate, endDate)
    }

    private static Map<String, Object> buildHQL(StringBuilder sb, long platformId, Long time, Integer firmStatus) {
        Map<String, Object> map = [platformId: platformId]
        sb.append(" FROM DockingPlatformStatusHistory WHERE platformId = :platformId ")
        if (firmStatus != null) {
            sb.append(" AND firmStatus = :firmStatus")
            map.put("firmStatus", firmStatus)
        }
        if (time) {
            DateTime dateTime = new DateTime(time)
            sb.append(" AND dateCreated BETWEEN :start AND :end ")
            map.put("start", dateTime.withMillisOfDay(0).toDate())
            map.put("end", dateTime.millisOfDay().withMaximumValue().toDate())
        }
        return map
    }

    DockingPlatformStatusHistory createAndSaveDockingPlatformStatusHistory(Long platformId, Integer firmStatus, String message) {
        DockingPlatformStatusHistory statusHistory = new DockingPlatformStatusHistory(platformId: platformId,
                firmStatus: firmStatus,
                message: message,
                status: 1,)
        statusHistory.save(failOnError: true, flush: true)
    }

    /**
     * 计算统计数据
     */
    private Map<String, Object> calculateStatistics(List<DockingPlatformStatusHistory> historyList, Date startDate, Date endDate) {
        Map<String, Object> statistics = [:]

        if (!historyList) {
            return [
                onlineRate: "0%",
                abnormalDuration: "0小时0分",
                abnormalCount: 0
            ]
        }

        long totalDayMillis = endDate.time - startDate.time
        long abnormalMillis = 0
        int abnormalCount = 0

        // 计算异常时长和次数
        for (int i = 0; i < historyList.size(); i++) {
            DockingPlatformStatusHistory current = historyList[i]

            if (current.firmStatus == 3) { // 异常状态
                abnormalCount++

                // 计算异常持续时间
                Date abnormalStart = current.dateCreated
                Date abnormalEnd = endDate

                // 如果有下一条记录，异常结束时间为下一条记录时间
                if (i + 1 < historyList.size()) {
                    abnormalEnd = historyList[i + 1].dateCreated
                }

                abnormalMillis += (abnormalEnd.time - abnormalStart.time)
            }
        }

        // 计算在线率
        long onlineMillis = totalDayMillis - abnormalMillis
        double onlineRate = (onlineMillis * 100.0) / totalDayMillis

        // 格式化异常时长
        long abnormalHours = abnormalMillis / (1000 * 60 * 60)
        long abnormalMinutes = (abnormalMillis % (1000 * 60 * 60)) / (1000 * 60)

        statistics.put("onlineRate", String.format("%.1f%%", onlineRate))
        statistics.put("abnormalDuration", "${abnormalHours}小时${abnormalMinutes}分")
        statistics.put("abnormalCount", abnormalCount)

        return statistics
    }

    /**
     * 构建时间轴数据
     */
    private List<Map<String, Object>> buildTimelineData(List<DockingPlatformStatusHistory> historyList, Date startDate, Date endDate) {
        List<Map<String, Object>> timelineData = []

        if (!historyList) {
            return timelineData
        }

        for (int i = 0; i < historyList.size(); i++) {
            DockingPlatformStatusHistory current = historyList[i]
            Date segmentStart = current.dateCreated
            Date segmentEnd = endDate

            // 如果有下一条记录，段结束时间为下一条记录时间
            if (i + 1 < historyList.size()) {
                segmentEnd = historyList[i + 1].dateCreated
            }

            String statusText = getStatusText(current.firmStatus)
            String statusClass = getStatusClass(current.firmStatus)

            timelineData.add([
                startTime: segmentStart.time,
                endTime: segmentEnd.time,
                status: current.firmStatus,
                statusText: statusText,
                statusClass: statusClass,
                message: current.message ?: ""
            ])
        }

        return timelineData
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer firmStatus) {
        switch (firmStatus) {
            case 1: return "正常"
            case 3: return "异常"
            case 4: return "检测中"
            case 5: return "未知"
            default: return "未知"
        }
    }

    /**
     * 获取状态样式类
     */
    private String getStatusClass(Integer firmStatus) {
        switch (firmStatus) {
            case 1: return "normal"
            case 3: return "abnormal"
            case 4: return "checking"
            case 5: return "unknown"
            default: return "unknown"
        }
    }
}

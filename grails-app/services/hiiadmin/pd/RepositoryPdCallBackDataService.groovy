package hiiadmin.pd

import com.alibaba.fastjson2.JSONObject
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import org.joda.time.DateTime
import timetabling.pd.PdCallBackData

@Transactional
class RepositoryPdCallBackDataService {
    
    def createData(String callbackJson) {
        
        if (callbackJson == null || callbackJson.trim().length() == 0) {
            return
        }
        JSONObject jsonObject = JSONObject.parseObject(callbackJson)
        PdCallBackData pdCallBackData = new PdCallBackData()
        pdCallBackData.bizId = jsonObject.getString("bizId")
        pdCallBackData.bizType = jsonObject.getInteger("bizType")
        pdCallBackData.organizationId = jsonObject.getString("organizationId")
        pdCallBackData.syncAction = jsonObject.getString("syncAction")
        pdCallBackData.callBackJson = jsonObject.getJSONArray("bizData").toJSONString()
        Long pushTime = jsonObject.getLong("pushTime")
        if (!pushTime) {
            pushTime = new DateTime().millis
        }
        pdCallBackData.pushTime = pushTime
        pdCallBackData.status = 1
        pdCallBackData.dataStatus = ConstantEnum.DataStatus.NONE.status
        pdCallBackData.save(failOnError: true, flush: true)
    }
    
    List<PdCallBackData> fetchCallBackDataListByOrder() {
        return PdCallBackData.findAllByStatusAndDataStatus(1, ConstantEnum.DataStatus.NONE.status, [sort: "pushTime", order: "asc", max: 10, offset: 0])
    }
    
    def saveData(PdCallBackData pdCallBackData) {
        pdCallBackData.save(failOnError: true, flush: true)
    }
}

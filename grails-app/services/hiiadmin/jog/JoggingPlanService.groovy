package hiiadmin.jog

import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.google.common.collect.HashMultimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.mapstruct.jogging.JoggingRecordMapper
import hiiadmin.module.jogging.dto.JoggingRecordDTO
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.apache.commons.collections.CollectionUtils
import org.joda.time.DateTime
import timetabling.jog.*
import timetabling.org.Semester
import timetabling.org.Unit
import timetabling.org.UnitStudent

import javax.annotation.Resource
import java.text.DecimalFormat
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @Date 2022-11-30 17:23
 */

@Slf4j
@Transactional
class JoggingPlanService {

    UnitStudentService unitStudentService

    @Resource
    JoggingRecordMapper joggingRecordMapper

    UnitService unitService

    JoggingTimeService joggingTimeService

    JoggingPointService joggingPointService

    List<JoggingPlan> fetchAllJoggingPlanByCampusId(Long campusId) {
        JoggingPlan.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    JoggingPlan getJoggingPlan(Long id) {
        JoggingPlan.findById(id)
    }

    def saveJoggingPlan(JoggingPlan joggingPlan) {
        joggingPlan.save(failOnError: true)
    }

    def updateJoggingPlan(JoggingPlan joggingPlan, Long promoterId, String name, String unitIdCodes, Long startDate, Long endDate, Integer highTime, Integer lowTime, Integer pointNumber, String cylinderNumber, String timeJson, String pointDeviceJson) {
        if (name) {
            joggingPlan.name = name
        }
        if (unitIdCodes) {
            joggingPlan.unitIdCodes = unitIdCodes
        }
        if (startDate) {
            joggingPlan.startDate = new Date(startDate)
        }
        if (endDate) {
            joggingPlan.endDate = new Date(endDate)
        }
        if (highTime) {
            joggingPlan.highTime = highTime
        }
        if (lowTime) {
            joggingPlan.lowTime = lowTime
        }
        if (pointNumber) {
            joggingPlan.pointNumber = pointNumber
        }
        if (cylinderNumber) {
            joggingPlan.totalNumber = new BigDecimal(cylinderNumber)
        }

        joggingPlan.timeJson = timeJson
        joggingPlan.pointDeviceJson = pointDeviceJson
        joggingPlan.promoterId = promoterId
        saveJoggingPlan(joggingPlan)

        joggingPlan
    }

    List<JoggingPoint> fetchAllPointByPlanId(Long planId) {
        JoggingPoint.findAllByJoggingPlanIdAndStatus(planId, 1 as byte, [sort: 'idx', order: 'asc'])
    }

    List<JoggingTime> fetchAllJoggingTimeByPlanIdByLastDay(Long planId, Long dayStamp) {
        int dayOfWeek = new DateTime(dayStamp).getDayOfWeek()
        JoggingTime.findAllByJoggingPlanIdAndStatusAndWeekDay(planId, 1 as byte, dayOfWeek)
    }

    List<JoggingRecord> fetchAllJoggingRecordByBetweenTime(Long campusId, Long dayTimeStamp, Date startTime, Date endTime) {
        JoggingRecord.findAllByCampusIdAndDayTimeStampAndPointTimeBetweenAndStatus(campusId, dayTimeStamp, startTime, endTime, 1 as byte)
    }

    @Transactional(rollbackFor = Exception.class)
    def delete(Long campusId, Long stamp) {
        JoggingPlan joggingPlan = JoggingPlan.findByCampusIdAndStatus(campusId, 1 as byte)
        if (joggingPlan) {
            List<HistoryJoggingTime> historyJoggingTimeList = HistoryJoggingTime.findAllByCampusIdAndStatus(campusId, 1 as byte)
            List<JoggingStudent> joggingStudentList = JoggingStudent.findAllByCampusIdAndStatus(campusId, 1 as byte)
            List<JoggingStudentCompletionRate> studentRates = JoggingStudentCompletionRate.findAllByCampusIdAndStatus(campusId, 1 as byte)
            List<JoggingUnitCompletionRate> unitRates = JoggingUnitCompletionRate.findAllByCampusIdAndStatus(campusId, 1 as byte)
            List<JoggingTimeStudent> joggingTimeStudentList = JoggingTimeStudent.findAllByCampusIdAndStatus(campusId, 1 as byte)
            Long yesterday = new DateTime(stamp).withTimeAtStartOfDay().minusDays(1).millis
            List<JoggingStudentCompletionDay> joggingStudentCompletionDayList = JoggingStudentCompletionDay.findAllByCampusIdAndDayDatestampAndStatus(campusId, yesterday, 1 as byte)

            if (historyJoggingTimeList) {
                historyJoggingTimeList*.status = 0 as byte
                historyJoggingTimeList*.save(failOnError: true)
            }

            if (joggingStudentList) {
                joggingStudentList*.status = 0 as byte
                joggingStudentList*.save(failOnError: true)
            }

            if (studentRates) {
                studentRates*.status = 0 as byte
                studentRates*.save(failOnError: true)
            }

            if (unitRates) {
                unitRates*.status = 0 as byte
                studentRates*.save(failOnError: true)
            }

            if (joggingTimeStudentList) {
                joggingTimeStudentList*.status = 0 as byte
                joggingTimeStudentList*.save(failOnError: true)
            }
            
            if (joggingStudentCompletionDayList) {
                joggingStudentCompletionDayList*.status = 0 as byte
                joggingStudentCompletionDayList*.save(failOnError: true)
            }
        }
    }

    /**
     * 今日计算昨日数据
     * @param campusId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    def calculateStudentJogging(Long campusId, Long todayStamp) {
        List<JoggingPlan> joggingPlanList = fetchAllJoggingPlanByCampusId(campusId)
        HashMultimap<String, String> map = [:]
        if (joggingPlanList != null && joggingPlanList?.size() > 0) {
            map = unitStudentService.transformUnitCodeUnitIdMultiMap(campusId, null)
        }
        List<UnitStudent> allUnitStudents = []
        Long yesterdayStamp = new DateTime(todayStamp).minusDays(1).withTimeAtStartOfDay().millis
        Long minus2DayStamp = new DateTime(todayStamp).minusDays(2).withTimeAtStartOfDay().millis
        Long semesterId = fetchCurrentSemesterByCampusId(campusId, false)?.id
        joggingPlanList?.each {
            JoggingPlan joggingPlan ->
                Long planStartDateStamp = new DateTime(joggingPlan.startDate?.time).withTimeAtStartOfDay().millis
                Long planEndDateStamp = new DateTime(joggingPlan.endDate?.time).plusDays(1).withTimeAtStartOfDay().millis
                List<JoggingStudent> joggingStudentList = []
                List<JoggingTimeStudent> joggingTimeStudentList = []

                List<Long> unitIdList = []
                List<UnitStudent> unitStudentList = []
                List<String> unitCodeList = joggingPlan.unitIdCodes.split(",")
                unitCodeList.each {
                    List<String> encodeIdList = map.get(it).toList()
                    List<Long> decodeUnitIdList = encodeIdList?.collect {
                        PhenixCoder.decodeId(it)
                    }
                    unitIdList.addAll(decodeUnitIdList)
                }

                unitIdList?.removeAll([null])
                if (unitIdList != null && unitIdList.size() > 0) {
                    unitStudentList = UnitStudent.findAllByCampusIdAndStatusAndUnitIdInList(campusId, 1 as byte, unitIdList)
                    allUnitStudents.addAll(unitStudentList)
                }
                
                if (todayStamp >= planStartDateStamp && todayStamp <= planEndDateStamp) {
                    List<JoggingPoint> joggingPointList = fetchAllPointByPlanId(joggingPlan.id)
                    List<Long> pointIdList = joggingPointList*.pointId
                    Integer pointNum = pointIdList?.size() ?: 0
                    // 半圈至少需要的打卡点位
                    Integer halfPointNum = ((pointNum + 3)  / 2) as Integer
                    List<JoggingRecordDTO> joggingRecordDTOTotalList = []
                    Map<Long, List<JoggingRecordDTO>> studentIdRecordMap = [:]

                    boolean initJoggingStudent = false

                    if (pointIdList) {
                        List<Node> nodeList = transformPointNode(pointIdList)
                        List<JoggingTime> joggingTimeList = fetchAllJoggingTimeByPlanIdByLastDay(joggingPlan.id, yesterdayStamp)
                        List<HistoryJoggingTime> historyJoggingTimeList = []
                        joggingTimeList.each {
                            JoggingTime joggingTime ->
                                if (joggingTime.isStart) {
                                    initJoggingStudent = true
                                    Date datetime = TimeUtils.getDateStartTime(yesterdayStamp)
                                    Date startDateTime = TimeUtils.getDataForSqlTime(datetime, joggingTime.startTime)
                                    Date endDateTime = TimeUtils.getDataForSqlTime(datetime, joggingTime.endTime)
                                    List<JoggingRecord> joggingRecordList = fetchAllJoggingRecordByBetweenTime(campusId, yesterdayStamp, startDateTime, endDateTime)
                                    if (joggingRecordList) {
                                        joggingRecordList.each {
                                            JoggingRecordDTO dto = joggingRecordMapper.convert2JoggingRecordDTO(it, joggingTime.id)
                                            joggingRecordDTOTotalList.add(dto)
                                        }
                                    }


                                    HistoryJoggingTime historyJoggingTime = new HistoryJoggingTime(
                                            campusId: campusId,
                                            joggingPlanId: joggingPlan.id,
                                            dayTimeStamp: yesterdayStamp,
                                            startDate: startDateTime,
                                            endDate: endDateTime,
                                            status: 1 as byte
                                    )

                                    historyJoggingTimeList.add(historyJoggingTime)
                                }
                        }

                        joggingRecordDTOTotalList.removeAll([null])

                        if (CollectionUtils.isNotEmpty(joggingRecordDTOTotalList)) {
                            studentIdRecordMap = joggingRecordDTOTotalList.groupBy { it.studentId }
                        }

                        if (CollectionUtils.isNotEmpty(historyJoggingTimeList)) {
                            historyJoggingTimeList*.save(failOnError: true)
                        }

                        if (initJoggingStudent) {
                            unitStudentList.each {
                                UnitStudent unitStudent ->
                                    List<JoggingRecordDTO> joggingRecords = []
                                    joggingRecords = studentIdRecordMap.get(unitStudent.studentId)
                                    joggingRecords?.sort { it.pointTime?.getTime() }

                                    Map joggingRecordStudentMap = joggingRecords?.groupBy { it.joggingTimeId }
                                    BigDecimal num = 0
                                    Byte status = ConstantEnum.JoggingStatus.NO_JOGGING.type

                                    joggingRecordStudentMap?.entrySet()?.each {
                                        BigDecimal timeNum = 0
                                        if (it.value) {
                                            if (it.value?.size() >= halfPointNum) {
                                                List<JoggingDeviceDTO> dtoList = transform2JoggingDeviceDTO(it.value)
                                                def calculationMap = calculationRules(nodeList, pointNum, joggingPlan.lowTime, joggingPlan.highTime, dtoList, halfPointNum)
                                                num = num + calculationMap.num
                                                timeNum = calculationMap.num
                                            } else {
                                                log.info("时间段id@${it.key}，学生id@${unitStudent?.id}，跑操记录小于点位数".toString())
                                            }
                                        } else {
                                            log.info("时间段id@${it.key}学生没有跑操记录studentId@${unitStudent?.studentId}.toString()")
                                        }
                                        
                                        JoggingTimeStudent joggingTimeStudent = new JoggingTimeStudent()
                                        joggingTimeStudent.campusId = campusId
                                        joggingTimeStudent.studentId = unitStudent.studentId
                                        joggingTimeStudent.joggingPlanId = joggingPlan.id
                                        joggingTimeStudent.joggingTimeId = it.key
                                        joggingTimeStudent.realNumber = timeNum
                                        
                                        joggingTimeStudent.status = 1 as byte
                                        joggingTimeStudent.dayTimeStamp = yesterdayStamp
                                        joggingTimeStudent.semesterId = semesterId
                                        joggingTimeStudentList.add(joggingTimeStudent)
                                    }

                                    if (num >= joggingPlan.totalNumber) {
                                        status = ConstantEnum.JoggingStatus.FINISH.type
                                    } else if (num > 0 && num < joggingPlan.totalNumber) {
                                        status = ConstantEnum.JoggingStatus.NOT_FINISH.type
                                    }

                                    JoggingStudent joggingStudent = new JoggingStudent(
                                            campusId: campusId,
                                            sectionId: unitStudent.sectionId,
                                            gradeId: unitStudent.gradeId,
                                            unitId: unitStudent.unitId,
                                            studentId: unitStudent.studentId,
                                            joggingPlanId: joggingPlan.id,
                                            joggingStatus: status,
                                            realNumber: num,
                                            dayTimeStamp: yesterdayStamp,
                                            status: 1 as byte,
                                            semesterId: semesterId
                                    )

                                    joggingStudentList << joggingStudent
                            }
                        } else {
                            log.info("未开启跑操计划，不做处理CampusId@${campusId}, plandId@${joggingPlan.id}")
                        }
                    } else {
                        log.info("跑操计划未关联点位planId@${joggingPlan.id}".toString())
                    }
                } else {
                    log.info("不在跑操设置日期范围内，不做处理CampusId@${campusId}, plandId@${joggingPlan.id}")
                }

                if (joggingStudentList) {
                    joggingStudentList*.save(failOnError: true)
                }
                
                if (joggingTimeStudentList) {
                    joggingTimeStudentList*.save(failOnError: true)
                }
        }

        def yesterdayStudentCompletionDayMap = createDailyStudentCompletionDay(allUnitStudents, campusId, semesterId, yesterdayStamp, minus2DayStamp)

        // 统计跑操率
        calculationStudentAndUnitRate(campusId, yesterdayStamp, yesterdayStudentCompletionDayMap)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiAdmin_schoolS_fetchAllSemesterByCampusId_joggingService", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    Semester fetchCurrentSemesterByCampusId(Long campusId, boolean cache = true) {
        Semester.findByCampusIdAndCurrentTypeAndStatusGreaterThanEquals(campusId, 1 as byte, 1 as byte)
    }


    /**
     * 计算圈数规则
     */
    def calculationRules(List<Node> nodeList, Integer pointNum, Integer minMills, Integer maxMills, List<JoggingDeviceDTO> dtoList, Integer halfPointNum) {
        CalculationRules calculationRules = new CalculationRules()
        // 圈数
        BigDecimal num = 0

        if (dtoList != null && dtoList.size() >= halfPointNum) {
            dtoList.eachWithIndex { JoggingDeviceDTO entry, int i ->
                if (i == 0) {
                    calculationRules.first = entry.pointId
                }
                calculationRules.happenedTime = entry.happenedTime

                if (i < dtoList.size() - 1) {
                    Node node = nodeList.find { it.value == entry.pointId }
                    JoggingDeviceDTO joggingDeviceDTO = dtoList.get(i + 1)
                    if (node) {
                        Integer timeSeconds = TimeUtils.getDateBetween(calculationRules.happenedTime, joggingDeviceDTO.happenedTime, ChronoUnit.SECONDS) as Integer
                        // 正序
                        if (calculationRules.next) {
                            boolean equals = node.next.value == joggingDeviceDTO.pointId
                            if (equals && timeSeconds >= minMills && timeSeconds <= maxMills) {
                                calculationRules.num = calculationRules.num + 1

                            } else {
                                // 正序转逆序计算
                                boolean nextEquals = node.pre.value == joggingDeviceDTO.pointId
                                if (nextEquals && timeSeconds >= minMills && timeSeconds <= maxMills) {
                                    calculationRules.num = 1
                                    calculationRules.next = false
                                    calculationRules.first = entry.pointId
                                } else {
                                    // 相同点位，间隔默认40s
                                    similarPoint(calculationRules, node, joggingDeviceDTO, entry, timeSeconds)
                                }
                            }
                        } else {
                            // 逆序
                            boolean preEquals = node.pre.value == joggingDeviceDTO.pointId
                            if (preEquals && timeSeconds >= minMills && timeSeconds <= maxMills) {
                                calculationRules.num = calculationRules.num + 1
                            } else {
                                // 逆序转正序计算
                                boolean nextEquals = node.next.value == joggingDeviceDTO.pointId
                                if (nextEquals && timeSeconds >= minMills && timeSeconds <= maxMills) {
                                    calculationRules.num = 1
                                    calculationRules.next = true
                                    calculationRules.first = entry.pointId
                                } else {
                                    // 相同点位判断
                                    similarPoint(calculationRules, node, joggingDeviceDTO, entry, timeSeconds)
                                }
                            }
                        }
                    } else {
                        calculationRules.first = joggingDeviceDTO.pointId
                    }
                }

                if (i == dtoList.size() - 1) {
                    if (calculationRules.num >= pointNum && calculationRules.first == entry.pointId) {
                        num += 1
                        calculationRules.num = 0
                        calculationRules.time = dtoList.get(i).happenedTime
                    } else if (calculationRules.num >= (halfPointNum - 1) && calculationRules.num < pointNum) {
                        num += 0.5
                    }
                } else {
                    if (calculationRules.num >= pointNum && calculationRules.first == dtoList.get(i + 1).pointId) {
                        num += 1
                        calculationRules.num = 0
                        calculationRules.time = dtoList.get(i + 1).happenedTime
                    } else if (calculationRules.num >= (halfPointNum - 1) && calculationRules.num < pointNum) {
                        // 算半圈需要根据正序/逆序计算下一个点位是否成功,[halfPointNum, pointNum + 1)区间内有失败可视为半圈
                            boolean nextNodeSuccess = nextNodePointSuccess(calculationRules, 40, minMills, maxMills, i, dtoList, nodeList)
                            if (nextNodeSuccess) {
                                num += 0.5
                            }
                    }
                }
            }
        } else {
            return [num: num, time: null]
        }

        return [num: num, time: calculationRules.time]
    }
    
    void similarPoint(CalculationRules calculationRules, Node node, JoggingDeviceDTO joggingDeviceDTO, JoggingDeviceDTO entry, Integer timeSeconds) {
        boolean similar = node.value == joggingDeviceDTO.pointId
        if (similar && timeSeconds <= 40) {
            calculationRules.happenedTime = entry.happenedTime
        } else {
            calculationRules.num = 0
            calculationRules.first = joggingDeviceDTO.pointId
        }
    }

    // 判断是否需要算半圈
    boolean nextNodePointSuccess(CalculationRules calculationRules, Integer defaultSectionMills, Integer minMills, Integer maxMills, int i, List<JoggingDeviceDTO> dtoList, List<Node> nodeList) {
        boolean flag = false
        int size = dtoList.size() ?: 0
        if (i <= (size- 3)) {
            JoggingDeviceDTO nextDTO = dtoList.get(i + 1)
            JoggingDeviceDTO next2DTO = dtoList.get(i + 2)
            
            boolean similar = nextDTO.pointId == next2DTO.pointId
            Integer seconds = TimeUtils.getDateBetween(nextDTO.happenedTime, next2DTO.happenedTime, ChronoUnit.SECONDS) as Integer
            if (similar) {
                if (seconds <= defaultSectionMills) {
                    return false
                } else {
                    flag = true
                }
            } else {
                Node nextNode = nodeList.find{it.value == nextDTO.pointId}
                boolean next = false
                boolean pre = false
                if (calculationRules.next) {
                    next = nextNode.next.value == next2DTO.pointId
                } else {
                    pre = nextNode.pre.value == next2DTO.pointId
                }
                
                if (next || pre) {
                    if (seconds >= minMills && seconds <= maxMills) {
                        return false
                    } else {
                        flag = true
                    }
                }
            }
        } else {
            return false
        }
        
        
        flag
    }

    @Transactional(rollbackFor = Exception.class)
    def calculationStudentAndUnitRate(Long campusId, Long yesterdayStamp, Map<Long, Integer> yesterdayStudentCompletionDayMap) {
        Long semesterId = fetchCurrentSemesterByCampusId(campusId, false)?.id
        def studentIdCylinderNumMap = countSumCylinderByStudentId(campusId, semesterId)

        
        List<JoggingStudentCylinder> cylinderList = JoggingStudentCylinder.findAllByCampusIdAndSemesterIdAndStatus(campusId, semesterId, 1 as byte)


        List<JoggingStudentCompletionRate> rateList = []
        List<JoggingUnitCompletionRate> unitRateList = []
        List<UnitStudent> unitStudentList = unitStudentService.fetchAllNormalUnitStudentByCampusId(campusId)
        Map<Long, Long> studentIdSectionIdMap = unitStudentList?.collectEntries {[it.studentId, it.sectionId]}
        Map<Long, Long> studentIdGradeIdMap = unitStudentList?.collectEntries {[it.studentId, it.gradeId]}
        Map<Long, Long> studentIdUnitIdMap = unitStudentList?.collectEntries {[it.studentId, it.unitId]}

        cylinderList.each {
            JoggingStudentCylinder cylinder ->
                JoggingStudentCompletionRate rate = new JoggingStudentCompletionRate(
                        campusId: campusId,
                        studentId: cylinder.studentId,
                        sectionId: studentIdSectionIdMap.get(cylinder?.studentId),
                        gradeId: studentIdGradeIdMap.get(cylinder?.studentId),
                        unitId: studentIdUnitIdMap.get(cylinder?.studentId),
                        dayDatestamp: yesterdayStamp,
                        semesterId: semesterId,
                        status: 1 as byte
                )
                // 查询今日之前的已跑操圈数
                BigDecimal num = studentIdCylinderNumMap.get(cylinder.studentId) ?: 0
                rate.completionNumber = num

                Integer completedNum = yesterdayStudentCompletionDayMap?.get(cylinder.studentId) ?: 0
                
                rate.completionDay = completedNum
                if (cylinder.totalDays == 0) {
                    rate.rate = "0"
                } else if (completedNum && completedNum >= cylinder.totalDays) {
                    rate.rate = "100"
                } else {
                    DecimalFormat df = new DecimalFormat("#")
                    String r = df.format((completedNum / (double) cylinder.totalDays) * 100)
                    rate.rate = r
                }

                rateList << rate
        }

        Map<Long, List<JoggingStudentCompletionRate>> unitIdRateMap = [:]
        List<Unit> unitList = unitService.fetchAllAdminUnitViaCampusId(campusId)
        Map<Long, Long> unitIdSectionIdMap = unitList?.collectEntries {[it.id, it.sectionId]}
        Map<Long, Long> unitIdGradeIdMap = unitList?.collectEntries {[it.id, it.gradeId]}
        unitIdRateMap = rateList.groupBy { it.unitId }
        unitIdRateMap.remove(null)
        unitIdRateMap.each { entry ->
            List<JoggingStudentCompletionRate> studentCompletionRateList = unitIdRateMap.get(entry.key)
            Integer totalStudent = studentCompletionRateList.size() ?: 0
            Integer alreadyComplete = studentCompletionRateList.findAll { it.rate == "100" }?.size() ?: 0
            JoggingUnitCompletionRate unitRate = new JoggingUnitCompletionRate(
                    campusId: campusId,
                    sectionId: unitIdSectionIdMap.get(entry.key),
                    gradeId: unitIdGradeIdMap.get(entry.key),
                    unitId: entry.key,
                    semesterId: semesterId,
                    status: 1 as byte,
                    dayDatestamp: yesterdayStamp
            )

            if (totalStudent == 0) {
                unitRate.rate = "0"
            } else if (alreadyComplete > 0 && alreadyComplete >= totalStudent) {
                unitRate.rate = "100"
            } else {
                DecimalFormat df = new DecimalFormat("#")
                String r = df.format((alreadyComplete / (double) totalStudent) * 100)
                unitRate.rate = r
            }

            unitRateList << unitRate
        }

        if (rateList) {
            rateList*.save(fialOnError: true)
        }

        if (unitRateList) {
            unitRateList*.save(failOnError: true)
        }
    }

    @Transactional(rollbackFor = Exception.class)
    def createDailyStudentCompletionDay(List<UnitStudent> unitStudentList, Long campusId, Long semesterId, Long stamp, Long minus2DayStamp) {
        List<JoggingStudentCompletionDay> joggingStudentCompletionDayList = []
        Map<Long, Integer> yesterdayStudentCompletionDayMap = [:]
        if (unitStudentList) {
            unitStudentList.removeAll([null])
            unitStudentList = unitStudentList?.unique{it.studentId}
            
            // 查询昨日完成天数
            def studentIdCompletedNumMap = countYesterdayCompletedByStudentId(campusId, semesterId, minus2DayStamp)
            def studentYesterdayCompletedNumMultimap = countCompletedByStudentId(campusId, semesterId, stamp)
            unitStudentList.each {
                Integer alreadyNum = studentIdCompletedNumMap?.get(it.studentId) ?: 0
                
                // 查询当天完成的
                Set<Byte> yesterdayCompletedNum = studentYesterdayCompletedNumMultimap?.get(it.studentId)
                if (yesterdayCompletedNum?.size() == 1 && yesterdayCompletedNum != null) {
                    Byte completedFlag = yesterdayCompletedNum?.toList()?.get(0)
                    // set集合为1，代表该学生所在全部跑操计划都已完成
                    if (completedFlag!= null && completedFlag == 1 as byte) {
                        alreadyNum += 1
                    }
                }

                yesterdayStudentCompletionDayMap.put(it.studentId, alreadyNum)
                
                JoggingStudentCompletionDay joggingStudentCompletionDay = new JoggingStudentCompletionDay(
                        completionDay: alreadyNum,
                        campusId: campusId,
                        semesterId: semesterId,
                        studentId: it.studentId,
                        dayDatestamp: stamp,
                        status: 1 as byte
                )

                joggingStudentCompletionDayList << joggingStudentCompletionDay
            }
        }
        
        if (joggingStudentCompletionDayList) {
            joggingStudentCompletionDayList*.save(failOnError: true)
        }

        yesterdayStudentCompletionDayMap
    }

    def countSumCylinderByStudentId(Long campusId, Long semesterId) {
        Map<Long, BigDecimal> studentIdCylinderNumMap = [:]
        String hql = """ SELECT js.studentId, SUM(js.realNumber) FROM JoggingStudent js 
                            WHERE js.campusId = :campusId 
                            AND js.semesterId = :semesterId 
                            AND js.status = 1 
                            GROUP BY js.studentId """

        def re = JoggingStudent.executeQuery(hql, [campusId: campusId, semesterId: semesterId])
        re.each {
            Long studentId = it[0] as Long
            BigDecimal num = it[1] as BigDecimal

            studentIdCylinderNumMap.put(studentId, num)
        }

        studentIdCylinderNumMap
    }
    
    def countCompletedByStudentId(Long campusId, Long semesterId, Long stamp) {
        HashMultimap<Long, Byte> studentIdNumMap = HashMultimap.create()
        String hql = """ SELECT js.studentId, js.joggingPlanId, js.joggingStatus FROM JoggingStudent js 
                            WHERE js.campusId = :campusId 
                            AND js.semesterId = :semesterId 
                            AND js.status = 1
                            AND js.dayTimeStamp = :stamp
                            GROUP BY js.studentId, js.joggingPlanId, js.joggingStatus """

        def re = JoggingStudent.executeQuery(hql, [campusId: campusId, semesterId: semesterId, stamp: stamp])
        re.each {
            Long studentId = it[0] as Long
            Byte num = it[2] as Byte
            studentIdNumMap.put(studentId, num)
        }

        studentIdNumMap
    }

    def countYesterdayCompletedByStudentId(Long campusId, Long semesterId, Long stamp) {
        Map<Long, Integer> studentIdNumMap = [:]
        String hql = """ SELECT jsc.studentId, jsc.completionDay FROM JoggingStudentCompletionDay jsc
                            WHERE jsc.campusId = :campusId 
                            AND jsc.semesterId = :semesterId 
                            AND jsc.status = 1
                            AND jsc.dayDatestamp = :stamp
                            GROUP BY jsc.studentId, jsc.completionDay """

        def re = JoggingStudentCompletionDay.executeQuery(hql, [campusId: campusId, semesterId: semesterId, stamp: stamp])
        re.each {
            Long studentId = it[0] as Long
            Integer num = it[1] as Integer
            studentIdNumMap.put(studentId, num)
        }

        studentIdNumMap
    }


    /**
     * 获取设备id,点位id  map
     * @param campusId
     * @return
     */
    def transformDeviceIdPoint(Long campusId) {
        List<PointDevice> pointDeviceList = PointDevice.findAllByCampusIdAndStatus(campusId, 1 as byte)
        Map<Long, Long> deviceIdPointMap = [:]
        pointDeviceList.each {
            deviceIdPointMap.put(it.deviceId, it.pointId)
        }

        deviceIdPointMap
    }


    /**
     * 组装点位
     * @param pointIdList
     * @return
     */
    def transformPointNode(List<Long> pointIdList) {
        List<Node> nodeList = []
        if (pointIdList != null && pointIdList.size() > 0) {
            if (pointIdList.size() == 1) {
                Long point = pointIdList.get(0)
                Node node = new Node()
                Node pre = new Node()
                Node next = new Node()
                node.value = point
                pre.value = point
                next.value = point
                node.pre = pre
                node.next = next
                nodeList.add(node)
                return nodeList
            }
            pointIdList.eachWithIndex { long entry, int i ->
                Node node = new Node()
                Node pre = new Node()
                Node next = new Node()
                if (i == 0) {
                    node.value = entry
                    pre.value = pointIdList.get(pointIdList.size() - 1)
                    next.value = pointIdList.get(i + 1)
                    node.pre = pre
                    node.next = next
                    nodeList.add(node)
                } else if (i == pointIdList.size() - 1) {
                    node.value = pointIdList.get(i)
                    pre.value = pointIdList.get(i - 1)
                    next.value = pointIdList.get(0)
                    node.pre = pre
                    node.next = next
                    nodeList.add(node)
                } else {
                    node.value = pointIdList.get(i)
                    pre.value = pointIdList.get(i - 1)
                    next.value = pointIdList.get(i + 1)
                    node.pre = pre
                    node.next = next
                    nodeList.add(node)
                }
            }
        }

        return nodeList
    }


    def transform2JoggingDeviceDTO(List<JoggingRecordDTO> joggingRecordList) {
        List<JoggingDeviceDTO> joggingDeviceDTOList = []
        joggingRecordList.each {
            JoggingDeviceDTO joggingDeviceDTO = new JoggingDeviceDTO()
            joggingDeviceDTO.pointId = it.pointId
            joggingDeviceDTO.happenedTime = it.pointTime
            joggingDeviceDTOList.add(joggingDeviceDTO)
        }

        joggingDeviceDTOList
    }

    def deleteJoggingPlanById(Long campusId, Long id) {
        JoggingPlan joggingPlan = getJoggingPlan(id)
        joggingPlan.status = 0
        saveJoggingPlan(joggingPlan)
        joggingPointService.deleteJoggingPointByCampusIdAndJoggingPlanId(campusId, id)
        joggingTimeService.deleteJoggingTimeByCampusIdAndJoggingPlanId(campusId, id)
    }

    class Node {
        Node pre
        Node next
        Long value
    }

    class CalculationRules {
        Integer num = 0
        Long first
        Date time
        boolean next = true
        Date happenedTime
    }

    class JoggingDeviceDTO {
        Long pointId
        Date happenedTime
    }

}

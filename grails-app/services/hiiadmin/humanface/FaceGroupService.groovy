package hiiadmin.humanface

import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.apiCloud.ALiYunApiInvoker
import hiiadmin.ferries.FerriesService
import hiiadmin.ferries.FerriesStudentService
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.pic.CheckFaceService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.BedService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.visitor.VisitorService
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import timetabling.ParentStudent
import timetabling.building.Bed
import timetabling.gated.Ferries
import timetabling.gated.FerriesStudent
import timetabling.humanface.FaceGroup
import timetabling.humanface.pic.CheckFace
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus
import timetabling.visitor.Visitor
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major

import static hiiadmin.ConstantEnum.*

@Transactional
@Slf4j
class FaceGroupService {

    @Lazy
    TeacherService teacherService

    @Lazy
    StudentService studentService

    @Lazy
    ParentService parentService

    @Lazy
    FerriesService ferriesService

    FerriesStudentService ferriesStudentService

    @Lazy
    VisitorService visitorService

    @Lazy
    UnitService unitService

    @Lazy
    GradeService gradeService

    @Lazy
    BedService bedService

    @Autowired
    ALiYunApiInvoker aLiYunApiInvoker

    UserEncodeInfoService userEncodeInfoService

    CheckFaceService checkFaceService

    FacultyService facultyService

    MajorService majorService


    def findTeacherFaceByCampusIdAndSearchValuePage(Long campusId, Boolean gender, String searchValue, Long departmentId, int resultStatus = 99, byte userType = 99 as byte, int p, int s) {
        List<TeacherVO> teacherList = teacherService.findTeacherByCampusIdAndSearchValue(campusId, gender, searchValue, departmentId, resultStatus, userType, p, s)
        List<FaceGroupVO> faceGroupVOList = []
        if (teacherList != null && teacherList.size() > 0) {
            List<Long> teacherIds = teacherList.collect { PhenixCoder.decodeId(it.id)}
            Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacherIds)
            teacherList.each {
                TeacherVO teacher ->
                    FaceGroupVO vo = FaceGroupVO.setFaceGroupVoToTeacherVO(teacher)
                    int status = 3
                    if (vo.pic) {
                        CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUrlMd5AndName(campusId, teacher.avatar, teacher?.name)
                        if (checkFace != null) {
                            status = checkFace.resultStatus
                            if (checkFace.checkMessage.equals("success")) {
                                vo.checkMessage = "-"
                            } else
                                vo.checkMessage = checkFace.checkMessage
                        }
                    }
                    vo.resultStatus = status
                    vo.mobile = teacherIdMobileMap.get(PhenixCoder.decodeId(teacher.id))
                    faceGroupVOList << vo
            }
        }
        //如果有搜索名称，就只放回当前页面
        int total = (searchValue ? teacherList.size() : teacherService.countTeacherBySchoolIdAndGender(campusId, gender, searchValue, departmentId, resultStatus))
        [list: faceGroupVOList, total: total]
    }

    def findStudentFaceByCampusIdAndSearchValuePage(Long campusId, Boolean gender, String searchValue, Long sectionId, Long gradeId, Long unitId, Long buildingId, Long layerId, Long doorPlateId, int resultStatus, byte userType, int p, int s, Long facultyId, Long majorId) {
        
        Campus campus = Campus.findById(campusId)
        
        List<Student> studentList = studentService.findAllStudentByCampusIdAndSearchValue(campusId, gender, searchValue, sectionId, gradeId, unitId, doorPlateId, layerId, buildingId, resultStatus, p, s, facultyId, majorId)
        int total = studentService.countStudentByCampusIdAndSearchValue(campusId, gender, searchValue, sectionId, gradeId, unitId, doorPlateId, layerId, buildingId, resultStatus, facultyId, majorId)
        List<FaceGroupVO> faceGroupVOList = []
        if (studentList != null && studentList.size() > 0) {
            List<Long> studentIdList = studentList*.id
            Map<Long, Unit> studentIdUnitMap = unitService.getStudentIdUnitMapByStudentIdList(campusId, studentIdList)
            List<Bed> bedList = bedService.fetchAllBedByStudentIdInList(studentIdList)
            Map<Long, Bed> studentIdBedMap = [:]
            bedList.each {
                bed ->
                    studentIdBedMap.put(bed.studentId, bed)
            }
            List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)
            Map<Long, Grade> gradeIdGradeMap = [:]
            gradeList.each {
                grade ->
                    gradeIdGradeMap.put(grade.id, grade)
            }
            
            Map<Long, Faculty> facultyMap = [:]
            Map<Long, Major> majorMap = [:]
            
            if (campus && campus.type == 2 as byte) {
                List<Faculty> facultyList = facultyService.fetchAllFacultyByCampus(campusId)
                facultyMap = facultyList?.collectEntries {[it.id, it]}
                List<Major> majorList = majorService.fetchAllMajorByCampus(campusId)
                majorMap = majorList?.collectEntries{[it.id, it]}
            }

            
            studentList.each {
                Student student ->
                    Unit unit = studentIdUnitMap.get(student.id)
                    String gradeName = ""
                    String sectionName = ""
                    String facultyName = ""
                    String majorName = ""
                    if (unit) {
                        Grade grade = gradeIdGradeMap.get(unit.gradeId)
                        if (grade) {
                            gradeName = gradeService.appendGradeName(grade)
                            if (grade.sectionId) {
                                sectionName = SectionType.getEnumByType(grade?.sectionId).name
                            }
                        }
                        
                        Faculty faculty = facultyMap?.get(unit.facultyId)
                        if (faculty) {
                            facultyName = facultyService.appendFacultyName(faculty)
                        }
                        
                        Major major = majorMap?.get(unit.majorId)
                        if (major) {
                            majorName = majorService.appendMajorName(major)
                        }
                    }
                    Bed bed = studentIdBedMap.get(student.id)
                    String dormRoom = ""
                    if (bed) {
                        if (StringUtils.isNotBlank(bed?.buildingName)) {
                            dormRoom = bed?.buildingName
                        }

                        if (StringUtils.isNotBlank(bed?.buildingLayerName)) {
                            dormRoom = dormRoom + bed?.buildingLayerName
                        }

                        if (StringUtils.isNotBlank(bed?.doorPlateName)) {
                            dormRoom = dormRoom + bed?.doorPlateName
                        }
                    }
                    int status = 3
                    String checkMessage = null
                    if (student.pic) {
                        CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUserIdAndUserType(campusId, student.id, UserTypeEnum.STUDENT.type)
                        if (checkFace != null) {
                            status = checkFace.resultStatus
                            if (checkFace.checkMessage.equals("success")) {
                                checkMessage = "-"
                            } else {
                                checkMessage = checkFace.checkMessage
                            }
                        }
                    }

                    faceGroupVOList << FaceGroupVO.setFaceGroupVoToStudent(student, gradeName, unit?.alias ?: unit?.name, sectionName, dormRoom, facultyName, majorName, campus?.type, status, checkMessage)
            }
        }
        [list: faceGroupVOList, total: total]
    }

    def findParentFaceByCampusIdAndSearchValuePage(Long campusId, String name, Long facultyId = null, Long majorId = null, Long sectionId = null, Long gradeId = null, Long unitId = null, Long studentId = null, int resultStatus = 99, byte userType = 99 as byte, int s, int p) {
        List<Parent> parentList = parentService.findParentPageByCampusIdAndSearchValue(campusId, name, facultyId, majorId, sectionId, gradeId, unitId, studentId, resultStatus, s, p)
        Integer total = parentService.countParentPageByCampusIdAndSearchValue(campusId, name, facultyId, majorId, sectionId, gradeId, unitId, resultStatus, studentId)
        List<FaceGroupVO> faceGroupVOList = []
        if (parentList != null && parentList.size() > 0) {
            Map<Long, String> parentIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, parentList*.id)
            Multimap<Long, ParentStudent> parentIdParentStudentMap = HashMultimap.create()
            List<ParentStudent> parentStudents = parentService.fetchAllParentStudentByParentIdInListAndCampusId(parentList*.id, campusId)
            parentStudents?.each {
                parentIdParentStudentMap.put(it.parentId, it)
            }
            List<Student> studentList = studentService.fetchAllStudentByStudentIdInList(parentStudents*.studentId)
            Map<Long, Student> studentMap = studentList?.collectEntries {[it.id, it] }
            Map<Long, Unit> unitMap = unitService.getStudentIdUnitMapByStudentIdList(campusId, studentList*.id)
            Map<Long, String> unitIdNameMap = unitService.createGradeUnitNameViaUnitIdInList(unitMap.keySet().toList())
            parentList.each {
                Parent parent ->
                    Set<ParentStudent> parentStudentList = parentIdParentStudentMap.get(parent.id)
                    List<String> relationList = []
                    List<String> unitNameList = []
                    parentStudentList.each {
                        ParentStudent parentStudent ->
                            String relation = "家长"
                            Student student = studentMap.get(parentStudent.studentId)
                            Unit unit = unitMap.get(student.id)
                            unitNameList << unitIdNameMap.get(unit?.id)
                            if (StringUtils.isEmpty(parentStudent.studentName)) {
                                relation = student?.name
                            } else {
                                relation = parentStudent.studentName
                            }
                            if (parentStudent.appellation != null) {
                                String memo = "家长"
                                if (parentStudent.appellation == AppellationType.OTHER.type && parentStudent.memo) {
                                    memo = parentStudent.memo
                                    relation = (relation + "的" + memo) ?: "家长"
                                } else {
                                    relation = (relation + "的" + AppellationType.getEnumByType(parentStudent.appellation).name ?: memo) ?: "家长"
                                }
                            }
                            if (relation == null || relation.equals("null")) {
                                relation = "家长"
                            }
                            relationList << relation
                    }
                    FaceGroupVO vo = FaceGroupVO.setFaceGroupVOToParent(parent)
                    vo.mobile = parentIdMobileMap.get(parent.id)
                    vo.relation = relationList
                    vo.unitNameList = unitNameList

                    int status = 3
                    if (vo.pic) {
                        CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUserIdAndUserType(campusId, parent.id, UserTypeEnum.PARENT.type)
                        if (checkFace != null) {
                            status = checkFace.resultStatus
                            if (checkFace.checkMessage.equals("success")) {
                                vo.checkMessage = "-"
                            } else
                                vo.checkMessage = checkFace.checkMessage
                        }
                    }

                    vo.resultStatus = status

                    faceGroupVOList << vo
            }
        }
        [list: faceGroupVOList, total: total]

    }

    def findFerriesByCampusIdAndSearchValuePage(Long campusId, String name, String studentName, Long gradeId, Long unitId, int resultStatus = 99, byte userType = 99 as byte, int s, int p) {
        List<Ferries> ferriesList = []
        if (studentName) {
            ferriesList = ferriesService.findAllFerriesByCampusIdAndSearchValueLimit(campusId, studentName, p, s)
        } else {
            ferriesList = ferriesService.fetchAllFerriesLimit(campusId, name, gradeId, unitId, null, resultStatus, p, s)
        }
        List<FaceGroupVO> faceGroupVOList = []
        Map<Long, String> ferriesIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.FERRIES.type, ferriesList*.id)
        ferriesList.each { ferries ->
            List<FerriesStudent> ferriesStudentList = ferriesStudentService.fetchAllFerriesStudentByFerriesIdAndCampusId(campusId, ferries.id)
            Set<Long> studentIdSet = new HashSet<>()
            List<String> relationList = []
            ferriesStudentList.each {
                FerriesStudent ferriesStudent ->
                    if (!studentIdSet.contains(ferriesStudent.studentId)) {
                        studentIdSet.add(ferriesStudent.studentId)
                        Student student = studentService.fetchStudentByIdViaCache(ferriesStudent.studentId)
                        if (student && student.status == 1) {
                            Grade grade = gradeService.fetchNormalGradeByStudentId(student.id)
                            Unit unit = unitService.fetchAdministrativeUnitByStudentId(student.id)
                            StringBuilder relation = new StringBuilder()
                            if (grade) {
                                String sectionName = SectionType.getEnumByType(grade.sectionId).name
                                relation.append(sectionName).append(grade?.name)
                            }

                            if (unit) {
                                String alias = unit?.alias ? unit?.alias : unit?.name
                                relation.append(alias)
                            }
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }
                            if (StringUtils.isNotBlank(ferriesStudent.relation)) {
                                relation.append(ferriesStudent.relation)
                            }
                            if (StringUtils.isNotBlank(relation.toString())) {
                                relationList << relation.toString().replaceAll("(^-)|(-\$)", "")
                            } else {
                                relation.append("家长")
                                relationList << relation.toString()
                            }
                            if (StringUtils.isNotBlank(student.name) && !StringUtils.isNotBlank(ferriesStudent.relation) && StringUtils.isNotBlank(relation.toString())) {
                                relation.append("")
                                relationList << relation.toString()
                            }
                        }
                    }
            }
            FaceGroupVO faceGroupVO = FaceGroupVO.setFaceGroupVOToFerries(ferries)
            faceGroupVO.mobile = ferriesIdMobileMap.get(ferries.id)
            faceGroupVO.relation = relationList

            int status = 3
            if (ferries.pics) {
                CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUrlMd5AndName(campusId, ferries.pics, ferries?.name)
                if (checkFace != null) {
                    status = checkFace.resultStatus
                    if (checkFace.checkMessage.equals("success")) {
                        faceGroupVO.checkMessage = "-"
                    } else
                        faceGroupVO.checkMessage = checkFace.checkMessage
                }
            }
            faceGroupVO.resultStatus = status

            faceGroupVOList << faceGroupVO
        }
        Integer total = 0
        if (studentName) {
            List<Long> ferriesIdList = ferriesService.findAllFerriesByCampusIdAndSearchValue(campusId, studentName)
            total = ferriesIdList ? ferriesIdList.size() : 0
        } else {
            total = ferriesService.countFerries(campusId, name, gradeId, unitId, null, resultStatus)
        }
        [list: faceGroupVOList, total: total]

    }


    def buildVisitorByCampusIdAndSearchValuePage(Long campusId, String searchValue, Long daytimeStamp, int s, int p) {
        def resultMap = visitorService.fetchVisitorByCampusIdAndSearchValuePage(campusId, searchValue, daytimeStamp, s, p)
        List<Visitor> visitorList = resultMap.list
        List<FaceGroupVO> faceGroupVOList = []
        if (visitorList != null && visitorList.size() > 0) {
            Map<Long, String> visitorIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitorList*.id)
            visitorList.each {
                Visitor visitor ->
                    FaceGroupVO vo = FaceGroupVO.setFaceGroupVOToVisitor(visitor)
                    vo.mobile = visitorIdMobileMap.get(visitor.id)
                    faceGroupVOList << vo
            }

        }
        [list: faceGroupVOList, total: resultMap.total]
    }


    FaceGroupVO getFaceGroupByUserIdAndUserType(Long userId, Byte userType, Long campusId) {
        FaceGroupVO vo = new FaceGroupVO()
        Campus campus = Campus.findById(campusId)
        String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, userType, userId, true)
        switch (userType) {
            case UserTypeEnum.TEACHER.type:
                Teacher teacher = teacherService.fetchTeacherById(userId)
                vo = FaceGroupVO.setFaceGroupVoToTeacher(teacher)
                vo.mobile = mobile
                TeacherSchoolCampus teacherSchool = teacherService.fetchTeacherSchoolCampusByCampusIdAndTeacherId(campusId, teacher.id)
                if (teacherSchool) {
                    vo.code = teacherSchool?.jobNum
                }
                break
            case UserTypeEnum.STUDENT.type:
                Student student = studentService.fetchStudentById(userId)
                Unit unit = unitService.fetchAdministrativeUnitByStudentId(userId)
                String gradeName = ""
                String sectionName = ""
                String facultyName = ""
                String majorName = ""
                if (unit) {
                    Grade grade = gradeService.fetchGradeById(unit.gradeId)
                    if (grade) {
                        gradeName = gradeService.appendGradeName(grade)
                        if (grade.sectionId) {
                            sectionName = SectionType.getEnumByType(grade?.sectionId).name
                        }
                    }
                    
                    Faculty faculty = facultyService.fetchFacultyByIdNoStatus(unit.facultyId)
                    if (faculty) {
                        facultyName = facultyService.appendFacultyName(faculty)
                    }
                    
                    Major major = majorService.fetchMajorById(unit.majorId)
                    if (major) {
                        majorName = majorService.appendMajorName(major)
                    }
                }
                Bed bed = bedService.fetchBedByStudentId(userId)
                String dormRoom = ""
                if (bed) {

                    if (StringUtils.isNotBlank(bed?.buildingName)) {
                        dormRoom = bed?.buildingName
                    }

                    if (StringUtils.isNotBlank(bed?.buildingLayerName)) {
                        dormRoom = dormRoom + bed?.buildingLayerName
                    }

                    if (StringUtils.isNotBlank(bed?.doorPlateName)) {
                        dormRoom = dormRoom + bed?.doorPlateName
                    }

                }
                vo = FaceGroupVO.setFaceGroupVoToStudent(student, gradeName, unit?.name, sectionName, dormRoom, facultyName, majorName, campus?.type)
                break
            case UserTypeEnum.PARENT.type:
                Parent parent = parentService.fetchParentById(userId)
                List<String> relationList = []
                if (parent) {
                    List<ParentStudent> parentStudentList = parentService.fetchAllParentStudentByParentIdAndCampusId(parent.id, campusId)
                    parentStudentList.each {
                        ParentStudent parentStudent ->
                            String relation
                            if (StringUtils.isEmpty(parentStudent.studentName)) {
                                Student student = studentService.fetchStudentById(parentStudent.studentId)
                                relation = student?.name
                            } else {
                                relation = parentStudent.studentName
                            }
                            if (StringUtils.isNotBlank(parentStudent?.memo)) {
                                relation = relation + parentStudent?.memo
                            } else {
                                if (parentStudent.appellation != null) {
                                    relation = relation + AppellationType.getEnumByType(parentStudent.appellation).name
                                }
                            }

                            if (StringUtils.isNotBlank(relation)) {
                                relationList << relation
                            }
                    }
                }

                vo = FaceGroupVO.setFaceGroupVOToParent(parent)
                vo.mobile = mobile
                vo.relation = relationList
                break
            case UserTypeEnum.FERRIES.type:
                Ferries ferries = ferriesService.fetchFerriesByFerriesId(userId)
                List<String> relationList = []
                if (ferries) {
                    List<FerriesStudent> ferriesStudentList = ferriesStudentService.fetchAllFerriesStudentByFerriesIdAndCampusId(campusId, ferries.id)
                    Set<Long> studentIdSet = new HashSet<>()
                    if (ferriesStudentList != null && ferriesStudentList.size() > 0) {
                        ferriesStudentList.each {
                            FerriesStudent ferriesStudent ->
                                if (!studentIdSet.contains(ferriesStudent.studentId)) {
                                    studentIdSet.add(ferriesStudent.studentId)
                                    Student student = studentService.fetchStudentByIdViaCache(ferriesStudent.studentId)
                                    if (student && student.status == 1) {
                                        Grade grade = gradeService.fetchNormalGradeByStudentId(student.id)
                                        Unit unit = unitService.fetchAdministrativeUnitByStudentId(student.id)
                                        StringBuilder relation = new StringBuilder()
                                        if (grade) {
                                            String sectionName = SectionType.getEnumByType(grade.sectionId).name
                                            relation.append(sectionName).append(grade?.name)
                                        }

                                        if (unit) {
                                            String alias = unit?.alias ? unit?.alias : unit?.name
                                            relation.append(alias)
                                        }
                                        if (StringUtils.isNotBlank(student.name)) {
                                            relation.append("-" + student.name)
                                        }
                                        if (StringUtils.isNotBlank(ferriesStudent.relation)) {
                                            relation.append("-" + ferriesStudent.relation)
                                        }
                                        if (StringUtils.isNotBlank(relation.toString())) {
                                            relationList << relation.toString().replaceAll("(^-)|(-\$)", "")
                                        }
                                    }
                                }

                        }
                    }
                }
                vo = FaceGroupVO.setFaceGroupVOToFerries(ferries)
                vo.mobile = mobile
                vo.relation = relationList
                break
            case UserTypeEnum.VISITORS.type:
                Visitor visitor = visitorService.fetchVisitorById(userId)
                vo = FaceGroupVO.setFaceGroupVOToVisitor(visitor)
                vo.mobile = mobile
                break
            default:
                throw new RuntimeException("没有该类型用户！")
        }
        return vo
    }


    List<FaceGroupVO> fetchFaceGroupList(Long campusId) {
        List<FaceGroup> faceGroupList = fetchAllByCampusIdAndFirm(campusId, DeviceFirm.ALY_DEVICE.firm)
        List<FaceGroupVO> faceGroupVOList = []
        if (faceGroupList != null && faceGroupList.size() > 0) {
            faceGroupList.each {
                FaceGroup faceGroup ->
                    faceGroupVOList << new FaceGroupVO(id: faceGroup.id, faceGroupName: faceGroup.name)
            }
        }
        return faceGroupVOList
    }

    List<FaceGroup> fetchAllByCampusIdAndFirm(Long campusId, Integer firm) {
        FaceGroup.findAllByCampusIdAndFirmAndStatus(campusId, firm, 1 as Byte)
    }

    FaceGroup fetchAllByCampusIdAndFirmAndUserType(Long campusId, Byte userType, Integer firm) {
        FaceGroup.findByCampusIdAndFirmAndUserTypeAndStatus(campusId, firm, userType, 1 as Byte)
    }

    FaceGroup saveFaceGroup(FaceGroup faceGroup) {
        faceGroup.save(failOnError: true, flush: true)
    }

}

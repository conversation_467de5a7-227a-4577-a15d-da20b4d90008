package hiiadmin.humanface

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import grails.async.Promises
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.ALiYunApiInvoker
import hiiadmin.apiCloud.AntennaHkIsupApi
import hiiadmin.apiCloud.EdgeALiYunApiInvoker
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.module.docking.DockJsonParseAliYunVO
import hiiadmin.module.humanface.dto.DeviceDTO
import hiiadmin.module.humanface.dto.DeviceRunDateDTO
import hiiadmin.module.humanface.dto.DeviceRunTimeDTO
import hiiadmin.module.imp.DeviceImportRecordVO
import hiiadmin.school.device.DeviceService
import hiiadmin.syn.SchoolSynFirmService
import hiiadmin.syn.SynTableRecordService
import hiiadmin.utils.TimeUtils
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.annotation.Propagation
import timetabling.building.DoorPlateDevice
import timetabling.device.Device
import timetabling.docking.DockingPlatform
import timetabling.sync.SchoolSynFirm
import timetabling.sync.SynTableRecord

@Transactional
@Slf4j
class HumanAddDeviceService {

    @Autowired
    ALiYunApiInvoker aLiYunApiInvoker

    @Autowired
    EdgeALiYunApiInvoker edgeALiYunApiInvoker

    @Autowired
    AntennaHkIsupApi antennaHkIsupApi

    SchoolSynFirmService schoolSynFirmService

    DeviceService deviceService


    SynTableRecordService synTableRecordService

    DockingPlatformCampusService dockingPlatformCampusService

    /**
     *
     * @param id
     * @param name
     * @param positionIds
     * @param model
     * @param algs
     * @param time
     * @param mac
     * @param deviceNo
     * @return
     */
    ServiceResult<Device> updateDeviceSyn(Long id, Byte type, String name, String positionIds, String model, String algs, String time, String nodeMac, String deviceNo) {


        Device device = deviceService.fetchDeviceById(id)
        if (device == null) {
            return ServiceResult.failure("100", "该设备不存在")
        }

        Device d = deviceService.fetchDeviceByCampusIdAndName(device.campusId, name)
        if (d && d.id != id) {
            return ServiceResult.failure("100", "校区内设备名称已存在")
        }
        device.name = name
        device.deviceName = name

        if (type) {
            device.type = type
        }
        if (StringUtils.isNotBlank(algs)) {
            device.algs = algs
        }
        if (StringUtils.isNotBlank(time)) {
            device.runTimes = time
        }

        device.deviceNo = deviceNo

        if (model) {
            device.model = model
        }
        if (nodeMac) {
            device.nodeMac = nodeMac
        }
        ServiceResult<Device> result = updateDeviceSyn(device, nodeMac)

        if (!result.success) {
            return ServiceResult.failure(result.code, result.message)
        }
        
        

        result = deviceService.createPositionDevice(id, positionIds)
        if (!result.success) {
            return ServiceResult.failure(result.code, result.message)
        }

        if (device && device.firm == ConstantEnum.DeviceFirm.HK_ISUP.firm && name) {
            Promises.task {
                antennaHkIsupApi.syncDeviceName([campusId: device.campusId, serial: device.indexCode, name: name])
            }.onError {Throwable throwable ->
                log.error("syncDevice name error:", throwable)
            }
        }
        
        return result
    }

    ServiceResult batchUpdateDevicePosition(String deviceIds, String positionIds) {
        List<Long> deviceIdList = ToStringUnits.idsString2LongList(deviceIds)
        for (Long deviceId in deviceIdList) {
            ServiceResult result = deviceService.createPositionDevice(deviceId, positionIds)
            if (!result.success) {
                return ServiceResult.failure(result.code, result.message)
            }
        }
        return ServiceResult.success()
    }

    /**
     * 修改设备
     * */
    ServiceResult<Device> updateDeviceSyn(Device device, String mac) {

        switch (device.firm) {
            case ConstantEnum.DeviceFirm.ALY_DEVICE.firm:
                try {
                    synALiYunUpdateDevice(device, mac)
                } catch (Exception e) {
                    return ServiceResult.failure("100", e.message)
                }
                break
        }
        device = deviceService.saveDevice(device)
        return ServiceResult.success(device)
    }


    String synALiYunUpdateDevice(Device device, String mac) throws RuntimeException {
        String runTimes = getResolveRunTimes(device.runTimes)
        DockingPlatform synFirm = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(device.campusId, ConstantEnum.DeviceFirm.ALY_DEVICE.firm)

        DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(synFirm.dockingJson)
        Map map = [:]
        if (aliYunVO.version) {
            map.put("gpuId", device.gpuId)
            map.put("fps", device.fps)
            map.put("version", aliYunVO.version)
        }
        map = getMapAlgor(map, device?.algs)
        map.put("campusId", device?.campusId)
        map.put("schoolId", device?.schoolId)
        map.put("deviceId", device.id)
        map.put("name", device?.name)
        map.put("address", device?.address)
        map.put("time", runTimes)
        map.put("nodeMac", mac)
        map.put("addressLink", synFirm?.addressLink)
        map.put("version", synFirm?.version)
        ResultVO resultVO = null
        if (aliYunVO.version) {
            resultVO = edgeALiYunApiInvoker.synSaveDeviceToALiYun(map)
        } else {
            resultVO = aLiYunApiInvoker.synSaveDeviceToALiYun(map)
        }
        if (resultVO.status != 1) {
            throw new RuntimeException("更新设备异常,msg:${resultVO.message}")
        } else {
            String id = resultVO.result.get("id")
            return id
        }
    }


    /**
     * 删除设备，同步删除其他平台设备，失败就需要回滚删除失败
     * */
    void deleteDeviceByDeviceId(Long deviceId) {
        Device device = deviceService.fetchDeviceById(deviceId)
        if (!device) {
            throw new RuntimeException("设备不存在")
        }
        DockingPlatform schoolSynFirm = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(device.campusId, ConstantEnum.DeviceFirm.ALY_DEVICE.firm)

        DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(schoolSynFirm.dockingJson)

        if (schoolSynFirm && StringUtils.isNotBlank(schoolSynFirm.addressLink)) {
            SynTableRecord synTableRecord = synTableRecordService.fetchSynTableRecordByDomainIdAndDomainName(device.id, "Device", ConstantEnum.DeviceFirm.ALY_DEVICE.firm)
            boolean synTableResult = false
            if (synTableRecord) {
                synTableResult = true
            }
            switch (device.firm) {
                case ConstantEnum.DeviceFirm.ALY_DEVICE.firm:
                    if (synTableResult) {
                        synALiYunDeleteDevice(schoolSynFirm.addressLink, synTableRecord.synCode, aliYunVO.version)
                        synTableRecord.status = 0
                        synTableRecord.lastSaveType = "delete"
                    }
                    break
            }
            if (synTableResult) {
                synTableRecordService.saveSynTableRecord(synTableRecord)
            }
        }
        device.status = 0 as byte
        saveDevice(device)
        DoorPlateDevice doorPlateDevice = deviceService.fetchDoorPlateDeviceViaDeviceId(deviceId)
        if (doorPlateDevice) {
            doorPlateDevice.status = DoorPlateDevice.DEVICE_DELETE_STATUS
            doorPlateDevice.save(failOnError: true, flush: true)
        }
    }

    def synALiYunDeleteDevice(String addressLink, String synCode, Integer version) {
        Map map = [:]
        map.put("id", synCode)
        map.put("addressLink", addressLink)
        map.put("version", version)
        ResultVO resultVO = null
        if (version) {
            resultVO = edgeALiYunApiInvoker.synDeleteDeviceToALiYun(map)
        } else {
            resultVO = aLiYunApiInvoker.synDeleteDeviceToALiYun(map)
        }
        if (resultVO.status != 1) {
            throw new RuntimeException("删除失败")
        }
    }

    ServiceResult<DeviceVO> findDeviceImgByDevice(Long id) {
        Device device = deviceService.fetchDeviceById(id)
        if (device == null) {
            return ServiceResult.failure("100", "不存在！")
        }
        DeviceVO deviceVO = new DeviceVO().buildVO(device)
        DockingPlatform firm = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(device.campusId, ConstantEnum.DeviceFirm.ALY_DEVICE.firm)


        if (firm) {
            DeviceDTO deviceDTO = getALiDeviceImg(device.id, firm)
            if (deviceDTO) {
                deviceVO.link = schoolSynFirmService.getAliYunImgLink(deviceDTO?.originalImage, deviceVO.nodeMac, firm)
                deviceVO.onlineStatus = deviceDTO?.onlineStatus
                deviceVO.connectStatus = deviceDTO?.connectStatus


            }
        }
        return ServiceResult.success(deviceVO)
    }

    DeviceDTO getALiDeviceImg(Long deviceId, DockingPlatform firm) {
        SynTableRecord synTableRecord = synTableRecordService.fetchSynTableRecordByDomainIdAndDomainName(deviceId, "Device", ConstantEnum.DeviceFirm.ALY_DEVICE.firm)
        DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(firm.dockingJson)
        if (synTableRecord) {
            Map map = [:]
            map.put("id", synTableRecord.synCode)
            map.put("addressLink", firm.addressLink)
            map.put("version", aliYunVO.version)
            try {
                ResultVO resultVO = null
                if (aliYunVO.version) {
                    resultVO = edgeALiYunApiInvoker.synFindDeviceToALiYun(map)
                } else {
                    resultVO = aLiYunApiInvoker.synFindDeviceToALiYun(map)
                }
                if (resultVO?.status == 1) {
                    DeviceDTO deviceDTO = resultVO.result.get("deviceDTO") as DeviceDTO
                    return deviceDTO
                }
            } catch (Exception e) {
                log.warn("a li access exception,msg${e.message}".toString())
            }

        }
        return null
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    DeviceImportRecordVO importSaveDevice(Device device, String nodeIp) {
        DeviceImportRecordVO vo = new DeviceImportRecordVO(deviceName: device.name, resultStatus: DeviceImportRecordVO.RESULT_FAILURE)
        SchoolSynFirm firm = schoolSynFirmService.fetchSchoolSynFirmByCampusIdAndFirm(device.campusId, ConstantEnum.DeviceFirm.ALY_DEVICE.firm)
        if (firm == null) {
            vo.failureReason = "未开启阿里平台"
            return vo
        }

        String nodeMac = null

        vo = judgeDeviceSpecification(device, nodeIp, vo)

        if (vo.resultStatus == DeviceImportRecordVO.RESULT_FAILURE) {
            return vo
        }

        if (StringUtils.isNotEmpty(nodeIp)) {
            nodeMac = schoolSynFirmService.getAliYunNodeMacByNodeIp(nodeIp, device.campusId, device.firm)
            if (StringUtils.isEmpty(nodeMac)) {
                vo.failureReason = "算法节点未匹配"
                vo.resultStatus = DeviceImportRecordVO.RESULT_FAILURE
                return vo
            }
        }
        boolean judgmentDevice = deviceService.judgmentDeviceNameAndIpIsExist(device.campusId, device.name, device.deviceIp, device.firm)
        if (!judgmentDevice) {
            vo.failureReason = "该名称或者ip已存在，名称和ip不能重复！"
            vo.resultStatus = DeviceImportRecordVO.RESULT_FAILURE
            return vo
        }
        if (StringUtils.isEmpty(device.runTimes)) {
            List<DeviceRunDateDTO> list = [new DeviceRunDateDTO(startTime: TimeUtils.getDateStartTime(new Date()).time, stopTime: TimeUtils.getDateEndTime(new Date()).time)]
            device.runTimes = JSON.toJSONString(list)
        }
        try {
            device.nodeMac = nodeMac
            ServiceResult<Long> result = saveDeviceSyn(device, nodeMac)
            if (!result.success) {
                vo.failureReason = result.message
                vo.resultStatus = DeviceImportRecordVO.RESULT_FAILURE
            }
        } catch (Exception e) {
            vo.failureReason = e.message
            vo.resultStatus = DeviceImportRecordVO.RESULT_FAILURE
            return vo
        }
        vo.resultStatus = DeviceImportRecordVO.RESULT_SUCCESS
        vo.failureReason = "添加成功！"
        return vo
    }

    DeviceImportRecordVO judgeDeviceSpecification(Device device, String nodeIp, DeviceImportRecordVO vo) {

        switch (device.deviceKind) {
            case ConstantEnum.DeviceKind.IPC.type:
                if (StringUtils.isEmpty(nodeIp)) {
                    vo.failureReason = "必要参数节点ip为空"
                    return vo
                }
                return judgeDeviceSpecificationIPC(device, vo)
                break
            case ConstantEnum.DeviceKind.NVR.type:
                return judgeDeviceSpecificationNVR(device, vo)
                break
        }

        vo.resultStatus = DeviceImportRecordVO.RESULT_SUCCESS
        return vo

    }

    DeviceImportRecordVO judgeDeviceSpecificationIPC(Device device, DeviceImportRecordVO vo) {
        switch (device.protocolType) {
            case ConstantEnum.ProtocolEnumType.HK.type:
                if (StringUtils.isEmpty(device.deviceName)) {
                    vo.failureReason = "必要参数设备名称为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.deviceIp)) {
                    vo.failureReason = "必要参数设备IP为空"
                    return vo
                }
                if (!device.port) {
                    vo.failureReason = "必要参数设备端口号为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.userName)) {
                    vo.failureReason = "必要参数用户名为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.password)) {
                    vo.failureReason = "必要参数设备密码为空"
                    return vo
                }
                break
            case ConstantEnum.ProtocolEnumType.GB.type:
                if (StringUtils.isEmpty(device.deviceName)) {
                    vo.failureReason = "必要参数设备名称为空"
                    return vo
                }
                if (!device.standardId) {
                    vo.failureReason = "必要参数国标id为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.password)) {
                    vo.failureReason = "必要参数密码为空"
                    return vo
                }
                break
            case ConstantEnum.ProtocolEnumType.RTSP.type:
                if (StringUtils.isEmpty(device.deviceName)) {
                    vo.failureReason = "必要参数设备名称为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.deviceIp)) {
                    vo.failureReason = "必要参数设备IP为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.url)) {
                    vo.failureReason = "必要参数流传输地址为空"
                    return vo
                }
                if (!device.port) {
                    vo.failureReason = "必要参数设备端口号为空"
                    return vo
                }
                break
        }
        vo.resultStatus = DeviceImportRecordVO.RESULT_SUCCESS
        return vo
    }

    def judgeDeviceSpecificationNVR(Device device, DeviceImportRecordVO vo) {
        switch (device.protocolType) {
            case ConstantEnum.ProtocolEnumType.HK.type:
                if (StringUtils.isEmpty(device.deviceName)) {
                    vo.failureReason = "必要参数设备名称为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.deviceIp)) {
                    vo.failureReason = "必要参数设备IP为空"
                    return vo
                }
                if (!device.port) {
                    vo.failureReason = "必要参数设备端口号为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.userName)) {
                    vo.failureReason = "必要参数用户名为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.password)) {
                    vo.failureReason = "必要参数设备密码为空"
                    return vo
                }
                break
            case ConstantEnum.ProtocolEnumType.GB.type:
                if (StringUtils.isEmpty(device.deviceName)) {
                    vo.failureReason = "必要参数设备名称为空"
                    return vo
                }
                if (!device.standardId) {
                    vo.failureReason = "必要参数国标id为空"
                    return vo
                }
                if (StringUtils.isEmpty(device.password)) {
                    vo.failureReason = "必要参数密码为空"
                    return vo
                }
                break
        }
        vo.resultStatus = DeviceImportRecordVO.RESULT_SUCCESS
        return vo
    }


    /**
     * 添加设备
     * */
    ServiceResult<Long> saveDeviceSyn(Device device, String mac) {
        device = saveDevice(device)
        switch (device.firm) {
            case ConstantEnum.DeviceFirm.ALY_DEVICE.firm:
                device.indexCode = synALiYunSaveDevice(device, mac)
                break
        }
        device = saveDevice(device)
        return ServiceResult.success(device.id)
    }

    Device saveDevice(Device device) {
        device.save(failOnError: true, flush: true)
    }

    String getResolveRunTimes(String time) {
        String runTimes = ""
        if (StringUtils.isNotBlank(time)) {
            try {
                List<DeviceRunDateDTO> list = JSON.parseArray(time, DeviceRunDateDTO.class)
                List<DeviceRunTimeDTO> dtoList = []
                list.each {
                    DeviceRunDateDTO dto ->
                        dtoList << new DeviceRunTimeDTO(startTime: TimeUtils.getFormatDate(dto.startTime, TimeUtils.DAY_HOUR_MINUTE), stopTime: TimeUtils.getFormatDate(dto.stopTime, TimeUtils.DAY_HOUR_MINUTE))
                }
                runTimes = JSON.toJSONString(dtoList)
            } catch (Exception e) {
                log.info("解析异常，时间格式错误！msg@${e.message}".toString())

            }
        } else {
            throw new RuntimeException("运行时间为空！")
        }
        return runTimes
    }

    def synALiYunSaveDevice(Device device, String mac) throws RuntimeException {
        String runTime = getResolveRunTimes(device.runTimes)
        DockingPlatform synFirm = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(device.campusId, ConstantEnum.DeviceFirm.ALY_DEVICE.firm)
        if (synFirm == null || StringUtils.isEmpty(synFirm.addressLink)) {
            throw new RuntimeException("同步失败，未配置阿里平台！")
        }
        DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(synFirm.dockingJson)
        Params params = null
        switch (device.protocolType) {
            case ConstantEnum.ProtocolEnumType.GB.type:
                params = new Params(
                        gbUname: device.standardId,
                        gbPwd: device.password,
                        gbId: device.standardId
                )
                break
            case ConstantEnum.ProtocolEnumType.HK.type:
                params = new Params(
                        hikUsr: device.userName,
                        hikSecret: device.password,
                        gbId: device.userName,
                        ip: device.deviceIp,
                        port: device.port
                )
                break
            case ConstantEnum.ProtocolEnumType.RTSP.type:
                params = new Params(
                        rtspUsr: device.userName,
                        rtspSecret: device.password,
                        ip: device.deviceIp,
                        port: device.port,
                        url: device.url
                )
                break
        }
        Map map = [:]
        if (aliYunVO.version) {
            map.put("gpuId", device.gpuId)
            map.put("fps", device.fps)
            map.put("version", aliYunVO.version)
        }
        map = getMapAlgor(map, device?.algs)
        map.put("deviceId", device?.id)
        map.put("name", device?.name)
        map.put("type", ConstantEnum.DeviceKind.getEnumByType(device?.deviceKind as Byte).name)
        map.put("protocol", ConstantEnum.ProtocolEnumType.getEnumByType(device?.protocolType).name)
        map.put("params", JSON.toJSONString(params))
        map.put("address", device?.address)
        map.put("campusId", device?.campusId)
        map.put("schoolId", device.schoolId)
        map.put("nodeMac", mac)
        map.put("addressLink", synFirm.addressLink)
        map.put("time", runTime)

        try {
            ResultVO resultVO = null
            if (aliYunVO.version) {
                resultVO = edgeALiYunApiInvoker.synSaveDeviceToALiYun(map)
            } else {
                resultVO = aLiYunApiInvoker.synSaveDeviceToALiYun(map)
            }
            if (resultVO.status == 1) {
                return resultVO.result.get("id")
            } else {
                throw new RuntimeException(resultVO?.message)
            }
        } catch (Exception e) {
            log.info("【阿里同步】设备同步失败！msg@${e.message}".toString())
            throw new RuntimeException("同步失败,msg@${e.message}")
        }
    }

    Map getMapAlgor(Map map, String algs) {
        if (StringUtils.isNotBlank(algs)) {
            List<Long> algsList = ToStringUnits.idsString2LongList(algs)
            //必填项设置初始值
            map.put("face", 0)
            map.put("body", 0)
            map.put("car", 0)
            map.put("nonCar", 0)
            algsList.each {
                long alg ->
                    switch (alg) {
                        case ConstantEnum.Algorithm.FACE.type:
                            map.put("face", 1)
                            break
                        case ConstantEnum.Algorithm.BODY.type:
                            map.put("body", 1)
                            break
                        case ConstantEnum.Algorithm.CAR.type:
                            map.put("car", 1)
                            break
                        case ConstantEnum.Algorithm.NON_CAR.type:
                            map.put("nonCar", 1)
                            break
                    }
            }
        }
        return map
    }


    static class Params {

        String ip
        String port
        String url

        //rtsp
        String rtspUsr
        String rtspSecret
        //海康
        String hikUsr
        String hikSecret

        //GB
        String gbUname
        String gbPwd
        //说明：gbUname与gbId相同
        String gbId
    }

}

package hiiadmin.planAfterSchool

import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import org.joda.time.DateTime
import timetabling.pickUp.LeavePlanLevelParentAuthDevice
import timetabling.pickUp.afterDaily.LeavePlanLevelParentAuthDeviceDaily

@Transactional
class LeavePlanLevelParentAuthDeviceService {

    List<LeavePlanLevelParentAuthDevice> fetchAllLeavePlanLevelParentAuthDeviceByCampusId(long campusId) {
        LeavePlanLevelParentAuthDevice.findAllByCampusIdAndStatus(campusId, 1)
    }

    List<LeavePlanLevelParentAuthDevice> fetchAllLeavePlanLevelParentAuthDeviceByLevelId(long levelId) {
        LeavePlanLevelParentAuthDevice.findAllByLevelIdAndStatus(levelId, 1)
    }

    Multimap<Long, Long> transformLevelIdDeviceIdMultimap4Task(long taskId) {
        Multimap<Long, Long> levelIdDeviceIdMultimap = HashMultimap.create()
        List<LeavePlanLevelParentAuthDevice> levelList = fetchAllLeavePlanLevelParentAuthDeviceByTaskId(taskId)
        levelList.each {
            levelIdDeviceIdMultimap.put(it.levelId, it.deviceId)
        }
        levelIdDeviceIdMultimap
    }

    List<LeavePlanLevelParentAuthDevice> fetchAllLeavePlanLevelParentAuthDeviceByTaskId(long taskId) {
        LeavePlanLevelParentAuthDevice.findAllByTaskIdAndStatus(taskId, 1)
    }

    Multimap<Long, LeavePlanLevelParentAuthDevice> transformParentAuthDeviceMultimap(long campusId) {
        Multimap<Long, LeavePlanLevelParentAuthDevice> levelIdParentAuthDeviceMultimap = HashMultimap.create()

        List<LeavePlanLevelParentAuthDevice> levelList = fetchAllLeavePlanLevelParentAuthDeviceByCampusId(campusId)
        levelList.each {
            levelIdParentAuthDeviceMultimap.put(it.levelId, it)
        }
        levelIdParentAuthDeviceMultimap
    }

    static String DELETE_L_P_L_P_A_D_4_LEVEL_ID_HQL = """UPDATE LeavePlanLevelParentAuthDevice 
                SET status = :status, version = version+1, lastUpdated = NOW() WHERE levelId = :levelId"""

    void deleteLeavePlanLevelAuthDevice4LevelId(long levelId) {
        LeavePlanLevelParentAuthDevice.executeUpdate(DELETE_L_P_L_P_A_D_4_LEVEL_ID_HQL, [levelId: levelId, status: 0])
    }

    void initLeavePlanLevelParentAuthDevice(long campusId, long taskId, long levelId, List<Long> deviceIdList) {
        List<LeavePlanLevelParentAuthDevice> authDevices = deviceIdList.collect { deviceId ->
            LeavePlanLevelParentAuthDevice authDevice = LeavePlanLevelParentAuthDevice.findOrCreateByLevelIdAndDeviceId(levelId, deviceId)
            authDevice.taskId = taskId
            authDevice.status = 1
            authDevice.campusId = campusId
            authDevice
        }
        authDevices*.save(failOnError: true, flush: true)
    }

    void saveParentDeviceDailyTotal(long leaveId, DateTime dateTime) {
        List<LeavePlanLevelParentAuthDevice> authDeviceList = fetchAllLeavePlanLevelParentAuthDeviceByLevelId(leaveId)
        if (authDeviceList != null && authDeviceList.size() > 0) {
            List<LeavePlanLevelParentAuthDeviceDaily> deviceDailyList = []
            authDeviceList.each {
                LeavePlanLevelParentAuthDevice device ->
                    deviceDailyList << new LeavePlanLevelParentAuthDeviceDaily(
                            campusId: device.campusId,
                            dayStamp: dateTime.millis,
                            leaveId: leaveId,
                            taskId: device.taskId,
                            status: 1 as Byte,
                            deviceId: device.deviceId
                    )

            }
            saveLeavePlanLevelParentAuthDeviceDailyList(deviceDailyList)
        }
    }

    def saveLeavePlanLevelParentAuthDeviceDailyList(List<LeavePlanLevelParentAuthDeviceDaily> deviceDailyList){
        deviceDailyList*.save(failOnError:true,flush:true)
    }
}

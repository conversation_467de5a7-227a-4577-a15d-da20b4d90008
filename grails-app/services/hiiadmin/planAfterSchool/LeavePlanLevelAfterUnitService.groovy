package hiiadmin.planAfterSchool

import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.utils.TimeUtils
import org.joda.time.DateTime
import timetabling.pickUp.LeavePlanLevelAfterUnit
import timetabling.pickUp.afterDaily.LeavePlanLevelAfterUnitDaily

@Transactional
@Slf4j
class LeavePlanLevelAfterUnitService {

    List<LeavePlanLevelAfterUnit> fetchAllLeavePlanLevelAfterUnitByLevelId(long levelId) {
        LeavePlanLevelAfterUnit.findAllByLevelIdAndStatus(levelId, 1)
    }

    LeavePlanLevelAfterUnit fetchLeavePlanLevelByTaskAndAfterUnitId(Long taskId, Long afterUnitId) {
        LeavePlanLevelAfterUnit.findByTaskIdAndAfterUnitIdAndStatus(taskId, afterUnitId, 1)
    }


    List<LeavePlanLevelAfterUnit> fetchAllLeavePlanLevelAfterUnitByTaskIdAndAfterUnitId(long taskId, long afterUnitId) {
        LeavePlanLevelAfterUnit.findAllByTaskIdAndAfterUnitIdAndStatus(taskId, afterUnitId, 1)
    }


    List<LeavePlanLevelAfterUnit> taskLeavePlanLevelAfterUnit4Student(long taskId, long studentId) {
        LeavePlanLevelAfterUnit.executeQuery(TASK_L_P_L_A_UNIT_4_STUDENT_HQL, [taskId: taskId, studentId: studentId])
    }

    def initLeavePlanLevelAfterUnitDailyByLevelId(long leaveId, DateTime dateTime) {
        List<LeavePlanLevelAfterUnit> levelAfterUnitList = fetchAllLeavePlanLevelAfterUnitByLevelId(leaveId)

    }

    static String TASK_L_P_L_A_UNIT_4_STUDENT_HQL = """FROM LeavePlanLevelAfterUnit lplau 
                WHERE lplau.taskId = :taskId 
                    AND lplau.status = 1 
                    AND lplau.afterUnitId IN ( SELECT aus.afterUnitId FROM AfterUnitStudent aus WHERE aus.studentId = :studentId AND aus.status = 1)"""

    Multimap<Long, LeavePlanLevelAfterUnit> transformAfterUnitMultimap(long campusId) {
        Multimap<Long, LeavePlanLevelAfterUnit> levelIdAfterUnitMultimap = HashMultimap.create()
        List<LeavePlanLevelAfterUnit> levelList = fetchAllLeavePlanLevelAfterUnitByCampusId(campusId)
        levelList.each {
            levelIdAfterUnitMultimap.put(it.levelId, it)
        }
        levelIdAfterUnitMultimap
    }

    List<LeavePlanLevelAfterUnit> fetchAllLeavePlanLevelAfterUnitByCampusId(long campusId) {
        LeavePlanLevelAfterUnit.findAllByCampusIdAndStatus(campusId, 1)
    }

    static String DELETE_L_P_L_A_U_4_LEVEL_ID_HQL = """UPDATE LeavePlanLevelAfterUnit 
                SET status = :status, version = version+1, lastUpdated = NOW() WHERE levelId = :levelId"""

    void deleteLeavePlanLevelAuthDevice4LevelId(long levelId) {
        LeavePlanLevelAfterUnit.executeUpdate(DELETE_L_P_L_A_U_4_LEVEL_ID_HQL, [levelId: levelId, status: 0])
    }

    void initLeavePlanLevelAfterUnit(long campusId, long taskId, long levelId, List<Long> afterUnitIdList) {
        List<LeavePlanLevelAfterUnit> levelAfterUnits = afterUnitIdList.collect { afterUnitId ->
            LeavePlanLevelAfterUnit afterUnit = LeavePlanLevelAfterUnit.findOrCreateByLevelIdAndAfterUnitId(levelId, afterUnitId)
            afterUnit.taskId = taskId
            afterUnit.status = 1
            afterUnit.campusId = campusId
            afterUnit
        }
        levelAfterUnits*.save(failOnError: true, flush: true)
    }

    def initTaskCopyLeavePlanLevelAfterUnit(Long levelId, DateTime dateTime) {
        List<LeavePlanLevelAfterUnit> afterUnitList = fetchAllLeavePlanLevelAfterUnitByLevelId(levelId)
        List<LeavePlanLevelAfterUnitDaily> unitDailies = []
        if (afterUnitList != null && afterUnitList.size() > 0) {
            afterUnitList.each {
                LeavePlanLevelAfterUnit afterUnit ->
                    unitDailies << new LeavePlanLevelAfterUnitDaily(
                            campusId: afterUnit.campusId,
                            taskId: afterUnit.taskId,
                            levelId: levelId,
                            dayStamp: dateTime.millis,
                            status: 1 as Byte,
                            afterUnitId: afterUnit.afterUnitId
                    )
            }
            saveLeavePlanLevelAfterUnitDailyList(unitDailies)
        }


    }

    List<LeavePlanLevelAfterUnit> fetchLeavePlanLevelAfterUnitByAfterUnitId(Long afterUnitId){
        LeavePlanLevelAfterUnit.findAllByAfterUnitIdAndStatus(afterUnitId,1 as Byte)
    }

    def saveLeavePlanLevelAfterUnitList(List<LeavePlanLevelAfterUnit> unitDailies) {
        unitDailies*.save(failOnError: true, flush: true)
    }
    def  deleteLeavePlanLevelAfterUnitByCampusIdAndAfterUnitId(Long campusId, Long afterUnitId){
        List<LeavePlanLevelAfterUnit> planLevelAfterUnitList = fetchLeavePlanLevelAfterUnitByAfterUnitId(afterUnitId)
        if (planLevelAfterUnitList!=null && planLevelAfterUnitList.size() > 0) {
            planLevelAfterUnitList*.status=0
            saveLeavePlanLevelAfterUnitList(planLevelAfterUnitList)
        }
    }

    def deleteAfterUintJoinLeavePlanLevelAfterUnit(Long campusId, Long afterUnitId) {

        deleteLeavePlanLevelAfterUnitDailyByAfterUnitIdAndTimestamp(campusId,afterUnitId)
        deleteLeavePlanLevelAfterUnitByCampusIdAndAfterUnitId(campusId,afterUnitId)
    }

    def saveLeavePlanLevelAfterUnitDailyList(List<LeavePlanLevelAfterUnitDaily> unitDailies) {
        unitDailies*.save(failOnError: true, flush: true)
    }

    def  saveLeavePlanLevelAfterUnitDaily(LeavePlanLevelAfterUnitDaily daily){
        daily.save(failOnError: true, flush: true)
    }


    LeavePlanLevelAfterUnitDaily fetchLeavePlanLevelAfterUnitDailyByAfterUnitIdAndTimestamp(Long campusId, Long afterUnitId, Long timestamp) {
        LeavePlanLevelAfterUnitDaily.findByCampusIdAndAfterUnitIdAndDayStampAndStatus(campusId, afterUnitId, timestamp, 1 as Byte)
    }

    def  deleteLeavePlanLevelAfterUnitDailyByAfterUnitIdAndTimestamp(Long campusId, Long afterUnitId){
        Long stampTime = TimeUtils.getDateStartTime(System.currentTimeMillis()).time
        LeavePlanLevelAfterUnitDaily unitDaily = fetchLeavePlanLevelAfterUnitDailyByAfterUnitIdAndTimestamp(campusId,afterUnitId,stampTime)
        if (unitDaily){
            unitDaily.status = 0 as Byte
            saveLeavePlanLevelAfterUnitDaily(unitDaily)
        }
    }




}

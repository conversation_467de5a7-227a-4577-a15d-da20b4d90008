package hiiadmin.planAfterSchool

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.biz.PositionService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.pickUp.*
import hiiadmin.pickUp.AfterUnitService
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.apache.commons.collections.CollectionUtils
import org.joda.time.DateTime
import timetabling.Position
import timetabling.device.Device
import timetabling.pickUp.*

import java.text.SimpleDateFormat

@Transactional
@Slf4j
class LeavePlanTaskService {

    static final int TEN_MINUTE_MILLS = 600000

    LeavePlanLevelService leavePlanLevelService

    LeavePlanLevelAuthDeviceService leavePlanLevelAuthDeviceService

    LeavePlanLevelParentAuthDeviceService leavePlanLevelParentAuthDeviceService

    LeavePlanLevelScreenDeviceService leavePlanLevelScreenDeviceService

    LeavePlanLevelAfterUnitService leavePlanLevelAfterUnitService

    AfterUnitService afterUnitService

    DeviceService deviceService

    PositionService positionService

    LeavePlanTask fetchLeavePlanTaskById(long taskId) {
        LeavePlanTask.get(taskId)
    }


    LeavePlanTask fetchTodayTaskByCampusId(Long campusId) {
        Integer dayOfWeek = new DateTime().dayOfWeek
        LeavePlanTask leavePlanTask = LeavePlanTask.findByCampusIdAndDayOfWeekAndStatus(campusId, dayOfWeek, 1)
        return leavePlanTask
    }

    List<LeavePlanTaskVO> transformLeavePlanTaskVO(Long campusId) {
        Map<Integer, LeavePlanTask> dayOfWeekLeavePlanTaskMap = transformDayOfWeekLeavePlanTaskMap(campusId)

        Multimap<Long, LeavePlanLevel> taskIdPlanLevelMultimap = leavePlanLevelService.transformTaskIdPlanLevelMultimap4Campus(campusId)
        Multimap<Long, LeavePlanLevelAuthDevice> levelIdAuthDeviceMultimap = leavePlanLevelAuthDeviceService.transformLevelIdAuthDeviceMultimap4Campus(campusId)
        Multimap<Long, LeavePlanLevelParentAuthDevice> levelIdParentAuthDeviceMultimap = leavePlanLevelParentAuthDeviceService.transformParentAuthDeviceMultimap(campusId)
        Multimap<Long, LeavePlanLevelScreenDevice> levelIdScreenDeviceMultimap = leavePlanLevelScreenDeviceService.transformScreenDeviceMultimap(campusId)
        Multimap<Long, LeavePlanLevelAfterUnit> levelIdAfterUnitMultimap = leavePlanLevelAfterUnitService.transformAfterUnitMultimap(campusId)
        Map<Long, Position> positionMap = positionService.transformationDeviceIdKeyPosition(campusId)

        Map<Long, Device> deviceIdMap = deviceService.transformDeviceMap(campusId)
        Map<Long, AfterUnit> afterUnitIdMap = afterUnitService.transformAfterUnitMap(campusId)

        List<LeavePlanTaskVO> voList = (1..7).collect { dayOfWeek ->

            LeavePlanTask planTask = dayOfWeekLeavePlanTaskMap.get(dayOfWeek)
            if (!planTask) {
                planTask = LeavePlanTask.findOrCreateByCampusIdAndDayOfWeek(campusId, dayOfWeek)
                planTask.status = 1
                planTask.save(failOnError: true, flush: true)
            }
            LeavePlanTaskVO planTaskVO = new LeavePlanTaskVO().buildVO(planTask)

            long taskId = planTask.id
            Set<LeavePlanLevel> leavePlanLevelSet = taskIdPlanLevelMultimap.get(taskId)

            planTaskVO.levelList = leavePlanLevelSet.collect { planLevel ->
                LeavePlanLevelVO planLevelVO = new LeavePlanLevelVO().buildVO(planLevel)

                long levelId = planLevel.id
                Set<LeavePlanLevelAuthDevice> authDeviceSet = levelIdAuthDeviceMultimap.get(levelId)
                List<LeavePlanLevelAuthDeviceVO> authDeviceVOList = []
                authDeviceSet.each { authDevice ->
                    Device device = deviceIdMap.get(authDevice.deviceId)
                    if (device) {

                        LeavePlanLevelAuthDeviceVO leavePlanLevelAuthDeviceVO = new LeavePlanLevelAuthDeviceVO().buildVO(device)
                        Position position = positionMap.get(authDevice.deviceId)
                        if (position){
                            leavePlanLevelAuthDeviceVO.positionId = PhenixCoder.encodeId(position.id)
                            leavePlanLevelAuthDeviceVO.positionName = position.name
                        }
                        authDeviceVOList << leavePlanLevelAuthDeviceVO
                    }
                }
                planLevelVO.authDevices = authDeviceVOList
                Set<LeavePlanLevelParentAuthDevice> parentAuthDeviceSet = levelIdParentAuthDeviceMultimap.get(levelId)
                List<LeavePlanLevelParentAuthDeviceVO> parentAuthDeviceVOList = []
                parentAuthDeviceSet.each { parentDevice ->
                    Device device = deviceIdMap.get(parentDevice.deviceId)
                    if (device) {
                        LeavePlanLevelParentAuthDeviceVO leavePlanLevelParentAuthDeviceVO = new LeavePlanLevelParentAuthDeviceVO().buildVO(device)
                        Position position = positionMap.get(parentDevice.deviceId)
                        if (position){
                            leavePlanLevelParentAuthDeviceVO.positionId = PhenixCoder.encodeId(position.id)
                            leavePlanLevelParentAuthDeviceVO.positionName = position.name
                        }
                        parentAuthDeviceVOList << leavePlanLevelParentAuthDeviceVO
                    }
                }
                planLevelVO.parentDevices = parentAuthDeviceVOList
                Set<LeavePlanLevelScreenDevice> screenDeviceSet = levelIdScreenDeviceMultimap.get(levelId)
                List<LeavePlanLevelScreenDeviceVO> levelScreenDeviceVOList = []
                screenDeviceSet.each { screenDevice ->
                    Device device = deviceIdMap.get(screenDevice.deviceId)
                    if (device) {
                        LeavePlanLevelScreenDeviceVO leavePlanLevelScreenDeviceVO = new LeavePlanLevelScreenDeviceVO().buildVO(device)
                        Position position = positionMap.get(screenDevice.deviceId)
                        if (position){
                            leavePlanLevelScreenDeviceVO.positionId = PhenixCoder.encodeId(position.id)
                            leavePlanLevelScreenDeviceVO.positionName = position.name
                        }
                        levelScreenDeviceVOList << leavePlanLevelScreenDeviceVO
                    }
                }
                planLevelVO.screenDevices = levelScreenDeviceVOList
                Set<LeavePlanLevelAfterUnit> afterUnitSet = levelIdAfterUnitMultimap.get(levelId)
                List<LeavePlanLevelAfterUnitVO> levelAfterUnitVOList = []
                afterUnitSet.each { afterUnit ->
                    AfterUnit unit = afterUnitIdMap.get(afterUnit.afterUnitId)
                    if (unit) {
                        LeavePlanLevelAfterUnitVO leavePlanLevelAfterUnitVO = new LeavePlanLevelAfterUnitVO().buildVO(unit)
                        levelAfterUnitVOList << leavePlanLevelAfterUnitVO
                    }
                }
                planLevelVO.afterUnits = levelAfterUnitVOList
                planLevelVO
            }
            planTaskVO
        }
        voList
    }

    Map<Integer, LeavePlanTask> transformDayOfWeekLeavePlanTaskMap(long campusId) {
        List<LeavePlanTask> taskList = LeavePlanTask.findAllByCampusId(campusId)

        Map<Integer, LeavePlanTask> dayOfWeekLeavePlanTaskMap = taskList.collectEntries {
            [it.dayOfWeek, it]
        }
        dayOfWeekLeavePlanTaskMap
    }

    LeavePlanTask fetchTodayTaskByClientId(Long campusId) {
        Integer dayOfWeek = new DateTime().dayOfWeek
        LeavePlanTask leavePlanTask = LeavePlanTask.findByCampusIdAndDayOfWeekAndStatus(campusId, dayOfWeek, 1)
        leavePlanTask
    }

    void createOrUpdateLevelPlan(long campusId, String json) {
        DateTime dateTime = new DateTime()
        JSONArray jsonArray = JSONArray.parseArray(json)
        Multimap<Integer, Long> dayOrWeekPlanLevelIdMultimap = HashMultimap.create()

        jsonArray.each { jo1 ->
            JSONObject weekJsonObject = jo1 as JSONObject
            Integer dayOfWeek = weekJsonObject.dayOfWeek as Integer

            LeavePlanTask planTask = LeavePlanTask.findOrCreateByCampusIdAndDayOfWeek(campusId, dayOfWeek)
            planTask.status = 1
            planTask.save(failOnError: true, flush: true)

            Long taskId = planTask.id

            JSONArray levelJsonArray = weekJsonObject.levelList as JSONArray

            List<Long> levelAfterUnitIdList = []
            Set<String> nameSet = []
            levelJsonArray.each { jo2 ->
                JSONObject levelJsonObject = jo2 as JSONObject
                Integer index = levelJsonObject.index as Integer
                String name = levelJsonObject.name,
                       afterUnitIds = levelJsonObject.afterUnitIds ?: null,
                       authDeviceIds = levelJsonObject.authDeviceIds ?: null,
                       parentDeviceIds = levelJsonObject.parentDeviceIds ?: null,
                       screenDeviceIds = levelJsonObject.screenDeviceIds ?: null

                Boolean delete = levelJsonObject.delete as Boolean

                Long levelId = levelJsonObject.id as Long,
                     startTime = levelJsonObject.startTime as Long,
                     endDateTime = levelJsonObject.endTime as Long

                if (startTime && endDateTime) {
                    if (!isStartTimeGT10EndTime(startTime, endDateTime)) {
                        throw new HiiAdminException("开始时间和结束时间 之间间隔小于10分钟")
                    }
                }

                List<Long> afterUnitIdIdList = afterUnitIds?.split(",")?.collect { it as Long }

                if (!delete) {
                    if (!PatternUtils.isName(name)) {
                        throw new HiiAdminException("批次名称异常【${name}】，仅支持汉字、字母、数字")
                    }
                    if (!startTime) {
                        throw new HiiAdminException("批次【${name}】未配置时间")
                    }
                    if (afterUnitIds) {
                        Set<Long> interSectionIds = CollectionUtils.intersection(afterUnitIdIdList, levelAfterUnitIdList)
                        if (interSectionIds.size() == 0) {
                            levelAfterUnitIdList.addAll(afterUnitIdIdList)
                        } else {
                            Long afterUnitId = interSectionIds[0]
                            AfterUnit afterUnit = afterUnitService.getAfterUnit(afterUnitId)
                            LeavePlanLevelAfterUnit leavePlanLevelAfterUnit = leavePlanLevelAfterUnitService.fetchLeavePlanLevelByTaskAndAfterUnitId(taskId, afterUnitId)
                            LeavePlanLevel leavePlanLevel = LeavePlanLevel.findById(leavePlanLevelAfterUnit?.levelId)
                            throw new HiiAdminException("同一个班级不能在同一天不同批次, 班级:${afterUnit?.name}, 批次名称:${leavePlanLevel?.name}")
                        }
                    }
                }

                List<Long> authDeviceIdList = authDeviceIds?.split(",")?.collect { it as Long }
                List<Long> parentDeviceIdList = parentDeviceIds?.split(",")?.collect { it as Long }
                List<Long> screenDeviceIdList = screenDeviceIds?.split(",")?.collect { it as Long }


                if (levelId) {
                    leavePlanLevelAuthDeviceService.deleteLeavePlanLevelAuthDevice4LevelId(levelId)
                    leavePlanLevelParentAuthDeviceService.deleteLeavePlanLevelAuthDevice4LevelId(levelId)
                    leavePlanLevelScreenDeviceService.deleteLeavePlanLevelAuthDevice4LevelId(levelId)
                    leavePlanLevelAfterUnitService.deleteLeavePlanLevelAuthDevice4LevelId(levelId)
                    if (delete) {
                        leavePlanLevelService.deleteLeavePlanLevel4LevelId(levelId)
                    } else {
                        int last = nameSet.size()
                        nameSet.add(name)
                        int now = nameSet.size()
                        if (now == last) {
                            throw new HiiAdminException("批次名称不能重复【${name}】".toString())
                        }
                        leavePlanLevelService.updateLeavePlanLevel(levelId, index, name, startTime, endDateTime, endDateTime)

                        initLevelInfo(campusId, taskId, levelId, authDeviceIdList, parentDeviceIdList, screenDeviceIdList, afterUnitIdIdList)

                        dayOrWeekPlanLevelIdMultimap.put(dayOfWeek, levelId)

                    }
                } else {
                    int last = nameSet.size()
                    nameSet.add(name)
                    int now = nameSet.size()
                    if (now == last) {
                        throw new HiiAdminException("批次名称不能重复【${name}】".toString())
                    }
                    LeavePlanLevel planLevel = leavePlanLevelService.createAndSaveLeavePlanLevel(campusId, taskId, name, index, startTime, endDateTime, endDateTime)
                    levelId = planLevel.id

                    initLevelInfo(campusId, taskId, levelId, authDeviceIdList, parentDeviceIdList, screenDeviceIdList, afterUnitIdIdList)

                    dayOrWeekPlanLevelIdMultimap.put(dayOfWeek, levelId)
                }
            }
        }
        List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndType(campusId, ConstantEnum.DeviceType.PICK_SCREEN.type)
        deviceService.iotOperationPushByDeviceListAndType(deviceList, ConstantEnum.LeanCloudType.LEAVE_SCHOOL_DATA.type, null, ConstantEnum.DeviceType.PICK_SCREEN.type)

    }


    void initLevelInfo(long clientId, long taskId, long levelId, List<Long> authDeviceIdList, List<Long> parentDeviceIdList, List<Long> screenDeviceIdList, List<Long> afterUnitIdIdList) {
        leavePlanLevelAuthDeviceService.initLeavePlanLevelAuthDevice(clientId, taskId, levelId, authDeviceIdList)
        leavePlanLevelParentAuthDeviceService.initLeavePlanLevelParentAuthDevice(clientId, taskId, levelId, parentDeviceIdList)
        leavePlanLevelScreenDeviceService.initLeavePlanLevelScreenDevice(clientId, taskId, levelId, screenDeviceIdList)
        leavePlanLevelAfterUnitService.initLeavePlanLevelAfterUnit(clientId, taskId, levelId, afterUnitIdIdList)

    }

    boolean isStartTimeGT10EndTime(Long startTime, Long endTime) {
        boolean flag = false
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm")
        String startDateStr = simpleDateFormat.format(new Date(startTime))
        startTime = TimeUtils.strToDate(startDateStr, "HH:mm").time

        String endDateStr = simpleDateFormat.format(new Date(endTime))
        endTime = TimeUtils.strToDate(endDateStr, "HH:mm").time

        if (endTime - startTime >= TEN_MINUTE_MILLS) {
            flag = true
        }
        return flag
    }

}

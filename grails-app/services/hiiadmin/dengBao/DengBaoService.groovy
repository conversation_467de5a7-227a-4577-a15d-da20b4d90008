package hiiadmin.dengBao

import grails.gorm.transactions.Transactional
import org.springframework.scheduling.annotation.Async
import timetabling.OpRecord

@Transactional
class DengBaoService {

    /**
     * 等保审计用，记录每次操作
     * @param staffId
     * @param visit
     * @param controllerName
     * @param actionName
     * @param params
     * @return
     */
    @Async
    def saveRecord(String ip, Long staffId, String visit, String controllerName, String actionName, Map params) {
        OpRecord opRecord = new OpRecord()
        opRecord.dateCreated = new Date()
        opRecord.lastUpdated = new Date()
        opRecord.ip = ip
        opRecord.staffId = staffId
        opRecord.visit = visit
        opRecord.controllerName = controllerName
        opRecord.actionName = actionName
        opRecord.params = params.toMapString()
        opRecord.save(failOnError: true)
    }
}

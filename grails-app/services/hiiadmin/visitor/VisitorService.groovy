package hiiadmin.visitor

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.VisitorVO
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime
import org.springframework.util.CollectionUtils
import timetabling.gated.Ferries
import timetabling.visitor.Visitor

import static hiiadmin.ConstantEnum.ApprovalStatus
import static hiiadmin.ConstantEnum.UserTypeEnum

@Transactional
class VisitorService {

    UserEncodeInfoService userEncodeInfoService

    def fetchVisitorViaIdCardAndDaytimeStamp(String idCard, Long daytimeStamp) {
        Visitor.findByIdCardAndDaytimeStampAndStatusInList(idCard, daytimeStamp, [ApprovalStatus.APPROVALING.status, ApprovalStatus.APPROVALED.status])
    }

    def fetchVisitorViaMobileAndDaytimeStamp(String mobile, Long daytimeStamp) {
        Visitor.findByMobileAndDaytimeStampAndStatusInList(mobile, daytimeStamp, [ApprovalStatus.APPROVALING.status, ApprovalStatus.APPROVALED.status])
    }

    Visitor fetchVisitorById(Long id) {
        Visitor.get(id)
    }

    def fetchVisitorByTrueMobile(String mobile) {
        userEncodeInfoService.fetchVisitorByTrueMobile(mobile)
    }

    def fetchVisitorByCampusIdAndSearchValuePage(Long campusId, String searchValue, Long daytimeStamp, int s = 30, Integer p = null) {
        StringBuilder sb = new StringBuilder()

        sb.append("SELECT v ")
        Map map = buildHQL(sb, campusId, searchValue, daytimeStamp)
        if (p) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        List<Visitor> visitorList = Visitor.executeQuery(sb, map)

        StringBuilder countSb = new StringBuilder()
        countSb.append("SELECT COUNT(1)")
        Map countMap = buildHQL(countSb, campusId, searchValue, daytimeStamp)
        Integer count = Visitor.executeQuery(countSb.toString(), countMap)[0] as Integer

        [list: visitorList, total: count]
    }

    def buildHQL(StringBuilder sb, Long campusId, String searchValue, Long daytimeStamp) {
        Map map = [:]
        sb.append(" FROM Visitor as v where campusId= :campusId and daytimeStamp = :daytimeStamp")
        map.put("campusId", campusId)
        map.put("daytimeStamp", TimeUtils.getFormatDateToDate(daytimeStamp ?: System.currentTimeMillis()).time)
        if (StringUtils.isNotBlank(searchValue)) {
            sb.append(" AND visitorName LIKE :name")
            map.put("name", searchValue + "%")
        }
        map
    }

    def fetchVisitorByCampusIdAndSearchValuePageV2(Long campusId, String searchValue, Long daytimeStamp, Long startTime, Long endTime, Integer timeType, Boolean visited, Integer p, Integer s) {

        List<Long> userIdList = []
        if (StringUtils.isNotBlank(searchValue)) {

            if (searchValue.isNumber()) {
                userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, searchValue)
                if (CollectionUtils.isEmpty(userIdList)) {
                    return [list: [], total: 0]
                }
            } 
        }
        
        StringBuilder stringBuilder = new StringBuilder()
        stringBuilder.append("SELECT v ")
        Map map = buildHQLV2(stringBuilder, campusId, searchValue, daytimeStamp, startTime, endTime, timeType, visited, userIdList)
        if (p) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        List<Visitor> visitorList = Visitor.executeQuery(stringBuilder.toString(), map)

        StringBuilder countSb = new StringBuilder()
        countSb.append("SELECT COUNT(1)")
        Map countMap = buildHQLV2(countSb, campusId, searchValue, daytimeStamp, startTime, endTime, timeType, visited, userIdList)
        Integer count = Visitor.executeQuery(countSb.toString(), countMap)[0] as Integer

        [list: visitorList, total: count]
    }

    def buildHQLV2(StringBuilder sb, Long campusId, String searchValue, Long daytimeStamp, Long startTime, Long endTime, Integer timeType, Boolean visited, List<Long> userIdList) {
        Map map = [:]
        sb.append(" FROM Visitor as v where v.campusId = :campusId ")
        map.put("campusId", campusId)
        
        if (daytimeStamp) {
            sb.append(" AND v.daytimeStamp = :daytimeStamp ")
            map.put("daytimeStamp", new DateTime(daytimeStamp).withMillisOfDay(0).millis)
        }

        if (StringUtils.isNotBlank(searchValue)) {

            if (userIdList) {
                sb.append(" AND v.id IN :userIdList ")
                map.put("userIdList", userIdList)
            } else {
                sb.append(" AND v.visitorName LIKE :name")
                map.put("name", searchValue + "%")
            }
        }
        
        if (startTime!= null && endTime!= null) {
            
            if (timeType != null) {
                Date startDate = new DateTime(startTime).withMillisOfDay(0).toDate()
                Date endDate = new DateTime(endTime).withMillisOfDay(0).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate()
                switch (timeType) {
                    case 1:
                        sb.append(" AND v.dateTime BETWEEN :startDate AND :endDate ")
                        break
                    case 2:
                        sb.append(" AND v.comeTime BETWEEN :startDate AND :endDate ")
                        break
                }

                map.put("startDate", startDate)
                map.put("endDate", endDate)
            } else {
                startTime = new DateTime(startTime).withMillisOfDay(0).millis
                endTime = new DateTime(endTime).plusDays(1).withMillisOfDay(0).millis
                sb.append(" AND v.daytimeStamp BETWEEN :startTime AND :endTime ")
                map.put("startTime", startTime)
                map.put("endTime", endTime)
            }
        }
        
        if (visited != null) {
            if (visited) {
                sb.append(" AND v.comeTime IS NOT NULL ")
            } else {
                sb.append(" AND v.comeTime IS NULL ")
            }
        }
        
        sb.append(" ORDER BY v.id DESC ")
        map
    }

    def fetchAllVisitorByCampusIdAndSearchValue(Long campusId, String searchValue, int p, int s) {
        def c = Visitor.createCriteria()
        def resultMap = c.list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            eq("status", ApprovalStatus.APPROVALED.status)
            like("visitorName", searchValue + "%")
            order("id", "desc")
        }

        [list: resultMap as List<Visitor>, total: resultMap.totalCount ?: 0]
    }

    List<Visitor> fetchAllVisitorByCampusIdAndDay(Long campusId, Long dayStamp) {
        Visitor.findAllByCampusIdAndDaytimeStampAndStatus(campusId, new DateTime(dayStamp).withMillisOfDay(0).millis, ApprovalStatus.APPROVALED.status)
    }

    def fetchVisitorByCampusIdAndSearchValuePageJoinRelatedUser(Long campusId, String searchValue, Long daytimeStamp, Integer firm, Boolean firmStatus, int s = 30, Integer p = null) {
        Map map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("SELECT v FROM Visitor as v where campusId= :campusId and daytimeStamp = :daytimeStamp")
        map.put("campusId", campusId)
        map.put("daytimeStamp", TimeUtils.getFormatDateToDate(daytimeStamp ?: System.currentTimeMillis()).time)
        if (StringUtils.isNotBlank(searchValue)) {
            sb.append(" AND f.name LIKE :name")
            map.put("name", searchValue + "%")
        }
        if (firmStatus) {
            sb.append(""" AND EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = v.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status )""")
            map.put("userType", UserTypeEnum.VISITORS.type)
            map.put("firm", firm)
        } else {
            sb.append(""" AND NOT EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = v.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status)""")
            map.put("userType", UserTypeEnum.VISITORS.type)
            map.put("firm", firm)
        }
        if (p) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        map.put("status", 1 as byte)
        Ferries.executeQuery(sb, map)
    }

    Integer countVisitorByCampusIdAndSearchValuePageJoinRelatedUser(Long campusId, String searchValue, Long daytimeStamp, Integer firm, Boolean firmStatus) {
        StringBuilder sb = new StringBuilder()
        Map map = [:]
        sb.append("SELECT COUNT(f.id) FROM Visitor as v where campusId= :campusId and daytimeStamp = :daytimeStamp")
        map.put("campusId", campusId)
        map.put("daytimeStamp", TimeUtils.getFormatDateToDate(daytimeStamp ?: System.currentTimeMillis()).time)
        if (StringUtils.isNotBlank(searchValue)) {
            sb.append(" AND f.name LIKE :name")
            map.put("name", searchValue + "%")
        }
        if (firmStatus) {
            sb.append(""" AND EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = v.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status )""")
            map.put("userType", UserTypeEnum.VISITORS.type)
            map.put("firm", firm)
        } else {
            sb.append(""" AND NOT EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = v.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status)""")
            map.put("userType", UserTypeEnum.VISITORS.type)
            map.put("firm", firm)
        }
        map.put("status", 1 as byte)
        Ferries.executeQuery(sb, map)[0] as Integer
    }


    List<Visitor> fetchVisitorByIds(List<Long> visitorIds) {
        if (!visitorIds) {
            return []
        }
        Visitor.findAllByIdInList(visitorIds)
    }

    List<Visitor> fetchAllVisitorByCampusId(Long campusId) {
        Visitor.findAllByCampusIdAndStatus(campusId, ApprovalStatus.APPROVALED.status)
    }
    
    
    List<VisitorVO> transformVisitorToVisitorVO(List<Visitor> visitors, Boolean encode) {
        List<VisitorVO> visitorVOS = []
        
        List<Long> visitorIds = visitors.collect { it.id }
        
        Map<Long, String> visitorMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, visitorIds, encode)
        Map<Long, String> visitorIdCardMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.ID_CARD.type, UserTypeEnum.VISITORS.type, visitorIds, encode)
        
        List<Long> teacherIds = visitors.collect { it.respondentId }
        teacherIds?.removeAll([null])
        
        Map<Long, String> teacherMobileMap = [:]
        if (teacherIds) {
            teacherMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacherIds, encode)
        }
        
        visitors?.each { Visitor visitor -> 
            VisitorVO vo = new VisitorVO()
            vo.id = PhenixCoder.encodeId(visitor.id)
            vo.mobile = visitorMobileMap.get(visitor.id) ?: ''
            
            if (visitor.respondentId) {
                vo.respondentId = PhenixCoder.encodeId(visitor.respondentId)
            }
            
            vo.respondentName = visitor.respondentName
            
            if (visitor.respondentId && teacherMobileMap.containsKey(visitor.respondentId)) {
                vo.respondentName = vo.respondentName + "（" + teacherMobileMap.get(visitor.respondentId) + "）"
            }
            
            if (visitor.dateTime) {
                vo.dateTime = new DateTime(visitor.dateTime).toString("yyyy-MM-dd HH:mm")
            }
            
            if (visitor.comeTime != null) {
                vo.visited = true
            }
            
            if (visitor.comeTime) {
                vo.comeTime = new DateTime(visitor.comeTime).toString("yyyy-MM-dd HH:mm")
            }
            
            vo.pic = visitor.pic
            vo.memo = visitor.memo
            if (visitor.idCard) {
                if (encode) {
                    vo.idCard = visitor.idCard?.substring(0,2) + "************" + visitor.idCard?.substring(14)
                } else {
                    vo.idCard = visitor.idCard
                }
            } else {
                vo.idCard = visitorIdCardMap.get(visitor.id) ?: ''
            }
            vo.visitorName = visitor.visitorName
            
            visitorVOS << vo
        }
        
        visitorVOS
        
    }
}

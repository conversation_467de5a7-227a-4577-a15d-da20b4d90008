package hiiadmin

import com.alibaba.fastjson.JSON
import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTCreationException
import com.auth0.jwt.interfaces.Claim
import com.auth0.jwt.interfaces.DecodedJWT
import com.bugu.ServiceResult
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.auth.HikCloudSingleLoginService
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.login.BuildLoginUrlDTO
import hiiadmin.module.login.UserSingleLoginInfoDTO
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import timetabling.Client
import timetabling.Department
import timetabling.user.Staff
import timetabling.user.Teacher

@Transactional
@Slf4j
class Auth3Service {

    final static Logger logger = LoggerFactory.getLogger(Auth3Service.class)

    TeacherService teacherService

    ClientMenuService clientMenuService

    DepartmentService departmentService

    UserEncodeInfoService userEncodeInfoService

    HikCloudSingleLoginService hikCloudSingleLoginService


    ServiceResult<UserSingleLoginInfoDTO> campusSingleLoginByUserIdAndCampusMenuId(Long campusMenuId, Long campusId, Long userId, Integer loginUrlType) {
        if (campusMenuId == null) {
            return ServiceResult.failure("500", "必要参数为空")
        }
        switch (loginUrlType) {
            case ConstantEnum.LoginUrlType.DEFAULT.type:
                return campusSingleLoginByUserIdAndCampusMenuIdDefault(campusMenuId, campusId, userId)
            case ConstantEnum.LoginUrlType.HIK_CLOUD.type:
                return campusSingleLoginByUserIdAndCampusMenuIdHikCloud(campusMenuId, campusId, userId)
        }
        return ServiceResult.failure("500", "未知的登录类型")
    }

    ServiceResult<UserSingleLoginInfoDTO> campusSingleLoginByUserIdAndCampusMenuIdHikCloud(Long campusMenuId, Long campusId, Long userId) {
        log.info("[campusSingleLoginByUserIdAndCampusMenuIdHikCloud]campusMenuId@${campusMenuId},campusId@${campusId}".toString())
        Client client = clientMenuService.fetchClientByCampusIdAndCampusMenuId(campusId, campusMenuId)
        if (client == null) {
            return ServiceResult.failure("500", "该菜单未配置单点权限")
        }
        UserSingleLoginInfoDTO infoDTO = new UserSingleLoginInfoDTO()
        infoDTO.userId = userId
        infoDTO.userType = ConstantEnum.UserTypeEnum.TEACHER.type
        infoDTO.campusId = campusId
        try {
            BuildLoginUrlDTO buildLoginUrlDTO = hikCloudSingleLoginService.buildLoginUrlHikCloud(client)
            infoDTO.redirectUri = buildLoginUrlDTO.redirectUri
        } catch (Exception e) {
            return ServiceResult.failure("500", e.getMessage())
        }
        return ServiceResult.success(infoDTO)
    }

    ServiceResult<UserSingleLoginInfoDTO> campusSingleLoginByUserIdAndCampusMenuIdDefault(Long campusMenuId, Long campusId, Long userId) {
        String code = teacherService.getTeacherJobNumByByCampusIdAndTeacherId(campusId, userId)
        log.info("[campusSingleLoginByUserIdAndCampusMenuId]获取单点token,campusMenuId@${campusMenuId},campusId@${campusId},user@${userId}".toString())
        if (StringUtils.isEmpty(code)) {
            return ServiceResult.failure("500", "用户工号不存在")
        }
        Teacher teacher = teacherService.fetchTeacherById(userId)
        if (teacher == null) {
            return ServiceResult.failure("500", "用户不存在")
        }
        String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacher.id)
        if (StringUtils.isEmpty(mobile)) {
            return ServiceResult.failure("500", "用户手机号不存在")
        }
        Client client = clientMenuService.fetchClientByCampusIdAndCampusMenuId(campusId, campusMenuId)
        if (client == null) {
            return ServiceResult.failure("500", "该菜单未配置单点权限")
        }
        List<Department> departmentList = departmentService.findDepartmentListByTeacherIdAndCampusId(teacher.id, campusId)
        UserSingleLoginInfoDTO infoDTO = new UserSingleLoginInfoDTO()
        if (departmentList.size() > 0) {
            List<DepartmentVO> departmentVOList = []
            log.info("[campusSingleLoginByUserIdAndCampusMenuId]departmentIdList@${departmentList*.id}".toString())
            departmentList.each {
                Department department ->
                    departmentVOList << new DepartmentVO(id: department.id, name: department.name)
            }
            infoDTO.departmentList = departmentVOList
        }
        infoDTO.userId = teacher?.id
        infoDTO.userType = ConstantEnum.UserTypeEnum.TEACHER.type
        infoDTO.campusId = campusId
        infoDTO.name = teacher?.name
        infoDTO.teacherCode = code
        infoDTO.mobile = mobile
        String token = genThirdPartTokenByUserInfo(infoDTO, client.clientSecret)
        infoDTO.token = token
        infoDTO.clientId = client.clientId
        infoDTO.redirectUri = client.registeredRedirectUri + "?token=" + token + "&campusId=${campusId}&clientId=${client.clientId}"

        return ServiceResult.success(infoDTO)
    }


    def genThirdPartTokenByUserInfo(UserSingleLoginInfoDTO infoDTO, String clientSecret) {
        String token = ""
        try {
            //token过期时间
            Date expiresAt = TimeUtils.getSpecifiedDateTimeBySeconds(new Date(), 60 * 100)//5分钟
            Algorithm algorithm = Algorithm.HMAC256(clientSecret)
            token = JWT.create()
                    .withIssuer("bugu auth")
                    .withClaim("userInfo", JSON.toJSONString(infoDTO))
                    .withClaim("staffId", PhenixCoder.encodeId(infoDTO.userId))
                    .withClaim("code", infoDTO.teacherCode)
                    .withClaim("mobile", infoDTO.mobile)
                    .withExpiresAt(expiresAt)
                    .sign(algorithm)
        } catch (JWTCreationException exception) {
            //无效的签名配置/无法转换声明
            logger.error("为第三方sso登录生成token exception:{}", exception.message)
        }
        return token
    }

    /**
     * 生成第三方联合登录token
     * @param client
     */
    def genThirdPartToken(Client client, Staff staff) {
        String token = null
        String code
        try {
            code = teacherService.getTeacherJobNumByByCampusIdAndTeacherId(staff?.campusId, staff?.teacherId)
            Teacher teacher = teacherService.fetchTeacherById(staff?.teacherId)
            String mobile = ""
            if (teacher) {
                mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacher.id)
            }
            //token过期时间
            Date expiresAt = TimeUtils.getSpecifiedDateTimeBySeconds(new Date(), 60 * 5)//5分钟
            Algorithm algorithm = Algorithm.HMAC256(client.clientSecret)
            token = JWT.create()
                    .withIssuer("bugu auth")
                    .withClaim("staffId", PhenixCoder.encodeId(staff.id))
                    .withClaim("code", code)
                    .withClaim("mobile", mobile)
                    .withExpiresAt(expiresAt)
                    .sign(algorithm)
        } catch (JWTCreationException exception) {
            //无效的签名配置/无法转换声明
            logger.error("为第三方sso登录生成token exception:{}", exception.message)
        }
        token
    }

    /**
     * 生成第三方联合登录token
     * @param client
     */
    def genSystemToken(TeacherVO teacherVO, Client client) {
        log.info("[genSystemToken]teacherId@${teacherVO.id},code@${teacherVO.code},mobile@${teacherVO.mobile},sectet@${client.clientSecret}".toString())
        String token = null
        try {
            //token过期时间
            Date expiresAt = TimeUtils.getSpecifiedDateTimeBySeconds(new Date(), 60 * 10)//5分钟
            Algorithm algorithm = Algorithm.HMAC256(client.clientSecret)
            token = JWT.create()
                    .withIssuer("bugu auth")
                    .withClaim("teacherId", teacherVO.id)
                    .withClaim("code", teacherVO.code)
                    .withClaim("mobile", teacherVO.mobile)
                    .withExpiresAt(expiresAt)
                    .sign(algorithm)
        } catch (JWTCreationException exception) {
            //无效的签名配置/无法转换声明
            logger.error("为第三方sso登录生成token exception:{}", exception.message)
        }
        token

    }

    /**
     * 解析
     * @param token
     * @param client
     * @return
     */
    def verifyToken(String token, Client client) {
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256(client.clientSecret)).build()
        DecodedJWT jwtHl = verifier.verify(token)
        Map<String, Claim> claims = jwtHl.getClaims()
        claims
    }

    static void main(String[] args) {
        UserSingleLoginInfoDTO infoDTO = new UserSingleLoginInfoDTO(campusId: 1, userId: 1, teacherCode: "123", name: "测试")
        String token
        try {
            Algorithm algorithm = Algorithm.HMAC256("sq89Yai9a2adlZizopz7")
            token = JWT.create()
                    .withIssuer("auth0")
                    .withClaim("info", JSON.toJSONString(infoDTO))
                    .sign(algorithm)
        } catch (JWTCreationException exception) {
            //无效的签名配置/无法转换声明
            System.out.println(exception.message)
        }
        System.out.println(token)

        JWTVerifier verifier = JWT.require(Algorithm.HMAC256("sq89Yai9a2adlZizopz7")).build()
        DecodedJWT jwtHl = verifier.verify(token)
        Map<String, Claim> claims = jwtHl.getClaims()
        //String mobile = claims.get("mobile").asString()
        //String staffId = claims.get("info").asLong()
        String code = claims.get("info").asString()
        // System.out.println(mobile)
        // System.out.println(staffId)
        System.out.println(code)
    }
}

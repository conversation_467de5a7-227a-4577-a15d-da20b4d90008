package hiiadmin.mq.producer

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value


@Slf4j
@Transactional
class AuditLogProducerService {

    @Autowired
    private RocketMessageProducer messageProducer;

    @Value('${rocketmqV2.topic.auditLogsTopic}')
    private String auditLogsTopic

    void sendMessage(String tag, String key, byte[] bodyByte) {
        messageProducer.sendMessage(auditLogsTopic, tag, key, bodyByte)
    }
}

package hiiadmin.mq.producer


import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.apache.rocketmq.client.apis.ClientServiceProvider
import org.apache.rocketmq.client.apis.message.MessageBuilder
import org.apache.rocketmq.client.apis.producer.Producer
import org.apache.rocketmq.client.apis.producer.SendReceipt
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component


/**
 * @author: changzhaoliang* @date: 2020-11-25 09:19
 * @description:
 */
@Slf4j
@Component
class RocketMessageProducer {

    @Autowired
    @Qualifier("rocketV2Producer")
    Producer producer

    /**
     * 发送普通消息
     *
     * @param key
     * @param body
     */
    void sendMessage(String topic, String tag, String key, byte[] body) {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        MessageBuilder messageBuilder = provider.newMessageBuilder()
                .setTopic(topic)
                .setTag(tag)
                .setBody(body);
        if (StringUtils.isNotBlank(key)) {
            messageBuilder.setKeys(key)
        }
        try {
            SendReceipt sendReceipt = producer.send(messageBuilder.build());
            log.info("消息发送成功, messageId={}", sendReceipt.getMessageId());
        } catch (Throwable e) {
            log.error("消息发送失败：{}", e)
        }
    }

    /**
     * 发送延时消息
     *
     * @param key
     * @param body
     */
    void sendDelayMessage(String topic, String tag, String key, byte[] body, long delayTime) {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        MessageBuilder messageBuilder = provider.newMessageBuilder()
                .setTopic(topic)
                .setTag(tag)
                .setBody(body);
        if (StringUtils.isNotBlank(key)) {
            messageBuilder.setKeys(key)
        }
        delayTime = System.currentTimeMillis() + delayTime
        messageBuilder.setDeliveryTimestamp(delayTime)
        try {
            SendReceipt sendReceipt = producer.send(messageBuilder.build());
            log.info("消息发送成功, messageId={}", sendReceipt.getMessageId());
        } catch (Throwable e) {
            log.error("消息发送失败：{}", e)
        }
    }
}

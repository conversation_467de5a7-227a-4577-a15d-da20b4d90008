package hiiadmin.mq

import com.aliyun.openservices.ons.api.Message
import com.aliyun.openservices.ons.api.SendResult
import com.aliyun.openservices.ons.api.bean.OrderProducerBean
import com.aliyun.openservices.ons.api.exception.ONSClientException
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.service.mq.producer.OrderProducerOrdinaryRocketService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired

import javax.annotation.Resource

@Slf4j
@Transactional
abstract class AbstractOrderProducerService implements OrderProducerOrdinaryRocketService {

//    private OrderProducerBean orderProducer
//
//    @Autowired
//    void setOrderProducer(OrderProducerBean orderProducer) {
//        this.orderProducer = orderProducer
//    }
    @Resource
    OrderProducerBean orderProducer

    void sendMessage(Message msg, String shardingKey) {
        try {
            SendResult sendResult = orderProducer.send(msg, shardingKey)
            if (sendResult != null) {
                log.info("[AbstractOrderProducerService]消息发送成功,msg@${sendResult.toString()}".toString())
            }
        } catch (ONSClientException e) {
            log.warn("[AbstractOrderProducerService]消息发送失败,msg@${e.message}".toString())
        }
    }

    void sendMessage(String topic, String tag, String key, String shardingKey, byte[] bodyByte) {
        try {
            Message msg = new Message(topic, tag, bodyByte)
            if (StringUtils.isNotBlank(key)) {
                msg.setKey(key)
                msg.setShardingKey(shardingKey)
            }
            log.info("【sendMessage】发送消息成功,eventMsgTopic@${topic}".toString())
            this.sendMessage(msg, shardingKey)
        } catch (Exception e) {
            log.error("【sendMessage】发送消息失败,msg@${e.message}".toString())
        }
    }
}

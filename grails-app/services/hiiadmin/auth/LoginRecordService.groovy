package hiiadmin.auth

import grails.gorm.transactions.Transactional
import hiiadmin.CacheService
import hiiadmin.exceptions.PasswordInvalidException
import hiiadmin.module.LoginRecordBO
import hiiadmin.msg.MsgService
import hiiadmin.newMenu.BgbPasswordCheckService
import org.springframework.transaction.annotation.Propagation
import timetabling.user.Staff

@Transactional(propagation = Propagation.NOT_SUPPORTED)
class LoginRecordService {

    BgbPasswordCheckService bgbPasswordCheckService

    MsgService msgService

    CacheService cacheService

    private static final long TIMES = 5
    private static final long TIME_LIMIT = 1000 * 60 * 30

    void loginCheckAndCheckPassword(String password, Staff staff, String mobile) {
        String key = getLoginCacheKey(mobile)
        LoginRecordBO bo = cacheService.loginCache.get(key)
        if (bo) {
            if (bo.recordTime > System.currentTimeMillis() - TIME_LIMIT) {
                if (bo.failTimes >= TIMES) {
                    cacheService.loginCache.put(key, bo)
                    throw new PasswordInvalidException("密码错误次数过多，请半小时后重试")
                }
            }
        }
        try {
            bgbPasswordCheckService.checkPassword(password, staff)
            cacheService.loginCache.remove(key)
        } catch (PasswordInvalidException e) {
            updateCacheTimes(key, mobile)
            throw e
        }
    }

    private void updateCacheTimes(String key, String mobile) {
        LoginRecordBO bo = cacheService.loginCache.get(key)
        if (!bo) {
            bo = new LoginRecordBO(
                    account: mobile,
                    recordTime: System.currentTimeMillis(),
                    failTimes: 1
            )
        } else {
            bo.failTimes++
            bo.recordTime = System.currentTimeMillis()
        }
        cacheService.loginCache.put(key, bo)
    }

    boolean loginCheckAndCheckCode(String mobile, String checkNO) {
        String key = getLoginCacheKey(mobile)

        LoginRecordBO bo = cacheService.loginCache.get(key)
        if (bo) {
            if (bo.recordTime > System.currentTimeMillis() - TIME_LIMIT) {
                if (bo.failTimes >= TIMES) {
                    cacheService.loginCache.put(key, bo)
                    throw new PasswordInvalidException("失败次数过多，请半小时后重试")
                }
            }
        }

        if (msgService.checkNOValid(mobile, checkNO)) {
            cacheService.loginCache.remove(key)
            return true
        } else {
            updateCacheTimes(key, mobile)
            return false
        }
    }

    boolean checkResetPassword(String mobile, String checkNO) {
        String key = getResetPasswordCacheKey(mobile)
        LoginRecordBO bo = cacheService.loginCache.get(key)
        if (bo) {
            if (bo.recordTime > System.currentTimeMillis() - TIME_LIMIT) {
                if (bo.failTimes >= TIMES) {
                    cacheService.loginCache.put(key, bo)
                    throw new PasswordInvalidException("失败次数过多，请半小时后重试")
                }
            }
        }
        if (msgService.checkNOValid(mobile, checkNO)) {
            cacheService.loginCache.remove(key)
            return true
        } else {
            updateCacheTimes(key, mobile)
            return false
        }
    }


    static String getLoginCacheKey(String mobile) {
        return mobile + "login"
    }

    static String getResetPasswordCacheKey(String mobile) {
        return mobile + "resetPassword"
    }
}

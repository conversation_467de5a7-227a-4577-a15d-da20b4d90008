package hiiadmin.newMenu

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import hiiadmin.BizErrorCode
import hiiadmin.module.newMenu.BgbCampusMenuCategoryVO
import hiiadmin.module.newMenu.BgbCampusMenuVO
import timetabling.newMenu.BgbCampusCategoryBindMenu
import timetabling.newMenu.BgbCampusMenuCategory

@Transactional
class BgbCampusMenuCategoryService {

    BgbCampusMenuService bgbCampusMenuService

    BgbCampusCategoryBindMenuService bgbCampusCategoryBindMenuService

    def deleteBgbCampusMenuCategory(Long id, ResultVO resultVO) {
        BgbCampusMenuCategory bgbCampusMenuCategory = fetchBgbCampusMenuCategoryById(id)
        List<BgbCampusCategoryBindMenu> bgbCampusCategoryBindMenuList = bgbCampusCategoryBindMenuService.fetchBgbBaseCategoryBindMenuByCampusIdAndCategoryId(bgbCampusMenuCategory.campusId, bgbCampusMenuCategory.id)
        if (bgbCampusCategoryBindMenuList?.size() > 0) {
            resultVO = resultVO.failure(BizErrorCode.DELETE_MENU_CATEGORY_ERROR.code, BizErrorCode.DELETE_MENU_CATEGORY_ERROR.msg)
        } else {
            bgbCampusMenuCategory.status = 0
            bgbCampusMenuCategory.save(failOnError: true)
            resultVO = resultVO.success()
            resultVO.result.put("id", id)
        }
        resultVO
    }

    /**
     * 一级分组排序
     * @param sort
     * @return
     */
    def sortBgbBaseMenuCategory(Long campusId, Byte terminal, String sort) {
        List<BgbCampusMenuCategory> bgbCampusMenuCategories = fetchAllBgbCampusMenuCategoryByCampusIdAndTerminal(campusId, terminal)
        List<Object> objectList = JSONObject.parseArray(sort)
        objectList.each { Object object ->
            Long menuId = object.id
            Integer idx = object.idx
            BgbCampusMenuCategory bgbCampusMenuCategory = bgbCampusMenuCategories.find { it.id == menuId }
            bgbCampusMenuCategory.idx = idx
        }
        bgbCampusMenuCategories*.save(failOnError: true)
    }

    BgbCampusMenuCategory fetchBgbCampusMenuCategoryById(Long id) {
        BgbCampusMenuCategory.findById(id)
    }

    /**
     * 编辑学校菜单分组
     * @param id
     * @param name
     * @param icon
     * @return
     */
    def updateBgbCampusMenuCategory(Long id, String name, String icon, String color, Byte yunXueType) {
        BgbCampusMenuCategory bgbCampusMenuCategory = fetchBgbCampusMenuCategoryById(id)
        bgbCampusMenuCategory.name = name
        bgbCampusMenuCategory.icon = icon
        bgbCampusMenuCategory.color = color
        bgbCampusMenuCategory.yunXueType = yunXueType
        bgbCampusMenuCategory.save(failOnError: true)
    }

    /**
     * 创建学校菜单分组
     * @param schoolId
     * @param campusId
     * @param name
     * @param icon
     * @param terminal
     * @return
     */
    def createBgbCampusMenuCategory(Long schoolId, Long campusId, String name, String icon, Byte terminal, String color, Byte yunXueType) {
        BgbCampusMenuCategory bgbCampusMenuCategory = new BgbCampusMenuCategory(
                schoolId: schoolId,
                campusId: campusId,
                terminal: terminal,
                status: 1 as byte,
                name: name,
                icon: icon,
                color: color,
                yunXueType: yunXueType
        )
        bgbCampusMenuCategory.save(failOnError: true, flush: true)
        bgbCampusMenuCategory.idx = bgbCampusMenuCategory.id.intValue()
        bgbCampusMenuCategory.save(failOnError: true, flush: true)
    }

    /**
     * 判断名称是否重复
     * @param name
     * @return
     */
    def hasName(Long campusId, Byte terminal, String name) {
        BgbCampusMenuCategory.findByCampusIdAndTerminalAndNameAndStatus(campusId, terminal, name, 1 as byte)?.id
    }

    List<BgbCampusMenuCategory> fetchAllBgbCampusMenuCategoryByCampusIdAndTerminal(Long campusId, Byte terminal) {
        BgbCampusMenuCategory.findAllByCampusIdAndTerminalAndStatus(campusId, terminal, 1 as byte)
    }

    List<BgbCampusMenuCategory> fetchAllBgbCampusMenuCategoryByCampusIdAndTerminalAndSearchValue(Long campusId, Byte terminal, String searchValue) {
        if (searchValue) {
            return BgbCampusMenuCategory.findAllByCampusIdAndTerminalAndStatusAndNameLike(campusId, terminal, 1 as byte, '%' + searchValue + '%')
        } else {
            return fetchAllBgbCampusMenuCategoryByCampusIdAndTerminal(campusId, terminal)
        }
    }

    /**
     * 查询一级分组
     * @param campusId
     * @param terminal
     * @param searchValue
     * @return
     */
    def fetchAllBgbCampusMenuCategoryVOListByCampusIdAndTerminal(Long campusId, Byte terminal, String searchValue) {
        List<BgbCampusMenuCategory> bgbCampusMenuCategoryList = fetchAllBgbCampusMenuCategoryByCampusIdAndTerminalAndSearchValue(campusId, terminal, searchValue)
        List<BgbCampusMenuVO> menuVOList = bgbCampusMenuService.fetchAllLevelOneBgbCampusMenuVOByCampusIdAndTerminal(campusId, terminal, searchValue)
        List<BgbCampusMenuCategoryVO> bgbBaseMenuCategoryVOList = bgbCampusMenuCategoryList.collect {
            bgbCampusMenuCategory ->
                BgbCampusMenuCategoryVO bgbCampusMenuCategoryVO = new BgbCampusMenuCategoryVO(
                        id: bgbCampusMenuCategory.id,
                        name: bgbCampusMenuCategory.name,
                        icon: bgbCampusMenuCategory.icon,
                        idx: bgbCampusMenuCategory.idx,
                        color: bgbCampusMenuCategory.color,
                        yunXueType: bgbCampusMenuCategory.yunXueType,
                        menuList: menuVOList.findAll { it.categoryId == bgbCampusMenuCategory.id }?.sort { it.idx }
                )
                bgbCampusMenuCategoryVO
        }
        BgbCampusMenuCategoryVO bgbCampusMenuCategoryVO = new BgbCampusMenuCategoryVO(
                id: 0,
                name: "未分组",
                idx: 99999,
                menuList: menuVOList.findAll { !it.categoryId }?.sort { it.idx }
        )
        bgbBaseMenuCategoryVOList.add(bgbCampusMenuCategoryVO)
        bgbBaseMenuCategoryVOList?.sort { it.idx }
    }

}

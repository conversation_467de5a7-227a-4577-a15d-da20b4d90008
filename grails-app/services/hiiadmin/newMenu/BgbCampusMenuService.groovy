package hiiadmin.newMenu

import com.alibaba.fastjson.JSONObject
import com.google.common.collect.HashMultimap
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.module.newMenu.BgbCampusMenuVO
import timetabling.bi.ReportFormMenu
import timetabling.newMenu.BgbCampusCategoryBindMenu
import timetabling.newMenu.BgbCampusMenu
import timetabling.newMenu.BgbCampusRole
import timetabling.newMenu.BgbCampusRoleMenu
import timetabling.newMenu.BgbMenuCollection

@Transactional
class BgbCampusMenuService {

    BgbCampusCategoryBindMenuService bgbCampusCategoryBindMenuService
    
    BgbCampusRoleMenuService bgbCampusRoleMenuService

    /**
     * 创建学校菜单/二级分组
     * @param schoolId
     * @param campusId
     * @param categoryId
     * @param name
     * @param icon
     * @param terminal
     * @param baseMenuIds
     * @param superMenuId
     * @return
     */
    def createBgbCampusMenu(Long schoolId, Long campusId, Long categoryId, String name, String icon, Byte terminal, String color) {
        List<Long> newMenuIdList = []
        Byte userType = ConstantEnum.UserTypeEnum.TEACHER.type
        switch (terminal) {
            case ConstantEnum.BgbMenuTerminal.PARENT.type:
                userType = ConstantEnum.UserTypeEnum.PARENT.type
                break
            case ConstantEnum.BgbMenuTerminal.STUDENT.type:
                userType = ConstantEnum.UserTypeEnum.STUDENT.type
                break
        }
        BgbCampusMenu bgbCampusMenu = new BgbCampusMenu(
                schoolId: schoolId,
                campusId: campusId,
                name: name,
                icon: icon,
                color: color,
                path: "/" + userType + "/generalLevel2Menu",
                level: 1,
                type: ConstantEnum.BgbBaseMenuType.CATEGORY.type,
                terminal: terminal,
                status: 1 as byte
        )
        bgbCampusMenu.save(failOnError: true, flush: true)
        bgbCampusMenu.idx = bgbCampusMenu.id.intValue()
        bgbCampusMenu.save(failOnError: true, flush: true)

        newMenuIdList.add(bgbCampusMenu.id)
        if (categoryId) {
            BgbCampusCategoryBindMenu bgbCampusCategoryBindMenu = new BgbCampusCategoryBindMenu(
                    campusId: campusId,
                    schoolId: schoolId,
                    categoryId: categoryId,
                    menuId: bgbCampusMenu.id,
                    status: 1 as byte
            )
            bgbCampusCategoryBindMenu.save(failOnError: true)
        }

        newMenuIdList.removeAll([null])
        if (newMenuIdList != null && newMenuIdList?.size() > 0) {
            bindSuperRole(schoolId, campusId, newMenuIdList?.unique())
        }
    }

    def bindSuperRole(Long schoolId, Long campusId, List<Long> menuIdList) {
        BgbCampusRole bgbCampusRole = BgbCampusRole.findByCampusIdAndAdmin(campusId, true)

        if (bgbCampusRole) {
            List<BgbCampusRoleMenu> bgbCampusRoleMenuList = BgbCampusRoleMenu.findAllByMenuIdInListAndRoleIdAndStatus(menuIdList, bgbCampusRole.id, 1 as byte)
            if (bgbCampusRoleMenuList) {
                List<Long> uniqueIdList = bgbCampusRoleMenuList*.menuId?.unique()
                menuIdList.removeAll(uniqueIdList)
            }
            if (menuIdList != null && menuIdList?.size() > 0) {
                bgbCampusRoleMenuService.createCampusRoleMenu(schoolId, campusId, bgbCampusRole.id, menuIdList)
                bgbCampusRole.lastUpdated = new Date()
                bgbCampusRole.save(failOnError: true)
            }
        }
    }

    List<BgbCampusMenu> fetchAllBgbCampusMenuByIdListAndType(List<Long> idList, Byte type) {
        BgbCampusMenu.findAllByIdInListAndType(idList, type)
    }

    List<BgbCampusMenu> fetchAllBgbCampusMenuByIdList(List<Long> idList) {
        BgbCampusMenu.findAllByIdInList(idList)
    }


    /**
     * 子分组/菜单排序
     * @param categoryId
     * @param sortJson
     * @return
     */
    def sortBgbCampusMenu(String sortJson) {
        List<Object> objectList = JSONObject.parseArray(sortJson)
        List<Long> menuIdList = objectList*.id
        List<BgbCampusMenu> bgbCampusMenus = fetchAllBgbCampusMenuByIdList(menuIdList)
        Map<Long, BgbCampusMenu> idMenuMap = [:]
        bgbCampusMenus.each {
            idMenuMap.put(it.id, it)
        }
        objectList.each { Object object ->
            Long menuId = object.id
            Integer idx = object.idx
            BgbCampusMenu bgbCampusMenu = idMenuMap.get(menuId)
            bgbCampusMenu.idx = idx
        }
        bgbCampusMenus*.save(failOnError: true)
    }

    /**
     * 更新业务配置同步更新菜单参数
     * @param campusId
     * @param sourceType
     * @param sourceId
     * @param parameter
     * @return
     */
    def updateBgbCampusMenuByWorkSource(Long campusId, Byte sourceType, Long sourceId, String parameter, String icon = null, String color = null) {
        BgbCampusMenu bgbCampusMenu = BgbCampusMenu.findByCampusIdAndSourceTypeAndSourceIdAndStatus(campusId, sourceType, sourceId, 1 as byte)
        if (bgbCampusMenu) {
            bgbCampusMenu.parameter = parameter
            bgbCampusMenu.icon = icon
            bgbCampusMenu.color = color
            bgbCampusMenu.save(failOnError: true)
        }
    }
    
    def createOrUpdateCampusMenuByWordSource(Long schoolId, Long campusId, Byte terminal, Byte sourceType, Long sourceId, String parameter, String icon, String name, String color, String path) {
        BgbCampusMenu bgbCampusMenu = BgbCampusMenu.findOrCreateByCampusIdAndSourceTypeAndSourceIdAndStatusAndTerminal(campusId, sourceType, sourceId, 1 as byte, terminal)
        bgbCampusMenu.schoolId = schoolId
        bgbCampusMenu.parameter = parameter
        bgbCampusMenu.icon = icon
        bgbCampusMenu.name = name
        bgbCampusMenu.color = color
        bgbCampusMenu.path = path
        bgbCampusMenu.type = ConstantEnum.BgbBaseMenuType.MENU.type
        if (bgbCampusMenu.level == null) {
            bgbCampusMenu.level = 1
        }
        bgbCampusMenu.save(failOnError: true, flush: true)
    }

    /**
     * 根据不同的场景返回不同的菜单
     * @param superMenuId 子分组id
     * @param terminal 终端
     * @return
     */
    List<BgbCampusMenuVO> fetchBgbBaseMenuVOList(Long campusId, Long superMenuId, Byte terminal) {
        List<BgbCampusMenuVO> list = []
        if (superMenuId) {
            list = fetchBgbBaseMenuVOListBySuperMenuId(superMenuId)
        } else if (terminal) {
            list = fetchAllLevelOneBgbCampusMenuVOByCampusIdAndTerminal(campusId, terminal, null)
        }
        list?.sort { it.idx }
    }

    /**
     * 查询二级菜单
     * @param superMenuId
     * @return
     */
    List<BgbCampusMenuVO> fetchBgbBaseMenuVOListBySuperMenuId(Long superMenuId) {
        List<BgbCampusMenu> bgbCampusMenus = fetchBgbCampusMenuBySuperMenuId(superMenuId)
        HashMultimap<Long, Long> menuIdReportFormIdMap = fetchMenuIdReportFormMenuIdMapByMenuIdList(bgbCampusMenus*.id)
        List<BgbCampusMenuVO> bgbCampusMenuVOS = bgbCampusMenus.collect {
            BgbCampusMenuVO bgbCampusMenuVO = new BgbCampusMenuVO(
                    id: it.id,
                    name: it.name,
                    icon: it.icon,
                    path: it.path,
                    parameter: it.parameter,
                    idx: it.idx,
                    sourceType: it.sourceType,
                    color: it.color,
                    reportFormIds: menuIdReportFormIdMap.get(it.id)?.join(","),
                    memo: it.memo,
                    biMenuType: it.biMenuType,
                    specialPathJson: it.specialPathJson,
                    menuType: it.menuType
            )
            bgbCampusMenuVO
        }
        bgbCampusMenuVOS?.sort { it.idx }
    }

    Boolean canDeleteBgbCampusMenu(Long id) {
        fetchBgbCampusMenuBySuperMenuId(id)?.size() > 0
    }

    List<BgbCampusMenu> fetchBgbCampusMenuBySuperMenuId(Long superMenuId) {
        BgbCampusMenu.findAllBySuperMenuIdAndStatus(superMenuId, 1 as byte)
    }

    /**
     * 查询菜单/二级分组
     * @param campusId
     * @param terminal
     * @param searchValue
     * @return
     */
    def fetchAllLevelOneBgbCampusMenuVOByCampusIdAndTerminal(Long campusId, Byte terminal, String searchValue) {
        Map<Long, Long> menuIdCategoryIdMap = bgbCampusCategoryBindMenuService.getMenuIdCategoryIdMapByCampusId(campusId)
        List<BgbCampusMenu> bgbCampusMenuList = fetchAllBgbCampusMenuByCampusIdAndTerminalAndSearchValue(campusId, terminal, searchValue)
        List<BgbCampusMenu> levelOneMenuList = bgbCampusMenuList.findAll { it.level == 1 }
        HashMultimap<Long, BgbCampusMenu> superMenuIdMenuMap = HashMultimap.create()
        bgbCampusMenuList.each {
            superMenuIdMenuMap.put(it.superMenuId, it)
        }
        HashMultimap<Long, Long> menuIdReportFormIdMap = fetchMenuIdReportFormMenuIdMapByMenuIdList(bgbCampusMenuList*.id)
        List<BgbCampusMenuVO> menuList = levelOneMenuList.collect {
            bgbCampusMenu ->
                List<BgbCampusMenu> levelTwoMenuList = superMenuIdMenuMap.get(bgbCampusMenu.id) as List<BgbCampusMenu>
                List<BgbCampusMenuVO> levelTwoMenuVOList = levelTwoMenuList.collect { levelTwoMenu ->
                    BgbCampusMenuVO levelTwoMenuVO = new BgbCampusMenuVO(
                            id: levelTwoMenu.id,
                            name: levelTwoMenu.name,
                            icon: levelTwoMenu.icon,
                            path: levelTwoMenu.path,
                            superMenuId: levelTwoMenu.superMenuId,
                            level: levelTwoMenu.level,
                            type: levelTwoMenu.type,
                            idx: levelTwoMenu.idx,
                            categoryId: menuIdCategoryIdMap.get(levelTwoMenu.id),
                            parameter: levelTwoMenu.parameter,
                            sourceType: levelTwoMenu.sourceType,
                            color: levelTwoMenu.color,
                            reportFormIds: menuIdReportFormIdMap.get(levelTwoMenu.id)?.join(","),
                            memo: levelTwoMenu.memo,
                            biMenuType: levelTwoMenu.biMenuType,
                            specialPathJson: levelTwoMenu.specialPathJson
                    )
                    levelTwoMenuVO
                }
                BgbCampusMenuVO bgbCampusMenuVO = new BgbCampusMenuVO(
                        id: bgbCampusMenu.id,
                        name: bgbCampusMenu.name,
                        icon: bgbCampusMenu.icon,
                        path: bgbCampusMenu.path,
                        superMenuId: bgbCampusMenu.superMenuId,
                        level: bgbCampusMenu.level,
                        type: bgbCampusMenu.type,
                        idx: bgbCampusMenu.idx,
                        categoryId: menuIdCategoryIdMap.get(bgbCampusMenu.id),
                        subList: levelTwoMenuVOList?.sort { it.idx },
                        parameter: bgbCampusMenu.parameter,
                        sourceType: bgbCampusMenu.sourceType,
                        color: bgbCampusMenu.color,
                        reportFormIds: menuIdReportFormIdMap.get(bgbCampusMenu.id)?.join(","),
                        memo: bgbCampusMenu.memo,
                        biMenuType: bgbCampusMenu.biMenuType,
                        specialPathJson: bgbCampusMenu.specialPathJson
                )
                bgbCampusMenuVO
        }
        menuList?.sort { it.idx }
    }

    List<BgbCampusMenu> fetchAllBgbCampusMenuByCampusIdAndTerminal(Long campusId, Byte terminal) {
        BgbCampusMenu.findAllByCampusIdAndTerminalAndStatus(campusId, terminal, 1 as byte)
    }

    List<BgbCampusMenu> fetchAllBgbCampusMenuByCampusIdAndTerminalAndSearchValue(Long campusId, Byte terminal, String searchValue) {
        if (searchValue) {
            return BgbCampusMenu.findAllByCampusIdAndTerminalAndStatusAndNameLike(campusId, terminal, 1 as byte, "%" + searchValue + "%")
        } else {
            return fetchAllBgbCampusMenuByCampusIdAndTerminal(campusId, terminal)
        }
    }

    BgbCampusMenu fetchBgbCampusMenuById(Long id) {
        BgbCampusMenu.findById(id)
    }

    /**
     * 编辑菜单/二级分组
     * @param menuId
     * @param name
     * @param icon
     * @param parameter
     * @param moduleIds
     * @return
     */
    def updateBgbCampusMenu(Long menuId, String name, String icon, String color, String reportFormIds, String specialPathJson) {
        BgbCampusMenu bgbCampusMenu = fetchBgbCampusMenuById(menuId)
        bgbCampusMenu.name = name
        bgbCampusMenu.icon = icon
        bgbCampusMenu.color = color
        bgbCampusMenu.specialPathJson = specialPathJson
        bgbCampusMenu.save(failOnError: true)
        deleteReportFormMenuByMenuId(menuId)
        if (reportFormIds) {
            List<Long> reportFormIdList = reportFormIds.split(",").collect { it.toLong() }
            List<ReportFormMenu> newList = reportFormIdList?.collect { reportFormId ->
                ReportFormMenu reportFormMenu = new ReportFormMenu(
                        schoolId: bgbCampusMenu.schoolId,
                        campusId: bgbCampusMenu.campusId,
                        menuId: menuId,
                        reportFormId: reportFormId,
                        status: 1 as byte
                )
                reportFormMenu
            }
            if (newList?.size() > 0) {
                newList*.save(failOnError: true)
            }
        }
    }

    List<ReportFormMenu> fetchAllReportFormMenuByMenuId(Long menuId) {
        ReportFormMenu.findAllByMenuIdAndStatus(menuId, 1 as byte)
    }

    @Transactional(rollbackFor = Exception.class)
    def deleteReportFormMenuByMenuId(Long menuId) {
        String HQL = "update ReportFormMenu set status = 0 where menuId = :menuId and status = 1"
        ReportFormMenu.executeUpdate(HQL, [menuId: menuId])
    }

    /**
     * 移动分组
     * @param menuId
     * @param categoryId
     * @param superMenuId
     * @return
     */
    def changeCampusMenuCategory(Long menuId, Long categoryId, Long superMenuId) {
        BgbCampusMenu bgbCampusMenu = fetchBgbCampusMenuById(menuId)
        //更改一级分组
        bgbCampusCategoryBindMenuService.changeBgbCampusCategoryBindMenu(bgbCampusMenu.schoolId, bgbCampusMenu.campusId, categoryId, menuId)
        //更改二级分组
        if (superMenuId && bgbCampusMenu.superMenuId != superMenuId) {
            bgbCampusMenu.superMenuId = superMenuId
            bgbCampusMenu.level = 2
            bgbCampusMenu.save(failOnError: true)
        } else if (bgbCampusMenu.superMenuId) {
            bgbCampusMenu.superMenuId = null
            bgbCampusMenu.level = 1
            bgbCampusMenu.save(failOnError: true)
        }
    }

    /**
     * 删除菜单/二级分组
     * @param id
     */
    def deleteBgbCampusMenu(Long id) {
        BgbCampusMenu bgbCampusMenu = BgbCampusMenu.findById(id)
        bgbCampusMenu.status = 0
        bgbCampusMenu.save(failOnError: true)
        List<BgbMenuCollection> bgbMenuCollectionList = BgbMenuCollection.findAllByMenuIdAndStatus(id, 1 as byte)
        bgbMenuCollectionList*.status = 0 as byte
        bgbMenuCollectionList*.save(failOnError: true)
        List<BgbCampusRoleMenu> bgbCampusRoleMenuList = BgbCampusRoleMenu.findAllByMenuIdAndStatus(id, 1 as byte)
        bgbCampusRoleMenuList*.status = 0 as byte
        bgbCampusRoleMenuList*.save(failOnError: true)
        List<BgbCampusCategoryBindMenu> bgbCampusCategoryBindMenuList = BgbCampusCategoryBindMenu.findAllByMenuIdAndStatus(id, 1 as byte)
        bgbCampusCategoryBindMenuList*.status = 0 as byte
        bgbCampusCategoryBindMenuList*.save(failOnError: true)
    }

    List<ReportFormMenu> fetchAllByMenuIdList(List<Long> menuIdList) {
        if (menuIdList?.size() > 0) {
            ReportFormMenu.findAllByMenuIdInListAndStatus(menuIdList, 1 as byte)
        } else {
            return []
        }
    }

    HashMultimap<Long, Long> fetchMenuIdReportFormMenuIdMapByMenuIdList(List<Long> menuIdList) {
        HashMultimap<Long, Long> menuIdReportFormMenuIdMap = HashMultimap.create()
        List<ReportFormMenu> reportFormMenuList = fetchAllByMenuIdList(menuIdList)
        reportFormMenuList.each {
            menuIdReportFormMenuIdMap.put(it.menuId, it.reportFormId)
        }
        menuIdReportFormMenuIdMap
    }

}

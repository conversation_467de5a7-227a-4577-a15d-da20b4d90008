package hiiadmin.group


import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import timetabling.group.PersonGroup
import timetabling.group.PersonGroupDetail

@Transactional
class PersonGroupService {

    PersonGroup save(PersonGroup personGroup) {
        personGroup.save(failOnError: true)
        personGroup
    }

    PersonGroup fetchPersonGroupById(Long groupId) {
        PersonGroup.get(groupId)
    }

    List<PersonGroup> fetchAllPersonGroupByCampusId(long campusId, Byte userType = null) {
        if (userType != null) {
            return PersonGroup.findAllByCampusIdAndUserTypeAndStatus(campusId, userType, 1 as byte)
        } else {
            return PersonGroup.findAllByCampusIdAndStatus(campusId, 1 as byte)
        }
    }

    Multimap<Long, String> transformUserGroupNameMap(Long campusId, Byte userType) {
        Multimap<Long, String> userGroupName = HashMultimap.create()
        List<PersonGroupDetail> personGroupDetailList = PersonGroupDetail.findAllByCampusIdAndUserTypeAndStatus(campusId, userType, 1 as Byte)
        Map<Long, String> groupNameMap = [:]
        fetchAllPersonGroupByCampusId(campusId).each {
            groupNameMap.put(it.id, it.name)
        }
        personGroupDetailList.each {
            userGroupName.put(it.userId, groupNameMap.get(it.groupId))
        }
        userGroupName
    }

}

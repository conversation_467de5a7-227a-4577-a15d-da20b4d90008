package hiiadmin.messageControl

import grails.gorm.transactions.Transactional
import hiiadmin.exceptions.HiiAdminException
import timetabling.messageControl.MessageControlCenter

import static hiiadmin.ConstantEnum.MessageControlCenterType
import static hiiadmin.ConstantEnum.TrackMessageFeatureOpt

@Transactional
class MessageControlCenterService {

    MessageControlPersonService messageControlPersonService

    MessageControlCenter checkMessageControlCenter(long campusId, Integer type) {
        if (!type || !(type in MessageControlCenterType.allType())) {
            throw new HiiAdminException()
        }
        MessageControlCenter center = findOrCreateMessageControlCenter(campusId, type)
        center.openStatus = center.openStatus == 0 ? 1 : 0
        center.save(failOnError: true, flush: true)
    }

    int fetchMessageControlCenterOpenStatusByCampusIdAndType(long campusId, int type) {
        MessageControlCenter.findByCampusIdAndTypeAndStatus(campusId, type, 1)?.openStatus ?: 0
    }

    synchronized MessageControlCenter findOrCreateMessageControlCenter(long campusId, Integer type) {
        if (!type || !(type in MessageControlCenterType.allType())) {
            throw new HiiAdminException()
        }
        MessageControlCenter center = MessageControlCenter.findOrCreateByCampusIdAndType(campusId, type)
        if (center.status == null) {
            center.name = MessageControlCenterType.getEnumByType(type).name
            center.status = 1
            center.pushOptBit = 0
            center.save(failOnError: true, flush: true)
        }
        center.refresh()
    }


    MessageControlCenter getMessageControlCenter(long campusId, Integer type) {
        MessageControlCenter.findByCampusIdAndType(campusId, type)
    }

    MessageControlCenter saveMessageControlCenter(MessageControlCenter center) {
        center.save(failOnError: true, flush: true)
    }

    void updateMessageControlCenter(MessageControlCenter center, String pushOpt, String teacherIds, String roleIds, String departmentIds) {
        if (!center) {
            throw new HiiAdminException("配置不存在")
        }
        int pushOptBit = TrackMessageFeatureOpt.types2Bit(pushOpt)
        center.pushOptBit = pushOptBit
        saveMessageControlCenter(center)
        messageControlPersonService.cleanMessageControlPerson4feature(center.id)
        if (teacherIds && TrackMessageFeatureOpt.compareFieldBit(TrackMessageFeatureOpt.TEACHER.opt, pushOptBit)) {
            messageControlPersonService.updateTrackMessagePerson4feature(center.campusId, center.id, TrackMessageFeatureOpt.TEACHER.opt, teacherIds)
        }
        if (roleIds && TrackMessageFeatureOpt.compareFieldBit(TrackMessageFeatureOpt.ROLE.opt, pushOptBit)) {
            messageControlPersonService.updateTrackMessagePerson4feature(center.campusId, center.id, TrackMessageFeatureOpt.ROLE.opt, roleIds)
        }
        if (departmentIds && TrackMessageFeatureOpt.compareFieldBit(TrackMessageFeatureOpt.DEPARTMENT.opt, pushOptBit)) {
            messageControlPersonService.updateTrackMessagePerson4feature(center.campusId, center.id, TrackMessageFeatureOpt.DEPARTMENT.opt, departmentIds)
        }
    }

    MessageControlCenter fetchMessageControlCenterByCampusIdAndType(long campusId, Integer type) {
        MessageControlCenter.findByCampusIdAndTypeAndStatus(campusId, type, 1)
    }

}

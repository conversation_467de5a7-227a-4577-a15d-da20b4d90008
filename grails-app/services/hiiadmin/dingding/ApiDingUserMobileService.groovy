package hiiadmin.dingding

import com.aliyun.dingtalkcontact_1_0.Client
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody
import com.aliyun.tea.TeaException
import com.aliyun.teaopenapi.models.Config
import com.aliyun.teautil.Common
import com.aliyun.teautil.models.RuntimeOptions
import groovy.util.logging.Slf4j
import hiiadmin.exceptions.HiiAdminException

/**
 * <AUTHOR>
 * @Date 2024-07-26 18:59
 */
@Slf4j
class ApiDingUserMobileService {

    ApiDingUserAccessTokenService apiDingUserAccessTokenService

    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
    Client createClient() throws Exception {
        Config config = new Config()
        config.protocol = "https"
        config.regionId = "central"
        return new Client(config)
    }

    String getUserMobile(String code, Long appId, String corpId) throws Exception {
        String mobile = ""
        Client client = createClient()
        GetUserHeaders getUserHeaders = new GetUserHeaders()
        getUserHeaders.xAcsDingtalkAccessToken = apiDingUserAccessTokenService.getUserAccessToken(code, appId, corpId)
        try {
            GetUserResponse response = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions())
            GetUserResponseBody responseBody = response.getBody()
            log.info("responseBody.mobile: " + responseBody.getMobile())
            mobile = responseBody.getMobile()
        } catch (TeaException err) {
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.info("err.code: " + err.code + ", err.message: " + err.message)
                throw new HiiAdminException("err.code: " + err.code + ", err.message: " + err.message)
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err)
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                log.info("err.code: " + err.code + ", err.message: " + err.message)
                throw new HiiAdminException("err.code: " + err.code + ", err.message: " + err.message)
            }
        }
        return mobile
    }
}

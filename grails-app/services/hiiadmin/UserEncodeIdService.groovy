package hiiadmin

import com.bugu.PhoenixCoder
import com.bugu.teaching.clientapi.domain.basic.LectureSearchDirectResult
import com.bugu.teaching.clientapi.service.WeekLectureSearch
import com.google.common.collect.Sets
import grails.gorm.transactions.Transactional
import hiiadmin.biz.DepartmentService
import hiiadmin.school.SchoolService
import hiiadmin.school.UnitService
import org.apache.dubbo.config.annotation.DubboReference
import timetabling.Department
import timetabling.org.Semester
import timetabling.org.Unit
import timetabling.user.TeacherSchoolCampus
import timetabling.vocationalSchool.FacultyUser

@Transactional
class UserEncodeIdService {

    @DubboReference
    private WeekLectureSearch weekLectureSearch

    UnitService unitService

    DepartmentService departmentService

    SchoolService schoolService

    Map<String, String> fetchUserEncodeIdMap(Long userId, Long campusId, Long schoolId) {
        List<Unit> unitList = unitService.fetchAllUnitByCampusIdAndTeacherId(campusId, userId, null)
        List<Unit> masterUnitList = unitService.fetchAllHeadmasterUnitByCampusIdAnd4Teacher(campusId, userId)
        List<Department> departmentList = departmentService.findDepartmentByTeacherIdAndCampusId(userId, campusId)
        Semester semester = schoolService.fetchCurrentSemesterByCampusId(campusId)
        Set<String> unitIdList = Sets.newHashSet()
        Set<String> masterUnitIdList = Sets.newHashSet()
        Set<String> gradeIdList = Sets.newHashSet()
        Set<String> facultyIdList = Sets.newHashSet()
        Set<String> majorIdList = Sets.newHashSet()
        Set<String> teachingUnitIdList = Sets.newHashSet()
        Set<String> teachingGradeIdList = Sets.newHashSet()
        Set<String> teachingCourseIdList = Sets.newHashSet()
        Set<String> partyIdSet = Sets.newHashSet()
        String jobNum = ""
        unitList.each {
            unitIdList.add(PhoenixCoder.encodeStr(String.valueOf(it.id)))
            if (it.gradeId) {
                gradeIdList.add(PhoenixCoder.encodeStr(String.valueOf(it.gradeId)))
            }
            if (it.facultyId) {
                facultyIdList.add(PhoenixCoder.encodeStr(String.valueOf(it.facultyId)))
            }
            if (it.majorId) {
                majorIdList.add(PhoenixCoder.encodeStr(String.valueOf(it.majorId)))
            }
        }
        String HQL = "select fu.facultyId from FacultyUser fu left join Faculty f on fu.facultyId = f.id where  fu.campusId = :campusId and fu.userId = :userId and fu.status = :status and f.type = :type"
        List<Long> partyIdList = FacultyUser.executeQuery(HQL, [userId: userId, campusId: campusId, status: 1 as byte, type: 2 as byte]) as List<Long>
        partyIdList.each {
            partyIdSet.add(PhoenixCoder.encodeStr(String.valueOf(it)))
        }
        masterUnitList.each {
            masterUnitIdList.add(PhoenixCoder.encodeStr(String.valueOf(it.id)))
        }
        List<LectureSearchDirectResult> lectureSearchDirectResultList = weekLectureSearch.searchLectures2(1L, String.valueOf(campusId), String.valueOf(semester.id), null, [], [String.valueOf(userId)], [], [], null, [1L], [], [])
        lectureSearchDirectResultList.each {
            if (it.teachingUnitId) {
                teachingUnitIdList.add(PhoenixCoder.encodeStr(it.teachingUnitId))
            }
            if (it.gradeId) {
                teachingGradeIdList.add(PhoenixCoder.encodeStr(it.gradeId))
            }
            if (it.teachingCourseId) {
                teachingCourseIdList.add(PhoenixCoder.encodeStr(it.teachingCourseId))
            }
        }
        TeacherSchoolCampus teacherSchoolCampus = TeacherSchoolCampus.findBySchoolIdAndCampusIdAndTeacherIdAndStatus(schoolId, campusId, userId, 1 as byte)
        if (teacherSchoolCampus && teacherSchoolCampus.jobNum) {
            jobNum = teacherSchoolCampus.jobNum.startsWith("T") ? teacherSchoolCampus.jobNum.substring(1) : teacherSchoolCampus.jobNum
            jobNum = PhoenixCoder.encodeStr(jobNum)
        }
        Map<String, String> map = [
                schoolId        : PhoenixCoder.encodeStr(String.valueOf(schoolId)),
                campusId        : PhoenixCoder.encodeStr(String.valueOf(campusId)),
                facultyId       : facultyIdList?.join(","),
                majorId         : majorIdList?.join(","),
                departmentId    : departmentList?.collect { PhoenixCoder.encodeStr(String.valueOf(it.id)) }?.join(","),
                gradeId         : gradeIdList?.join(","),
                teachingGradeId : teachingGradeIdList?.join(","),
                unitId          : unitIdList?.join(","),
                masterUnitId    : masterUnitIdList?.join(","),
                teachingUnitId  : teachingUnitIdList?.join(","),
                teachingCourseId: teachingCourseIdList?.join(","),
                teacherId       : PhoenixCoder.encodeStr(String.valueOf(userId)),
                partyId         : partyIdSet?.join(","),
                jobNum          : jobNum
        ]
        map
    }
}

package hiiadmin.vocationalSchool

import com.bugu.ServiceResult
import com.google.common.collect.HashBasedTable
import com.google.common.collect.Table
import grails.gorm.transactions.Transactional
import hiiadmin.UserService
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.module.bugu.DoorPlateVO
import hiiadmin.module.bugu.GradeVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.bugu.UnitVO
import hiiadmin.module.vocationalSchool.FacultyVO
import hiiadmin.module.vocationalSchool.MajorVO
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.StringUtils
import timetabling.CardUser
import timetabling.ParentStudent
import timetabling.building.DoorPlate
import timetabling.building.DoorPlateUnit
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.org.UnitTeacher
import timetabling.pickUp.AfterUnitStudent
import timetabling.sign.SignInSeat
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.user.User
import timetabling.user.UserEncodeInfo
import timetabling.user.UserWxDing
import timetabling.vocationalSchool.CultivationLevel
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major
import timetabling.vocationalSchool.SchoolSystem

import static hiiadmin.ConstantEnum.*

@Transactional
class VocationalUnitService {

    UnitService unitService

    FacultyService facultyService

    MajorService majorService

    SchoolSystemService schoolSystemService

    CultivationLevelService cultivationLevelService

    GradeService gradeService

    TeacherService teacherService

    DoorPlateService doorPlateService

    ViewDOService viewDOService

    StudentService studentService

    PersonDataChangeService personDataChangeService

    ParentStudentService parentStudentService

    UserService userService

    ServiceResult<Long> saveOrUpdate(Long id, Long schoolId, Long campusId, String unitCode, String name, Long facultyId, Long majorId, Long gradeId) {
        if (!name) {
            throw new HiiAdminException("班级名称不能为空！")
        }

        if (!unitCode) {
            throw new HiiAdminException("班级编号不能为空！")
        }

        if (unitCode.length() > 25) {
            throw new HiiAdminException("班级编号不能超过25位")
        }

        if (!facultyId) {
            throw new HiiAdminException("班级所属院系不能为空！")
        }

        if (!majorId) {
            throw new HiiAdminException("班级专业不能为空！")
        }

        if (!gradeId) {
            throw new HiiAdminException("班级所选年级不能为空！")
        }

        Unit nameUnit = isNameOrCodeExists(name, null, campusId)
        if (nameUnit) {
            if (id) {
                if (id != nameUnit.id) {
                    throw new HiiAdminException("该班级名称已存在")
                }
            } else {
                throw new HiiAdminException("该班级名称已存在")
            }
        }

        Unit codeUnit = isNameOrCodeExists(null, unitCode, campusId)
        if (codeUnit) {
            if (id) {
                if (id != codeUnit.id) {
                    throw new HiiAdminException("该班级编号已存在")
                }
            } else {
                throw new HiiAdminException("该班级编号已存在")
            }
        }

        Unit unit = unitService.fetchUnitById(id)
        Grade grade = gradeService.fetchGradeById(gradeId)
        if (unit) {
            unit.code = unitCode
            unit.name = name
            unit.facultyId = facultyId
            unit.majorId = majorId
            unit.gradeId = gradeId
            unit.gradeCode = grade?.code?.toInteger()

            unit.save(failOnError: true, flush: true)
            
            List<UnitStudent> unitStudentList = UnitStudent.findAllByCampusIdAndUnitIdAndStatus(campusId, id, 1 as byte)
            if (unitStudentList.size() > 0) {
                // 同步更改院系id和专业id
                unitStudentList*.facultyId = facultyId
                unitStudentList*.majorId = majorId
                unitStudentList*.gradeId = gradeId
                unitStudentList*.save(failOnError: true)
            }
            return ServiceResult.success(id)
        }

        Unit saveUnit = new Unit(
                schoolId: schoolId,
                campusId: campusId,
                gradeId: gradeId,
                sectionId: SectionType.VOCATIONAL_SCHOOL.id,
                name: name,
                code: unitCode,
                facultyId: facultyId,
                majorId: majorId,
                type: 1 as byte,
                status: 1 as byte,
                unitLevel: 1 as String,
                gradeCode: grade?.code?.toInteger()
        )

        saveUnit.save(failOnError: true, flush: true)
        ServiceResult.success(saveUnit.id)
    }


    Unit isNameOrCodeExists(String name, String unitCode, Long campusId) {
        if (name) {
            return Unit.findByCampusIdAndNameAndStatus(campusId, name, 1 as byte)
        }

        if (unitCode) {
            return Unit.findByCampusIdAndCodeAndStatus(campusId, unitCode, 1 as byte)
        }

        null
    }

    def fetchAllVocationalUnitBySearch(Long campusId, Long facultyId, Long majorId, Long cultivationLevelId, Long schoolSystemId, Long gradeId, String searchValue, int p, int s) {

        StringBuilder stringBuilder = new StringBuilder()
        Map map = buildSearchHQL(stringBuilder, campusId, facultyId, majorId, cultivationLevelId, schoolSystemId, gradeId, searchValue, 1 as Byte, p, s)
        List<Unit> unitList = Unit.executeQuery(stringBuilder.toString(), map)

        StringBuilder countBuilder = new StringBuilder()
        Map countMap = buildSearchHQL(countBuilder, campusId, facultyId, majorId, cultivationLevelId, schoolSystemId, gradeId, searchValue, 1 as Byte, 0, 0, true)
        Integer count = Unit.executeQuery(countBuilder, countMap)[0] as Integer
        [list: unitList, total: count ?: 0]
    }

    Map buildSearchHQL(StringBuilder stringBuilder, Long campusId, Long facultyId, Long majorId, Long cultivationLevelId, Long schoolSystemId, Long gradeId, String searchValue, Byte unitType, int p, int s, boolean count = false) {
        Map map = [:]
        String HQL = null
        if (count) {
            HQL = """ SELECT COUNT(1) FROM Unit u WHERE u.status = 1 AND u.campusId = :campusId AND u.type = :unitType """
        } else {
            HQL = """ SELECT u FROM Unit u WHERE u.status = 1 AND u.campusId = :campusId AND u.type = :unitType """
        }
        stringBuilder.append(HQL)
        map.put("campusId", campusId)
        map.put("unitType", unitType)

        if (facultyId) {
            stringBuilder.append(""" AND u.facultyId = :facultyId """)
            map.put("facultyId", facultyId)
        }

        if (majorId) {
            stringBuilder.append(""" AND u.majorId = :majorId """)
            map.put("majorId", majorId)
        }

        if (gradeId) {
            stringBuilder.append(""" AND u.gradeId = :gradeId """)
            map.put("gradeId", gradeId)
        }

        if (cultivationLevelId && schoolSystemId) {
            stringBuilder.append(""" AND u.majorId IN (SELECT m.id FROM Major m WHERE m.cultureId = :cultureId AND m.schoolSystemId = :schoolSystemId AND m.status >= 0) """)
            map.put("cultureId", cultivationLevelId)
            map.put("schoolSystemId", schoolSystemId)
        } else if (cultivationLevelId == null && schoolSystemId) {
            stringBuilder.append(""" AND u.majorId IN (SELECT m.id FROM Major m WHERE m.schoolSystemId = :schoolSystemId AND m.status >= 0)""")
            map.put("schoolSystemId", schoolSystemId)
        } else if (cultivationLevelId && schoolSystemId == null) {
            stringBuilder.append(""" AND u.majorId IN (SELECT m.id FROM Major m WHERE m.cultureId = :cultivationLevelId AND m.status >= 0)""")
            map.put("cultivationLevelId", cultivationLevelId)
        }

        if (s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        if (searchValue) {
            stringBuilder.append(""" AND (u.name LIKE :searchValue) """)
            map.put("searchValue", "%" + searchValue + "%")
        }

        stringBuilder.append(" ORDER BY u.id DESC ")

        map

    }

    def fetchAllUnitByCampus(Long campusId, Long facultyId, Long majorId, Long cultivationLevelId, Long schoolSystemId, Long gradeId, String searchValue, int p, int s) {
        def map = fetchAllVocationalUnitBySearch(campusId, facultyId, majorId, cultivationLevelId, schoolSystemId, gradeId, searchValue, p, s)
        List<Unit> unitList = map.list
        Integer total = map.total
        List<UnitVO> vos = []
        Map<Long, List<UnitStudent>> unitStudentMap = [:]
        if (unitList) {
            List<Long> unitIdList = unitList*.id
            unitStudentMap = unitService.fetchAllUnitStudentViaUnitIdList(unitIdList)?.groupBy { it.unitId }
        }
        Map<Long, Faculty> facultyMap = facultyService.buildClassFacultyMap(campusId, false)
        Map<Long, Major> majorMap = majorService.buildMajorMap(campusId, false)
        Map<Long, Grade> gradeMap = gradeService.fetchAllGradeIdMapByCampusId(campusId)
        Map<Long, SchoolSystem> schoolSystemMap = schoolSystemService.buildSchoolSystemMap(campusId)
        Map<Long, CultivationLevel> cultivationLevelMap = cultivationLevelService.buildCultivationLevelMap(campusId)
        unitList.each {
            Unit unit ->
                UnitVO unitVO = new UnitVO().buildVO(unit)
                unitVO.unitCode = unit.code
                Faculty faculty = facultyMap?.get(unit.facultyId)
                unitVO.facultyName = faculty?.name
                unitVO.facultyStatus = faculty?.status

                Major major = majorMap?.get(unit.majorId)
                unitVO.majorName = major?.name
                unitVO.majorStatus = major?.status

                SchoolSystem schoolSystem = schoolSystemMap?.get(major?.schoolSystemId)
                unitVO.schoolSystemId = major?.schoolSystemId
                unitVO.schoolSystemName = schoolSystem?.name
                unitVO.schoolSystemStatus = schoolSystem?.status

                CultivationLevel cultivationLevel = cultivationLevelMap?.get(major?.cultureId)
                unitVO.cultivationLevelId = major?.cultureId
                unitVO.cultivationLevelName = cultivationLevel?.name
                unitVO.cultivationLevelStatus = cultivationLevel?.status

                List<UnitStudent> unitStudentList = unitStudentMap?.get(unit.id)
                unitVO.studentTotal = unitStudentList ? unitStudentList?.size() : 0

                Grade grade = gradeMap?.get(unit?.gradeId)
                unitVO.gradeStatus = grade?.status
                unitVO.gradeName = grade?.name

                Teacher teacher = teacherService.fetchHeadmasterTeacherByUnitId(unit.id)
                TeacherVO teacherVO = new TeacherVO().buildVO(teacher)
                unitVO.teacherVO = teacherVO
                DoorPlate doorPlate = doorPlateService.fetchDoorPlateByUnitId(unit.id)
                DoorPlateVO doorPlateVO = viewDOService.transformDoorPlateVO(doorPlate, false)
                unitVO.doorPlateVO = doorPlateVO
                vos << unitVO
        }

        [list: vos, total: total]
    }


    List<FacultyVO> treeUnit(Long campusId) {
        List<Faculty> facultyList = facultyService.fetchAllClassFacultyByCampus(campusId, 1 as byte)
        List<Unit> unitList = unitService.fetchAllUnitByCampusIdAndUnitType(campusId, 1 as byte)
        Map<Long, List<Unit>> baseFacultyUnitMap = unitList?.groupBy { it.facultyId }
        Map<Long, Major> majorMap = majorService.buildMajorMap(campusId)
        Map<Long, Grade> gradeMap = gradeService.fetchAllNormalGradeIdMapByCampusId(campusId, false)

        List<FacultyVO> facultyVOList = facultyList.collect {
            Faculty faculty ->
                FacultyVO facultyVO = new FacultyVO().buildVO(faculty)
                List<MajorVO> majorVOList = []
                List<Unit> facultyUnits = baseFacultyUnitMap?.get(faculty.id)
                Map<Long, List<Unit>> majorUnitMap = facultyUnits?.groupBy { it.majorId }
                majorUnitMap.each { entry ->
                    Major major = majorMap?.get(entry.key)
                    Map<Long, List<Unit>> gradeUnitMap = entry?.value?.groupBy { it.gradeId }
                    if (major) {
                        MajorVO majorVO = new MajorVO().buildVO(major)
                        List<GradeVO> gradeVOList = []
                        gradeUnitMap.each { gradeEntry ->
                            Grade grade = gradeMap?.get(gradeEntry.key)
                            if (grade) {
                                GradeVO gradeVO = new GradeVO().buildVO(grade)
                                List<Unit> gradeUnitList = gradeEntry.value
                                List<UnitVO> unitVOList = gradeUnitList.collect {
                                    new UnitVO().buildVO(it)
                                }

                                gradeVO.units = unitVOList
                                gradeVOList << gradeVO
                            }
                        }
                        majorVO.gradeVOList = gradeVOList
                        majorVOList << majorVO
                    }
                }
                facultyVO.majorVOList = majorVOList
                facultyVO
        }

        facultyVOList
    }
    
    List<FacultyVO> treeUnitV2(Long campusId, Integer hideGrade) {
        List<FacultyVO> facultyVOList = []
        Table<Long, Long, List<Unit>> table = HashBasedTable.create()
        Map<Long, List<Major>> facultyIdMajorMap = [:]
        Map<Long, List<Unit>> majorUnitMap = [:]
        
        List<Faculty> facultyList = facultyService.fetchAllClassFacultyByCampus(campusId, 1 as byte)
        List<Unit> unitList = unitService.fetchAllUnitByCampusIdAndUnitType(campusId, 1 as byte)
        List<Major> majorList = majorService.fetchAllMajorByCampus(campusId)
        List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)
        
        
        
        facultyIdMajorMap = majorList?.groupBy {it.facultyId}
        majorUnitMap = unitList.groupBy {it.majorId}
        
        majorUnitMap?.each { entry ->
            List<Unit> major4UnitList = entry.value
            Map<Long, List<Unit>> grade4UnitMap = major4UnitList.groupBy {it.gradeId}
            grade4UnitMap?.each {gradeUnitEntry ->
                if (entry.key && gradeUnitEntry.key && gradeUnitEntry.value) {
                    table.put(entry.key, gradeUnitEntry.key, gradeUnitEntry.value)
                }
            }
        }
        
        facultyList.each {
            Faculty faculty ->
                FacultyVO facultyVO = new FacultyVO().buildVO(faculty)
                List<MajorVO> majorVOList = []
                List<Major> majors = facultyIdMajorMap?.get(faculty.id)
                Boolean facultyFlag = false
                majors.each {
                    Major major ->
                        MajorVO majorVO = new MajorVO().buildVO(major)
                        List<GradeVO> gradeVOList = []
                        List<UnitVO> unitVOList4Major = []
                        Boolean majorFlag = false
                        
                        if (hideGrade == 1) {
                            List<Unit> majorUnits = majorUnitMap?.get(major.id)
                            
                            if (majorUnits) {
                                facultyFlag = true
                                majorFlag = true
                            }
                            
                            majorUnits?.each {Unit u ->
                                UnitVO unitVO = new UnitVO().buildVO(u)
                                unitVOList4Major << unitVO
                            }
                            
                            majorVO.unitVOList = unitVOList4Major
                        } else {
                            gradeList.each {
                                Grade grade ->
                                    GradeVO gradeVO = new GradeVO().buildVO(grade)
                                    Boolean gradeFlag = false
                                    List<Unit> majorGradeUnitList = table.get(major.id, grade.id)
                                    if (majorGradeUnitList?.size() > 0) {
                                        majorFlag = true
                                        gradeFlag = true
                                        facultyFlag = true
                                    }
                                    List<UnitVO> unitVOList = majorGradeUnitList.collect{
                                        new UnitVO().buildVO(it)
                                    }

                                    gradeVO.units = unitVOList
                                    gradeVO.unitFlag = gradeFlag
                                    gradeVOList << gradeVO
                            }
                        }
                        
                        majorVO.unitFlag = majorFlag
                        majorVO.gradeVOList = gradeVOList
                        majorVOList << majorVO
                }
                facultyVO.unitFlag = facultyFlag
                facultyVO.majorVOList = majorVOList
                facultyVOList << facultyVO
        }
        return facultyVOList
    }

    List<FacultyVO> unEncodeTreeUnitV2(Long campusId, Integer hideGrade) {
        List<FacultyVO> facultyVOList = []
        Table<Long, Long, List<Unit>> table = HashBasedTable.create()
        Map<Long, List<Major>> facultyIdMajorMap = [:]
        Map<Long, List<Unit>> majorUnitMap = [:]

        List<Faculty> facultyList = facultyService.fetchAllClassFacultyByCampus(campusId, 1 as byte)
        List<Unit> unitList = unitService.fetchAllUnitByCampusIdAndUnitType(campusId, 1 as byte)
        List<Major> majorList = majorService.fetchAllMajorByCampus(campusId)
        List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)



        facultyIdMajorMap = majorList?.groupBy {it.facultyId}
        majorUnitMap = unitList.groupBy {it.majorId}

        majorUnitMap?.each { entry ->
            List<Unit> major4UnitList = entry.value
            Map<Long, List<Unit>> grade4UnitMap = major4UnitList.groupBy {it.gradeId}
            grade4UnitMap?.each {gradeUnitEntry ->
                if (entry.key && gradeUnitEntry.key && gradeUnitEntry.value) {
                    table.put(entry.key, gradeUnitEntry.key, gradeUnitEntry.value)
                }
            }
        }

        facultyList.each {
            Faculty faculty ->
                FacultyVO facultyVO = new FacultyVO().buildVO(faculty)
                facultyVO.id = faculty.id
                facultyVO.campusId = faculty.campusId
                List<MajorVO> majorVOList = []
                List<Major> majors = facultyIdMajorMap?.get(faculty.id)
                Boolean facultyFlag = false
                majors.each {
                    Major major ->
                        MajorVO majorVO = new MajorVO().buildVO(major)
                        majorVO.id = major.id
                        majorVO.campusId = major.campusId
                        majorVO.facultyId = major.facultyId
                        List<GradeVO> gradeVOList = []
                        List<UnitVO> unitVOList4Major = []
                        Boolean majorFlag = false

                        if (hideGrade == 1) {
                            List<Unit> majorUnits = majorUnitMap?.get(major.id)

                            if (majorUnits) {
                                facultyFlag = true
                                majorFlag = true
                            }

                            majorUnits?.each {Unit u ->
                                UnitVO unitVO = new UnitVO().buildVO(u)
                                unitVO.id = u.id
                                unitVO.gradeId = u.gradeId
                                unitVO.sectionId = u.sectionId
                                unitVO.facultyId = u.facultyId
                                unitVO.majorId = u.majorId
                                unitVOList4Major << unitVO
                            }

                            majorVO.unitVOList = unitVOList4Major
                        } else {
                            gradeList.each {
                                Grade grade ->
                                    GradeVO gradeVO = new GradeVO().buildVO(grade)
                                    gradeVO.id = grade.id
                                    gradeVO.campusId = grade.campusId
                                    gradeVO.sectionId = grade.sectionId
                                    Boolean gradeFlag = false
                                    List<Unit> majorGradeUnitList = table.get(major.id, grade.id)
                                    if (majorGradeUnitList?.size() > 0) {
                                        majorFlag = true
                                        gradeFlag = true
                                        facultyFlag = true
                                    }
                                    List<UnitVO> unitVOList = majorGradeUnitList.collect{
                                        UnitVO unitVO = new UnitVO().buildVO(it)
                                        unitVO.id = it.id
                                        unitVO.gradeId = it.gradeId
                                        unitVO.sectionId = it.sectionId
                                        unitVO.facultyId = it.facultyId
                                        unitVO.majorId = it.majorId
                                        unitVO
                                    }

                                    gradeVO.units = unitVOList
                                    gradeVO.unitFlag = gradeFlag
                                    gradeVOList << gradeVO
                            }
                        }

                        majorVO.unitFlag = majorFlag
                        majorVO.gradeVOList = gradeVOList
                        majorVOList << majorVO
                }
                facultyVO.unitFlag = facultyFlag
                facultyVO.majorVOList = majorVOList
                facultyVOList << facultyVO
        }
        return facultyVOList
    }

    void graduateUnit(Long campusId, Long userId, Byte userType, String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<Long> unitIdList = ToStringUnits.idsString2LongList(ids)
            List<Unit> unitList = unitService.fetchAllUnitByIdInList(unitIdList)
            if (unitList) {
                unitList*.status = YearAndGradeAndUnitStatus.FINISH.status
                unitList*.save(failOnError: true, flush: true)
            }

            List<UnitTeacher> unitTeacherList = UnitTeacher.findAllByUnitIdInListAndStatus(unitIdList, 1 as byte)
            if (unitTeacherList) {
                unitTeacherList*.status = YearAndGradeAndUnitStatus.FINISH.status
                unitTeacherList*.save(failOnError: true, flush: true)
            }

            List<Student> studentList = studentService.fetchAllStudentByUnitIdInList(unitIdList)
            List<ParentStudent> parentStudentList = []
            if (studentList) {
                studentList*.status = YearAndGradeAndUnitStatus.FINISH.status
                studentList*.save(failOnError: true, flush: true)
                
                parentStudentList = parentStudentService.fetchAllParentStudentByStudentIdInList(studentList.id, false)
            }
            
            if (parentStudentList) {
                parentStudentList*.status = YearAndGradeAndUnitStatus.FINISH.status
                parentStudentList*.save(failOnError: true)
            }

            List<UnitStudent> unitStudentList = UnitStudent.findAllByUnitIdInListAndStatus(unitIdList, 1 as byte)
            if (unitStudentList) {
                unitStudentList*.status = YearAndGradeAndUnitStatus.FINISH.status
                unitStudentList*.save(failOnError: true, flush: true)
            }

            List<DoorPlateUnit> doorPlateUnitList = DoorPlateUnit.findAllByUnitIdInListAndStatus(unitIdList, 1 as byte)
            if (doorPlateUnitList) {
                doorPlateUnitList*.status = YearAndGradeAndUnitStatus.FINISH.status
                doorPlateUnitList*.save(failOnError: true, flush: true)
            }

            //一卡通账号改为失效
            if (studentList) {
                List<Long> studentIdList = studentList*.id
                List<CardUser> cardUserList = studentService.fetchStudentCardUserInStudentList(studentIdList)
                if (cardUserList.size() > 0) {
                    cardUserList*.status = CardUser.USER_DELETE_STATUS
                    cardUserList*.save(failOnError: true, flush: true)
                }
                if (studentIdList.size() > 0) {
                    int changeBit = PersonDataChangeEnum.noGroupHandlerBit()
                    personDataChangeService.studentsDataChange(campusId, studentIdList, userId, userType, changeBit, OperateType.DEL.type, "毕业年级删除人员")
                }

                // 学生身份证失效
                if (studentIdList.size() > 0) {
                    List<UserEncodeInfo> userEncodeInfoList = UserEncodeInfo.findAllByUserIdInListAndUserTypeAndStatus(studentIdList, 1 as byte, 1 as byte)
                    if (userEncodeInfoList) {
                        userEncodeInfoList*.status = YearAndGradeAndUnitStatus.FINISH.status
                        userEncodeInfoList*.save(failOnError: true)
                    }
                }

                if (studentIdList) {
                    List<SignInSeat> signInSeatList = SignInSeat.findAllByStudentIdInListAndStatus(studentIdList, 1 as byte)
                    if (signInSeatList) {
                        signInSeatList*.status = 0 as byte
                        signInSeatList*.save(failOnError: true)
                    }
                }

                // 放学班数据删除
                if (studentIdList.size() > 0) {
                    List<AfterUnitStudent> afterUnitStudentList = AfterUnitStudent.findAllByCampusIdAndStudentIdInListAndStatus(campusId, studentIdList, 1 as byte)
                    if (afterUnitStudentList) {
                        afterUnitStudentList*.status = 0 as byte
                        afterUnitStudentList*.save(failOnError: true)
                    }
                }


                // 删除学生登入信息
                if (studentIdList) {
                    userService.deleteUserInfo(campusId, studentIdList, 1 as byte)
                }
            }
        }
    }

    def saveImpClass(List<UnitVO> unitVOS) {
        List<Unit> unitList = []
        unitVOS.each {
            Unit saveUnit = new Unit(
                    schoolId: it.schoolId,
                    campusId: it.campusId,
                    gradeId: it.gradeId,
                    sectionId: SectionType.VOCATIONAL_SCHOOL.id,
                    name: it.name,
                    code: it.unitCode,
                    facultyId: it.facultyId,
                    majorId: it.majorId,
                    type: 1 as byte,
                    status: 1 as byte,
                    unitLevel: 1 as String
            )
            unitList << saveUnit
        }

        unitList*.save(failOnError: true, flush: true)
    }
    
    List<Unit> fetchAllUnitByMajorAndGrade(Long campusId, Long majorId, Long gradeId) {
        Unit.findAllByCampusIdAndMajorIdAndGradeIdAndStatus(campusId, majorId, gradeId, 1 as byte)
    }

    List<Unit> fetchAllAdminUnitByGradeIdAndMajorId(Long campusId, List<Long> gradeIdList, List<Long> majorIdList) {
        Unit.findAllByCampusIdAndMajorIdInListAndGradeIdInListAndStatus(campusId, majorIdList, gradeIdList, 1 as byte)
    }

    List<Unit> fetchAllUnitByMajorId(Long campusId, Long majorId) {
        Unit.findAllByCampusIdAndMajorIdAndStatus(campusId, majorId, 1 as byte)
    }

    List<Unit> fetchAllUnitByFacultyId(Long campusId, Long facultyId) {
        Unit.findAllByCampusIdAndFacultyIdAndStatus(campusId, facultyId, 1 as byte)
    }
}

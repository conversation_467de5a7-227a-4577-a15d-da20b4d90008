package hiiadmin.approval

import com.alibaba.fastjson.JSONObject
import com.bugu.MobileUtil
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.handler.HandlerFactory
import hiiadmin.module.approval.*
import hiiadmin.school.repository.RepositoryStudentService
import hiiadmin.school.repository.RepositoryVisitorService
import hiiadmin.store.StoreGoodsService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import timetabling.oa.*
import timetabling.org.Campus
import timetabling.store.StoreGoods
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import static hiiadmin.ConstantEnum.ApprovalFormType.*
import static hiiadmin.ConstantEnum.ApprovalStatus.APPROVALING
import static hiiadmin.ConstantEnum.UserTypeEnum.*

@Transactional
class ApprovalV2DealJsonService {

    RepositoryStudentService repositoryStudentService

    RepositoryVisitorService repositoryVisitorService

    StoreGoodsService storeGoodsService

    WenYinApprovalRecordService wenYinApprovalRecordService

    @Autowired
    HandlerFactory handlerFactory

    def dealApprovalIdJson(Long userId, ApprovalTemplateThemeNew approvalTemplateThemeNew,
                           Campus campus,
                           ApprovalNew approvalNew,
                           String idJson,
                           Teacher teacher,
                           Parent parent,
                           Student student,
                           WenYinApprovalRecord wenYinApprovalRecord) {
        if (idJson) {
            BaseFormVO baseFormVO = JSONObject.parseObject(idJson, BaseFormVO.class)
            if (!baseFormVO.formType) {
                approvalNew.studentId = baseFormVO.studentId
            } else {
                switch (baseFormVO.formType) {
                //学生请假
                    case STUDENT_LEAVE.type:
                        StudentLeaveVO studentLeaveVO = JSONObject.parseObject(idJson, StudentLeaveVO.class)
                        LeaveDirection leaveDirection = getLeaveDirection(studentLeaveVO.leaveDirectionId)
                        if (leaveDirection.issue == 1 as byte || campus?.type == 2) {
                            hasApprovalByStudentIdAndLeaveDirectionIdAndTime(studentLeaveVO.studentId, studentLeaveVO.leaveDirectionId, TimeUtils.setDateForSql(studentLeaveVO.leaveStartTime), TimeUtils.setDateForSql(studentLeaveVO.leaveEndTime), approvalNew?.id)
                        }
                        if (approvalTemplateThemeNew) {
                            approvalNew.leaveCategoryId = approvalTemplateThemeNew.leaveCategoryId
                        }
                        approvalNew.leaveDirectionId = studentLeaveVO.leaveDirectionId
                        approvalNew.studentId = studentLeaveVO.studentId
                        approvalNew.leaveStartTime = TimeUtils.setDateForSql(studentLeaveVO.leaveStartTime)
                        approvalNew.leaveEndTime = TimeUtils.setDateForSql(studentLeaveVO.leaveEndTime)
                        approvalNew.duration = studentLeaveVO.duration
                        approvalNew.leaveReason = studentLeaveVO.leaveReason
                        approvalNew.leaveUnitType = studentLeaveVO.leaveUnitType
                        approvalNew.kitType = STUDENT_LEAVE.type
                        break
                        //访客预约
                    case VISITOR_APPOINTMENT.type:
                        VisitorAppointmentVO visitorAppointmentVO = JSONObject.parseObject(idJson, VisitorAppointmentVO.class)
                        //访客姓名
                        if (visitorAppointmentVO.promoterName) {
                            if (approvalTemplateThemeNew) {
                                switch (approvalTemplateThemeNew.promoterType) {
                                    case TEACHER.type:
                                        approvalNew.visitorName = teacher?.name
                                        break
                                    case PARENT.type:
                                        approvalNew.visitorName = parent?.name
                                        break
                                    case STUDENT.type:
                                        approvalNew.visitorName = student?.name
                                        break
                                }
                            }
                        } else {
                            approvalNew.visitorName = visitorAppointmentVO.visitorName
                        }
                        //访客手机号码
                        if (visitorAppointmentVO.promoterMobile) {
                            if (approvalTemplateThemeNew) {
                                switch (approvalTemplateThemeNew.promoterType) {
                                    case TEACHER.type:
                                        approvalNew.visitorMobile = MobileUtil.decodeMobile(teacher.encodeMobile)
                                        break
                                    case PARENT.type:
                                        approvalNew.visitorName = MobileUtil.decodeMobile(parent.encodeMobile)
                                        break
                                }
                            }
                        } else {
                            approvalNew.visitorMobile = visitorAppointmentVO.visitorMobile?.replace(" ", "")
                        }
                        Long daytimeStamp = TimeUtils.getDateStartTime(visitorAppointmentVO.visitTime).getTime()
                        Visitor visitor = repositoryVisitorService.fetchVisitorViaMobileAndDaytimeStamp(approvalNew.visitorMobile, daytimeStamp)
                        if (visitor) {
                            throw new HiiAdminException("当天已有入校申请，请勿重复提交")
                        } else if (visitorAppointmentVO.visitorIdCard) {
                            visitor = repositoryVisitorService.fetchVisitorViaIdCardAndDaytimeStamp(visitorAppointmentVO.visitorIdCard, daytimeStamp)
                            if (visitor) {
                                throw new HiiAdminException("当天已有入校申请，请勿重复提交")
                            }
                        }
                        approvalNew.respondentName = visitorAppointmentVO.respondentName
                        approvalNew.visitReason = visitorAppointmentVO.visitReason
                        approvalNew.visitTime = TimeUtils.setDateForSql(visitorAppointmentVO.visitTime)
                        approvalNew.visitorIdCard = visitorAppointmentVO.visitorIdCard
                        approvalNew.visitorAvatar = visitorAppointmentVO.visitorAvatar
                        approvalNew.push = visitorAppointmentVO.push
                        approvalNew.single = visitorAppointmentVO.single
                        approvalNew.kitType = VISITOR_APPOINTMENT.type
                        approvalNew.studentId = visitorAppointmentVO.studentId
                        approvalNew.respondentId = PhenixCoder.decodeId(visitorAppointmentVO.respondentId)
                        break
                        //访客登记
                    case VISITOR_REGISTRATION.type:
                        VisitorRegistrationVO visitorRegistrationVO = JSONObject.parseObject(idJson, VisitorRegistrationVO.class)
                        //访客姓名
                        if (visitorRegistrationVO.promoterName) {
                            switch (approvalTemplateThemeNew.promoterType) {
                                case TEACHER.type:
                                    approvalNew.visitorName = teacher.name
                                    break
                                case PARENT.type:
                                    approvalNew.visitorName = parent.name
                                    break
                                case STUDENT.type:
                                    approvalNew.visitorName = student.name
                                    break
                            }
                        } else {
                            approvalNew.visitorName = visitorRegistrationVO.visitorName
                        }
                        //访客手机号码
                        if (visitorRegistrationVO.promoterMobile) {
                            switch (approvalTemplateThemeNew.promoterType) {
                                case TEACHER.type:
                                    approvalNew.visitorMobile = MobileUtil.decodeMobile(teacher.encodeMobile)
                                    break
                                case PARENT.type:
                                    approvalNew.visitorName = MobileUtil.decodeMobile(parent.encodeMobile)
                                    break
                            }
                        } else {
                            approvalNew.visitorMobile = visitorRegistrationVO.visitorMobile?.replace(" ", "")
                        }
                        Long daytimeStamp = TimeUtils.getDateStartTime(visitorRegistrationVO.visitTime).getTime()
                        Visitor visitor = repositoryVisitorService.fetchVisitorViaMobileAndDaytimeStamp(approvalNew.visitorMobile, daytimeStamp)
                        if (visitor) {
                            throw new HiiAdminException("当天已有入校申请，请勿重复提交")
                        } else if (visitorRegistrationVO.visitorIdCard) {
                            visitor = repositoryVisitorService.fetchVisitorViaIdCardAndDaytimeStamp(visitorRegistrationVO.visitorIdCard, daytimeStamp)
                            if (visitor) {
                                throw new HiiAdminException("当天已有入校申请，请勿重复提交")
                            }
                        }
                        approvalNew.respondentName = visitorRegistrationVO.respondentName
                        approvalNew.visitReason = visitorRegistrationVO.visitReason
                        approvalNew.visitTime = TimeUtils.setDateForSql(visitorRegistrationVO.visitTime)
                        approvalNew.visitorIdCard = visitorRegistrationVO.visitorIdCard
                        approvalNew.visitorAvatar = visitorRegistrationVO.visitorAvatar
                        approvalNew.push = visitorRegistrationVO.push
                        approvalNew.single = visitorRegistrationVO.single
                        approvalNew.kitType = VISITOR_REGISTRATION.type
                        approvalNew.studentId = visitorRegistrationVO.studentId
                        approvalNew.respondentId = PhenixCoder.decodeId(visitorRegistrationVO.respondentId)
                        break
                        //老师请假
                    case TEACHER_LEAVE.type:
                        if (approvalNew.id == null) {
                            approvalNew.teacherId = baseFormVO.teacherId ?: userId
                        }
                        approvalNew.leaveCategoryId = baseFormVO.leaveCategoryId
                        approvalNew.leaveReason = baseFormVO.leaveReason
                        approvalNew.leaveStartTime = TimeUtils.setDateForSql(baseFormVO.leaveStartTime)
                        approvalNew.leaveEndTime = TimeUtils.setDateForSql(baseFormVO.leaveEndTime)
                        approvalNew.duration = baseFormVO.duration
                        approvalNew.kitType = TEACHER_LEAVE.type
                        approvalNew.leaveUnitType = baseFormVO.leaveUnitType
                        break
                        //学生荣誉申报
                    case STUDENT_HONOR_DECLARATION.type:
                        approvalNew.targetType = baseFormVO.targetType
                        if (baseFormVO.targetType == 2 as byte) {
                            approvalNew.unitIds = baseFormVO.unitIds
                        } else {
                            approvalNew.studentId = baseFormVO.studentId
                        }
                        approvalNew.awardName = baseFormVO.awardName
                        approvalNew.awardType = baseFormVO.awardType
                        approvalNew.awardTypeId = baseFormVO.awardTypeId
                        approvalNew.awardLevel = baseFormVO.awardLevel
                        approvalNew.awardLevelId = baseFormVO.awardLevelId
                        approvalNew.awardGrade = baseFormVO.awardGrade
                        approvalNew.awardGradeId = baseFormVO.awardGradeId
                        approvalNew.awardCategory = baseFormVO.awardCategory
                        approvalNew.awardCategoryId = baseFormVO.awardCategoryId
                        approvalNew.classificationIndex = baseFormVO.classificationIndex
                        approvalNew.awardReason = baseFormVO.awardReason
                        approvalNew.awardDocumentNo = baseFormVO.awardDocumentNo
                        approvalNew.awardingUnit = baseFormVO.awardingUnit
                        if (baseFormVO.awardTime) {
                            approvalNew.awardTime = TimeUtils.setDateForSql(baseFormVO.awardTime)
                        }
                        approvalNew.proofPhoto = baseFormVO.proofPhoto
                        approvalNew.kitType = STUDENT_HONOR_DECLARATION.type
                        approvalNew.teachingWeek = baseFormVO.teachingWeek
                        break
                        //文印申请
                    case WEN_YIN.type:
                        approvalNew.kitType = WEN_YIN.type
                        if (approvalNew && approvalNew.id != null) {
                            wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalNew.id)
                            if (wenYinApprovalRecord) {
                                wenYinApprovalRecord.subjectName = baseFormVO.subjectName
                                wenYinApprovalRecord.purpose = baseFormVO.purpose
                                wenYinApprovalRecord.printSpec = baseFormVO.printSpec
                                wenYinApprovalRecord.singleNum = baseFormVO.singleNum
                                wenYinApprovalRecord.printNum = baseFormVO.printNum
                                wenYinApprovalRecord.useTime = TimeUtils.setDateForSql(baseFormVO.useTime)
                                wenYinApprovalRecord.files = baseFormVO.files
                                wenYinApprovalRecord.save(failOnError: true)
                            }
                        } else {
                            wenYinApprovalRecord.schoolId = campus.schoolId
                            wenYinApprovalRecord.campusId = campus.id
                            wenYinApprovalRecord.approvalTemplateThemeId = approvalTemplateThemeNew.id
                            wenYinApprovalRecord.teacherId = approvalNew.promoterId
                            wenYinApprovalRecord.teacherName = approvalNew.promoterName
                            wenYinApprovalRecord.subjectName = baseFormVO.subjectName
                            wenYinApprovalRecord.purpose = baseFormVO.purpose
                            wenYinApprovalRecord.printSpec = baseFormVO.printSpec
                            wenYinApprovalRecord.singleNum = baseFormVO.singleNum
                            wenYinApprovalRecord.printNum = baseFormVO.printNum
                            wenYinApprovalRecord.useTime = TimeUtils.setDateForSql(baseFormVO.useTime)
                            wenYinApprovalRecord.files = baseFormVO.files
                        }
                        break
                        //周期性请假
                    case STUDENT_PERIODIC_LEAVE.type:
                        approvalNew.kitType = STUDENT_PERIODIC_LEAVE.type
                        StudentLeaveVO studentLeaveVO = JSONObject.parseObject(idJson, StudentLeaveVO.class)
                        LeaveDirection leaveDirection = getLeaveDirection(studentLeaveVO.leaveDirectionId)
                        if (!studentLeaveVO.leavePeriod) {
                            throw new HiiAdminException("未选择请假频次")
                        }
                        if (leaveDirection.issue == 1 as byte) {
                            List<Long> hasLeaveStudentIdList = countApprovalByStudentIdAndLeaveDirectionIdAndTime4Period(studentLeaveVO.studentId, studentLeaveVO.leaveDirectionId,
                                    TimeUtils.setDateForSql(studentLeaveVO.leaveStartDate), TimeUtils.setDateForSql(studentLeaveVO.leaveEndDate), TimeUtils.setDateForSql(studentLeaveVO.leaveStartTime), TimeUtils.setDateForSql(studentLeaveVO.leaveEndTime), studentLeaveVO.leavePeriod, approvalNew?.id)
                            if (hasLeaveStudentIdList.size() > 0) {
                                List<Student> studentList = repositoryStudentService.fetchAllStudentByStudentIdInList(hasLeaveStudentIdList)
                                String name = studentList*.name.join(",")
                                throw new HiiAdminException("以下学生：${name}，请假时间有交集")
                            }
                        }
                        approvalNew.leaveCategoryId = studentLeaveVO.leaveCategoryId
                        approvalNew.leaveDirectionId = studentLeaveVO.leaveDirectionId
                        approvalNew.studentId = studentLeaveVO.studentId
                        approvalNew.leaveStartTime = TimeUtils.setDateForSql(studentLeaveVO.leaveStartTime)
                        approvalNew.leaveEndTime = TimeUtils.setDateForSql(studentLeaveVO.leaveEndTime)
                        approvalNew.duration = studentLeaveVO.duration
                        approvalNew.leaveStartDate = TimeUtils.setDateForSql(studentLeaveVO.leaveStartDate)
                        approvalNew.leaveEndDate = TimeUtils.setDateForSql(studentLeaveVO.leaveEndDate)
                        approvalNew.leavePeriod = studentLeaveVO.leavePeriod
                        approvalNew.leaveReason = studentLeaveVO.leaveReason
                        approvalNew.leaveUnitType = studentLeaveVO.leaveUnitType
                        break
                        //临时调代课
                    case SUBSTITUTE_COURSE.type:
                        approvalNew.kitType = SUBSTITUTE_COURSE.type
                        if (approvalNew.id == null) {
                            approvalNew.teacherId = userId
                        }
                        approvalNew.courseJson = baseFormVO.courseJson
                        approvalNew.courseNum = baseFormVO.courseNum
                        approvalNew.memo = baseFormVO.memo
                        break
                        // 学生任职履历
                    case STUDENT_JOB.type:
                        approvalNew.kitType = STUDENT_JOB.type
                        approvalNew.studentId = baseFormVO.studentId
                        approvalNew.jobId = baseFormVO.jobId
                        if (baseFormVO.leaveStartTime) {
                            approvalNew.leaveStartTime = TimeUtils.setDateForSql(baseFormVO.leaveStartTime)
                        }

                        if (baseFormVO.leaveEndTime) {
                            approvalNew.leaveEndTime = TimeUtils.setDateForSql(baseFormVO.leaveEndTime)
                        }
                        break
                        //物品借用
                    case STORE_GOODS_BORROW.type:
                        approvalNew.kitType = STORE_GOODS_BORROW.type
                        approvalNew.teacherId = baseFormVO.teacherId
                        approvalNew.goodsId = PhenixCoder.decodeId(baseFormVO.goodsId)

                        if (StringUtils.isBlank(baseFormVO.borrowNum)) {
                            throw new HiiAdminException("借用数量必填！")
                        }

                        if (!baseFormVO.borrowNum.isNumber()) {
                            throw new HiiAdminException("借用数量必须为数字！")
                        }

                        if (!PatternUtils.isNumeric(baseFormVO.borrowNum)) {
                            throw new HiiAdminException("借用数量不能包含小数点！")
                        }

                        approvalNew.borrowNum = Integer.parseInt(baseFormVO.borrowNum)

                        if (baseFormVO.borrowStartTime) {
                            approvalNew.borrowStartTime = TimeUtils.setDateForSql(baseFormVO.borrowStartTime)
                        }

                        if (baseFormVO.borrowEndTime) {
                            approvalNew.borrowEndTime = TimeUtils.setDateForSql(baseFormVO.borrowEndTime)
                        }

                        StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(approvalNew.goodsId)
                        if (approvalNew.borrowNum > storeGoods.num) {
                            throw new HiiAdminException("借用数量超出库存，无法借用")
                        }
                        break
                        //复课返校
                    case RESUME_RETURN_SCHOOL.type:
                        approvalNew.kitType = RESUME_RETURN_SCHOOL.type
                        approvalNew.studentId = baseFormVO.studentId
                        approvalNew.memo = baseFormVO.memo
                        approvalNew.files = baseFormVO.files
                        break

                    default:
                        approvalNew.kitType = baseFormVO.formType
                        handlerFactory.createApprovalHandler(baseFormVO.formType)
                                .dealHandle(userId, approvalTemplateThemeNew, campus, approvalNew, baseFormVO, teacher, parent, student, wenYinApprovalRecord, null)
                        break
                }
            }
        }
    }


    def dealApprovalInvoiceJson(String invoiceJson, Long userId, ApprovalTemplateThemeNew approvalTemplateThemeNew, ApprovalNew approvalNew) {
        //发票
        if (invoiceJson && approvalTemplateThemeNew) {
            List<InvoiceJson> invoiceJsonList = JSONObject.parseArray(invoiceJson, InvoiceJson.class)

            invoiceJsonList?.each {
                if (it.invoiceCode == "_") {
                    throw new HiiAdminException("发票号码识别异常，请上传正确发票")
                }
            }

            List<String> invoiceCodeList = Invoice.findAllByStatus(1 as byte)*.code
            invoiceJsonList.each {
                if (!invoiceCodeList?.contains(it.invoiceCode)) {
                    Invoice invoice = new Invoice(
                            approvalId: approvalNew.id,
                            campusId: approvalNew.campusId,
                            code: it.invoiceCode,
                            url: it.url,
                            userId: userId,
                            userType: approvalTemplateThemeNew.promoterType,
                            status: 1 as byte
                    )
                    invoice.save(failOnError: true)
                    invoiceCodeList.add(it.invoiceCode)
                } else {
                    throw new HiiAdminException("发票号码为${it.invoiceCode.split("_")[1]}的发票重复")
                }
            }
        }
    }


    /**
     * 普通请假时间交集判断
     * @param studentId
     * @param leaveDirectionId
     * @param startDate
     * @param endDate
     * @return
     */
    def hasApprovalByStudentIdAndLeaveDirectionIdAndTime(String studentId, Long leaveDirectionId, Date startDate, Date endDate, Long approvalId) throws Exception {
        List<Long> studentIdList = studentId.split(",").collect { it.toLong() }
        def c = ApprovalNew.createCriteria()
        def list = c.list {
            eq("leaveDirectionId", leaveDirectionId)
            eq("status", APPROVALING.status)
            or {
                and {
                    le("leaveStartTime", endDate)
                    ge("leaveEndTime", startDate)
                }
                and {
                    le("leaveStartDate", endDate)
                    ge("leaveEndDate", startDate)
                }
            }
        } as List<ApprovalNew>

        if (approvalId && CollectionUtils.isNotEmpty(list)) {
            list.removeIf { it.id == approvalId }
        }
        if (list?.size() > 0) {
            list.each {
                ApprovalNew approvalNew ->
                    List<Long> studentIdList1 = approvalNew.studentId.split(",").collect { it.toLong() }.findAll { it in studentIdList }
                    //如果学生有交集
                    if (studentIdList1?.size() > 0) {
                        //如果是周期性请假
                        if (approvalNew.kitType == STUDENT_PERIODIC_LEAVE.type) {
                            List<LeaveRecordTimeVO> leaveRecordTimeVOList = TimeUtils.getTimeList(approvalNew.leaveStartDate, approvalNew.leaveEndDate, approvalNew.leaveStartTime, approvalNew.leaveEndTime, approvalNew.leavePeriod)
                            leaveRecordTimeVOList.each {
                                LeaveRecordTimeVO leaveRecordTimeVO ->
                                    //如果时间有交集
                                    if (TimeUtils.isConfilct(leaveRecordTimeVO.startTime, leaveRecordTimeVO.endTime, startDate, endDate)) {
                                        log.info("学生请假时间有交集:approvalId${approvalNew.id}")
                                        Student student = repositoryStudentService.fetchStudentById(studentIdList1.get(0))
                                        HasLeaveStudentVO hasLeaveStudentVO = new HasLeaveStudentVO(
                                                approvalId: approvalNew.id,
                                                startTime: approvalNew.leaveStartTime.getTime(),
                                                endTime: approvalNew.leaveEndTime.getTime(),
                                                submitter: approvalNew.promoterName,
                                                studentName: student?.name
                                        )
                                        String str = JSONObject.toJSONString(hasLeaveStudentVO)
                                        throw new HiiAdminException(str)
                                    }
                            }
                        } else if (TimeUtils.isConfilct(approvalNew.leaveStartTime, approvalNew.leaveEndTime, startDate, endDate)) {
                            log.info("学生请假时间有交集:approvalId${approvalNew.id}")
                            Student student = repositoryStudentService.fetchStudentById(studentIdList1.get(0))
                            HasLeaveStudentVO hasLeaveStudentVO = new HasLeaveStudentVO(
                                    approvalId: approvalNew.id,
                                    startTime: approvalNew.leaveStartTime.getTime(),
                                    endTime: approvalNew.leaveEndTime.getTime(),
                                    submitter: approvalNew.promoterName,
                                    studentName: student?.name
                            )
                            String str = JSONObject.toJSONString(hasLeaveStudentVO)
                            throw new HiiAdminException(str)
                        }
                        log.info("学生请假时间无交集:approvalId:${approvalNew.id}")
                    }
            }
        }
        def c1 = LeaveRecord.createCriteria()
        def list1 = c1.list {
            eq("leaveDirectionId", leaveDirectionId)
            "in"("studentId", studentIdList)
            eq("status", ConstantEnum.LeaveRecordStatus.INIT.status)
            le("leaveStartTime", endDate)
            ge("leaveEndTime", startDate)
        } as List<LeaveRecord>
        if (list1?.size() > 0) {
            LeaveRecord leaveRecord = list1.get(0)
            log.info("学生请假时间有交集:leaveRecordId${leaveRecord.id}")
            Student student = repositoryStudentService.fetchStudentById(leaveRecord.studentId)
            HasLeaveStudentVO hasLeaveStudentVO = new HasLeaveStudentVO(
                    approvalId: leaveRecord.approvalId,
                    startTime: leaveRecord.leaveStartTime.getTime(),
                    endTime: leaveRecord.leaveEndTime.getTime(),
                    submitter: leaveRecord.promoterName,
                    studentName: student?.name
            )
            String str = JSONObject.toJSONString(hasLeaveStudentVO)
            throw new HiiAdminException(str)
        }
    }


    LeaveDirection getLeaveDirection(Long id) {
        LeaveDirection.get(id)
    }

    /**
     * 周期性请假时间交集判断
     * @param studentId
     * @param leaveDirectionId
     * @param startDate
     * @param endDate
     * @return
     */
    List<Long> countApprovalByStudentIdAndLeaveDirectionIdAndTime4Period(String studentId, Long leaveDirectionId, Date startDate, Date endDate, Date startTime, Date endTime, String leavePeriod, Long approvalId) {
        List<Long> studentIdList = studentId.split(",").collect { it.toLong() }
        List<Long> ids = []
        def c = ApprovalNew.createCriteria()
        def list = c.list {
            eq("leaveDirectionId", leaveDirectionId)
            eq("status", APPROVALING.status)
            or {
                and {
                    le("leaveStartTime", startDate)
                    ge("leaveEndTime", startDate)
                }
                and {
                    le("leaveStartTime", endDate)
                    ge("leaveEndTime", endDate)
                }
                and {
                    ge("leaveStartTime", startDate)
                    le("leaveEndTime", endDate)
                }
                and {
                    le("leaveStartDate", startDate)
                    ge("leaveEndDate", startDate)
                }
                and {
                    le("leaveStartDate", endDate)
                    ge("leaveEndDate", endDate)
                }
                and {
                    ge("leaveStartDate", startDate)
                    le("leaveEndDate", endDate)
                }
            }
        } as List<ApprovalNew>


        if (approvalId && CollectionUtils.isNotEmpty(list)) {
            list.removeIf { it.id == approvalId }
        }


        List<LeaveRecordTimeVO> periodicLeaveList = TimeUtils.getTimeList(startDate, endDate, startTime, endTime, leavePeriod)
        if (list?.size() > 0) {
            periodicLeaveList.each {
                LeaveRecordTimeVO periodicLeaveTime ->
                    list.each {
                        ApprovalNew approvalNew ->
                            List<Long> studentIdList1 = approvalNew.studentId.split(",").collect { it.toLong() }.findAll { it in studentIdList }
                            //如果学生有交集
                            if (studentIdList1?.size() > 0) {
                                //如果是周期性请假
                                if (approvalNew.kitType == STUDENT_PERIODIC_LEAVE.type) {
                                    List<LeaveRecordTimeVO> leaveRecordTimeVOList = TimeUtils.getTimeList(approvalNew.leaveStartDate, approvalNew.leaveEndDate, approvalNew.leaveStartTime, approvalNew.leaveEndTime, approvalNew.leavePeriod)
                                    leaveRecordTimeVOList.each {
                                        LeaveRecordTimeVO leaveRecordTimeVO ->
                                            //如果时间有交集
                                            if (TimeUtils.isConfilct(leaveRecordTimeVO.startTime, leaveRecordTimeVO.endTime, periodicLeaveTime.startTime, periodicLeaveTime.endTime)) {
                                                ids.addAll(studentIdList1)
                                            }
                                    }
                                } else if (TimeUtils.isConfilct(approvalNew.leaveStartTime, approvalNew.leaveEndTime, periodicLeaveTime.startTime, periodicLeaveTime.endTime)) {
                                    ids.addAll(studentIdList1)
                                }
                            }
                    }
            }
        }
        def c1 = LeaveRecord.createCriteria()
        def list1 = c1.list {
            eq("leaveDirectionId", leaveDirectionId)
            "in"("studentId", studentIdList)
            eq("status", ConstantEnum.LeaveRecordStatus.INIT.status)
            or {
                and {
                    le("leaveStartTime", startDate)
                    ge("leaveEndTime", startDate)
                }
                and {
                    le("leaveStartTime", endDate)
                    ge("leaveEndTime", endDate)
                }
                and {
                    ge("leaveStartTime", startDate)
                    le("leaveEndTime", endDate)
                }
            }
        } as List<LeaveRecord>
        if (list1?.size() > 0) {
            list1.each {
                LeaveRecord leaveRecord ->
                    periodicLeaveList.each {
                        //如果时间有交集
                        if (TimeUtils.isConfilct(leaveRecord.leaveStartTime, leaveRecord.leaveEndTime, it.startTime, it.endTime)) {
                            ids.add(leaveRecord.studentId)
                        }
                    }
            }
        }
        return ids.toSet().toList()
    }


    static class InvoiceJson implements Serializable {
        String invoiceCode
        String url
    }
}

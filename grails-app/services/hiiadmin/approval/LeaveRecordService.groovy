package hiiadmin.approval

import com.alibaba.fastjson.JSONObject
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.factory.exportFile.ExportFileServiceFactory
import timetabling.oa.ApprovalExportRecord
import timetabling.oa.LeaveRecord

import static hiiadmin.ConstantEnum.LeaveRecordStatus

@Slf4j
@Transactional
class LeaveRecordService {

    ExportFileServiceFactory exportFileServiceFactory

    def fetchLeaveRecord(Long campusId, String searchValue, Long leaveCategoryId, Long leaveDirectionId, Long leaveStartTime, Long leaveEndTime, Byte status, String promoterName, Long applyStartTime, Long applyEndTime, Boolean gender) {
        Date date = new Date()
        def c = LeaveRecord.createCriteria()
        def list = c.list {
            eq("campusId", campusId)
            if (searchValue) {
                or {
                    like("studentName", searchValue + "%")
                    like("studentCode", searchValue + "%")
                }
            }
            if (leaveCategoryId) {
                eq("leaveCategoryId", leaveCategoryId)
            }
            if (leaveDirectionId) {
                eq("leaveDirectionId", leaveDirectionId)
            }
            if (leaveStartTime && leaveEndTime) {
                le("leaveStartTime", new Date(leaveEndTime))
                ge("leaveEndTime", new Date(leaveStartTime))
            }
            if (applyStartTime && applyEndTime) {
                between("applyTime", new Date(applyStartTime), new Date(applyEndTime))
            }
            if (status) {
                if (status == LeaveRecordStatus.INIT.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    gt("leaveStartTime", date)
                } else if (status == LeaveRecordStatus.ASKING.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    le("leaveStartTime", date)
                    ge("leaveEndTime", date)
                } else if (status == LeaveRecordStatus.END.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    lt("leaveEndTime", date)
                } else {
                    eq("status", status)
                }
            }
            if (promoterName) {
                like("promoterName", promoterName + "%")
            }
            if (gender != null) {
                eq("gender", gender)
            }
            order("dateCreated", "desc")
        } as List<LeaveRecord>
        list
    }


    def fetchLeaveRecordLimit(Long campusId, String searchValue, Long leaveCategoryId, Long leaveDirectionId, Long leaveStartTime, Long leaveEndTime, Byte status, String promoterName, Long applyStartTime, Long applyEndTime, Boolean gender, int p, int s) {
        Date date = new Date()
        def c = LeaveRecord.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            if (searchValue) {
                or {
                    like("studentName", searchValue + "%")
                    like("studentCode", searchValue + "%")
                }
            }
            if (leaveCategoryId) {
                eq("leaveCategoryId", leaveCategoryId)
            }
            if (leaveDirectionId) {
                eq("leaveDirectionId", leaveDirectionId)
            }
            if (leaveStartTime && leaveEndTime) {
                le("leaveStartTime", new Date(leaveEndTime))
                ge("leaveEndTime", new Date(leaveStartTime))
            }
            if (applyStartTime && applyEndTime) {
                between("applyTime", new Date(applyStartTime), new Date(applyEndTime))
            }
            if (status) {
                if (status == LeaveRecordStatus.INIT.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    gt("leaveStartTime", date)
                } else if (status == LeaveRecordStatus.ASKING.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    le("leaveStartTime", date)
                    ge("leaveEndTime", date)
                } else if (status == LeaveRecordStatus.END.status) {
                    eq("status", LeaveRecordStatus.INIT.status)
                    lt("leaveEndTime", date)
                } else {
                    eq("status", status)
                }
            }
            if (promoterName) {
                like("promoterName", promoterName + "%")
            }
            
            if (gender != null) {
                eq("gender", gender)
            }
            
            order("dateCreated", "desc")
        }
        [list: list as List<LeaveRecord>, total: list?.totalCount ?: 0]
    }

    def transformLeaveExcel(List<LeaveRecord> leaveRecordList,Boolean approvalDetail, ApprovalExportRecord approvalExportRecord) {
        try {
            approvalExportRecord.title = "学生请假记录导出表" + "-${System.currentTimeMillis()}.xlsx"
            String fileName = approvalExportRecord.title
            JSONObject jsonObject = new JSONObject(
                    leaveRecordList: leaveRecordList,
                    campusId: approvalExportRecord.campusId,
                    fileName: fileName,
                    approvalDetail: approvalDetail
            )
            String fileUrl = exportFileServiceFactory.creatFileService("leaveRecord").transformExcel(jsonObject)
            if (fileUrl) {
                approvalExportRecord.url = fileUrl
                approvalExportRecord.save(failOnError: true)
            }
        } catch (Exception e) {
            log.error("生成审批记录excel出错,message:${e.message}".toString(), e)
        }
    }


    def fetchLeaveRecordByCampusIdAndStartTimeAndEndTime(Long campusId, Date startDate, Date endDate) {
        def c = LeaveRecord.createCriteria()
        def list = c.list() {
            eq("campusId", campusId)
            eq("status", LeaveRecordStatus.INIT.status)
            le("leaveStartTime", endDate)
            ge("leaveEndTime", startDate)
        }
        list as List<LeaveRecord>
    }

    /**
     * 获取学生在当前时间段内的请假记录
     * @param studentId
     * @param startTime
     * @param endTime
     * @return
     */
    List<LeaveRecord> isLeaveRecordHasCreated(Long studentId, Date startTime, Date endTime) {
        def c = LeaveRecord.createCriteria()
        def list = c.list {
            and {
                eq("studentId", studentId)
                eq("status", LeaveRecordStatus.INIT.status)
                le('leaveStartTime', endTime)
                ge('leaveEndTime', startTime)
            }
        }
        if (!list) {
            return []
        }
        return list as List<LeaveRecord>
    }


    LeaveRecord fetchLeaveRecordById(Long id) {
        LeaveRecord.findById(id)
    }

    List<LeaveRecord> fetchAllLeaveRecordByCampusId(Long campusId) {
        LeaveRecord.findAllByCampusId(campusId)
    }

    List<LeaveRecord> fetchAllLeaveRecord4student(long campusId, Long studentId) {
        LeaveRecord.findAllByCampusIdAndStudentId(campusId, studentId)
    }
}

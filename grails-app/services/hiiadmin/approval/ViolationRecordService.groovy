package hiiadmin.approval

import com.alibaba.fastjson.JSONObject
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.factory.exportFile.ExportFileServiceFactory
import hiiadmin.module.approval.ViolationDemoteRecordVO
import hiiadmin.module.approval.ViolationRecordVO
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.utils.TimeUtils
import timetabling.oa.ApprovalExportRecord
import timetabling.oa.Violation
import timetabling.oa.ViolationRecord
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Student

@Slf4j
@Transactional
class ViolationRecordService {

    StudentService studentService

    UnitService unitService

    GradeService gradeService

    TeacherService teacherService

    UnitStudentService unitStudentService

    ExportFileServiceFactory exportFileServiceFactory

    CampusService campusService

    def pageViolationRecord(Long campusId, String searchValue, Long startTime, Long endTime, Long studentId, int p, int s) {
        List<Long> studentIdList = []
        if (searchValue) {
            List<Student> studentList = studentService.findAllStudentByCampusIdAndSearchValue(campusId, null, searchValue, null, null, null, null, null, null, 99, 1, 30)
            if (studentList?.size() == 0) {
                return [list: [], total: 0]
            } else {
                studentIdList = studentList*.id
            }
        }
        def c = ViolationRecord.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            if (studentId) {
                eq("studentId", studentId)
            }
            if (searchValue && studentIdList?.size() > 0) {
                "in"("studentId", studentIdList)
            }
            if (startTime) {
                ge("violateTime", new Date(startTime))
            }
            if (endTime) {
                le("violateTime", new Date(endTime))
            }
            eq("status", 1 as byte)
            order('id', 'desc')
        }
        [list: list as List<ViolationRecord>, total: list?.totalCount ?: 0]
    }

    def pageViolationRecordV2(Long campusId, String searchValue, Long startTime, Long endTime, Long studentId, int p, int s) {
        StringBuilder stringBuilder = new StringBuilder("SELECT vr ")
        Map map = buildHQL(stringBuilder, campusId, searchValue, startTime, endTime, studentId, p, s)
        List<ViolationRecord> violationRecordList = ViolationRecord.executeQuery(stringBuilder.toString(), map)

        StringBuilder countBuilder = new StringBuilder(" SELECT COUNT(1) ")
        Map countMap = buildHQL(countBuilder, campusId, searchValue, startTime, endTime, studentId, 0, 0)
        Integer count = ViolationRecord.executeQuery(countBuilder.toString(), countMap)[0] as Integer
        [list: violationRecordList, total: count]
    }

    static Map buildHQL(StringBuilder stringBuilder, Long campusId, String searchValue, Long startTime, Long endTime, Long studentId, int p, int s) {
        Map map = [:]
        stringBuilder.append(""" FROM ViolationRecord vr, Student s WHERE s.id = vr.studentId
                                    AND vr.campusId = :campusId 
                                    AND vr.pId IS NULL
                                    AND s.status IN (0, 1, 6, 7) """)

        map.put("campusId", campusId)

        if (searchValue) {
            stringBuilder.append(""" AND (s.name LIKE :searchValue OR s.code LIKE :searchValue) """)
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (studentId) {
            stringBuilder.append(""" AND vr.studentId = :studentId """)
            map.put("studentId", studentId)
        }

        if (startTime) {
            stringBuilder.append(""" AND vr.violateTime >= :startTime """)
            map.put("startTime", new Date(startTime))
        }

        if (endTime) {
            stringBuilder.append(""" AND vr.violateTime <= :endTime """)
            map.put("endTime", new Date(endTime))
        }

        if (s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        stringBuilder.append(""" AND vr.status = 1 ORDER BY vr.id DESC""")


        map
    }

    def transformViolationRecordVOList(List<ViolationRecord> violationRecordList) {
        List<ViolationRecordVO> violationRecordVOList = []
        if (violationRecordList?.size() > 0) {
            Byte campusType = campusService.fetchCampusById(violationRecordList[0].campusId)?.type ?: ConstantEnum.CampusType.K12.type

            List<Long> studentIdList = violationRecordList*.studentId
            List<Student> studentList = studentService.fetchAllStudentByStudentIdInList(studentIdList)
            Map<Long, Unit> longUnitMap = [:]
            Map<Long, Grade> longGradeMap = [:]
            longUnitMap = unitService.getStudentIdNormalUnitMapByStudentIdList(studentIdList, false)
            longGradeMap = gradeService.getStudentIdGradeMapByStudentIdList(studentIdList)
            violationRecordVOList = violationRecordList.collect { violationRecord ->
                Student student = studentList.find { it.id == violationRecord.studentId }
                String studentName = studentService.appendStudentStatus(student)
                Unit unit = longUnitMap.get(violationRecord.studentId)

                ViolationRecordVO violationRecordVO = new ViolationRecordVO(
                        id: violationRecord.id,
                        studentName: studentName,
                        unitName: unit ? (unit.alias ?: unit.name) : null,
                        sectionName: ConstantEnum.SectionType.getEnumByType(longGradeMap.get(violationRecord.studentId)?.sectionId)?.name,
                        gradeName: longGradeMap.get(violationRecord.studentId)?.name,
                        code: student?.code,
                        violateTime: violationRecord.violateTime.getTime(),
                        punishmentResult: violationRecord.punishmentResult,
                        publishTime: violationRecord.publishTime.getTime(),
                        score: violationRecord.score,
                        punishmentResultId: violationRecord.punishmentResultId
                )

                if (campusType == ConstantEnum.CampusType.K12.type) {
                    violationRecordVO.unitName = unit ? (unit.alias ?: unit.name) : null
                    violationRecordVO.sectionName =  ConstantEnum.SectionType.getEnumByType(longGradeMap.get(violationRecord.studentId)?.sectionId)?.name
                    violationRecordVO.gradeName = longGradeMap.get(violationRecord.studentId)?.name
                } else {
                    violationRecordVO.unitName = unit?.name
                }

                List<ViolationRecord> demoteRecords = ViolationRecord.findAllByPIdAndStatus(violationRecord.id, 1 as byte)
                demoteRecords.sort { -it.publishTime?.getTime() }
                if (demoteRecords) {
                    violationRecordVO.demotionResult = demoteRecords.get(0).punishmentResult
                    violationRecordVO.demotionResultId = demoteRecords.get(0).punishmentResultId
                }

                violationRecordVO
            }
        }
        violationRecordVOList
    }

    def fetchViolationRecordById(Long id) {
        ViolationRecord.findById(id)
    }

    def transformViolationRecordVO(Long id) {
        ViolationRecord violationRecord = fetchViolationRecordById(id)
        Student student = studentService.fetchStudentById(violationRecord.studentId)
        String studentName = studentService.appendStudentStatus(student)
        Long unitId = unitStudentService.fetchAdministrativeUnitIdByStudentId(violationRecord.studentId)
        String unitName = null
        String gradeName = null
        String sectionName = null
        if (unitId) {
            Byte campusType = campusService.fetchCampusById(violationRecord.campusId)?.type ?: ConstantEnum.CampusType.K12.type
            Unit unit = unitService.fetchUnitById(unitId)
            if (campusType == ConstantEnum.CampusType.K12.type) {
                unitName = unit ? (unit.alias ?: unit.name) : null
                Grade grade = gradeService.fetchGradeById(unit?.gradeId)
                gradeName = grade?.name
                sectionName = ConstantEnum.SectionType.getEnumByType(grade?.sectionId)?.name
            } else {
                unitName = unit?.name
            }
        }
        ViolationRecordVO vo = new ViolationRecordVO(
                id: violationRecord.id,
                studentName: studentName,
                avatar: student.pic,
                unitName: unitName,
                sectionName: sectionName,
                gradeName: gradeName,
                code: student?.code,
                violateTime: violationRecord.violateTime.getTime(),
                punishmentResult: violationRecord.punishmentResult,
                memo: violationRecord.memo,
                proofPhoto: violationRecord.proofPhoto,
                publishTeacherName: teacherService.fetchTeacherById(violationRecord.publishTeacherId)?.name,
                publishTime: violationRecord.publishTime.getTime(),
        )

        List<ViolationRecord> demotes = ViolationRecord.findAllByPIdAndStatus(violationRecord.id, 1 as byte)
        List<ViolationDemoteRecordVO> demoteRecordVOList = []
        demotes.each {
            ViolationDemoteRecordVO demoteRecordVO = new ViolationDemoteRecordVO()
            demoteRecordVO.id = it.id
            demoteRecordVO.demoteTime = it.violateTime?.getTime()
            demoteRecordVO.demoteResult = it.punishmentResult
            demoteRecordVO.pushTeacherName = teacherService.fetchTeacherById(it.publishTeacherId)?.name
            demoteRecordVO.registerTime = it.publishTime?.getTime()

            demoteRecordVOList << demoteRecordVO
        }

        demoteRecordVOList.sort { -it.registerTime }

        vo.demotes = demoteRecordVOList
        vo
    }

    def updateViolationRecord(Long id, Long demoteId, Long demoteTime, Long teacherId, Long campusId) {
        ViolationRecord violationRecord = fetchViolationRecordById(id)
        if (!violationRecord) {
            throw new HiiAdminException("记录不存在!")
        }

        Violation violation = Violation.findById(violationRecord.punishmentResultId)
        Violation demoteViolation = Violation.findById(demoteId)

        if (violation && violation.level >= demoteViolation.level) {
            throw new HiiAdminException("降级结果选择错误，不能比当前记录的降级结果高")
        }

        ViolationRecord demoteRecord = new ViolationRecord()
        demoteRecord.campusId = violationRecord.campusId
        demoteRecord.schoolId = violationRecord.schoolId
        demoteRecord.studentId = violationRecord.studentId
        demoteRecord.pId = violationRecord.id
        demoteRecord.violateTime = new Date(demoteTime)

        Campus campus = Campus.findById(campusId)
        if (campus.violationFlag && campus.singleViolation == 0) {
            violationRecord.score = demoteViolation?.score
            demoteRecord.score = demoteViolation?.score
        }

        demoteRecord.punishmentResult = demoteViolation?.name
        demoteRecord.punishmentResultId = demoteViolation?.id
        demoteRecord.publishTeacherId = teacherId
        demoteRecord.publishTime = new Date()
        demoteRecord.status = 1 as byte

        demoteRecord.save(failOnError: true)
        violationRecord.save(failOnError: true)
    }

    def deleteViolationRecord(Long id) {
        ViolationRecord violationRecord = fetchViolationRecordById(id)
        violationRecord.status = 0
        violationRecord.save(failOnError: true)

        List<ViolationRecord> demoteRecords = ViolationRecord.findAllByPIdAndStatus(id, 1 as byte)
        if (demoteRecords) {
            demoteRecords*.status = 0
            demoteRecords*.save(failOnError: true)
        }
    }

    Long fetchMaxDemoteNum(Long campusId, String searchValue, Long startTime, Long endTime) {
        String HQL = """ SELECT vr.studentId, vr.pId, COUNT(1) FROM ViolationRecord vr, Student s 
                                    WHERE vr.studentId = s.id 
                                    AND vr.campusId = :campusId
                                    AND vr.pId IS NOT NULL """
        Map map = [:]
        StringBuilder stringBuilder = new StringBuilder(HQL)
        map.put("campusId", campusId)
        if (searchValue) {
            stringBuilder.append(" AND (s.name LIKE :searchValue OR s.code LIKE :searchValue) ")
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (startTime) {
            stringBuilder.append(" AND vr.violateTime >= :startTime ")
            map.put("startTime", new Date(startTime))
        }

        if (endTime) {
            stringBuilder.append(" AND vr.violateTime <= :endTime ")
            map.put("endTime", new Date(endTime))
        }

        stringBuilder.append(" AND vr.status = 1 GROUP BY vr.studentId, vr.pId ")

        def re = ViolationRecord.executeQuery(stringBuilder.toString(), map)
        List<Long> longList = []
        re.each {
            longList.add(it[2])
        }

        longList.removeAll([null])
        return longList?.max() ?: 0L
    }

    def fetchViolationRecord(Long campusId, String searchValue, Long startTime, Long endTime) {
        String HQL = """ SELECT vr FROM ViolationRecord vr, Student s 
                                    WHERE vr.studentId = s.id 
                                    AND vr.campusId = :campusId
                                    AND vr.pId IS NULL """
        Map map = [:]
        StringBuilder stringBuilder = new StringBuilder(HQL)
        map.put("campusId", campusId)
        if (searchValue) {
            stringBuilder.append(" AND (s.name LIKE :searchValue OR s.code LIKE :searchValue) ")
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (startTime) {
            stringBuilder.append(" AND vr.violateTime >= :startTime ")
            map.put("startTime", new Date(startTime))
        }

        if (endTime) {
            stringBuilder.append(" AND vr.violateTime <= :endTime ")
            map.put("endTime", new Date(endTime))
        }

        stringBuilder.append(" AND vr.status = 1 ORDER BY vr.id DESC")
        List<ViolationRecord> violationRecordList = ViolationRecord.executeQuery(stringBuilder.toString(), map)
        violationRecordList
    }

    void transformViolationRecordExcel(List<ViolationRecord> violationRecordList, ApprovalExportRecord approvalExportRecord, Long maxNum, Long startTime, Long endTime) {
        try {
            if (startTime && endTime) {
                String startStr = TimeUtils.getTimeStr(startTime, "yyyy-MM-dd")
                String endStr = TimeUtils.getTimeStr(endTime, "yyyy-MM-dd")
                approvalExportRecord?.title = """学生违纪管理记录(${startStr}~${endStr})-${System.currentTimeMillis()}.xlsx"""
            } else {
                approvalExportRecord?.title = """学生违纪管理记录-${System.currentTimeMillis()}.xlsx"""
            }
            String fileName = approvalExportRecord?.title
            JSONObject jsonObject = new JSONObject(
                    violationRecordList: violationRecordList,
                    maxNum: maxNum,
                    startTime: startTime,
                    endTime: endTime,
                    fileName: fileName
            )
            String fileUrl = exportFileServiceFactory.creatFileService("violationRecord").transformExcel(jsonObject)
            approvalExportRecord.url = fileUrl
            approvalExportRecord.save(failOnError: true)
        } catch (Exception e) {
            log.error("生成违纪记录excel出错,message:${e.message}".toString(), e)
        }
    }

    List<Violation> fetchAllViolationByCampus(Long campusId) {
        Violation.findAllByCampusIdAndStatus(campusId, 1 as byte, [sort: 'level', order: 'asc'])
    }
}

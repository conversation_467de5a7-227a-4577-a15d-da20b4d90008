package hiiadmin.approval.repository

import grails.gorm.transactions.Transactional
import timetabling.oa.ApprovalTemplateThemeNew

@Transactional
class RepositoryApprovalTemplateThemeNewService {


    /**
     * 该校区下所有的审批类型
     * @param campusId
     * @return
     */
    def fetchApprovalTemplateThemeByCampusIdAndPromoterTypeAndFormTypeAndName(Long campusId, Byte promoterType, Integer formType, String name) {
        StringBuilder sb = new StringBuilder()
        Map<String, Object> sqlMap = [:]
        sb.append("FROM ApprovalTemplateThemeNew a WHERE a.campusId = :campusId")
        sqlMap.put("campusId", campusId)
        if (promoterType) {
            sb.append(" AND a.promoterType = :promoterType")
            sqlMap.put("promoterType", promoterType)
        }
        if (formType) {
            sb.append(" AND a.formType = :formType")
            sqlMap.put("formType", formType)
        }
        if (name) {
            sb.append(" AND a.name like :name")
            sqlMap.put("name", '%' + name + '%')
        }
        sb.append(" AND a.status != -1")
        String HQL = sb.toString()
        return ApprovalTemplateThemeNew.executeQuery(HQL, sqlMap) as List<ApprovalTemplateThemeNew>
    }


    def saveApprovalTemplateThemeNew(ApprovalTemplateThemeNew approvalTemplateThemeNew) {
        approvalTemplateThemeNew.save(failOnError: true, flush: true)
    }
    
    ApprovalTemplateThemeNew fetchOrCreateDefaultTheme(Long campusId, Byte userType, String themeName) {
        ApprovalTemplateThemeNew.findOrCreateByCampusIdAndPromoterTypeAndNameAndIsDefault(campusId, userType, themeName, 1)
    }

    def getApprovalTemplateTheme(Long id) {
        ApprovalTemplateThemeNew.get(id)
    }

    def fetchApprovalTemplateThemeByCampusIdAndName(Long campusId, String name, Byte userType) {
        ApprovalTemplateThemeNew.findByCampusIdAndNameAndPromoterTypeAndStatusGreaterThan(campusId, name, userType, -1 as byte)
    }

    def fetchApprovalTemplateThemeByLeaveCategoryIdInList(List<Long> leaveCategoryIdList) {
        ApprovalTemplateThemeNew.findAllByLeaveCategoryIdInListAndStatus(leaveCategoryIdList, 1 as byte)
    }

    List<ApprovalTemplateThemeNew> fetchAllApprovalTemplateThemeByGroupId(Long groupId) {
        ApprovalTemplateThemeNew.findAllByGroupIdAndStatus(groupId, 1 as byte)
    }

    List<ApprovalTemplateThemeNew> fetchAllApprovalTemplateThemeByGroupIdNoDelete(Long groupId) {
        ApprovalTemplateThemeNew.findAllByGroupIdAndStatusNotEqual(groupId, -1 as byte)
    }

}

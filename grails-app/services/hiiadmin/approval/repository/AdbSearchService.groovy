package hiiadmin.approval.repository

import adb.AdbApprovalNew
import grails.gorm.transactions.Transactional
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.utils.TimeUtils
import org.apache.commons.lang3.time.DateUtils

import static hiiadmin.ConstantEnum.ApprovalStatus.APPROVALDENY
import static hiiadmin.ConstantEnum.ApprovalStatus.APPROVALED
import static hiiadmin.ConstantEnum.ApprovalStatus.APPROVALING
import static hiiadmin.ConstantEnum.ApprovalStepType.APPROVAL
import static hiiadmin.ConstantEnum.ApprovalStepType.COPY_TO

@Transactional("adb")
class AdbSearchService implements ApprovalSearchService{

    @Override
    Map searchApprovalList(Long campusId, Long userId, Byte userType, String searchValue, Byte approvalStatus, String templateThemeIds, Integer type, Long startDate, Long endDate, int p, int s) {
        StringBuilder searchBuilder = new StringBuilder(" SELECT DISTINCT a.id ")
        Map searchMap = buildHql(searchBuilder, campusId, userId, userType, searchValue, approvalStatus, templateThemeIds, type, startDate, endDate, p, s)
        List<Long> approvalIdList = AdbApprovalNew.executeQuery(searchBuilder.toString(), searchMap) as List<Long>
        StringBuilder countBuilder = new StringBuilder(" SELECT COUNT(DISTINCT a.id ) ")
        Map countMap = buildHql(countBuilder, campusId, userId, userType, searchValue, approvalStatus, templateThemeIds, type, startDate, endDate, 0, 0)
        Long total = AdbApprovalNew.executeQuery(countBuilder.toString(), countMap)[0] as Long
        return [approvalIdList: approvalIdList, total: total]
    }

    def buildHql(StringBuilder stringBuilder, Long campusId, Long userId, Byte userType, String searchValue, Byte approvalStatus, String templateThemeIds, Integer type, Long startDate, Long endDate, int p, int s) {
        stringBuilder.append(""" FROM AdbApprovalNew a , AdbApprovalRecordNew ar 
                                    WHERE a.id = ar.approvalId
                                    AND a.campusId = :campusId """)
        Map map = [:]
        map.put("campusId", campusId)
        if (type != null) {
            switch (type) {
                case 1:
                    stringBuilder.append(""" AND ar.approvalStepUserId = :userId AND ar.userType = :userType AND ar.status = :status""")
                    map.put("status", APPROVALING.status)
                    map.put("userId", userId)
                    map.put("userType", userType)
                    break
                case 2:
                    stringBuilder.append(""" AND ar.approvalStepUserId = :userId AND ar.userType = :userType AND ar.approvalStepType = :approvalStepType 
                                                AND (ar.status = :status1 OR ar.status = :status2)""")
                    map.put("userId", userId)
                    map.put("userType", userType)
                    map.put("approvalStepType", APPROVAL.type)
                    map.put("status1", APPROVALED.status)
                    map.put("status2", APPROVALDENY.status)
                    break
                case 3:
                    stringBuilder.append(""" AND a.promoterId = :promoterId AND a.promoterType = :promoterType """)
                    map.put("promoterId", userId)
                    map.put("promoterType", userType)
                    break
                case 4:
                    stringBuilder.append(""" AND ar.approvalStepUserId = :userId AND ar.userType = :userType AND ar.approvalStepType = :approvalStepType """)
                    map.put("userId", userId)
                    map.put("userType", userType)
                    map.put("approvalStepType", COPY_TO.type)
                    break
            }
        } else {
            stringBuilder.append(""" AND ar.approvalStepUserId = :userId AND ar.userType = :userType  """)
            map.put("userId", userId)
            map.put("userType", userType)
        }

        if (approvalStatus != null) {
            stringBuilder.append(" AND a.status = :approvalStatus ")
            map.put("approvalStatus", approvalStatus)
        }

        if (searchValue) {
            stringBuilder.append(" AND (a.promoterName LIKE :searchValue or a.approvalTemplateThemeName LIKE :searchValue or a.valueJson LIKE :searchValue)")
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (startDate && endDate) {
            stringBuilder.append(" AND a.dateCreated >= :startDate")
            Date date = TimeUtils.getDateStartTime(DateUtils.addDays(new Date(), -6))
            map.put("startDate", date)
        }

        if (templateThemeIds) {
            List<String> themeIdList = templateThemeIds.split(",")
            if (themeIdList.size() > 20) {
                throw new HiiAdminException("关联审批单数量过多，暂时不支持")
            }

            stringBuilder.append(" AND a.approvalTemplateThemeId IN :themeIdList ")
            map.put('themeIdList', themeIdList)
        }

        if (s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        stringBuilder.append(""" ORDER BY a.id DESC """)

        return map
    }
}

package hiiadmin.approval

import com.alibaba.fastjson.JSONObject
import com.google.common.collect.Multimap
import grails.async.Promise
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.CacheService
import hiiadmin.apiCloud.ApiHii
import hiiadmin.approval.repository.RepositoryApprovalRecordService
import hiiadmin.approval.repository.RepositoryApprovalStepNewService
import hiiadmin.approval.repository.RepositoryApprovalTemplateNewService
import hiiadmin.approval.repository.RepositoryApprovalTemplateThemeNewService
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.exceptions.Asserts
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.handler.HandlerFactory
import hiiadmin.module.approval.ApprovalConditionVO
import hiiadmin.module.approval.ApprovalProcessVO
import hiiadmin.module.approval.BaseFormVO
import hiiadmin.module.approval.NodePropsVO
import hiiadmin.module.bugu.ParentVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.SchoolService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.TimeUtils
import hiiadmin.vocationalSchool.FacultyService
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import timetabling.ParentStudent
import timetabling.oa.*
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.SchoolYear
import timetabling.user.Parent
import timetabling.user.Staff
import timetabling.user.Student
import timetabling.user.Teacher

import static grails.async.Promises.task
import static hiiadmin.ConstantEnum.*
import static hiiadmin.ConstantEnum.ApprovalStatus.*
import static hiiadmin.ConstantEnum.ApprovalStepType.*
import static hiiadmin.ConstantEnum.UserTypeEnum.*

@Slf4j
@Transactional
class ApprovalV2Service {

    SchoolService schoolService

    TeacherService teacherService

    ParentService parentService

    StudentService studentService

    ParentStudentService parentStudentService

    CacheService cacheService

    WenYinApprovalRecordService wenYinApprovalRecordService

    @Autowired
    ApiHii apiHii

    DepartmentService departmentService

    UserEncodeInfoService userEncodeInfoService

    FacultyService facultyService

    ApprovalV2DealJsonService approvalV2DealJsonService

    ApprovalUpdateRecordService approvalUpdateRecordService

    @Autowired
    HandlerFactory handlerFactory

    RepositoryApprovalTemplateThemeNewService repositoryApprovalTemplateThemeNewService
    RepositoryApprovalTemplateNewService repositoryApprovalTemplateNewService
    RepositoryApprovalStepNewService repositoryApprovalStepNewService
    RepositoryApprovalRecordService repositoryApprovalRecordService

    BgbCampusRoleService bgbCampusRoleService

    BgbStaffRoleService bgbStaffRoleService

    StaffService staffService


    /**
     * 审批操作
     * @param approvalStepId
     * @param userId
     * @param status
     * @param suggestion
     * @param pics
     * @param autograph
     * @param deliverIds
     * @return
     */
    def updateApproval(Long approvalStepId, Long userId, Byte status, String suggestion, String pics, String autograph,
                       String deliverIds, String ding, String formValueJson, String updateRecordJson, String invoiceJson,
                       String idJson, String baseFormJson, String valueJson) {
        ApprovalStepNew approvalStepNew = repositoryApprovalStepNewService.getApprovalStep(approvalStepId)
        Long approvalId = approvalStepNew.approvalId
        ApprovalNew approvalNew = getApproval(approvalId)

        if (formValueJson) {
            approvalNew.formValueJson = formValueJson
        }

        if (updateRecordJson) {
            approvalUpdateRecordService.updateJson(approvalNew.id, updateRecordJson)
        }

        if (valueJson) {
            approvalNew.valueJson = valueJson
        }

        if (baseFormJson) {
            approvalNew.baseFormJson = baseFormJson
        }

        Campus campus = schoolService.fetchCampusByCampusId(approvalNew.campusId)
        approvalStepNew.suggestion = suggestion
        approvalStepNew.pics = pics
        approvalStepNew.autograph = autograph
        //处理业务
        approvalV2DealJsonService.dealApprovalIdJson(userId, null, campus, approvalNew, idJson, null, null, null, null)
        //处理发票
        approvalV2DealJsonService.dealApprovalInvoiceJson(invoiceJson, userId, null, approvalNew)

        if (status != FAILURE.status) {
            switch (approvalStepNew.userType) {
                case PARENT.type:
                    if (approvalNew.studentId) {
                        List<Long> studentIdList = approvalNew.studentId.split(",").collect { it.toLong() }
                        Long stepStudentId = studentIdList.get(0)
                        //如果只有一个学生
                        if (studentIdList.size() == 1) {
                            Student student = studentService.fetchStudentById(stepStudentId)
                            ParentStudent parentStudent = parentStudentService.fetchParentStudentByParentIdAndStudentId(userId, stepStudentId)
                            if (parentStudent.appellation == AppellationType.OTHER.type) {
                                approvalStepNew.stepInfo = student.name + parentStudent.memo
                            } else {
                                approvalStepNew.stepInfo = student.name + AppellationType.getEnumByType(parentStudent.appellation)?.name
                            }
                        }
                        //如果学生多选不做更改，维持提交时生成的信息
                    } else {
                        Parent parent = parentService.fetchParentById(userId)
                        approvalStepNew.stepInfo = parent?.name
                    }
                    break
                case TEACHER.type:
                    Teacher teacher = teacherService.fetchTeacherById(userId)
                    approvalStepNew.stepInfo = teacher.name
                    break
                case STUDENT.type:
                    Student student = studentService.fetchStudentById(userId)
                    approvalStepNew.stepInfo = student.name
                    break
            }
        }
        List<ApprovalRecordNew> approvalRecordNewList = fetchApprovalRecordByStepId(approvalStepId)
        //找到个人记录
        ApprovalRecordNew approvalRecordNew = approvalRecordNewList.find { it.approvalStepUserId == userId }
        approvalRecordNewList.remove(approvalRecordNew)
        //拒绝
        if (status == APPROVALDENY.status) {
            //个人记录
            approvalRecordNew.status = APPROVALDENY.status
            approvalRecordNew.save(failOnError: true)
            //处理其他未操作人员
            approvalRecordNewList*.status = DELETE.status
            approvalRecordNewList*.save(failOnError: true)
            //当前阶段
            approvalStepNew.status = APPROVALDENY.status
            approvalStepNew.save(failOnError: true)
            //审批本身
            approvalNew.status = APPROVALDENY.status
            approvalNew.save(failOnError: true)
            apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALDENY.status])
            //文印单独处理
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalId)
                wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            //发送审批结果给发起人
            def d1 = task {
                sleep(3000L)
                apiHii.sendApprovalResult2PromoterV2(approvalNew.id, approvalNew.status)
            }
            d1.onComplete {
                log.info("发送审批结果给发起人成功,approvalId:${approvalNew.id}")
            }
            //检查是否有上传发票
            List<Invoice> invoiceList = fetchInvoiceByApprovalId(approvalNew.id)
            if (invoiceList && invoiceList.size() > 0) {
                invoiceList*.status = 0 as byte
                invoiceList*.save(failOnError: true)
            }
            //拒绝的后续业务
            handlerFactory.createApprovalHandler(approvalNew.kitType).onError(null, approvalNew)
        }
        //同意
        else if (status == APPROVALED.status) {
            //个人记录
            approvalRecordNew.status = APPROVALED.status
            //如果是或签
            if (approvalStepNew.approvalMethodType == ApprovalMethodType.OR_SIGN.type) {
                //处理其他未操作人员
                approvalRecordNewList*.status = DELETE.status
                approvalRecordNewList*.save(failOnError: true)
                //当前阶段
                approvalStepNew.status = APPROVALED.status
                approvalStepNew.approvalStepUserIds = userId
                approvalStepNew.save(failOnError: true)
                //如果是最终审批
                if (approvalNew.finalStepId == approvalStepId) {
                    //审批本身
                    approvalNew.status = APPROVALED.status
                    approvalNew.lastUpdated = new Date()
                    approvalNew.save(failOnError: true, frush: true)
                    apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALED.status])
                    //文印单独处理
                    if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                        WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalId)
                        wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
                    }
                    def d2 = task {
                        sleep(3000L)
                        //处理审批通过后的业务
                        apiHii.dealApprovalRelated(approvalNew.id, ding)
                    }
                    d2.onComplete {
                        log.info("处理审批通过后的业务,approvalId:${approvalNew.id}".toString())
                    }
                    //处理审批之后的抄送
                    List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchCopyStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                    if (nextCopyStepList?.size() > 0) {
                        nextCopyStepList*.status = APPROVALED.status
                        nextCopyStepList*.save(failOnError: true)
                        List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                        nextCopyRecordList*.status = APPROVALED.status
                        nextCopyRecordList*.save(failOnError: true)
                        //发送审批之后的抄送消息
                        def s1 = task {
                            sleep(3000L)
                            nextCopyRecordList.each {
                                log.info("审批之后的抄送消息发送,approvalRecordId:${it.id}".toString())
                                apiHii.sendApprovalRecordMessageV2(2, it.id)
                            }
                        }
                        s1.onComplete {
                            log.info("审批之后的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                        }
                    }
                }
                //进入下一个审批
                else {
                    ApprovalStepNew nextApprovalStepNew = repositoryApprovalStepNewService.fetchStepByApprovalIdAndApprovalStepTypeAndIdx(approvalId, APPROVAL.type, approvalStepNew.idx)
                    //如果是多学生审批且是家长审批且没有设置转交人
                    if (approvalNew.studentId?.split(",")?.size() > 1 && nextApprovalStepNew.approvalStepUserType == ApprovalProcessUserType.STUDENT_PARENT.type && !nextApprovalStepNew.batchStudentTransferIds) {
                        dealBatchStudentApprovalStep(approvalNew, nextApprovalStepNew, null, ding, campus)
                    } else {
                        ApprovalRecordNew nextApprovalRecordNew = fetchApprovalRecordByStepIdAndUserId(nextApprovalStepNew.id, approvalNew.promoterId)
                        if (approvalNew.promoterAutoApproval == 1 as byte && nextApprovalRecordNew) {
                            promoterAutoApproval(campus, nextApprovalStepNew, approvalNew, nextApprovalRecordNew, ding)
                        } else {
                            nextApprovalStepNew.status = APPROVALING.status
                            nextApprovalStepNew.save(failOnError: true)
                            List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(nextApprovalStepNew.id)
                            nextApprovalRecordNewList*.status = APPROVALING.status
                            nextApprovalRecordNewList*.save(failOnError: true)
                            //下一个审批阶段发送消息
                            def s2 = task {
                                sleep(3000L)
                                nextApprovalRecordNewList.each {
                                    log.info("下一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(1, it.id)
                                }
                            }
                            s2.onComplete {
                                log.info("下一个审批阶段消息提醒,approvalId:${approvalNew.id},approvalStepId:${nextApprovalStepNew.id}".toString())
                            }
                        }
                        //处理下一个审批之前的抄送
                        List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchNextCopyApprovalStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx, nextApprovalStepNew.idx)
                        if (nextCopyStepList?.size() > 0) {
                            nextCopyStepList*.status = APPROVALED.status
                            nextCopyStepList*.save(failOnError: true)
                            List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                            nextCopyRecordList*.status = APPROVALED.status
                            nextCopyRecordList*.save(failOnError: true)
                            //下一个审批之前的抄送消息
                            def s3 = task {
                                sleep(3000L)
                                nextCopyRecordList.each {
                                    log.info("下一个审批之前的抄送消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                                }
                            }
                            s3.onComplete {
                                log.info("下一个审批之前的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                }
            }
            //会签
            else if (approvalStepNew.approvalMethodType == ApprovalMethodType.JOINTLY_SIGN.type) {
                //如果还有待审批人
                if (approvalRecordNewList?.size() > 0) {
                    //新增该用户操作阶段
                    ApprovalStepNew approvalStepNew1 = new ApprovalStepNew(schoolId: approvalStepNew.schoolId,
                            campusId: approvalStepNew.campusId,
                            approvalTemplateId: approvalStepNew.approvalTemplateId,
                            approvalId: approvalStepNew.approvalId,
                            idx: approvalStepNew.idx,
                            approvalStepUserType: approvalStepNew.approvalStepUserType,
                            approvalStepType: approvalStepNew.approvalStepType,
                            approvalStepUserIds: userId,
                            stepName: approvalStepNew.stepName,
                            status: APPROVALED.status,
                            approvalMethodType: ApprovalMethodType.JOINTLY_SIGN.type,
                            approvalUserNull: approvalStepNew.approvalUserNull,
                            transferIds: approvalStepNew.transferIds,
                            stepInfo: approvalStepNew.stepInfo,
                            suggestion: approvalStepNew.suggestion,
                            pics: approvalStepNew.pics,
                            autograph: approvalStepNew.autograph,
                            userType: approvalStepNew.userType)
                    approvalStepNew1.save(failOnError: true)
                    //原阶段删除已操作人的信息
                    if (approvalStepNew.approvalStepUserIds) {
                        List<String> stepUserIdList = approvalStepNew.approvalStepUserIds?.split(",")
                        stepUserIdList.remove(approvalRecordNew.approvalStepUserId.toString())
                        approvalStepNew.approvalStepUserIds = stepUserIdList.join(",")
                        Long firstUserId = stepUserIdList.get(0).toLong()
                        switch (approvalStepNew.userType) {
                            case TEACHER.type:
                                approvalStepNew.stepInfo = stepUserIdList.size() > 1 ? Teacher.get(firstUserId).name + "等${stepUserIdList.size()}人" : Teacher.get(firstUserId).name
                                break
                            case PARENT.type:
                                approvalStepNew.stepInfo = stepUserIdList.size() > 1 ? Parent.get(firstUserId).name + "等${stepUserIdList.size()}人" : Parent.get(firstUserId).name
                                break
                        }
                    }
                    //原审批记录的stepId转为新stepId
                    approvalRecordNew.approvalStepId = approvalStepNew1.id
                    //处理未进行的步骤
                    List<ApprovalStepNew> futureStepList = repositoryApprovalStepNewService.fetchFutureStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                    futureStepList.each { ApprovalStepNew approvalStep -> approvalStep.idx = approvalStep.idx + 1
                    }
                    futureStepList*.save(failOnError: true)
                    //原阶段往后延
                    approvalStepNew.idx = approvalStepNew.idx + 1
                    approvalStepNew.suggestion = null
                    approvalStepNew.pics = null
                    approvalStepNew.autograph = null
                    approvalStepNew.save(failOnError: true)
                }
                //全部通过进行下一步
                else {
                    approvalStepNew.approvalStepUserIds = userId
                    approvalStepNew.status = APPROVALED.status
                    approvalStepNew.save(failOnError: true)
                    //如果是最终审批
                    if (approvalNew.finalStepId == approvalStepId) {
                        //审批本身
                        approvalNew.status = APPROVALED.status
                        approvalNew.lastUpdated = new Date()
                        approvalNew.save(failOnError: true, frush: true)
                        apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALED.status])
                        //文印单独处理
                        if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                            WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalId)
                            wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
                        }
                        def d3 = task {
                            sleep(3000L)
                            //处理审批通过后的业务
                            apiHii.dealApprovalRelated(approvalNew.id, ding)
                        }
                        d3.onComplete {
                            log.info("处理审批通过后的业务,approvalId:${approvalNew.id}")
                        }
                        //处理审批之后的抄送
                        List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchCopyStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                        if (nextCopyStepList?.size() > 0) {
                            nextCopyStepList*.status = APPROVALED.status
                            nextCopyStepList*.save(failOnError: true)
                            List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                            nextCopyRecordList*.status = APPROVALED.status
                            nextCopyRecordList*.save(failOnError: true)
                            //发送审批之后的抄送消息
                            def s4 = task {
                                sleep(3000L)
                                nextCopyRecordList.each {
                                    log.info("审批之后的抄送消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                                }
                            }
                            s4.onComplete {
                                log.info("审批之后的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                    //进入下一个审批
                    else {
                        ApprovalStepNew nextApprovalStepNew = repositoryApprovalStepNewService.fetchStepByApprovalIdAndApprovalStepTypeAndIdx(approvalId, APPROVAL.type, approvalStepNew.idx)
                        //如果是多学生审批且是家长审批且没有设置转交人
                        if (approvalNew.studentId?.split(",")?.size() > 1 && nextApprovalStepNew.approvalStepUserType == ApprovalProcessUserType.STUDENT_PARENT.type && !nextApprovalStepNew.batchStudentTransferIds) {
                            dealBatchStudentApprovalStep(approvalNew, nextApprovalStepNew, null, ding, campus)
                        } else {
                            ApprovalRecordNew nextApprovalRecordNew = fetchApprovalRecordByStepIdAndUserId(nextApprovalStepNew.id, approvalNew.promoterId)
                            if (approvalNew.promoterAutoApproval == 1 as byte && nextApprovalRecordNew) {
                                promoterAutoApproval(campus, nextApprovalStepNew, approvalNew, nextApprovalRecordNew, ding)
                            } else {
                                nextApprovalStepNew.status = APPROVALING.status
                                nextApprovalStepNew.save(failOnError: true)
                                List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(nextApprovalStepNew.id)
                                nextApprovalRecordNewList*.status = APPROVALING.status
                                nextApprovalRecordNewList*.save(failOnError: true)
                                //下一个审批阶段发送消息
                                def s5 = task {
                                    sleep(3000L)
                                    nextApprovalRecordNewList.each {
                                        log.info("下一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                                        apiHii.sendApprovalRecordMessageV2(1, it.id)
                                    }
                                }
                                s5.onComplete {
                                    log.info("下一个审批阶段消息提醒,approvalId:${approvalNew.id},approvalStepId:${nextApprovalStepNew.id}".toString())
                                }
                            }
                            //处理下一个审批之前的抄送
                            List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchNextCopyApprovalStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx, nextApprovalStepNew.idx)
                            if (nextCopyStepList?.size() > 0) {
                                nextCopyStepList*.status = APPROVALED.status
                                nextCopyStepList*.save(failOnError: true)
                                List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                                nextCopyRecordList*.status = APPROVALED.status
                                nextCopyRecordList*.save(failOnError: true)
                                //下一个审批之前的抄送消息
                                def s6 = task {
                                    sleep(3000L)
                                    nextCopyRecordList.each {
                                        log.info("下一个审批之前的抄送消息发送,approvalRecordId:${it.id}".toString())
                                        apiHii.sendApprovalRecordMessageV2(2, it.id)
                                    }
                                }
                                s6.onComplete {
                                    log.info("下一个审批之前的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                                }
                            }
                        }
                    }
                }
            }
            approvalRecordNew.save(failOnError: true)
        }
        //转交
        else if (status == DELIVER.status) {
            //个人记录
            List<String> stepUserIdList = approvalStepNew.approvalStepUserIds.split(",")
            String deliverId = deliverIds.split(",")[0]
            if (stepUserIdList.contains(deliverId)) {
                throw new HiiAdminException("已重复转交同一用户，请刷新页面")
            }
            Teacher firstDeliver = teacherService.fetchTeacherById(deliverId.toLong())
            //新增该用户操作阶段
            ApprovalStepNew approvalStepNew1 = new ApprovalStepNew(schoolId: approvalStepNew.schoolId,
                    campusId: approvalStepNew.campusId,
                    approvalTemplateId: approvalStepNew.approvalTemplateId,
                    approvalId: approvalStepNew.approvalId,
                    idx: approvalStepNew.idx,
                    approvalStepUserType: approvalStepNew.approvalStepUserType,
                    approvalStepType: approvalStepNew.approvalStepType,
                    approvalStepUserIds: userId,
                    stepName: approvalStepNew.stepName,
                    status: DELIVER.status,
                    approvalMethodType: ApprovalMethodType.JOINTLY_SIGN.type,
                    approvalUserNull: approvalStepNew.approvalUserNull,
                    transferIds: approvalStepNew.transferIds,
                    stepInfo: approvalStepNew.stepInfo,
                    suggestion: approvalStepNew.suggestion,
                    pics: approvalStepNew.pics,
                    autograph: approvalStepNew.autograph,
                    userType: approvalStepNew.userType)
            approvalStepNew1.save(failOnError: true)
            approvalRecordNew.approvalStepId = approvalStepNew1.id
            approvalRecordNew.status = DELIVER.status
            approvalRecordNew.save(failOnError: true)
            //处理未进行的步骤
            List<ApprovalStepNew> futureStepList = repositoryApprovalStepNewService.fetchFutureStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
            futureStepList.each { ApprovalStepNew approvalStep -> approvalStep.idx = approvalStep.idx + 1
            }
            futureStepList*.save(failOnError: true)
            //当前阶段
            stepUserIdList.remove(userId.toString())
            approvalStepNew.idx = approvalStepNew.idx + 1
            approvalStepNew.suggestion = null
            approvalStepNew.pics = null
            approvalStepNew.autograph = null
            approvalStepNew.approvalStepUserIds = stepUserIdList?.size() > 0 ? stepUserIdList.join(",") + "," + deliverIds : deliverIds
            Integer size = approvalStepNew.approvalStepUserIds.split(",").size()
            approvalStepNew.stepInfo = firstDeliver.name + "等${size}人"
            approvalStepNew.save(failOnError: true)
            ApprovalRecordNew newRecord = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew, deliverId.toLong())
            newRecord.status = APPROVALING.status
            newRecord.save(failOnError: true)
            //转交审批阶段消息发送
            def s7 = task {
                sleep(3000L)
                log.info("转交审批阶段消息发送,approvalRecordId:${newRecord.id}".toString())
                apiHii.sendApprovalRecordMessageV2(1, newRecord.id)
            }
            s7.onComplete {
                log.info("转交审批阶段消息提醒,approvalId:${approvalNew.id},newRecord:${newRecord.id}".toString())
            }
        }
        //撤销
        else if (status == FAILURE.status) {
            if (approvalNew.status == APPROVALED.status) {
                throw new HiiAdminException("审批已同意，不可撤销")
            }
            approvalNew.status = FAILURE.status
            approvalNew.save(failOnError: true)
            apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: FAILURE.status])
            //文印单独处理
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalId)
                wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            approvalStepNew.stepName = "撤销审批"
            approvalStepNew.stepInfo = approvalNew.promoterName
            approvalStepNew.status = FAILURE.status
            approvalStepNew.approvalStepUserType = ApprovalUserType.PROMOTER.type
            approvalStepNew.approvalStepType = ApprovalStepType.FAILURE.type
            approvalStepNew.save(failOnError: true)
            List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(approvalStepNew.id)
            nextApprovalRecordNewList*.status = FAILURE.status
            nextApprovalRecordNewList*.save(failOnError: true)
            List<ApprovalStepNew> futureStepList = repositoryApprovalStepNewService.fetchFutureStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
            if (futureStepList && futureStepList.size() > 0) {
                futureStepList*.status = DELETE.status
                futureStepList*.save(failOnError: true)
                List<ApprovalRecordNew> futureRecordList = fetchApprovalRecordByStepIdInList(futureStepList*.id)
                futureRecordList*.status = DELETE.status
                futureRecordList*.save(failOnError: true)
            }
            //检查是否有上传发票
            List<Invoice> invoiceList = fetchInvoiceByApprovalId(approvalNew.id)
            if (invoiceList && invoiceList.size() > 0) {
                invoiceList*.status = 0 as byte
                invoiceList*.save(failOnError: true)
            }
            //撤销的后续业务
            handlerFactory.createApprovalHandler(approvalNew.kitType).onCancelHandel(null, approvalNew)
        }
        approvalNew
    }


    List<Invoice> fetchInvoiceByApprovalId(Long approvalId) {
        Invoice.findAllByApprovalIdAndStatus(approvalId, 1 as byte)
    }

    /**
     * 创建审批2.0
     * @param approvalTemplateThemeId
     * @param valueJson
     * @param userId
     * @param baseFormJson
     * @param approvalTemplateIdList
     * @param promoterChoiceIdList
     * @return
     */
    def createApprovalV2(Long approvalTemplateThemeId, String valueJson, Long userId, String baseFormJson, String templateJson, String idJson, String invoiceJson, String ding, String formValueJson) {
        //获取模板主题
        ApprovalTemplateThemeNew approvalTemplateThemeNew = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(approvalTemplateThemeId)
        Campus campus = schoolService.fetchCampusByCampusId(approvalTemplateThemeNew.campusId)
        Teacher teacher = null
        Parent parent = null
        Student student = null
        Long promoterId = null
        String promoterName = null
        Byte affairStatus = null
        switch (approvalTemplateThemeNew.promoterType) {
            case TEACHER.type:
                teacher = teacherService.fetchTeacherById(userId)
                promoterId = teacher.id
                promoterName = teacher.name
                break
            case PARENT.type:
                parent = parentService.fetchParentById(userId)
                promoterId = parent.id
                promoterName = parent.name
                break
            case STUDENT.type:
                student = studentService.fetchStudentById(userId)
                promoterId = student.id
                promoterName = student.name
                break
        }
        //发起人信息
        ApprovalTemplateNew rootApprovalTemplate = repositoryApprovalTemplateNewService.getApprovalTemplateNew(approvalTemplateThemeNew.rootApprovalTemplateId)
        ApprovalNew approvalNew = new ApprovalNew()
        approvalNew.approvalTemplateThemeId = approvalTemplateThemeId
        approvalNew.approvalTemplateThemeName = approvalTemplateThemeNew.name
        approvalNew.approvalFormId = approvalTemplateThemeNew.formId
        approvalNew.autograph = approvalTemplateThemeNew.autograph
        approvalNew.comments = approvalTemplateThemeNew.comments
        approvalNew.promoterAutoApproval = approvalTemplateThemeNew.promoterAutoApproval
        approvalNew.printTime = approvalTemplateThemeNew.printTime
        approvalNew.campusId = rootApprovalTemplate.campusId
        approvalNew.schoolId = rootApprovalTemplate.schoolId
        switch (approvalTemplateThemeNew.promoterType) {
            case TEACHER.type:
                approvalNew.promoterId = teacher.id
                approvalNew.promoterName = teacher.name
                approvalNew.promoterType = TEACHER.type
                approvalNew.promoterMobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, TEACHER.type, teacher.id)
                approvalNew.promoterAvatar = teacher.avatar
                break
            case PARENT.type:
                parent = parentService.fetchParentById(userId)
                approvalNew.promoterId = parent.id
                approvalNew.promoterName = parent.name
                approvalNew.promoterType = PARENT.type
                approvalNew.promoterMobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, PARENT.type, parent.id)
                approvalNew.promoterAvatar = parent.avatar
                break
            case STUDENT.type:
                approvalNew.promoterId = student.id
                approvalNew.promoterName = student.name
                approvalNew.promoterType = STUDENT.type
                approvalNew.promoterAvatar = student.pic
                break
        }
        //具体审批参数
        approvalNew.valueJson = valueJson
        approvalNew.baseFormJson = baseFormJson
        approvalNew.formValueJson = formValueJson

        WenYinApprovalRecord wenYinApprovalRecord = new WenYinApprovalRecord()

        //处理套件
        approvalV2DealJsonService.dealApprovalIdJson(userId, approvalTemplateThemeNew, campus, approvalNew, idJson, teacher, parent, student, wenYinApprovalRecord)
        String time = TimeUtils.getFormatDate(new Date(), TimeUtils.TIME_FORMAT_MILLIS)
        String num = (int) ((Math.random() * 9 + 1) * 1000)
        while (cacheService.approvalNo.get(time) && cacheService.approvalNo.get(time) == num) {
            num = (int) ((Math.random() * 9 + 1) * 1000)
        }
        cacheService.approvalNo.put(time, num)
        approvalNew.approvalNo = time + num
        approvalNew.status = INIT.status
        approvalNew.kitJson = idJson
        approvalNew.save(failOnError: true)
        Long approvalId = approvalNew.id
        //文印单独处理生成文印记录（用于查询，有多个查询记录页面，目的不在审批表单中冗余多余字段）
        if (approvalNew.kitType) {
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                wenYinApprovalRecord.approvalId = approvalId
                wenYinApprovalRecord.applyTime = approvalNew.dateCreated
                wenYinApprovalRecord = wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            BaseFormVO baseFormVO = null
            if (idJson) {
                baseFormVO = JSONObject.parseObject(idJson, BaseFormVO.class)
            }
            handlerFactory.createApprovalHandler(approvalNew.kitType).onSaveHandel(baseFormVO, approvalNew)
        }

        //发票
        approvalV2DealJsonService.dealApprovalInvoiceJson(invoiceJson, userId, approvalTemplateThemeNew, approvalNew)

        List<ApprovalProcessVO> approvalProcessVOList = JSONObject.parseArray(templateJson, ApprovalProcessVO.class)
        List<Long> approvalTemplateIdList = approvalProcessVOList*.templateId
        //发起阶段
        ApprovalStepNew firstStep = createApprovalStep(approvalId, rootApprovalTemplate, 0, promoterName, approvalNew.promoterType, rootApprovalTemplate.approvalStepUserIds)
        ApprovalRecordNew firstRecord = repositoryApprovalRecordService.createApprovalRecord(approvalId, firstStep, promoterId)
        //该审批使用到的流程
        List<ApprovalTemplateNew> approvalTemplateNewList = repositoryApprovalTemplateNewService.getApprovalTemplateList(approvalTemplateIdList)
        //如果只有发起阶段就直接通过
        if (approvalTemplateNewList.size() == 1) {
            approvalNew.finalStepId = firstStep.id
            approvalNew.status = APPROVALED.status
            approvalNew.lastUpdated = new Date()
            approvalNew.save(failOnError: true, frush: true)

            affairStatus = APPROVALED.status
            //文印单独处理
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            def d1 = task {
                //延迟3s以后执行
                sleep(3000L)
                apiHii.dealApprovalRelated(approvalNew.id, ding)
            }
            d1.onComplete {
                log.info("审批完成后业务处理成功，approvalId:${approvalNew.id}")
            }
            return approvalNew
        }

        //审批流程处理(不包含发起阶段)
        List<ApprovalStepNew> approvalStepNewList = []
        List<ApprovalRecordNew> approvalRecordNewList = []
        for (int i = 0; i < approvalTemplateNewList.size(); i++) {
            ApprovalTemplateNew approvalTemplateNew = approvalTemplateNewList.get(i)
            //发起阶段跳过
            if (approvalTemplateNew.approvalStepType == ApprovalTemplateStepType.PROMOTE.type) {
                continue
            }
            ApprovalStepNew approvalStepNew1 = null
            String stepInfo = null
            String stepUserId = null
            switch (approvalTemplateNew.approvalStepUserType) {
            //指定人员
                case ApprovalProcessUserType.PERSON.type:
                    approvalStepNew1 = transformPerson(approvalTemplateNew, approvalId, i, approvalRecordNewList, approvalStepNew1)
                    break
                    //发起人自己
                case ApprovalProcessUserType.PROMOTER.type:
                    approvalStepNew1 = transformPromoter(approvalTemplateThemeNew, teacher, parent, student, approvalId, approvalTemplateNew, i, stepInfo, approvalNew, stepUserId, promoterId, approvalRecordNewList, approvalStepNew1)
                    break
                    //所选学生家长
                case ApprovalProcessUserType.STUDENT_PARENT.type:
                    approvalStepNew1 = transformStudentParent(approvalNew, student, approvalId, approvalTemplateNew, i, approvalRecordNewList, approvalStepNew1)
                    break
                    //部门主管
                case ApprovalProcessUserType.HEAD_OF_DEPARTMENT.type:
                    approvalStepNew1 = transformHeadOfDept(campus, approvalTemplateThemeNew, userId, approvalId, approvalTemplateNew, i, approvalRecordNewList, approvalStepNew1)
                    break
                    //发起人自选
                case ApprovalProcessUserType.PROMOTER_CHOOSE.type:
                    approvalStepNew1 = transformPromoterChoose(approvalProcessVOList, approvalTemplateNew, approvalId, i, approvalRecordNewList, approvalStepNew1)
                    break
                    //所选学生班主任
                case ApprovalProcessUserType.CLASS_MASTER.type:
                    approvalStepNew1 = transformClassMaster(approvalNew, approvalId, approvalTemplateNew, i, approvalRecordNewList, approvalStepNew1)
                    break
                    //                指定部门
                case ApprovalProcessUserType.DEPARTMENT.type:
                    approvalStepNew1 = transformDepartment(approvalTemplateNew, campus, approvalId, i, approvalRecordNewList, approvalStepNew1)

                    break
//                指定角色
                case ApprovalProcessUserType.ROLE.type:
                    approvalStepNew1 = transformRole(approvalTemplateNew, approvalId, i, approvalRecordNewList, approvalStepNew1)
                    break
            }
            if (approvalStepNew1.approvalStepType == APPROVAL.type) {
                approvalNew.finalStepId = approvalStepNew1.id
            }
            approvalStepNewList.add(approvalStepNew1)
        }

        //有审批阶段，处理审批阶段
        List<ApprovalStepNew> approvalApprovalStepList = approvalStepNewList.findAll {
            it.approvalStepType == APPROVAL.type
        }
        if (approvalApprovalStepList && approvalApprovalStepList.size() > 0) {
            approvalNew.status = APPROVALING.status
            //文印单独处理
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            approvalApprovalStepList.sort { it.idx }
            //处理第一个审批阶段
            ApprovalStepNew firstApprovalStep = approvalApprovalStepList.get(0)
            if (firstApprovalStep) {
                //如果是多学生审批且是家长审批且没有设置转交人
                if (approvalNew.studentId?.split(",")?.size() > 1 && firstApprovalStep.approvalStepUserType == ApprovalProcessUserType.STUDENT_PARENT.type && !firstApprovalStep.batchStudentTransferIds) {
                    dealBatchStudentApprovalStep(approvalNew, firstApprovalStep, wenYinApprovalRecord, ding, campus)
                } else {
                    //如果审批人是发起人
                    ApprovalRecordNew approvalRecordNew = fetchApprovalRecordByStepIdAndUserId(firstApprovalStep.id, promoterId)
                    if (approvalNew.promoterAutoApproval == 1 as byte && approvalRecordNew) {
                        promoterAutoApproval(campus, firstApprovalStep, approvalNew, approvalRecordNew, ding)
                    } else {
                        firstApprovalStep.status = APPROVALING.status
                        firstApprovalStep.save(failOnError: true)
                        List<ApprovalRecordNew> firstApprovalRecordList = approvalRecordNewList.findAll {
                            it.approvalStepId == firstApprovalStep.id
                        }
                        if (firstApprovalRecordList && firstApprovalRecordList.size() > 0) {
                            firstApprovalRecordList*.status = APPROVALING.status
                            firstApprovalRecordList*.save(failOnError: true)
                            //第一个审批阶段消息发送
                            def s1 = task {
                                sleep(3000L)
                                firstApprovalRecordList.each {
                                    log.info("第一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(1, it.id)
                                }
                            }
                            s1.onComplete {
                                log.info("第一个审批阶段消息提醒,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                }
            }
            //处理第一个审批阶段前的抄送
            List<ApprovalStepNew> firstCopyStepList = approvalStepNewList.findAll {
                it.idx < firstApprovalStep.idx && it.approvalStepType == COPY_TO.type
            }
            if (firstCopyStepList && firstCopyStepList.size() > 0) {
                firstCopyStepList*.status = APPROVALED.status
                firstCopyStepList*.save(failOnError: true)
                List<ApprovalRecordNew> approvalCopyRecordList = approvalRecordNewList.findAll {
                    it.approvalStepId in firstCopyStepList*.id
                }
                if (approvalCopyRecordList && approvalCopyRecordList.size() > 0) {
                    approvalCopyRecordList*.status = APPROVALED.status
                    approvalCopyRecordList*.save(failOnError: true)
                    //第一个审批阶段前的抄送消息发送
                    def s2 = task {
                        sleep(3000L)
                        approvalCopyRecordList.each {
                            log.info("第一个审批阶段前的抄送消息发送,approvalRecordId:${it.id}".toString())
                            apiHii.sendApprovalRecordMessageV2(2, it.id)
                        }
                    }
                    s2.onComplete {
                        log.info("第一个审批阶段前的抄送消息发送,approvalId:${approvalNew.id}".toString())
                    }
                }

            }
            //保存最后一步审批
            if (approvalNew.finalStepId) {
                approvalNew.save(failOnError: true)
            }
        }
        //没有审批阶段，处理抄送阶段
        else {
            List<ApprovalStepNew> approvalCopyStepList = approvalStepNewList.findAll {
                it.approvalStepType == COPY_TO.type
            }
            approvalCopyStepList*.status = APPROVALED.status
            approvalCopyStepList*.save(failOnError: true)
            approvalRecordNewList*.status = APPROVALED.status
            approvalRecordNewList*.save(failOnError: true)
            approvalNew.status = APPROVALED.status
            approvalNew.lastUpdated = new Date()
            approvalNew.save(failOnError: true, frush: true)
            affairStatus = APPROVALED.status
            //处理无审批阶段的所有抄送
            def s3 = task {
                sleep(3000L)
                approvalRecordNewList.each {
                    log.info("处理无审批阶段的所有抄送,approvalRecordId:${it.id}".toString())
                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                }
            }
            s3.onComplete {
                log.info("处理无审批阶段的所有抄送,approvalId:${approvalNew.id}".toString())
            }
            if (approvalNew.status == APPROVALED.status) {
                //文印单独处理
                if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                    wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
                }
                def d2 = task {
                    //延迟3s以后执行
                    sleep(3000L)
                    apiHii.dealApprovalRelated(approvalNew.id, ding)
                }
                d2.onComplete {
                    log.info("审批完成后业务处理成功，approvalId:${approvalNew.id}")
                }
            }
        }

        // 发起阶段通过直接添加通过
        apiHii.createAffairProcess([campusId       : approvalNew.campusId,
                                    approvalThemeId: approvalNew.approvalTemplateThemeId,
                                    approvalId     : approvalNew.id,
                                    userId         : userId,
                                    userType       : approvalNew.promoterType,
                                    status         : affairStatus])

        approvalNew
    }

    private ApprovalStepNew transformRole(ApprovalTemplateNew approvalTemplateNew, long approvalId, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        String objIds = approvalTemplateNew.approvalStepUserIds
        Set<Long> teacherIdSet = []
        List<Long> roleIdList = null
        if (objIds == "all") {
            roleIdList = bgbCampusRoleService.fetchCampusRoleByCampusId(approvalTemplateNew.campusId).collect { it.id }
        } else if (objIds) {
            roleIdList = objIds.split(",").collect { it.toLong() }
        }
        List<Long> staffIdList = bgbStaffRoleService.transformBgbStaffRoleIdList(roleIdList)
        if (staffIdList) {
            List<Staff> staffList = staffService.fetchStaffByIdList(staffIdList)
            staffList.each {
                teacherIdSet.add(it.teacherId)
            }
        }
        if (teacherIdSet.isEmpty()) {
            if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
                List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
                List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
                stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
                approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
                transferList.each { Teacher teacher1 ->
                    ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                    approvalRecordNewList.add(approvalRecordNew)
                }
            } else {
                Asserts.fail("无相关人员，无法提交")
            }
        } else {
            List<Long> teacherIdList = teacherIdSet.toList()
            List<Teacher> teacherList = teacherService.fetchAllTeacherByIdInList(teacherIdList)
            stepInfo = teacherList.size() > 1 ? teacherList.get(0).name + "等${teacherList.size()}人" : teacherList.get(0).name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, teacherIdList.join(","))
            teacherList.each { Teacher teacher1 ->
                ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                approvalRecordNewList.add(approvalRecordNew)
            }
        }
        return approvalStepNew1
    }

    private ApprovalStepNew transformDepartment(ApprovalTemplateNew approvalTemplateNew, Campus campus, long approvalId, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        Set<Long> teacherIdSet = []
        String objIds = approvalTemplateNew.approvalStepUserIds
        if (campus.type == CampusType.VOCATIONAL.type) {
            if (objIds == "all") {
                facultyService.transformAllTeacherIdBCampusId(approvalTemplateNew.campusId).each { teacherIdSet.add(it) }
            } else if (objIds) {
                List<Long> deptIds = objIds.split(",").collect { it.toLong() }
                facultyService.fetchAllUserByFacultyIds(approvalTemplateNew.campusId, deptIds, TEACHER.type)?.each { teacherIdSet.addAll(it.userId) }
            }
        } else {
            Multimap<Long, Long> multimap = departmentService.transformDepartmentIdStaffDepartment(approvalTemplateNew.campusId)
            if (objIds == "all") {
                multimap.values().each {
                    teacherIdSet.addAll(it)
                }
            } else if (objIds) {
                objIds.split(",").each {
                    if (multimap.containsKey(it.toLong())) {
                        teacherIdSet.addAll(multimap.get(it.toLong()))
                    }
                }
            }
        }
        if (teacherIdSet.isEmpty()) {
            if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
                List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
                List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
                stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
                approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
                transferList.each { Teacher teacher1 ->
                    ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                    approvalRecordNewList.add(approvalRecordNew)
                }
            } else {
                Asserts.fail("无相关人员，无法提交")
            }
        } else {
            List<Long> teacherIdList = teacherIdSet.toList()
            List<Teacher> teacherList = teacherService.fetchAllTeacherByIdInList(teacherIdList)
            stepInfo = teacherList.size() > 1 ? teacherList.get(0).name + "等${teacherList.size()}人" : teacherList.get(0).name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, teacherIdList.join(","))
            teacherList.each { Teacher teacher1 ->
                ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                approvalRecordNewList.add(approvalRecordNew)
            }
        }
        return approvalStepNew1
    }


    private ApprovalStepNew transformClassMaster(ApprovalNew approvalNew, long approvalId, ApprovalTemplateNew approvalTemplateNew, int i, ArrayList<ApprovalRecordNew> approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        Long stepStudentId = approvalNew.studentId.split(",")?.collect { it.toLong() }?.get(0)
        Teacher classMaster = teacherService.fetchHeadmasterTeacherByStudentId(stepStudentId)
        if (classMaster) {
            stepInfo = classMaster.name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, classMaster.id.toString())
            ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, classMaster.id)
            approvalRecordNewList.add(approvalRecordNew)
        } else if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
            List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
            List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
            stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
            transferList.each { Teacher teacher1 ->
                ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                approvalRecordNewList.add(approvalRecordNew)
            }
        } else {
            throw new HiiAdminException("无相关人员，无法提交")
        }
        return approvalStepNew1
    }

    private ApprovalStepNew transformPromoterChoose(List<ApprovalProcessVO> approvalProcessVOList, ApprovalTemplateNew approvalTemplateNew, long approvalId, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        ApprovalProcessVO approvalProcessVO = approvalProcessVOList.find {
            it.templateId == approvalTemplateNew.id
        }
        List<Long> promoterChoiceIdList = approvalProcessVO.choiceIds.split(",").collect { it.toLong() }
        List<Teacher> promoterChooseList = teacherService.fetchAllTeacherByIdInList(promoterChoiceIdList)
        stepInfo = promoterChooseList.size() > 1 ? promoterChooseList.get(0).name + "等${promoterChooseList.size()}人" : promoterChooseList.get(0).name
        approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, approvalProcessVO.choiceIds)
        promoterChooseList.each { Teacher teacher1 ->
            ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
            approvalRecordNewList.add(approvalRecordNew)
        }
        return approvalStepNew1
    }

    private ApprovalStepNew transformHeadOfDept(Campus campus, ApprovalTemplateThemeNew approvalTemplateThemeNew, long userId, long approvalId, ApprovalTemplateNew approvalTemplateNew, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        List<Long> supervisorIdList = []
        if (campus.type == CampusType.VOCATIONAL.type) {
            supervisorIdList = facultyService.fetchSupervisorIdByCampusIdAndTeacherId(approvalTemplateThemeNew.campusId, userId)
        } else {
            supervisorIdList = departmentService.findDepartmentSupervisorByTeacherId(approvalTemplateThemeNew.campusId, userId)
        }
        if (supervisorIdList && supervisorIdList.size() > 0) {
            List<Teacher> teacherList = teacherService.fetchAllTeacherByIdInList(supervisorIdList)
            stepInfo = teacherList.size() > 1 ? teacherList.get(0).name + "等${teacherList.size()}人" : teacherList.get(0).name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, supervisorIdList.join(","))
            teacherList.each { Teacher teacher1 ->
                ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                approvalRecordNewList.add(approvalRecordNew)
            }
        } else if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
            List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
            List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
            stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
            approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
            transferList.each { Teacher teacher1 ->
                ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                approvalRecordNewList.add(approvalRecordNew)
            }
        } else {
            throw new HiiAdminException("无相关人员，无法提交")
        }
        return approvalStepNew1
    }

    private ApprovalStepNew transformStudentParent(ApprovalNew approvalNew, Student student, long approvalId, ApprovalTemplateNew approvalTemplateNew, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        List<Long> studentIdList = approvalNew.studentId.split(",")?.collect { it.toLong() }
        Long stepStudentId = studentIdList.get(0)
        //如果是单个学生
        if (studentIdList?.size() == 1) {
            if (!student) {
                student = studentService.fetchStudentById(stepStudentId)
            }
            List<ParentVO> parentVOList = parentService.fetchAllParentByStudentId(stepStudentId)
            if (parentVOList && parentVOList.size() > 0) {
                stepInfo = parentVOList.size() > 1 ? student.name + parentVOList.get(0).appellationName + "等${parentVOList.size()}人" : student.name + parentVOList.get(0).appellationName
                approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, PARENT.type, parentVOList*.id.join(","))
                parentVOList.each { ParentVO parentVO ->
                    ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, parentVO.id)
                    approvalRecordNewList.add(approvalRecordNew)
                }
            } else if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
                List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
                List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
                stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
                approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
                transferList.each { Teacher teacher1 ->
                    ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                    approvalRecordNewList.add(approvalRecordNew)
                }
            } else {
                throw new HiiAdminException("无相关人员，无法提交")
            }
        }
        //如果是多个学生
        else {
            //如果是审批
            if (approvalTemplateNew.approvalStepType == ApprovalTemplateStepType.APPROVAL.type) {
                //如果选择了自动转交人
                if (approvalTemplateNew.batchStudentTransferIds) {
                    List<Long> transferIdList = approvalTemplateNew.batchStudentTransferIds.split(",").collect { it.toLong() }
                    List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
                    stepInfo = "多个学生，自动转交给" + (transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name)
                    approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
                    transferList.each { Teacher teacher1 ->
                        ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                        approvalRecordNewList.add(approvalRecordNew)
                    }
                } else {
                    stepInfo = "多个学生，自动通过"
                    approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, PARENT.type, null)
                }
            }
            //如果是抄送
            else if (approvalTemplateNew.approvalStepType == ApprovalTemplateStepType.COPY_TO.type) {
                //如果选择了抄送全部家长
                if (approvalTemplateNew.copyToParent == 1 as byte) {
                    List<ParentVO> parentVOList = parentService.fetchAllParentByStudentIdList(studentIdList)
                    if (parentVOList && parentVOList.size() > 0) {
                        stepInfo = "多个学生，抄送全部家长"
                        approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, PARENT.type, parentVOList*.id.join(","))
                        parentVOList.each { ParentVO parentVO ->
                            ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, parentVO.id)
                            approvalRecordNewList.add(approvalRecordNew)
                        }
                    } else if (approvalTemplateNew.approvalUserNull == ApprovalUserNullType.AUTOMATIC_TRANSFER.type && approvalTemplateNew.transferIds) {
                        List<Long> transferIdList = approvalTemplateNew.transferIds.split(",").collect { it.toLong() }
                        List<Teacher> transferList = teacherService.fetchAllTeacherByIdInList(transferIdList)
                        stepInfo = transferList.size() > 1 ? transferList.get(0).name + "等${transferList.size()}人" : transferList.get(0).name
                        approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, null)
                        transferList.each { Teacher teacher1 ->
                            ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, teacher1.id)
                            approvalRecordNewList.add(approvalRecordNew)
                        }
                    } else {
                        throw new HiiAdminException("无相关人员，无法提交")
                    }
                }
                //如果选择了不抄送
                else {
                    stepInfo = "多个学生，不抄送家长"
                    approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, PARENT.type, null)
                }
            }
        }
        return approvalStepNew1
    }

    private ApprovalStepNew transformPromoter(ApprovalTemplateThemeNew approvalTemplateThemeNew, Teacher teacher, Parent parent, Student student, long approvalId, ApprovalTemplateNew approvalTemplateNew, int i, String stepInfo, ApprovalNew approvalNew, String stepUserId, long promoterId, ArrayList<ApprovalRecordNew> approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        switch (approvalTemplateThemeNew.promoterType) {
            case TEACHER.type:
                stepInfo = teacher.name
                stepUserId = teacher.id
                break
            case PARENT.type:
                stepInfo = parent.name
                stepUserId = parent.id
                break
            case STUDENT.type:
                stepInfo = student.name
                stepUserId = student.id
                break
        }
        approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, approvalNew.promoterType, stepUserId)
        ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, promoterId)
        approvalRecordNewList.add(approvalRecordNew)
        return approvalStepNew1
    }

    private ApprovalStepNew transformPerson(ApprovalTemplateNew approvalTemplateNew, long approvalId, int i, approvalRecordNewList, ApprovalStepNew approvalStepNew1) {
        String stepInfo
        List<Long> userIdList = approvalTemplateNew.approvalStepUserIds.split(",").collect { it.toLong() }
        Teacher firstTeacher = teacherService.fetchTeacherById(userIdList?.get(0))
        stepInfo = (userIdList.size() > 1 ? firstTeacher.name + "等${userIdList.size()}人" : firstTeacher.name)
        //审批步骤
        approvalStepNew1 = createApprovalStep(approvalId, approvalTemplateNew, i, stepInfo, TEACHER.type, approvalTemplateNew.approvalStepUserIds)
        //个人审批记录
        userIdList.each { Long approvalStepUserId ->
            ApprovalRecordNew approvalRecordNew = repositoryApprovalRecordService.createApprovalRecord(approvalId, approvalStepNew1, approvalStepUserId)
            approvalRecordNewList.add(approvalRecordNew)
        }
        return approvalStepNew1
    }

    /**
     * 自动通过审批人为发起人的流程
     * @param approvalId
     * @param approvalStepNew
     * @return
     */
    def promoterAutoApproval(Campus campus, ApprovalStepNew approvalStepNew, ApprovalNew approvalNew, ApprovalRecordNew approvalRecordNew, String ding) {
        Long approvalStepId = approvalStepNew.id
        Long approvalId = approvalNew.id
        List<ApprovalRecordNew> approvalRecordNewList = fetchApprovalRecordByStepId(approvalStepId)
        if (approvalRecordNew) {
            Long userId = approvalNew.promoterId
            switch (approvalStepNew.userType) {
                case PARENT.type:
                    Student student = studentService.fetchStudentById(approvalNew.studentId.toLong())
                    ParentStudent parentStudent = parentStudentService.fetchParentStudentByParentIdAndStudentId(userId, approvalNew.studentId.toLong())
                    if (parentStudent.appellation == AppellationType.OTHER.type) {
                        approvalStepNew.stepInfo = student.name + parentStudent.memo
                    } else {
                        approvalStepNew.stepInfo = student.name + AppellationType.getEnumByType(parentStudent.appellation)?.name
                    }
                    break
                case TEACHER.type:
                    Teacher teacher = teacherService.fetchTeacherById(userId)
                    approvalStepNew.stepInfo = teacher.name
                    break
                case STUDENT.type:
                    Student student = studentService.fetchStudentById(userId)
                    approvalStepNew.stepInfo = student.name
                    break
            }
            approvalStepNew.autoApproval = 1 as byte
            approvalRecordNewList.remove(approvalRecordNew)
            //个人记录
            approvalRecordNew.status = APPROVALED.status
            //如果是或签
            if (approvalStepNew.approvalMethodType == ApprovalMethodType.OR_SIGN.type) {
                //处理其他未操作人员
                approvalRecordNewList*.status = DELETE.status
                approvalRecordNewList*.save(failOnError: true)
                //当前阶段
                approvalStepNew.status = APPROVALED.status
                approvalStepNew.approvalStepUserIds = userId
                approvalStepNew.save(failOnError: true)
                //如果是最终审批
                if (approvalNew.finalStepId == approvalStepId) {
                    //审批本身
                    approvalNew.status = APPROVALED.status
                    approvalNew.lastUpdated = new Date()
                    approvalNew.save(failOnError: true, frush: true)
                    apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALED.status])
                    //处理审批通过后的业务
                    def d3 = task {
                        //延迟3s以后执行
                        sleep(3000L)
                        apiHii.dealApprovalRelated(approvalNew.id, ding)
                    }
                    d3.onComplete {
                        log.info("审批完成后业务处理成功，approvalId:${approvalNew.id}")
                    }
                    //处理审批之后的抄送
                    List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchCopyStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                    if (nextCopyStepList?.size() > 0) {
                        nextCopyStepList*.status = APPROVALED.status
                        nextCopyStepList*.save(failOnError: true)
                        List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                        nextCopyRecordList*.status = APPROVALED.status
                        nextCopyRecordList*.save(failOnError: true)
                        //发送审批之后的抄送消息
                        def t1 = task {
                            sleep(3000L)
                            nextCopyRecordList.each {
                                log.info("审批之后的抄送消息发送,approvalRecordId:${it.id}".toString())
                                apiHii.sendApprovalRecordMessageV2(2, it.id)
                            }
                        }
                        t1.onComplete {
                            log.info("审批之后的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                        }
                    }
                }
                //进入下一个审批
                else {
                    ApprovalStepNew nextApprovalStepNew = repositoryApprovalStepNewService.fetchStepByApprovalIdAndApprovalStepTypeAndIdx(approvalId, APPROVAL.type, approvalStepNew.idx)
                    //如果是多学生审批且是家长审批且没有设置转交人
                    if (approvalNew.studentId?.split(",")?.size() > 1 && nextApprovalStepNew.approvalStepUserType == PARENT.type && !nextApprovalStepNew.batchStudentTransferIds) {
                        dealBatchStudentApprovalStep(approvalNew, nextApprovalStepNew, null, ding, campus)
                    } else {
                        ApprovalRecordNew nextApprovalRecordNew = fetchApprovalRecordByStepIdAndUserId(nextApprovalStepNew.id, approvalNew.promoterId)
                        //如果审批人是发起人
                        if (approvalNew.promoterAutoApproval == 1 as byte && nextApprovalRecordNew) {
                            promoterAutoApproval(campus, nextApprovalStepNew, approvalNew, nextApprovalRecordNew, ding)
                        } else {
                            nextApprovalStepNew.status = APPROVALING.status
                            nextApprovalStepNew.save(failOnError: true)
                            List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(nextApprovalStepNew.id)
                            nextApprovalRecordNewList*.status = APPROVALING.status
                            nextApprovalRecordNewList*.save(failOnError: true)
                            //下一个审批阶段发送消息
                            def t2 = task {
                                sleep(3000L)
                                nextApprovalRecordNewList.each {
                                    log.info("下一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(1, it.id)
                                }
                            }
                            t2.onComplete {
                                log.info("下一个审批阶段消息提醒,approvalId:${approvalNew.id},approvalStepId:${nextApprovalStepNew.id}".toString())
                            }
                        }
                        //处理下一个审批之前的抄送
                        List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchNextCopyApprovalStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx, nextApprovalStepNew.idx)
                        if (nextCopyStepList?.size() > 0) {
                            nextCopyStepList*.status = APPROVALED.status
                            nextCopyStepList*.save(failOnError: true)
                            List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                            nextCopyRecordList*.status = APPROVALED.status
                            nextCopyRecordList*.save(failOnError: true)
                            //下一个审批之前的抄送消息
                            def t3 = task {
                                sleep(3000L)
                                nextCopyRecordList.each {
                                    log.info("下一个审批之前的抄送消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                                }
                            }
                            t3.onComplete {
                                log.info("下一个审批之前的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                }
            }
            //会签
            else if (approvalStepNew.approvalMethodType == ApprovalMethodType.JOINTLY_SIGN.type) {
                //如果还有待审批人
                if (approvalRecordNewList?.size() > 0) {
                    //新增该用户操作阶段
                    ApprovalStepNew approvalStepNew1 = new ApprovalStepNew(schoolId: approvalStepNew.schoolId,
                            campusId: approvalStepNew.campusId,
                            approvalTemplateId: approvalStepNew.approvalTemplateId,
                            approvalId: approvalStepNew.approvalId,
                            idx: approvalStepNew.idx,
                            approvalStepUserType: approvalStepNew.approvalStepUserType,
                            approvalStepType: approvalStepNew.approvalStepType,
                            approvalStepUserIds: userId,
                            stepName: approvalStepNew.stepName,
                            status: APPROVALED.status,
                            approvalMethodType: ApprovalMethodType.OR_SIGN.type,
                            approvalUserNull: approvalStepNew.approvalUserNull,
                            transferIds: approvalStepNew.transferIds,
                            stepInfo: approvalStepNew.stepInfo,
                            suggestion: approvalStepNew.suggestion,
                            pics: approvalStepNew.pics,
                            autograph: approvalStepNew.autograph,
                            autoApproval: approvalStepNew.autoApproval,
                            userType: approvalStepNew.userType
                    )
                    approvalStepNew1.save(failOnError: true)
                    //原阶段删除已操作人的信息
                    if (approvalStepNew.approvalStepUserIds) {
                        List<String> stepUserIdList = approvalStepNew.approvalStepUserIds?.split(",")
                        stepUserIdList.remove(approvalRecordNew.approvalStepUserId.toString())
                        approvalStepNew.approvalStepUserIds = stepUserIdList.join(",")
                        Long firstUserId = stepUserIdList.get(0).toLong()
                        switch (approvalStepNew.userType) {
                            case TEACHER.type:
                                approvalStepNew.stepInfo = stepUserIdList.size() > 1 ? Teacher.get(firstUserId).name + "等${stepUserIdList.size()}人" : Teacher.get(firstUserId).name
                                break
                            case PARENT.type:
                                approvalStepNew.stepInfo = stepUserIdList.size() > 1 ? Parent.get(firstUserId).name + "等${stepUserIdList.size()}人" : Parent.get(firstUserId).name
                                break
                        }
                    }
                    //原审批记录的stepId转为新stepId
                    approvalRecordNew.approvalStepId = approvalStepNew1.id
                    //处理未进行的步骤
                    List<ApprovalStepNew> futureStepList = repositoryApprovalStepNewService.fetchFutureStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                    futureStepList.each { ApprovalStepNew approvalStep -> approvalStep.idx = approvalStep.idx + 1
                    }
                    futureStepList*.save(failOnError: true)
                    //原阶段往后延
                    approvalStepNew.idx = approvalStepNew.idx + 1
                    approvalStepNew.suggestion = null
                    approvalStepNew.pics = null
                    approvalStepNew.autograph = null
                    approvalStepNew.status = APPROVALING.status
                    approvalStepNew.save(failOnError: true)
                    approvalRecordNewList*.status = APPROVALING.status
                    approvalRecordNewList*.save(failOnError: true)

                }
                //全部通过进行下一步
                else {
                    approvalStepNew.approvalStepUserIds = userId
                    approvalStepNew.status = APPROVALED.status
                    approvalStepNew.save(failOnError: true)
                    //如果是最终审批
                    if (approvalNew.finalStepId == approvalStepId) {
                        //审批本身
                        approvalNew.status = APPROVALED.status
                        approvalNew.lastUpdated = new Date()
                        approvalNew.save(failOnError: true, frush: true)
                        apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALED.status])
                        def d4 = task {
                            sleep(3000L)
                            //处理审批通过后的业务
                            apiHii.dealApprovalRelated(approvalNew.id, ding)
                        }
                        d4.onComplete {
                            log.info("处理审批通过后的业务,approvalId:${approvalNew.id}")
                        }
                        //处理审批之后的抄送
                        List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchCopyStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
                        if (nextCopyStepList?.size() > 0) {
                            nextCopyStepList*.status = APPROVALED.status
                            nextCopyStepList*.save(failOnError: true)
                            List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                            nextCopyRecordList*.status = APPROVALED.status
                            nextCopyRecordList*.save(failOnError: true)
                            //发送审批之后的抄送消息
                            def t4 = task {
                                sleep(3000L)
                                nextCopyRecordList.each {
                                    log.info("审批之后的抄送消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                                }
                            }
                            t4.onComplete {
                                log.info("审批之后的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                    //进入下一个审批
                    else {
                        ApprovalStepNew nextApprovalStepNew = repositoryApprovalStepNewService.fetchStepByApprovalIdAndApprovalStepTypeAndIdx(approvalId, APPROVAL.type, approvalStepNew.idx)
                        ApprovalRecordNew nextApprovalRecordNew = fetchApprovalRecordByStepIdAndUserId(nextApprovalStepNew.id, approvalNew.promoterId)
                        if (approvalNew.promoterAutoApproval == 1 as byte && nextApprovalRecordNew) {
                            promoterAutoApproval(campus, nextApprovalStepNew, approvalNew, nextApprovalRecordNew, ding)
                        } else {
                            nextApprovalStepNew.status = APPROVALING.status
                            nextApprovalStepNew.save(failOnError: true)
                            List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(nextApprovalStepNew.id)
                            nextApprovalRecordNewList*.status = APPROVALING.status
                            nextApprovalRecordNewList*.save(failOnError: true)
                            //下一个审批阶段发送消息
                            def t5 = task {
                                sleep(3000L)
                                nextApprovalRecordNewList.each {
                                    log.info("下一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(1, it.id)
                                }
                            }
                            t5.onComplete {
                                log.info("下一个审批阶段消息提醒,approvalId:${approvalNew.id},approvalStepId:${nextApprovalStepNew.id}".toString())
                            }
                        }
                        //处理下一个审批之前的抄送
                        List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchNextCopyApprovalStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx, nextApprovalStepNew.idx)
                        if (nextCopyStepList?.size() > 0) {
                            nextCopyStepList*.status = APPROVALED.status
                            nextCopyStepList*.save(failOnError: true)
                            List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                            nextCopyRecordList*.status = APPROVALED.status
                            nextCopyRecordList*.save(failOnError: true)
                            //下一个审批之前的抄送消息
                            def t6 = task {
                                sleep(3000L)
                                nextCopyRecordList.each {
                                    log.info("下一个审批之前的抄送消息发送,approvalRecordId:${it.id}".toString())
                                    apiHii.sendApprovalRecordMessageV2(2, it.id)
                                }
                            }
                            t6.onComplete {
                                log.info("下一个审批之前的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                            }
                        }
                    }
                }
            }
            approvalRecordNew.save(failOnError: true)
        }
    }

    /**
     * 处理未设置转交人的多学生审批
     * @param approvalNew
     * @param approvalStepNew
     * @param wenYinApprovalRecord
     * @param wxAppId
     * @param ding
     * @param campus
     * @return
     */
    def dealBatchStudentApprovalStep(ApprovalNew approvalNew, ApprovalStepNew approvalStepNew, WenYinApprovalRecord wenYinApprovalRecord, String ding, Campus campus) {
        Long approvalId = approvalNew.id
        approvalStepNew.status = APPROVALED.status
        approvalStepNew.autoApproval = 1 as byte
        approvalStepNew.save(failOnError: true)
        //如果是最终审批
        if (approvalNew.finalStepId == approvalStepNew.id) {
            //审批本身
            approvalNew.status = APPROVALED.status
            approvalNew.lastUpdated = new Date()
            approvalNew.save(failOnError: true, frush: true)
            apiHii.updateAffairProcess([campusId: approvalNew.campusId, approvalId: approvalNew.id, status: APPROVALED.status])
            //文印单独处理
            if (approvalNew.kitType == ApprovalFormType.WEN_YIN.type) {
                if (!wenYinApprovalRecord) {
                    wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordByApprovalId(approvalId)
                }
                wenYinApprovalRecordService.updateWenYinApprovalRecordStatus(wenYinApprovalRecord, approvalNew.status)
            }
            def d5 = task {
                sleep(3000L)
                //处理审批通过后的业务
                apiHii.dealApprovalRelated(approvalNew.id, ding)
            }
            d5.onComplete {
                log.info("处理审批通过后的业务,approvalId:${approvalNew.id}")
            }
            //处理审批之后的抄送
            List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchCopyStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx)
            if (nextCopyStepList?.size() > 0) {
                nextCopyStepList*.status = APPROVALED.status
                nextCopyStepList*.save(failOnError: true)
                List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                nextCopyRecordList*.status = APPROVALED.status
                nextCopyRecordList*.save(failOnError: true)
                //发送审批之后的抄送消息
                def s1 = task {
                    sleep(3000L)
                    nextCopyRecordList.each {
                        log.info("审批之后的抄送消息发送,approvalRecordId:${it.id}".toString())
                        apiHii.sendApprovalRecordMessageV2(2, it.id)
                    }
                }
                s1.onComplete {
                    log.info("审批之后的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                }
            }
        }
        //处理下一个审批流程
        else {
            ApprovalStepNew nextApprovalStepNew = repositoryApprovalStepNewService.fetchStepByApprovalIdAndApprovalStepTypeAndIdx(approvalId, APPROVAL.type, approvalStepNew.idx)
            //如果是多学生审批且是家长审批且没有设置转交人
            if (approvalNew.studentId?.split(",")?.size() > 1 && nextApprovalStepNew.approvalStepUserType == ApprovalProcessUserType.STUDENT_PARENT.type && !nextApprovalStepNew.batchStudentTransferIds) {
                dealBatchStudentApprovalStep(approvalNew, nextApprovalStepNew, wenYinApprovalRecord, ding, campus)
            } else {
                ApprovalRecordNew nextApprovalRecordNew = fetchApprovalRecordByStepIdAndUserId(nextApprovalStepNew.id, approvalNew.promoterId)
                if (approvalNew.promoterAutoApproval == 1 as byte && nextApprovalRecordNew) {
                    promoterAutoApproval(campus, nextApprovalStepNew, approvalNew, nextApprovalRecordNew, ding)
                } else {
                    nextApprovalStepNew.status = APPROVALING.status
                    nextApprovalStepNew.save(failOnError: true)
                    List<ApprovalRecordNew> nextApprovalRecordNewList = fetchApprovalRecordByStepId(nextApprovalStepNew.id)
                    nextApprovalRecordNewList*.status = APPROVALING.status
                    nextApprovalRecordNewList*.save(failOnError: true)
                    //下一个审批阶段发送消息
                    def s2 = task {
                        sleep(3000L)
                        nextApprovalRecordNewList.each {
                            log.info("下一个审批阶段消息发送,approvalRecordId:${it.id}".toString())
                            apiHii.sendApprovalRecordMessageV2(1, it.id)
                        }
                    }
                    s2.onComplete {
                        log.info("下一个审批阶段消息提醒,approvalId:${approvalNew.id},approvalStepId:${nextApprovalStepNew.id}".toString())
                    }
                }
                //处理下一个审批之前的抄送
                List<ApprovalStepNew> nextCopyStepList = repositoryApprovalStepNewService.fetchNextCopyApprovalStepByApprovalIdAndIdx(approvalId, approvalStepNew.idx, nextApprovalStepNew.idx)
                if (nextCopyStepList?.size() > 0) {
                    nextCopyStepList*.status = APPROVALED.status
                    nextCopyStepList*.save(failOnError: true)
                    List<ApprovalRecordNew> nextCopyRecordList = fetchApprovalRecordByStepIdInList(nextCopyStepList*.id)
                    nextCopyRecordList*.status = APPROVALED.status
                    nextCopyRecordList*.save(failOnError: true)
                    //下一个审批之前的抄送消息
                    def s3 = task {
                        sleep(3000L)
                        nextCopyRecordList.each {
                            log.info("下一个审批之前的抄送消息发送,approvalRecordId:${it.id}".toString())
                            apiHii.sendApprovalRecordMessageV2(2, it.id)
                        }
                    }
                    s3.onComplete {
                        log.info("下一个审批之前的抄送消息消息发送,approvalId:${approvalNew.id}".toString())
                    }
                }
            }
        }
    }

    List<ApprovalRecordNew> fetchApprovalRecordByStepIdInList(List<Long> approvalStepIdList) {
        ApprovalRecordNew.findAllByApprovalStepIdInList(approvalStepIdList)
    }

    List<ApprovalRecordNew> fetchApprovalRecordByStepId(Long approvalStepId) {
        ApprovalRecordNew.findAllByApprovalStepId(approvalStepId)
    }


    /**
     * 审批阶段
     * @param approvalId
     * @param approvalTemplateNew
     * @param idx
     * @param stepInfo
     * @return
     */
    def createApprovalStep(Long approvalId, ApprovalTemplateNew approvalTemplateNew, Integer idx, String stepInfo, Byte userType, String approvalStepUserIds) {
        ApprovalStepNew approvalStepNew = new ApprovalStepNew(campusId: approvalTemplateNew.campusId,
                schoolId: approvalTemplateNew.schoolId,
                approvalId: approvalId,
                approvalTemplateId: approvalTemplateNew.id,
                idx: idx,
                stepName: approvalTemplateNew.name,
                stepInfo: stepInfo,
                approvalStepType: approvalTemplateNew.approvalStepType,
                approvalStepUserType: approvalTemplateNew.approvalStepUserType,
                approvalStepUserIds: approvalStepUserIds,
                approvalMethodType: approvalTemplateNew.approvalMethodType ?: ApprovalMethodType.OR_SIGN.type,
                approvalUserNull: approvalTemplateNew.approvalUserNull,
                transferIds: approvalTemplateNew.transferIds,
                batchStudentTransferIds: approvalTemplateNew.batchStudentTransferIds,
                copyToParent: approvalTemplateNew.copyToParent,
                conditions: approvalTemplateNew.conditions,
                editCardJson: approvalTemplateNew.editCardJson,
                status: INIT.status,
                userType: userType)
        if (approvalStepNew.approvalStepUserType == ApprovalProcessUserType.PROMOTER_CHOOSE.type) {
            approvalStepNew.choiceIds = approvalStepUserIds
        }
        if (approvalStepNew.approvalStepType == PROMOTE.type) {
            approvalStepNew.status = APPROVALED.status
        }
        approvalStepNew.save(failOnError: true)
    }

    def saveApprovalAutographType(String json) {
        List<AutographTypeVO> autographTypeVOList = JSONObject.parseArray(json, AutographTypeVO.class)
        List<ApprovalStepNew> approvalStepNewList = []
        autographTypeVOList.each { AutographTypeVO autographTypeVO ->
            ApprovalStepNew approvalStepNew = repositoryApprovalStepNewService.getApprovalStep(autographTypeVO.stepId)
            approvalStepNew.autographType = autographTypeVO.typeId
            approvalStepNewList.add(approvalStepNew)
        }
        approvalStepNewList*.save(failOnError: true)
        approvalStepNewList.size() ?: 0
    }

    List<ApprovalAutographType> fetchApprovalAutographTypeByCampusId(Long campusId) {
        ApprovalAutographType.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    List<ApprovalForm> fetchApprovalFormByCampusId(Long campusId) {
        ApprovalForm.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }


    List<ApprovalExportRecord> fetchApprovalExportRecordViaCampusIdAndType(Long campusId, String types = "1") {
        List<Byte> typeList = types.split(",").collect { it as Byte }
        ApprovalExportRecord.findAllByCampusIdAndTypeInList(campusId, typeList)
    }

    def saveApprovalExportRecord(ApprovalExportRecord approvalExportRecord) {
        approvalExportRecord.save(failOnError: true)
    }


    ApprovalRecordNew fetchApprovalRecordByStepIdAndUserId(Long approvalStepId, Long userId) {
        ApprovalRecordNew.findByApprovalStepIdAndApprovalStepUserId(approvalStepId, userId)
    }

    def getApproval(Long id) {
        ApprovalNew.get(id)
    }

    def getAllApproval(List<Long> idList) {
        ApprovalNew.getAll(idList)
    }

    def fetchApprovalList(Long campusId, Byte promoterType, Long themeId, Byte status, Long createStartTime, Long createEndTime, Long updateStartTime, Long updateEndTime, String promoterName, String approvalNo) {
        def c = ApprovalNew.createCriteria()
        def list = c.list {
            if (themeId) {
                eq("approvalTemplateThemeId", themeId)
            }
            eq("campusId", campusId)
            if (promoterType) {
                eq("promoterType", promoterType)
            }
            if (status) {
                eq("status", status)
            }
            if (createStartTime && createStartTime) {
                ge("dateCreated", TimeUtils.getDateStartTime(createStartTime))
                le("dateCreated", TimeUtils.getDateEndTime(new Date(createEndTime)))
            }
            if (updateStartTime && updateEndTime) {
                ge("lastUpdated", TimeUtils.getDateStartTime(updateStartTime))
                le("lastUpdated", TimeUtils.getDateEndTime(new Date(updateEndTime)))
            }
            if (promoterName) {
                eq("promoterName", promoterName)
            }
            if (approvalNo) {
                eq("approvalNo", approvalNo)
            }
            order("id", "desc")
        } as List<ApprovalNew>
        list
    }

    def fetchApprovalListLimit(Long campusId, Byte promoterType, Long themeId, Byte status, Long createStartTime, Long createEndTime, Long updateStartTime, Long updateEndTime, String promoterName, String approvalNo, int p, int s) {
        def c = ApprovalNew.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            if (themeId) {
                eq("approvalTemplateThemeId", themeId)
            }
            eq("campusId", campusId)
            if (promoterType) {
                eq("promoterType", promoterType)
            }
            if (status) {
                eq("status", status)
            }
            if (createStartTime && createEndTime) {
                ge("dateCreated", TimeUtils.getDateStartTime(createStartTime))
                le("dateCreated", TimeUtils.getDateEndTime(new Date(createEndTime)))
            }
            if (updateStartTime && updateEndTime) {
                ge("lastUpdated", TimeUtils.getDateStartTime(updateStartTime))
                le("lastUpdated", TimeUtils.getDateEndTime(new Date(updateEndTime)))
            }
            if (promoterName) {
                eq("promoterName", promoterName)
            }
            if (approvalNo) {
                eq("approvalNo", approvalNo)
            }
            order("id", "desc")
        }
        [list: list as List<ApprovalNew>, total: list?.totalCount ?: 0]
    }

    def getApprovalTemplateTheme(Long id) {
        ApprovalTemplateThemeNew.get(id)
    }

    /**
     * 该校区下所有的审批类型
     * @param campusId
     * @return
     */
    def fetchApprovalTemplateThemeByCampusIdAndPromoterTypeAndFormTypeAndName(Long campusId, Byte promoterType, Integer formType, String name) {
        StringBuilder sb = new StringBuilder()
        Map<String, Object> sqlMap = [:]
        sb.append("FROM ApprovalTemplateThemeNew a WHERE a.campusId = :campusId")
        sqlMap.put("campusId", campusId)
        if (promoterType) {
            sb.append(" AND a.promoterType = :promoterType")
            sqlMap.put("promoterType", promoterType)
        }
        if (formType) {
            sb.append(" AND a.formType = :formType")
            sqlMap.put("formType", formType)
        }
        if (name) {
            sb.append(" AND a.name like :name")
            sqlMap.put("name", '%' + name + '%')
        }
        sb.append(" AND a.status != -1")
        String HQL = sb.toString()
        return ApprovalTemplateThemeNew.executeQuery(HQL, sqlMap) as List<ApprovalTemplateThemeNew>
    }

    static class AutographTypeVO implements Serializable {
        Long typeId
        Long stepId
    }

    /**
     * 升年级时更新审批流中的年级id
     * @param campusId
     * @return
     */
    def updateProcessJsonGradeIdByGradeUpdate(Long campusId) {
        Promise task = task {
            SchoolYear schoolYear = SchoolYear.findByCampusIdAndNameAndStatus(campusId, new DateTime().getYear(), 1 as byte)
            List<Grade> gradeList = Grade.findAllByCampusIdAndSchoolYearIdAndStatus(campusId, schoolYear.id, 1 as byte)
            Map<String, Long> gradeCodeIdMap = [:]
            gradeList.each {
                gradeCodeIdMap.put(it.code, it.id)
            }
            List<ApprovalTemplateThemeNew> approvalTemplateThemeNewList = ApprovalTemplateThemeNew.findAllByCampusIdAndStatus(campusId, 1 as byte)
            approvalTemplateThemeNewList.each {
                NodePropsVO nodePropsVO = JSONObject.parseObject(it.processJson, NodePropsVO.class)
                nodePropsVO = analyzingNodePropsVO4UpdateGrade(nodePropsVO, gradeCodeIdMap)
                it.processJson = JSONObject.toJSONString(nodePropsVO)
            }
            approvalTemplateThemeNewList*.save(failOnError: true)
        }
        task.onComplete {
            log.info("更新审批流中的年级id完成,campusId:${campusId}")
        }
        task.onError { Throwable throwable -> log.error("更新审批流中的年级id异常", throwable)
        }

    }

    /**
     * 解析节点更新年级id
     * @param nodePropsVO
     * @return
     */
    def analyzingNodePropsVO4UpdateGrade(NodePropsVO nodePropsVO, Map<String, Long> gradeCodeIdMap) {
        //如果是条件分支节点
        if (nodePropsVO.nodeType == 2 && nodePropsVO.conditionNodes?.size() > 0) {
            nodePropsVO.conditionNodes.each {
                analyzingNodePropsVO4UpdateGrade(it, gradeCodeIdMap)
            }
        }
        //如果是普通条件节点
        if (nodePropsVO.nodeType == 3 && nodePropsVO.properties) {
            ApprovalConditionVO conditions = JSONObject.parseObject(nodePropsVO.properties, ApprovalConditionVO.class)
            conditions.conditions.each {
                //如果是年级
                if (it.item.conditionType == 4) {
                    List<Long> gradeIdList = it.result.value?.split(",")?.collect { it.toLong() }
                    if (gradeIdList?.size() > 0) {
                        List<Long> idList = []
                        List<Grade> gradeList = Grade.findAllByIdInList(gradeIdList)
                        gradeList.each { grade -> idList.add(gradeCodeIdMap.get(grade.code))
                        }
                        it.result.value = idList.join(",")
                    }
                }
            }
            nodePropsVO.properties = JSONObject.toJSONString(conditions)
        }
        if (nodePropsVO.childNode) {
            analyzingNodePropsVO4UpdateGrade(nodePropsVO.childNode, gradeCodeIdMap)
        }
        nodePropsVO
    }

}

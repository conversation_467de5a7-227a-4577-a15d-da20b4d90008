package hiiadmin.approval

import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import hiiadmin.approval.repository.RepositoryApprovalTemplateThemeNewService
import hiiadmin.attendance.NonresidentService
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.mapstruct.approval.ApprovalTemplateVOMapper
import hiiadmin.module.approval.*
import hiiadmin.module.bugu.ParentVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.vocationalSchool.FacultyService
import org.springframework.beans.factory.annotation.Autowired
import timetabling.ParentStudent
import timetabling.oa.*
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Staff
import timetabling.user.Student

import static hiiadmin.ConstantEnum.*

@Transactional
class ApprovalNewVOService {

    GradeService gradeService

    UnitService unitService

    CampusService campusService

    StudentService studentService

    ParentStudentService parentStudentService

    NonresidentService nonresidentService

    ApprovalUpdateRecordService approvalUpdateRecordService

    RepositoryApprovalTemplateThemeNewService repositoryApprovalTemplateThemeNewService

    DepartmentService departmentService

    BgbCampusRoleService bgbCampusRoleService

    BgbStaffRoleService bgbStaffRoleService

    FacultyService facultyService

    StaffService staffService

    @Autowired
    ApprovalTemplateVOMapper ApprovalTemplateVOMapper

    ApprovalTemplateNewVO transformApprovalTemplateNewVO(ApprovalTemplateNew approvalTemplateNew, String stepInfo = null) {
        ApprovalTemplateNewVO vo = stepInfo ? ApprovalTemplateVOMapper.cover2VO(approvalTemplateNew, stepInfo) : ApprovalTemplateVOMapper.cover2VO(approvalTemplateNew)
        switch (vo.approvalStepUserType) {
            case ApprovalProcessUserType.DEPARTMENT.type:
                String objIds = approvalTemplateNew.approvalStepUserIds
                vo.approvalDepartmentIds = approvalTemplateNew.approvalStepUserIds
                Campus campus = campusService.fetchCampusById(approvalTemplateNew.campusId)
                Set<Long> teacherIdSet = []
                if (campus.type == CampusType.VOCATIONAL.type) {
                    if (objIds == "all") {
                        facultyService.transformAllTeacherIdBCampusId(approvalTemplateNew.campusId).each { teacherIdSet.add(it) }
                    } else if (objIds) {
                        List<Long> deptIds = objIds.split(",").collect { it.toLong() }
                        facultyService.fetchAllUserByFacultyIds(approvalTemplateNew.campusId, deptIds, UserTypeEnum.TEACHER.type)?.each { teacherIdSet.addAll(it.userId) }
                    }
                } else {
                    Multimap<Long, Long> multimap = departmentService.transformDepartmentIdStaffDepartment(approvalTemplateNew.campusId)
                    if (objIds == "all") {
                        multimap.values().each {
                            teacherIdSet.addAll(it)
                        }
                    } else if (objIds) {
                        objIds.split(",").each {
                            if (multimap.containsKey(it.toLong())) {
                                teacherIdSet.addAll(multimap.get(it.toLong()))
                            }
                        }
                    }
                }
                vo.approvalStepUserIds = teacherIdSet.join(",")
                break
            case ApprovalProcessUserType.ROLE.type:
                String objIds = approvalTemplateNew.approvalStepUserIds
                vo.approvalRoleIds = approvalTemplateNew.approvalStepUserIds
                Set<Long> teacherIdSet = []
                List<Long> roleIdList = null
                if (objIds == "all") {
                    roleIdList = bgbCampusRoleService.fetchCampusRoleByCampusId(approvalTemplateNew.campusId).collect { it.id }
                } else if (objIds) {
                    roleIdList = objIds.split(",").collect { it.toLong() }
                }
                List<Long> staffIdList = bgbStaffRoleService.transformBgbStaffRoleIdList(roleIdList)
                if (staffIdList) {
                    List<Staff> staffList = staffService.fetchStaffByIdList(staffIdList)
                    staffList.each {
                        teacherIdSet.add(it.id)
                    }
                }
                vo.approvalStepUserIds = teacherIdSet.join(",")
                break
        }

        vo
    }

    ApprovalTemplateThemeGroupVO buildApprovalTemplateThemeGroupVO(ApprovalTemplateThemeGroup approvalTemplateThemeGroup) {
        ApprovalTemplateThemeGroupVO approvalTemplateThemeGroupVO = new ApprovalTemplateThemeGroupVO(id: approvalTemplateThemeGroup.id,
                name: approvalTemplateThemeGroup.name,
                weight: approvalTemplateThemeGroup.weight)
        approvalTemplateThemeGroupVO
    }

    ApprovalStepVO buildApprovalStepVO(ApprovalStepNew approvalStep, ApprovalNew approvalNew, List<ApprovalAutographType> approvalAutographTypeList) {
        ApprovalAutographType approvalAutographType = approvalAutographTypeList.find { it.id == approvalStep.autographType }
        ApprovalStepVO approvalStepVO = new ApprovalStepVO(id: approvalStep.id,
                name: approvalStep.stepName,
                idx: approvalStep.idx,
                stepInfo: approvalStep.stepInfo,
                approvalStepType: approvalStep.approvalStepType,
                approvalStepUserType: approvalStep.approvalStepUserType,
                userType: approvalStep.userType,
                approvalStepUserIds: approvalStep.approvalStepUserIds,
                approvalMethodType: approvalStep.approvalMethodType,
                approvalUserNull: approvalStep.approvalUserNull,
                transferIds: approvalStep.transferIds,
                batchStudentTransferIds: approvalStep.batchStudentTransferIds,
                copyToParent: approvalStep.copyToParent,
                choiceIds: approvalStep.choiceIds,
                suggestion: approvalStep.suggestion,
                pics: approvalStep.pics,
                autograph: approvalStep.autograph,
                autographType: approvalStep.autographType,
                autographTypeName: approvalAutographType?.name,
                autoApproval: approvalStep.autoApproval,
                status: approvalStep.status,
                lastUpdated: approvalStep.lastUpdated.getTime())
        if (approvalStepVO.userType == UserTypeEnum.PARENT.type && approvalStepVO.approvalStepUserIds && approvalNew.studentId && !approvalNew.studentId.contains(",")) {
            List<Long> parentIdList = approvalStepVO.approvalStepUserIds.split(",").collect { it.toLong() }
            Student student = studentService.fetchStudentById(approvalNew.studentId.toLong())
            List<ParentStudent> parentStudentList = parentStudentService.fetchAllParentStudentByStudentId(approvalNew.studentId.toLong())
            List<ParentVO> parentVOList = parentIdList.collect { Long parentId ->
                ParentStudent parentStudent = parentStudentList.find { it.parentId == parentId }
                new ParentVO(id: parentId,
                        appellationName: parentStudent?.appellation ? AppellationType.getEnumByType(parentStudent?.appellation)?.name : parentStudent?.memo,
                        studentName: student?.name)
            }
            approvalStepVO.parentList = parentVOList
        }
        approvalStepVO
    }

    ApprovalStepVO buildApprovalStepVO(ApprovalStepNew approvalStep, ApprovalNew approvalNew) {
        ApprovalStepVO approvalStepVO = new ApprovalStepVO(id: approvalStep.id,
                name: approvalStep.stepName,
                idx: approvalStep.idx,
                stepInfo: approvalStep.stepInfo,
                approvalStepType: approvalStep.approvalStepType,
                approvalStepUserType: approvalStep.approvalStepUserType,
                userType: approvalStep.userType,
                approvalStepUserIds: approvalStep.approvalStepUserIds,
                approvalMethodType: approvalStep.approvalMethodType,
                approvalUserNull: approvalStep.approvalUserNull,
                transferIds: approvalStep.transferIds,
                choiceIds: approvalStep.choiceIds,
                suggestion: approvalStep.suggestion,
                pics: approvalStep.pics,
                autograph: approvalStep.autograph,
                status: approvalStep.status,
                lastUpdated: approvalStep.lastUpdated.getTime(),
                autoApproval: approvalStep.autoApproval,
                studentIds: approvalNew.studentId,
                batchStudentTransferIds: approvalStep.batchStudentTransferIds,
                copyToParent: approvalStep.copyToParent)
        if (approvalStepVO.userType == UserTypeEnum.PARENT.type && approvalStepVO.approvalStepUserIds && approvalNew.studentId && !approvalNew.studentId.contains(",")) {
            List<Long> parentIdList = approvalStepVO.approvalStepUserIds.split(",").collect { it.toLong() }
            Student student = studentService.fetchStudentById(approvalNew.studentId.toLong())
            List<ParentStudent> parentStudentList = parentStudentService.fetchAllParentStudentByStudentId(approvalNew.studentId.toLong())
            List<ParentVO> parentVOList = parentIdList.collect { Long parentId ->
                ParentStudent parentStudent = parentStudentList.find { it.parentId == parentId }
                new ParentVO(id: parentId,
                        appellationName: parentStudent?.appellation ? AppellationType.getEnumByType(parentStudent?.appellation)?.name : parentStudent?.memo,
                        studentName: student?.name)
            }
            approvalStepVO.parentList = parentVOList
        }
        approvalStepVO
    }

    ApprovalNewVO buildApprovalNewVO(ApprovalNew approvalNew, ApprovalStepNew approvalStepNew, ApprovalRecordNew approvalRecordNew, Integer byMe) {
        ApprovalTemplateThemeNew approvalTemplateThemeNew = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(approvalNew.approvalTemplateThemeId)
        ApprovalNewVO approvalNewVO = new ApprovalNewVO(id: approvalNew.id,
                name: approvalNew.approvalTemplateThemeName,
                promoterId: approvalNew.promoterId,
                promoterName: approvalNew.promoterName,
                promoterAvatar: approvalNew.promoterAvatar,
                promoterType: approvalNew.promoterType,
                dateCreated: approvalNew.dateCreated.getTime(),
                valueJson: approvalNew.valueJson,
                formJson: approvalTemplateThemeNew?.formJson,
                conditionJson: approvalTemplateThemeNew?.conditionJson,
                formValueJson: approvalNew?.formValueJson,
                finalStepId: approvalNew.finalStepId,
                status: approvalNew.status,
                approvalNo: approvalNew.approvalNo,
                autograph: approvalNew.autograph,
                comments: approvalNew.comments,
                promoterAutoApproval: approvalNew.promoterAutoApproval,
                printTime: approvalNew.printTime,
                approvalStepId: approvalStepNew?.id,
                approvalRecordStatus: approvalRecordNew?.status,
                conditions: approvalStepNew?.conditions,
                editCardJson: approvalStepNew?.editCardJson,
                byMe: byMe,
                updateRecordJson: approvalUpdateRecordService.getRecord(approvalNew.id))
        switch (approvalNewVO.promoterType) {
            case UserTypeEnum.TEACHER.type:
                approvalNewVO.promoterMobile = approvalNew.promoterMobile?.replaceAll("(\\d{3})\\d{4}(\\d{4})", "\$1****\$2")
                break
            case UserTypeEnum.PARENT.type:
                ParentStudent parentStudent = parentStudentService.fetchCurrentStudentIdByParentId(approvalNewVO.promoterId)
                Student student = studentService.fetchStudentById(parentStudent?.studentId)
                approvalNewVO.bindStudentName = student?.name ?: "" + ((parentStudent?.appellation ? AppellationType.getEnumByType(parentStudent?.appellation)?.name : parentStudent?.memo) ?: "")
                break
            case UserTypeEnum.STUDENT.type:
                Unit unit = unitService.fetchAdministrativeUnitByStudentId4Suspend(approvalNew.promoterId)
                Grade grade = gradeService.fetchGradeById(unit?.gradeId)
                approvalNewVO.unitId = unit?.id
                approvalNewVO.promoterName = studentService.appendStudentStatusByStudentId(approvalNew.promoterId)
                approvalNewVO.nonresident = nonresidentService.isNonresidentByStudentIdOnDate(approvalNew.promoterId)
                Campus campus = campusService.fetchCampusById(approvalNew?.campusId)
                if (campus && campus.type == 2) {
                    approvalNewVO.unitName = unit?.alias ?: unit?.name
                } else {
                    approvalNewVO.unitName = (SectionType.getEnumByType(grade?.sectionId)?.name ?: "") + (grade?.name ?: "") + (unit?.alias ?: (unit?.name ?: ""))
                }
                break
        }
        approvalNewVO
    }


    ApprovalTemplateThemeNewVO buildApprovalTemplateThemeNewVO(ApprovalTemplateThemeNew approvalTemplateThemeNew, Boolean easy = true) {
        ApprovalTemplateThemeNewVO approvalTemplateThemeNewVO = new ApprovalTemplateThemeNewVO(id: approvalTemplateThemeNew.id,
                name: approvalTemplateThemeNew.name,
                promoterNames: approvalTemplateThemeNew.promoterNames,
                configured: approvalTemplateThemeNew.processJson ? true : false,
                lastUpdated: approvalTemplateThemeNew?.lastUpdated?.getTime(),
                promoterType: approvalTemplateThemeNew.promoterType,
                formType: approvalTemplateThemeNew.formType,
                icon: approvalTemplateThemeNew.icon,
                color: approvalTemplateThemeNew.color,
                groupId: approvalTemplateThemeNew.groupId,
                weight: approvalTemplateThemeNew.weight,
                status: approvalTemplateThemeNew.status,
                jumpTemplateId: approvalTemplateThemeNew.jumpTemplateId)
        if (!easy) {
            approvalTemplateThemeNewVO.tips = approvalTemplateThemeNew.tips
            approvalTemplateThemeNewVO.buttonText = approvalTemplateThemeNew.buttonText
            approvalTemplateThemeNewVO.autograph = approvalTemplateThemeNew.autograph
            approvalTemplateThemeNewVO.comments = approvalTemplateThemeNew.comments
            approvalTemplateThemeNewVO.promoterAutoApproval = approvalTemplateThemeNew.promoterAutoApproval
            approvalTemplateThemeNewVO.printTime = approvalTemplateThemeNew.printTime
            approvalTemplateThemeNewVO.formJson = approvalTemplateThemeNew.formJson
            approvalTemplateThemeNewVO.conditionJson = approvalTemplateThemeNew.conditionJson
            approvalTemplateThemeNewVO.processJson = approvalTemplateThemeNew.processJson
        }
        approvalTemplateThemeNewVO
    }

    ApprovalNewVOV2 buildApprovalNewVOV2(ApprovalNew approvalNew, Multimap<Long, ApprovalStepNew> approvalIdStepMap) {
        ApprovalNewVOV2 approvalNewVOV2 = new ApprovalNewVOV2()
        approvalNewVOV2.id = approvalNew.id
        approvalNewVOV2.approvalNo = approvalNew.approvalNo
        approvalNewVOV2.themeId = approvalNew.approvalTemplateThemeId
        approvalNewVOV2.themeName = approvalNew.approvalTemplateThemeName
        if (approvalNew.promoterType == UserTypeEnum.STUDENT.type) {
            approvalNewVOV2.promoterName = studentService.appendStudentStatusByStudentId(approvalNew.promoterId)
        } else {
            approvalNewVOV2.promoterName = approvalNew.promoterName
        }
        ApprovalStepNew approvalStepNew
        List<ApprovalStepNew> approvalStepNewList = approvalIdStepMap.get(approvalNew.id).toList()
        if (approvalNew?.status == ApprovalStatus.APPROVALED.status) {
            approvalStepNew = approvalStepNewList.find { it.id == approvalNew.finalStepId }
        } else {
            approvalStepNew = approvalStepNewList.find { it.status == approvalNew.status }
        }


        approvalNewVOV2.stepInfo = approvalStepNew?.stepInfo
        approvalNewVOV2.dateCreated = approvalNew.dateCreated
        approvalNewVOV2.lastUpdated = approvalNew.lastUpdated
        approvalNewVOV2.status = approvalNew.status

        approvalNewVOV2
    }


}

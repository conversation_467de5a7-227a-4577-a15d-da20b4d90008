package hiiadmin.evaluation

import com.bugu.teaching.clientapi.domain.basic.Course
import com.bugu.teaching.clientapi.domain.basic.Unit
import com.bugu.teaching.clientapi.domain.lecture.LectureOriginTeachingRelationCell
import com.bugu.teaching.clientapi.service.TeachingRelationService
import com.bugu.teaching.clientapi.service.TeachingUnitService
import com.google.common.collect.Lists
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.module.evaluation.StudentEvaluationTaskVO
import org.apache.dubbo.config.annotation.DubboReference
import org.springframework.transaction.annotation.Propagation
import timetabling.evaluation.StudentEvaluationCommitRecord
import timetabling.evaluation.StudentEvaluationTask

import static grails.async.Promises.task

@Transactional
@Slf4j
class StudentEvaluationTaskService {


    @DubboReference(timeout = 60000)
    private TeachingUnitService teachingUnitService

    @DubboReference(timeout = 60000)
    private TeachingRelationService relationService
    

    CacheService cacheService


    def findAllEvaluationTask(Long campusId, Integer p, Integer s) {
        List<StudentEvaluationTaskVO> voList = Lists.newArrayList()
        List<StudentEvaluationTask> list = StudentEvaluationTask.findAllByCampusIdAndStatusGreaterThan(campusId, 0 as byte, [max: s, offset: (p - 1) * s, sort: "dateCreated", order: "desc"])
        if (list) {
//            List<Long> gradeIdList = []
//            List<String> gradeIds = list*.gradeIds
//            gradeIds.each { g ->
//                String[] arr = g.trim().split(",")
//                List<String> arrList = arr.toList()
//                List<Long> l = arrList.collect() { it as Long }
//                gradeIdList.addAll(l)
//            }
            List<StudentEvaluationCommitRecord> evaluationCommitRecordList = StudentEvaluationCommitRecord.findAllByStudentEvaluationTaskIdInListAndStatus(list*.id, 1 as byte)
            Map<Long, List<StudentEvaluationCommitRecord>> map = evaluationCommitRecordList.groupBy { i -> i.studentEvaluationTaskId }
            list.each { studentEvaluation ->
                StudentEvaluationTaskVO studentEvaluationTaskVO = new StudentEvaluationTaskVO().buildVO(studentEvaluation)
                if (map.containsKey(studentEvaluationTaskVO.id)) {
                    studentEvaluationTaskVO.commitCount = map.get(studentEvaluationTaskVO.id)*.studentId.toSet().size()
                }
                voList << studentEvaluationTaskVO
            }
        }
        Integer totalCount = StudentEvaluationTask.countByCampusIdAndStatusGreaterThan(campusId, 0 as byte)
        return [list: voList, total: totalCount ? totalCount : 0]
    }

    def findEvaluationTaskById(Long id) {
        StudentEvaluationTask studentEvaluationTask = StudentEvaluationTask.findById(id)
        StudentEvaluationTaskVO studentEvaluationTaskVO = new StudentEvaluationTaskVO().buildVO(studentEvaluationTask)
        return studentEvaluationTaskVO
    }


//    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "commentStatService_findAllCourse", key = "#campusId +'_'+ #gradeIds", cacheType = CacheType.REMOTE, postCondition = "#result == null || #result.empty")
//    @CacheRefresh(refresh = 3, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    Collection<Course> findAllCourseByCurrentSemester(Long campusId, String gradeIds, Byte campusType) {
        List<Course> courseArrayList = []
        String[] gradeIdArr = gradeIds.trim().split(",")
        List<Long> gradeIdList = []
        List<String> unitIdList = []
        gradeIdArr.each { s ->
            if (campusType == ConstantEnum.CampusType.K12.type) {
                Long gradeId = Long.parseLong(s.trim())
                gradeIdList << gradeId
            } else {
                unitIdList << s
            }
        }
        if (campusType == ConstantEnum.CampusType.K12.type) {
            List<Unit> unitList = teachingUnitService.findByGradeIdInList(gradeIdList, 1)
            unitIdList = unitList*.id
        }
        if (unitIdList?.size() > 0) {
            List<LectureOriginTeachingRelationCell> list = relationService.findAllTeachingRelation(campusId.toString(), 1 as byte)
            list?.findAll { l -> unitIdList.contains(l.unitId) && !"0".equals(l.courseStatus) }?.each { l ->
                Course course = new Course()
                course.id = l.courseId
                course.name = l.courseName
                course.subjectId = l.subjectId
                course.subjectName = l.subjectName
                course.campusId = campusId
                courseArrayList << course
            }
        }
        return courseArrayList.toSet()
    }

    def deleteById(Long id) {
        //删除评教任务
        byte status = 0
        String HQL1 = """ UPDATE StudentEvaluationTask SET status = :status, lastUpdated = NOW() WHERE id = :id """
        StudentEvaluationTask.executeUpdate(HQL1, [status: status, id: id], [failOnError: true, flush: true])
    }

    def save(List<StudentEvaluationCommitRecord> list) {
        if (list) {
            task {
                this.saveCommitRecord(list)
            }.onComplete {
                log.info("预生成评教数据成功")
            }.onError {
                log.error("预生成评教数据失败")
            }
        }
    }

    def update(String title, String questionJson, String gradeIds, String courseIds, Long id, Long campusId, String formJson, List<StudentEvaluationCommitRecord> list, Integer count, Integer mustCommitCount) {
        String flag = "studentEvaluationTask_${id}".toString()
        cacheService.studentEvaluationTask.put(flag, Boolean.TRUE)
        if (count > 0) {
            String deleteHQL = """ DELETE FROM StudentEvaluationCommitRecord WHERE studentEvaluationTaskId = :studentEvaluationTaskId AND campusId = :campusId """
            StudentEvaluationCommitRecord.executeUpdate(deleteHQL, [studentEvaluationTaskId: id, campusId: campusId])
        }
        String HQL = """ UPDATE StudentEvaluationTask SET title = :title, 
                                gradeIds = :gradeIds, courseIds = :courseIds,
                                questionJson = :questionJson,
                                formJson = :formJson,
                                mustCommitCount = :mustCommitCount,
                                lastUpdated = NOW(), version = version + 1
                                WHERE id = :id """
        StudentEvaluationTask.executeUpdate(HQL, [title: title, gradeIds: gradeIds, courseIds: courseIds, questionJson: questionJson, formJson: formJson, mustCommitCount: mustCommitCount, id: id], [failOnError: true, flush: true])
        log.info("*************修改成功********************")
        task {
            this.saveCommitRecord(list, id)
        }.onComplete {
            log.info("预生成评教数据成功")
        }.onError {
            log.error("预生成评教数据失败")
        }
    }

    def updateStatus(Long id, Byte status, Long campusId) {
        if (ConstantEnum.StudentEvaluationTaskStatus.DELETE.type.equals(status)) {
            String deleteHQL = """ DELETE FROM StudentEvaluationCommitRecord WHERE studentEvaluationTaskId = :studentEvaluationTaskId AND campusId = :campusId """
            StudentEvaluationCommitRecord.executeUpdate(deleteHQL, [studentEvaluationTaskId: id, campusId: campusId])
        }
        String HQL = """ UPDATE StudentEvaluationTask SET status = :status,
                            lastUpdated = NOW(), version = version + 1
                            WHERE id = :id """
        StudentEvaluationTask.executeUpdate(HQL, [status: status, id: id], [failOnError: true, flush: true])
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    def saveCommitRecord(List<StudentEvaluationCommitRecord> list, Long id = null) {
        list*.save([failOnError: true, flush: true])
        log.info("********添加成功*********")
        if (id) {
            String flag = "studentEvaluationTask_${id}".toString()
            cacheService.studentEvaluationTask.remove(flag)
        }
    }

    Long saveTask(StudentEvaluationTask studentEvaluationTask) {
        studentEvaluationTask.save([failOnError: true, flush: true])
        return studentEvaluationTask.id
    }
}

package hiiadmin.school.user

import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.google.common.base.Joiner
import com.google.common.collect.HashMultimap
import com.google.common.collect.Lists
import com.google.common.collect.Multimap
import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.EnvService
import hiiadmin.UserService
import hiiadmin.UserWxDingService
import hiiadmin.exceptions.AlreadyExistException
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.ParentVO
import hiiadmin.school.StudentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PatternUtils
import org.apache.commons.lang3.StringUtils
import timetabling.ParentStudent
import timetabling.org.UnitStudent
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.User
import timetabling.user.UserWxDing

import java.util.concurrent.TimeUnit

@Transactional
class ParentService {

    EnvService envService

    @Lazy
    StudentService studentService

    @Lazy
    UserService userService

    UserEncodeInfoService userEncodeInfoService

    UserWxDingService userWxDingService

    List<Parent> findParentPageByCampusIdAndSearchValue(Long campusId, String name, Long facultyId = null, Long majorId = null, Long sectionId = null, Long gradeId = null, Long unitId = null, Long studentId = null, int resultStatus = 99, int s = 30, Integer p = null) {
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT p ")
        Map map = new HashMap()
        formHQLParen(sb, map, campusId, name, facultyId, majorId, sectionId, gradeId, unitId, studentId, resultStatus)
        if (p && p > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        Parent.executeQuery(sb.toString(), map)
    }

    List<Parent> findParentPageByCampusIdAndSearchValueV2(Long campusId, String name,Long unitId = null,  int s = 30, Integer p = null) {
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT p ")
        Map map = new HashMap()
        formHQLParen(sb, map, campusId, name, null, null, null, null, unitId, null, 99)
        if (p && p > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        Parent.executeQuery(sb.toString(), map)
    }

    Integer countParentPageByCampusIdAndSearchValueV2(Long campusId, String name,Long unitId) {
        StringBuffer sb = new StringBuffer()
        Map map = new HashMap()
        sb.append("SELECT count(p.id) ")
        formHQLParen(sb,map, campusId, name, null, null, null, null, unitId, null, 99)
        Parent.executeQuery(sb.toString(), map)[0] as Integer
    }

    List<Parent> fetchAllByCampusId(Long campusId) {
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT p ")
        Map map = new HashMap()
        formHQLParen(sb, map, campusId, null, null, null, null, null, null, null, 99)
        Parent.executeQuery(sb.toString(), map)
    }

    Integer countParentPageByCampusIdAndSearchValue(Long campusId, String name, Long facultyId, Long majorId, Long sectionId, Long gradeId, Long unitId, int resultStatus, Long studentId = null) {
        StringBuffer sb = new StringBuffer()
        Map map = new HashMap()
        sb.append("SELECT count(p.id) ")
        formHQLParen(sb, map, campusId, name, facultyId, majorId, sectionId, gradeId, unitId, studentId, resultStatus)
        Parent.executeQuery(sb.toString(), map)[0] as Integer
    }

    Map formHQLParen(StringBuffer sb, Map map, Long campusId, String name, Long facultyId, Long majorId, Long sectionId = null, Long gradeId = null, Long unitId = null, Long studentId = null, int resultStatus = 99) {
        sb.append(""" FROM Parent p where p.status=1 AND EXISTS(
                      SELECT s.id FROM UnitStudent s INNER JOIN ParentStudent as u ON s.studentId = u.studentId
                      WHERE p.id=u.parentId AND s.status=1 AND u.status=1 AND s.campusId = :campusId """)
        if (studentId) {
            sb.append(""" AND s.studentId = :studentId """)
            map.put("studentId", studentId)
        }
        if (unitId && unitId != -1L) {
            sb.append(""" AND s.unitId = :unitId """)
            map.put("unitId", unitId)
        }
        if (gradeId && gradeId != -1L) {
            sb.append(""" AND s.gradeId = :gradeId """)
            map.put("gradeId", gradeId)
        }
        if (sectionId && sectionId != -1L) {
            sb.append(""" AND s.sectionId = :sectionId """)
            map.put("sectionId", sectionId)
        }
        
        if (facultyId) {
            sb.append(""" AND s.facultyId = :facultyId """)
            map.put("facultyId", facultyId)
        }
        
        if (majorId) {
            sb.append(""" AND s.majorId = :majorId """)
            map.put("majorId", majorId)
        }
        
        if (resultStatus == 4) {
            sb.append(""" AND p.avatar IS NULL """)
        }
        sb.append(""" )""")
        map.put("campusId", campusId)
        if (resultStatus && resultStatus != 4 && resultStatus != 3 && resultStatus != 99) {
            sb.append(""" AND EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND p.id=cf.userId AND cf.userType = 2 AND cf.resultStatus=:resultStatus AND p.avatar=cf.picUrl AND cf.status = 1 ORDER BY cf.id DESC )""")
            map.put("campusId", campusId)
            map.put("resultStatus", resultStatus)
        }
        if (resultStatus == 3) {
            sb.append(""" AND p.avatar IS NOT NULL AND NOT EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND p.id=cf.userId AND cf.userType = 2 AND cf.resultStatus != :resultStatus AND cf.status = 1 ORDER BY cf.id DESC )""")
            map.put("campusId", campusId)
            map.put("resultStatus", resultStatus)
        }
        if (StringUtils.isNotBlank(name)) {
            sb.append(""" AND p.name like :name """)
            String value = "%" + name + "%"
            map.put("name", value)
        }
        return map
    }

    List<Long> findAllParentIdsNoAvatarByCampusId(Long campusId) {
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("""SELECT p.id FROM Parent p where p.status = :status AND p.avatar IS NULL AND EXISTS(
                      SELECT s.id FROM UnitStudent s INNER JOIN ParentStudent as u ON s.studentId = u.studentId
                      WHERE p.id = u.parentId AND s.status = :status AND u.status = :status AND s.campusId = :campusId) """)
        map.put("status", 1 as byte)
        map.put("campusId", campusId)

        def re = Parent.executeQuery(sb.toString(), map)
        List<Long> parentIds = re as List<Long>
        parentIds
    }

    /**
     * 根据学生姓名/学号查询家长ids
     * @param cmapusId
     * @param searchValue
     * @return
     */
    List<Long> fetchAllParentByStudentNameOrCode(Long campusId, String searchValue) {
        List<Long> parentIds = []
        String hql = """SELECT p FROM ParentStudent p, Student s WHERE s.campusId = :campusId AND p.studentId = s.id AND p.status = 1 AND s.status = 1 
                        AND (s.name LIKE :searchValue OR s.code LIKE :searchValue)"""
        List<ParentStudent> parentStudentList = ParentStudent.executeQuery(hql, [campusId: campusId, searchValue: searchValue + "%"]) as List<ParentStudent>
        if (parentStudentList) {
            parentIds = parentStudentList*.parentId.unique()
        }
        return parentIds
    }

    List<Parent> findParentPageByCampusIdAndSearchValueJoinRelatedUser(Long campusId, String name, Boolean firmStatus, Integer firm, int s = 30, Integer p = null) {
        StringBuffer sb = new StringBuffer()
        sb.append(" SELECT p ")
        Map map = new HashMap()
        formHQLParenJoinRelatedUser(sb, map, campusId, name, firmStatus, firm)
        if (p) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        Parent.executeQuery(sb.toString(), map)
    }

    Integer countParentPageByCampusIdAndSearchValueJoinRelatedUser(Long campusId, String name, Boolean firmStatus, Integer firm) {
        StringBuffer sb = new StringBuffer()
        Map map = new HashMap()
        sb.append("SELECT count(p.id) ")
        formHQLParenJoinRelatedUser(sb, map, campusId, name, firmStatus, firm)
        Parent.executeQuery(sb.toString(), map)[0] as Integer
    }

    Map formHQLParenJoinRelatedUser(StringBuffer sb, Map map, Long campusId, String name, Boolean firmStatus, Integer firm) {
        sb.append(""" FROM Parent p where p.status=1 AND EXISTS(
                      SELECT s.id FROM UnitStudent s INNER JOIN ParentStudent as u ON s.studentId = u.studentId
                      WHERE p.id=u.parentId AND s.status=1 AND u.status=1 AND s.campusId = :campusId) """)
        map.put("campusId", campusId)

        if (firmStatus) {
            sb.append(""" AND EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = p.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status )""")
            map.put("userType", ConstantEnum.UserTypeEnum.PARENT.type)
            map.put("firm", firm)
        } else {
            sb.append(""" AND NOT EXISTS ( SELECT ru.id FROM RelatedUser ru WHERE ru.userId = p.id AND ru.userType = :userType AND ru.firm = :firm AND ru.status = :status)""")
            map.put("userType", ConstantEnum.UserTypeEnum.PARENT.type)
            map.put("firm", firm)
        }

        map.put("status", 1 as byte)

        if (StringUtils.isNotBlank(name)) {
            sb.append(""" AND p.name like :name """)
            String value = name + "%"
            map.put("name", value)
        }

        return map
    }

    Multimap<Long, ParentVO> getStudentIdParentListMapByStudentIdList(List<Long> studentIdList, boolean normal = true, boolean graduate = false) {
        Multimap<Long, ParentVO> studentIdMultimap = HashMultimap.create()
        if (studentIdList) {
            List<ParentStudent> parentStudentList = []
            if (normal) {
                if (graduate) {
                    parentStudentList = ParentStudent.findAllByStudentIdInListAndStatus(studentIdList, 6 as byte)
                } else {
                    parentStudentList = ParentStudent.findAllByStudentIdInListAndStatus(studentIdList, 1 as byte)
                }
            } else {
                parentStudentList = ParentStudent.findAllByStudentIdInListAndStatus(studentIdList, 7 as byte)
            }
            List<Long> parentIdList = parentStudentList*.parentId
            Map<Long, User> longUserMap = userService.getParentIdUserByParentIdInList(parentIdList)
            Map<Long, Parent> longParentMap = getParentIdParentMapByParentIdInList(parentIdList)
            Map<Long, String> longMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.PARENT.type, parentIdList, false, true)
            parentStudentList.each { parentStudent ->
                Parent parent = longParentMap.get(parentStudent.parentId)
                if (parent) {
                    ParentVO parentVO = new ParentVO().buildVO(parent, parentStudent.appellation, parentStudent.memo)
                    parentVO.mobile = longMobileMap.get(parent.id)
                    User user = longUserMap.get(parentStudent.parentId)
                    if (user) {
                        parentVO.bindWechat = user.wechatId != null
                        UserWxDing wxDing = userWxDingService.fetchUserWxDingByUserIdAndUserType(user?.id, ConstantEnum.UserTypeEnum.PARENT.type)
                        parentVO.bindDing = wxDing?.dingUserIdStr != null
                    } else {
                        parentVO.bindWechat = false
                    }
                    studentIdMultimap.put(parentStudent.studentId, parentVO)
                }
            }
        }
        studentIdMultimap
    }

    Integer countStudentParentByStudentIdList(List<Long> studentIdList) {
        ParentStudent.countByStudentIdInListAndStatus(studentIdList, 1 as byte)

    }

    List<String> getStudentParentUserOpenIdListByStudentIdList(List<Long> studentIdList) {
        List<ParentStudent> parentStudentList = ParentStudent.findAllByStudentIdInListAndStatus(studentIdList, 1 as byte)
        List<Long> parentIdList = parentStudentList*.parentId
        List<User> userList = User.findAllByUIdInListAndType(parentIdList, ConstantEnum.UserTypeEnum.PARENT.type)
        return userList*.openId
    }

    List<String> getStudentParentUserOpenIdByStudentId(Long studentId) {
        List<ParentStudent> parentStudentList = ParentStudent.findAllByStudentIdAndStatus(studentId, 1 as byte)
        List<Long> parentIdList = parentStudentList*.parentId
        List<User> userList = User.findAllByUIdInListAndType(parentIdList, ConstantEnum.UserTypeEnum.PARENT.type)
        return userList*.openId
    }

    Multimap<Long, ParentStudent> getParentIdValueParentStudentMapByParentIdInList(List<Long> parentIdList) {
        Multimap<Long, ParentStudent> map = HashMultimap.create()
        if (parentIdList) {
            List<ParentStudent> parentList = ParentStudent.findAllByParentIdInListAndStatus(parentIdList, 1 as Byte)
            parentList.each {
                ParentStudent parentStudent ->
                    map.put(parentStudent.parentId, parentStudent)
            }
        }
        map
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_parentS_fetchAllParentStudentByParentIdAndCampusId", key = "#parentId+ '_' + #campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 20, timeUnit = TimeUnit.MINUTES)
    List<ParentStudent> fetchAllParentStudentByParentIdAndCampusId(Long parentId, Long campusId, boolean cache = true) {
        String HQL = """ FROM ParentStudent p_s 
                      where p_s.parentId = :parentId 
                      AND p_s.status = 1 
                      AND EXISTS(SELECT u.id FROM UnitStudent as u WHERE p_s.studentId = u.studentId AND u.campusId=:campusId AND u.status = 1)"""
        Map map = [:]
        map.put("parentId", parentId)
        map.put("campusId", campusId)
        ParentStudent.executeQuery(HQL, map)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_parentS_fetchAllParentStudentByParentIdInListAndCampusId", key = "#parentIdInList + '_' + #campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 20, timeUnit = TimeUnit.MINUTES)
    List<ParentStudent> fetchAllParentStudentByParentIdInListAndCampusId(List<Long> parentIdInList, Long campusId, boolean cache = true) {
        String HQL = """ FROM ParentStudent p_s 
                      where p_s.parentId IN :parentIdInList 
                      AND p_s.status = 1 
                      AND EXISTS(SELECT u.id FROM UnitStudent as u WHERE p_s.studentId = u.studentId AND u.campusId=:campusId AND u.status = 1)"""
        Map map = [:]
        map.put("parentIdInList", parentIdInList)
        map.put("campusId", campusId)
        ParentStudent.executeQuery(HQL, map)
    }

    boolean hasOtherStudent4parentByStudentId(long campusId, long parentId, long studentId) {
        String HQL = """ FROM ParentStudent ps 
                                WHERE ps.parentId = :parentId 
                                AND ps.status = :status 
                                AND EXISTS ( SELECT u.id FROM UnitStudent as u 
                                        WHERE ps.studentId = u.studentId AND u.campusId= :campusId AND u.status = 1 AND u.studentId != :studentId)"""
        ParentStudent parentStudent = ParentStudent.find(HQL, [campusId: campusId, parentId: parentId, studentId: studentId, status: 1 as Byte])
        if (parentStudent) {
            return true
        } else {
            return false
        }
    }

    /**
     * 通过parentId获取家长和学生的关系
     * @param parentId
     * @param campusId
     * @return
     */
    List<String> getParentAndStudentRelationListByParentId(Long parentId, Long campusId) {
        List<String> relationList = []
        List<ParentStudent> parentStudentList = fetchAllParentStudentByParentIdAndCampusId(parentId, campusId)
        parentStudentList.each {
            ParentStudent parentStudent ->
                String relation
                if (StringUtils.isEmpty(parentStudent.studentName)) {
                    Student student = studentService.fetchStudentById(parentStudent.studentId)
                    relation = student?.name
                } else {
                    relation = parentStudent.studentName
                }
                if (parentStudent.appellation != null) {
                    relation = relation + ConstantEnum.AppellationType.getEnumByType(parentStudent.appellation).name
                }
                if (StringUtils.isNotBlank(relation)) {
                    relationList << relation
                }
        }
        return relationList
    }

    /**
     * 通过parentIdList获取家长和学生的关系
     * @param parentIdList
     * @param campusId
     * @return
     */
    Map<Long, String> getParentAndStudentRelationListByParentIdList(List<Long> parentIdList) {
        Map<Long, String> relationMap = [:]

        Multimap<Long, ParentStudent> parentStudentMultimap = getParentIdValueParentStudentMapByParentIdInList(parentIdList)
        parentIdList.each {
            List<String> relationList = []
            List<ParentStudent> parentStudentList = parentStudentMultimap.get(it).toList()
            parentStudentList.each {
                ParentStudent parentStudent ->
                    String relation
                    if (StringUtils.isEmpty(parentStudent.studentName)) {
                        Student student = studentService.fetchStudentById(parentStudent.studentId)
                        relation = student?.name
                    } else {
                        relation = parentStudent.studentName
                    }
                    if (parentStudent.appellation != null) {
                        relation = relation + ConstantEnum.AppellationType.getEnumByType(parentStudent.appellation).name
                    }
                    if (StringUtils.isNotBlank(relation)) {
                        relationList << relation
                    }
            }
            relationMap.put(it, Joiner.on(",").join(relationList))
        }

        relationMap

    }

    List<ParentStudent> getParentStudentByParentId(Long parentId) {
        ParentStudent.findAllByParentIdAndStatus(parentId, 1 as Byte)
    }

    Map<Long, Parent> getParentIdParentMapByParentIdInList(List<Long> parentIdList) {
        Map<Long, Parent> longParentMap = [:]
        if (parentIdList) {
            List<Parent> parentList = Parent.getAll(parentIdList)
            parentList.each {
                if (it) {
                    longParentMap[it.id] = it
                }
            }
        }
        longParentMap
    }

    List<ParentVO> fetchAllParentByStudentId(Long studentId) {
        List<ParentStudent> parentStudentList = ParentStudent.findAllByStudentIdAndStatus(studentId, 1 as byte)
        List<ParentVO> vos = []
        parentStudentList.each {
            ps ->
                Parent parent = Parent.get(ps.parentId)
                vos << new ParentVO().buildVO(parent, ps.appellation, ps.memo)
        }
        vos
    }

    Integer countParentByGradeId(Long gradeId, boolean graduate = false) {
        Integer count = 0
        if (graduate) {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.gradeId = :gradeId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    gradeId : gradeId,
                    unitType: ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status  : ConstantEnum.RelationStatus.FINISH.status
            ]) as List<Student>
            if (list) {
                List<Long> studentIds = list*.id.unique()
                count = ParentStudent.countByStudentIdInListAndStatus(studentIds, ConstantEnum.RelationStatus.FINISH.status)
            }
        } else {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.gradeId = :gradeId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    gradeId : gradeId,
                    unitType: ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status  : ConstantEnum.RelationStatus.NORMAL.status
            ]) as List<Student>
            if (list) {
                List<Long> studentIds = list*.id.unique()
                count = ParentStudent.countByStudentIdInListAndStatus(studentIds, 1 as byte)
            }
        }
        count ?: 0
    }

    Integer countParentBySectionId(Long campusId, Long sectionId, boolean graduate = false) {
        Integer count = 0
        if (graduate) {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.campusId = :campusId AND 
                        u_s.sectionId = :sectionId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    campusId : campusId,
                    sectionId: sectionId,
                    unitType : ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status   : ConstantEnum.RelationStatus.FINISH.status
            ]) as List<Student>
            if (list) {
                List<Long> studentIds = list*.id.unique()
                count = ParentStudent.countByStudentIdInListAndStatus(studentIds, ConstantEnum.RelationStatus.FINISH.status)
            }
        } else {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.campusId = :campusId AND 
                        u_s.sectionId = :sectionId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    campusId : campusId,
                    sectionId: sectionId,
                    unitType : ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status   : ConstantEnum.RelationStatus.NORMAL.status
            ]) as List<Student>
            if (list) {
                List<Long> studentIds = list*.id.unique()
                count = ParentStudent.countByStudentIdInListAndStatus(studentIds, 1 as byte)
            }
        }
        count ?: 0
    }

    Integer countParentByUnitId(Long unitId, boolean graduate = false) {
        Integer count = 0
        if (graduate) {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.unitId = :unitId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    unitId  : unitId,
                    unitType: ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status  : ConstantEnum.RelationStatus.FINISH.status
            ]) as List<Student>
            if (list) {
                List<Long> ids = list*.id
                count = ParentStudent.countByStudentIdInListAndStatus(ids, ConstantEnum.RelationStatus.FINISH.status)
            }
        } else {
            String HQL = """SELECT s FROM Student s ,UnitStudent u_s WHERE s.id = u_s.studentId AND 
                        u_s.unitId = :unitId AND 
                        u_s.unitType = :unitType AND
                        u_s.status = :status"""
            List<Student> list = Student.executeQuery(HQL, [
                    unitId  : unitId,
                    unitType: ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type,
                    status  : ConstantEnum.RelationStatus.NORMAL.status
            ]) as List<Student>
            if (list) {
                List<Long> ids = list*.id
                count = ParentStudent.countByStudentIdInListAndStatus(ids, 1 as byte)
            }
        }
        count ?: 0
    }

    Map<Long, Integer> countParentByUnitIdList(List<Long> unitIdList, boolean graduate = false) {
        Map<Long, Integer> unitParentCountMap = [:]
        byte status = 1 as byte
        
        if (graduate) {
            status = 6 as byte
        }

        String HQL = """ SELECT us.unitId, COUNT(p.id) FROM UnitStudent us, ParentStudent p WHERE 
                                us.studentId = p.studentId
                                AND us.status = :status
                                AND us.unitType = :unitType 
                                AND p.status = :status
                                AND us.unitId IN :unitIdList
                                GROUP BY us.unitId """
        
        def result = UnitStudent.executeQuery(HQL, [status: status, unitType: 1 as byte, unitIdList: unitIdList])
        result?.each {
            Long unitId = it[0] as Long
            Integer count = it[1] as Integer
            unitParentCountMap.put(unitId, count)
        }
        
        unitParentCountMap
    }

    Parent fetchOrCreateParentByMobile(String mobile) {
        if (PatternUtils.isMobile(mobile, envService)) {
            Parent parent = fetchOrCreateByTrueMobile(mobile)
            parent.password = UserService.createStoredPassWord(mobile)
            parent.save(failOnError: true, flush: true)
            return parent
        } else {
            throw new HiiAdminException("手机号异常")
        }
    }

    def saveParent(Parent parent) {
        parent.save(failOnError: true, flush: true)
    }

    Parent fetchOrCreateByTrueMobile(String trueMobile) {
        Parent parent = fetchParentByTrueMobile(trueMobile)
        if (!parent) {
            parent = new Parent(
                    status: 1 as byte
            )
            parent.save(failOnError: true, flush: true)
            userEncodeInfoService.saveUserEncodeInfo(trueMobile, ConstantEnum.UserTypeEnum.PARENT.type, parent.id, ConstantEnum.UserEncodeInfoType.MOBILE.type)
        }
        parent
    }

    Parent fetchParentByTrueMobile(String trueMobile) {
        userEncodeInfoService.fetchParentByTrueMobile(trueMobile)
    }

    Parent saveParentAndSaveEncode(Parent parent, trueMobile) {
        saveParent(parent)
        userEncodeInfoService.saveUserEncodeInfo(trueMobile, ConstantEnum.UserTypeEnum.PARENT.type, parent.id, ConstantEnum.UserEncodeInfoType.MOBILE.type)
        return parent
    }

    List<Parent> fetchAllParentByIds(List<Long> ids) {
        if (!ids) {
            return []
        }
        Parent.findAllByIdInListAndStatus(ids, 1 as Byte)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_parentService_fetchParentById", key = "#parentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 20, timeUnit = TimeUnit.MINUTES)
    Parent fetchParentById(Long parentId, boolean cache = true) {
        Parent.get(parentId)
    }

    /**
     * 家长是否存在该校区
     * @return true 存在 false:不存在
     * */
    Boolean parentExistCampus(Long campusId, Long parentId) {
        List<ParentStudent> parentStudentList = fetchAllParentStudentByParentIdAndCampusId(parentId, campusId, false)
        if (parentStudentList != null && parentStudentList.size() > 0) {
            return true
        }
        return false
    }


    void changeParentMobile(Long parentId, String mobile) {
        
        if (!PatternUtils.isMobile(mobile)){
            throw new HiiAdminException("手机号错误")
        }
        
        Parent p = fetchParentByTrueMobile(mobile)
        if (p && p.id && p.id != parentId) {
            throw new AlreadyExistException("手机号已存在")
        }
        Parent parent = fetchParentById(parentId)

        userEncodeInfoService.saveUserEncodeInfo(mobile, ConstantEnum.UserTypeEnum.PARENT.type, parentId, ConstantEnum.UserEncodeInfoType.MOBILE.type)
        parent.save(failOnError: true, flush: true)
        
    }

    void deleteParentStudent(Long parentId, Long studentId) {
        ParentStudent parentStudent = ParentStudent.findByParentIdAndStudentIdAndStatus(parentId, studentId, 1 as Byte)
        parentStudent.status = ParentStudent.DEFAULT_DELETE_STATUS
        Student student = studentService.fetchStudentById(studentId)
        if (student.parentCount && student.parentCount > 0) {
            student.parentCount = student.parentCount - 1
        }
        studentService.saveStudent(student)
        parentStudent.save(failOnError: true, flush: true)
    }

    String Parent_Student_Parent_HQL = """ SELECT ps, p FROM ParentStudent ps LEFT JOIN Parent p ON ps.parentId = p.id WHERE ps.status = 1 AND p.status  = 1 AND ps.studentId IN :studentIdList """

    List<ParentVO> fetchAllParentByStudentIdList(List<Long> studentIdList) {
        List<ParentVO> parentVOList = Lists.newArrayList()
        if (studentIdList.size() > 0) {
            def list = ParentStudent.executeQuery(Parent_Student_Parent_HQL, [studentIdList: studentIdList])
            list.each { obj ->
                def (ParentStudent ps, Parent p) = obj
                ParentVO parentVO = new ParentVO().buildVO(p, ps.appellation, ps.memo)
                parentVOList << parentVO
            }
        }
        parentVOList
    }
}

package hiiadmin.school

import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.exceptions.HiiAdminException
import timetabling.CardUser

import javax.annotation.Resource
import java.util.concurrent.TimeUnit

import static hiiadmin.ConstantEnum.UserTypeEnum

@Slf4j
@Transactional
class CardUserService {

    @Resource
    AntennaApi antennaApi

    void createOrUpdateCardUser4student(long schoolId, Long studentId, String cardNum) {
        CardUser cardUser = CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 1 as byte)
        if (cardUser?.cardNum != cardNum) {
            if (cardUser) {
                cardUser.status = CardUser.CARD_DELETE_STATUS
                cardUser.save(failOnError: true, flush: true)
            }
            CardUser card = fetchCardUserBySchoolIdAndCardNum(schoolId, cardNum)
            //同一个学校下卡号不能重复
            if (card && (card.type != UserTypeEnum.STUDENT.type || card.userId != studentId)) {
                throw new HiiAdminException("卡号：${cardNum}已存在，请更换后尝试".toString())
            }
            CardUser c = new CardUser(
                    schoolId: schoolId,
                    cardNum: cardNum,
                    userId: studentId,
                    type: UserTypeEnum.STUDENT.type,
                    status: 1 as byte
            )
            c.save(failOnError: true, flush: true)
        }
    }

    void createOrUpdateCardUser4teacher(long schoolId, Long teacherId, String cardNum) {
        CardUser cardUser = CardUser.findByUserIdAndTypeAndStatus(teacherId, UserTypeEnum.TEACHER.type, 1 as byte)
        if (cardUser?.cardNum != cardNum) {
            if (cardUser) {
                cardUser.status = CardUser.CARD_DELETE_STATUS
                cardUser.save(failOnError: true, flush: true)
            }
            CardUser card = fetchCardUserBySchoolIdAndCardNum(schoolId, cardNum)
            //同一个学校下卡号不能重复
            if (card && (card.type != UserTypeEnum.TEACHER.type || card.userId != teacherId)) {
                throw new HiiAdminException("卡号：${cardNum}已存在，请更换后尝试".toString())
            }
            CardUser c = new CardUser(
                    schoolId: schoolId,
                    cardNum: cardNum,
                    userId: teacherId,
                    type: UserTypeEnum.TEACHER.type,
                    status: 1 as byte
            )
            c.save(failOnError: true, flush: true)
        }
    }

    Map<Long, String> fetchAllCardUserByUserIdListAndType(List<Long> userIdList, Byte type) {
        List<CardUser> cardUserList = CardUser.findAllByUserIdInListAndTypeAndStatus(userIdList, type, 1 as byte)
        if (cardUserList) {
            return cardUserList.collectEntries { cardUser ->
                [cardUser.userId, cardUser.cardNum]
            }
        }
        return [:]
    }

    Map<Long, CardUser> fetchAllCardUserByUserIdListAndTypeMap(Long schoolId) {
        List<CardUser> cardUserList = CardUser.findAllBySchoolIdAndStatus(schoolId, 1 as Byte)
        if (cardUserList) {
            return cardUserList.collectEntries { cardUser ->
                [cardUser.userId, cardUser]
            }
        }
        return [:]
    }

    CardUser fetchCardUserBySchoolIdAndCardNum(Long schoolId, String cardNum) {
        CardUser.findBySchoolIdAndCardNumAndStatus(schoolId, cardNum, 1 as byte)
    }

    CardUser fetchCardUserByUserIdAndCardNum(Long userId, String cardNum, Byte type) {
        CardUser.findByUserIdAndCardNumAndTypeAndStatus(userId, cardNum, type, 1 as byte)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_cardUserS_fetchSCUBStudentId", key = "#studentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    CardUser fetchStudentCardUserByStudentId(Long studentId, boolean cache = true) {
        CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 1 as byte)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_cardUserS_fetchUserCardUserByUserIdAndUserType", key = "#userId + '_' + #userType",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    CardUser fetchUserCardUserByUserIdAndUserType(Long userId, Byte userType, boolean cache = true) {
        CardUser.findByUserIdAndTypeAndStatus(userId, userType, 1 as byte)
    }


    CardUser fetchStudentCardUserByStudentIdNotCache(Long studentId, boolean cache = true) {
        CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 1 as byte)
    }

    List<CardUser> fetchStudentCardUserByStudentIdList(List<Long> studentIdList) {
        CardUser.findAllByUserIdInListAndTypeAndStatus(studentIdList, UserTypeEnum.STUDENT.type, 1 as Byte)
    }

    List<CardUser> fetchStudentCardUserByTeacherIdList(List<Long> teacherIdList) {
        CardUser.findAllByUserIdInListAndTypeAndStatus(teacherIdList, UserTypeEnum.TEACHER.type, 1 as Byte)
    }

    Map<Long, CardUser> transformationStudentIdAndCardUserMap(List<Long> studentIdList) {
        List<CardUser> cardUserList = fetchStudentCardUserByStudentIdList(studentIdList)
        Map<Long, CardUser> studentMap = [:]
        if (cardUserList == null || cardUserList.size() < 1) {
            return studentMap
        }
        cardUserList.each {
            CardUser cardUser ->
                studentMap.put(cardUser.userId, cardUser)
        }
        return studentMap
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_cardUserS_fetchTCUBTeacherId", key = "#teacherId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    CardUser fetchTeacherCardUserByTeacherId(Long teacherId, boolean cache = true) {
        CardUser.findByUserIdAndTypeAndStatus(teacherId, UserTypeEnum.TEACHER.type, 1 as byte)
    }

    List<CardUser> fetchAllCardUserViaSchoolIdAndType(Long schoolId, Byte type) {
        CardUser.findAllBySchoolIdAndTypeAndStatus(schoolId, type, 1 as byte)
    }
    
    void deleteUserCardNum(Long userId, Byte userType) {
        CardUser cardUser = CardUser.findByUserIdAndTypeAndStatus(userId, userType, 1 as byte)
        if (cardUser) {
            cardUser.status = 0 as byte
            cardUser.save(failOnError: true)
        }
    }
}

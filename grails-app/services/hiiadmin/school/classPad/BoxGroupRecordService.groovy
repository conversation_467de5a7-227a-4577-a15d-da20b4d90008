package hiiadmin.school.classPad

import com.alibaba.fastjson.JSON
import com.bugu.CronFormat
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.EnvService
import hiiadmin.UserService
import hiiadmin.apiCloud.XJobApi
import hiiadmin.module.saas.SaasMsgBO
import hiiadmin.msg.SaasMessageSendService
import hiiadmin.school.CampusService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.utils.ExecutorHandlerEnum
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import timetabling.ParentStudent
import timetabling.classPad.BoxGroupRecord
import timetabling.classPad.BoxGroupRecordData
import timetabling.org.Campus

@Slf4j
@Transactional
class BoxGroupRecordService {

    BoxGroupRecordDataService boxGroupRecordDataService

    @Autowired
    XJobApi xJobApi

    CampusService campusService

    ParentStudentService parentStudentService

    UserService userService

    SaasMessageSendService saasMessageSendService

    EnvService envService

    def save(BoxGroupRecord boxGroupRecord) {
        boxGroupRecord.save(failOnError: true)
        boxGroupRecord.id
    }

    def fetchBoxGroupRecordById(Long id) {
        BoxGroupRecord.findByIdAndStatus(id, 1)
    }

    def fetchBoxGroupRecordByIdNoStatus(Long id) {
        BoxGroupRecord.findById(id)
    }

    def fetchBoxGroupRecordByIdNotStatus(Long id) {
        BoxGroupRecord.findByIdAndStatusNotEqual(id, 0)
    }

    def fetchBoxGroupRecordLimit(Long campusId, Long userId, Integer showType, Long groupId, Integer type, Integer publishUserType, Integer publishStatus, String title, String searchValue, Long startTime, Long endTime, int p, int s) {
        def c = BoxGroupRecord.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            eq("status", 1)
            if (showType == ConstantEnum.BoxGroupRecordShowType.PUBLISHING_ME.type) {
                eq("userId", userId)
            } else if (showType == ConstantEnum.BoxGroupRecordShowType.MANAGER_ME.type) {
                List<Long> recordIds = boxGroupRecordDataService.fetchGroupDataByCampusIdAndTeacherId(campusId, userId)*.recordId
                if (recordIds.size() > 0) {
                    inList("id", recordIds)
                    eq("publishStatus", ConstantEnum.BoxGroupRecordPublishStatus.PUBLISHED.status)
                } else {
                    eq("id", 0L)
                }
            }
            if (searchValue) {
                or {
                    like("title", "%" + searchValue + "%")
                    like("userName", "%" + searchValue + "%")
                }
            }
            if (groupId) {
                eq("groupId", groupId)
            }
            if (type) {
                eq("type", type)
            }
            if (publishUserType) {
                eq("publishUserType", publishUserType)
            }
            if (publishStatus != null) {
                eq("publishStatus", publishStatus)
            }
            if (title) {
                like("title", "%" + title + "%")
            }
            if (startTime && endTime) {
                ge("dateCreated", new Date(startTime))
                le("dateCreated", new Date(endTime))
            }
            order('dateCreated', 'desc')
        } as List<BoxGroupRecord>

        [list: list, count: list?.totalCount ?: 0]
    }

    def deleteBoxGroupRecordById(Long id) {
        boxGroupRecordDataService.deleteGroupDataByRecordId(id)
        BoxGroupRecord boxGroupRecord = fetchBoxGroupRecordById(id)
        boxGroupRecord.status = 0
        save(boxGroupRecord)
        if (boxGroupRecord.jobId) {
            xJobApi.removeJob(boxGroupRecord.jobId)
        }
    }

    List<BoxGroupRecord> fetchRecordByGroupIdList(List<Long> groupIds) {
        BoxGroupRecord.findAllByGroupIdInListAndStatus(groupIds, 1)
    }

    def moveRecordToNull4GroupIds(List<Long> groupIds) {
        List<BoxGroupRecord> boxGroupRecordList = fetchRecordByGroupIdList(groupIds)
        boxGroupRecordList*.groupId = null
        boxGroupRecordList*.save(failOnError: true)
    }

    def publishBoxGroupRecord(Long recordId, int publishType, Long publishTime, Integer publishUserType, Long userId, String userName) {
        if (publishType == 1) {
            createBoxGroupRecord(recordId, publishType, publishTime, publishUserType, ConstantEnum.BoxGroupRecordPublishStatus.PUBLISHED.status, userId, userName)
            sendMessage4BoxGroupRecord(recordId)
        } else {
            BoxGroupRecord boxGroupRecord = createBoxGroupRecord(recordId, publishType, publishTime, publishUserType, ConstantEnum.BoxGroupRecordPublishStatus.PUBLISHING.status, userId, userName)

            ResultVO resultVO = xJobApi.addJobAndStart([jobDesc        : "数据发布定时任务recordId_${boxGroupRecord.id}".toString(),
                                                        executorParam  : boxGroupRecord.id.toString(),
                                                        scheduleCron   : new DateTime(publishTime).toString(CronFormat.ONCE.dateFormat),
                                                        executorHandler: ExecutorHandlerEnum.dataPublish4GroupRecord.name(),
                                                        jobGroup       : 7])
            log.info("${JSON.toJSONString(resultVO)}".toString())
            Integer jobId = resultVO.result.get("id") as Integer
            boxGroupRecord.jobId = jobId
            save(boxGroupRecord)
        }
    }

    BoxGroupRecord createBoxGroupRecord(Long recordId, int publishType, Long publishTime, Integer publishUserType, Integer publishStatus, Long userId, String userName) {
        BoxGroupRecord boxGroupRecord = fetchBoxGroupRecordById(recordId)
        if (boxGroupRecord) {
            boxGroupRecord.publishStatus = publishStatus
            if (publishTime && StringUtils.isNotBlank(publishTime?.toString())) {
                boxGroupRecord.publishDate = new Date(publishTime)
            } else {
                boxGroupRecord.publishDate = new Date()
            }
            boxGroupRecord.publishUserType = publishUserType
            boxGroupRecord.publishType = publishType
            boxGroupRecord.publisherId = userId
            boxGroupRecord.publisherName = userName
            save(boxGroupRecord)

            List<BoxGroupRecordData> boxGroupRecordDataList = boxGroupRecordDataService.fetchAllGroupDataByRecordIdAndStatus(recordId, 0 as byte)
            if (boxGroupRecordDataList && boxGroupRecordDataList.size() > 0) {
                boxGroupRecordDataList*.status = 1 as byte
                boxGroupRecordDataService.saveAllBoxGroupRecordDataList(boxGroupRecordDataList)
            }
        }

        return boxGroupRecord
    }

    def updateBoxGroupRecord(Long recordId, Long groupId, String title) {
        BoxGroupRecord boxGroupRecord = fetchBoxGroupRecordById(recordId)
        if (boxGroupRecord) {
            boxGroupRecord.title = title
            boxGroupRecord.groupId = groupId
            save(boxGroupRecord)
        }
    }

    def sendMessage4BoxGroupRecord(Long recordId) {
        BoxGroupRecord boxGroupRecord = fetchBoxGroupRecordById(recordId)
        if (!boxGroupRecord) {
            log.warn( "数据发布不存在！发送失败@recordId:${recordId}")
        }
        List<BoxGroupRecordData> boxGroupRecordDataList = boxGroupRecordDataService.fetchAllGroupDataByRecordId(recordId)
        if (boxGroupRecordDataList.size() < 1) {
            log.warn("数据发布内容为空！发送失败@recordId:${recordId}")
        }
        Long campusId = boxGroupRecord.campusId
        List<Long> userIdList = []
        Byte userType = null
        if (boxGroupRecord.type == 1) {
            switch (boxGroupRecord.publishUserType) {
                case 1:
                    userType = ConstantEnum.UserTypeEnum.STUDENT.type
                    userIdList = boxGroupRecordDataList*.studentId
                    break

                case 2:
                case 7:
                    userType = ConstantEnum.UserTypeEnum.PARENT.type
                    List<Long> studentIds = boxGroupRecordDataList*.studentId
                    List<ParentStudent> parentStudents = parentStudentService.fetchAllParentStudentByStudentIdInList(studentIds)
                    if (parentStudents.size() < 1) {
                        log.warn("该学生发布数据中学生都不存在家长,发送失败@recordId:${recordId}")
                    }
                    userIdList = parentStudents*.parentId
                    break
            }
        } else if (boxGroupRecord.type == 2) {
            userType = ConstantEnum.UserTypeEnum.TEACHER.type
            userIdList = boxGroupRecordDataList*.teacherId
        }

        Campus campus = campusService.fetchCampusByCampusId(campusId)
        Long agentId = campus?.agentId
        String corpId = campus?.corpId

        String msgPath = null
        if (envService.isProd() && campusId < 10) {
            msgPath = """/6/active-micro-app/data-distribution/detail?id=${recordId}""".toString()
        } else {
            msgPath = """/6/active-micro-app/data-distribution/detail?id=${recordId}""".toString()
        }

        String time = new DateTime(boxGroupRecord.publishDate).toString("yyyy-MM-dd HH:mm:ss")
        Map wechatKeyMap = [keyword1: boxGroupRecord?.title ?: "",
                            keyword2: time,
                            keyword3: "点击查看详情",
                            msgPath : envService.getMsgPath() + msgPath,
                            time1   : time,
                            const2  : "点击查看详情"]

        Map dingKeyMap = [
                title : boxGroupRecord?.title,
                corpId: campus.corpId,
                url   : envService.getDingMsgPath() + msgPath +  "&corpid=${corpId}"
        ]

        userIdList.each { Long userId ->
            try {
                Map infoMap = userService.getOpenIdAndDingUserIdByUserIdAndUserType(userId, userType, campusId, corpId)
                String openId = infoMap.get("openId")
                if (openId) {
                    log.info("------send upload avatar message wx msg openId@${openId}")
                    saasMessageSendService.sendWechatPicUpload(openId, campusId, wechatKeyMap)
                    log.info("------send upload avatar message wx msg send success------")
                } else {
                    log.warn("send wechat upload error, userId@${userId}, userType@${userType}".toString())
                }

                String dingUserIdStr = infoMap.get("dingUserIdStr")
                if (dingUserIdStr && corpId && agentId) {
                    log.info("------send upload avatar message ding msg dingUserIdStr@${dingUserIdStr}")
                    SaasMsgBO bo = saasMessageSendService.transformDingBo(dingUserIdStr, campus.corpId, campus.agentId, campus.id, dingKeyMap)
                    saasMessageSendService.sendDingMessage2teacher(bo)
                    log.info("------send upload avatar message ding msg send success------")
                } else {
                    log.warn("send ding upload error, userId@${userId}, userType@${userType}".toString())
                }

            } catch(Exception e) {
                log.error("数据发布通知异常，msg@${e.message}".toString())
            }
        }
    }

}

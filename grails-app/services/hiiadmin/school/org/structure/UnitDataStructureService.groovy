package hiiadmin.school.org.structure

import grails.gorm.transactions.Transactional
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.org.feature.UnitTeacherService
import hiiadmin.school.user.structure.StudentDataStructureService
import timetabling.building.DoorPlateUnit
import timetabling.org.Campus
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.org.UnitTeacher

@Transactional
class UnitDataStructureService {

    UnitTeacherService unitTeacherService

    UnitStudentService unitStudentService

    UnitService unitService

    StudentDataStructureService studentDataStructureService


    /**
     * 非行政班可以直接删除
     *      删除非行政班时要同步删除行政班和学生的关联关系
     * 行政班不可直接删除
     *      1、存在学生时不能进行删除操作(职校直接删除学生)
     *      2、存在任课老师时不能删除(职校直接删除)
     *      删除同时删除班级房间关系
     * @param unitId
     */
    void deleteUnit(Long unitId, Long userId = null, Byte userType = null) {
        Unit unit = unitService.fetchUnitById(unitId)
        Campus campus = Campus.findByIdAndStatus(unit?.campusId, 1 as byte)
        //行政班存在学生不能删除，非行政班要同步删除学生关联关系,删除老师班级关系
        if (unit.type == ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type) {
            if (unitStudentService.existUnitStudent4Unit(unitId)) {
                if (campus?.type == 2) {
                    List<UnitStudent> unitStudentList = UnitStudent.findAllByUnitIdAndStatus(unitId, UnitStudent.NORMAL)
                    unitStudentList.each {
                        studentDataStructureService.deleteStudent(campus.id, it.studentId, userId, userType)
                    }
                } else {
                    throw new HiiAdminException("请先删除学生后继续操作")
                }
            }
            List<UnitTeacher> unitTeacherList = unitTeacherService.fetchAllUnitTeacherByUnitId(unitId)
            if (unitTeacherList?.size() > 0) {
                unitTeacherList*.status = UnitTeacher.DELETE_UNIT
                unitTeacherList*.save(failOnError: true)
            }
            DoorPlateUnit doorPlateUnit = DoorPlateUnit.findByUnitIdAndStatus(unitId, 1 as byte)
            if (doorPlateUnit) {
                doorPlateUnit.status = DoorPlateUnit.UNIT_DELETE_STATUS
                doorPlateUnit.save(failOnError: true, flush: true)
            }
        } else {
            List<UnitStudent> unitStudentList = UnitStudent.findAllByUnitIdAndStatus(unitId, UnitStudent.NORMAL)
            if (unitStudentList) {
                unitStudentList*.status = UnitStudent.DELETE_UNIT
                unitStudentList*.save(failOnError: true, flush: true)
            }
            List<UnitTeacher> unitTeacherList = UnitTeacher.findAllByUnitIdAndStatus(unitId, UnitTeacher.NORMAL)
            if (unitTeacherList) {
                unitTeacherList*.status = UnitTeacher.DELETE_UNIT
                unitTeacherList*.save(failOnError: true, flush: true)
            }
        }
        unit.status = ConstantEnum.YearAndGradeAndUnitStatus.DELETE.status
        unitService.saveUnit(unit)
    }
}

package hiiadmin.school

import com.alibaba.fastjson.JSON
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.bugu.IdCardUtil
import com.bugu.ServiceResult
import com.google.common.base.Joiner
import com.google.common.base.Preconditions
import com.google.common.collect.HashMultimap
import com.google.common.collect.Lists
import com.google.common.collect.Multimap
import com.google.common.collect.Sets
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import groovyx.gpars.GParsPool
import hiiadmin.BizErrorCode
import hiiadmin.EnvService
import hiiadmin.UserService
import hiiadmin.exceptions.AlreadyExistException
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.ferries.FerriesStudentService
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.listen.event.person.StudentPersonEvent
import hiiadmin.listen.module.person.StudentDataChange
import hiiadmin.module.ListVM
import hiiadmin.pic.CheckFaceService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.user.feature.ParentStudentService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.UserNameCorrectUtil
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.scheduling.annotation.Async
import timetabling.BatchRecord
import timetabling.CardUser
import timetabling.ParentStudent
import timetabling.building.Bed
import timetabling.gated.FerriesStudent
import timetabling.humanface.pic.CheckFace
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.user.Student
import timetabling.user.UserEncodeInfo

import java.util.concurrent.TimeUnit

import static com.bugu.PhoenixCoder.decodeId
import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class StudentService {

    EnvService envService

    UserService userService

    UnitStudentService unitStudentService

    ParentStudentService parentStudentService

    FerriesStudentService ferriesStudentService

    GradeService gradeService

    UserEncodeInfoService userEncodeInfoService

    CardUserService cardUserService

    CheckFaceService checkFaceService

    @Autowired
    ApplicationContext applicationContext

    PersonDataChangeService personDataChangeService

    void updateStudent(long id, String name, String code, Boolean gender, String eName) {
        boolean runFlag = false
        Student student = Student.get(id)
        if (name && student.name != name) {
            student.name = name
            runFlag = true
        }

        if (code && student.code != code) {
            Long campusId = unitStudentService.fetchUnitStudentByStudentId(id)?.campusId
            Student s = fetchStudentByCampusIdIdAndCodeAndStudentIdNotEq(campusId, code, id)
            if (s) {
                throw new HiiAdminException("学号已存在")
            }
            student.code = code
            runFlag = true
        }
        if (gender && student.gender != gender) {
            student.gender = gender
            runFlag = true
        }
        if (eName && student.eName != eName) {
            student.eName = eName
            runFlag = true
        }
        if (runFlag) {
            student.save(failOnError: true, flush: true)
        }
    }

    void updateStudentAvatar(long studentId, String avatar) {
        Student student = Student.get(studentId)
        if (student) {
            student.pic = avatar
            student.save(failOnError: true, flush: true)
        }
    }

    void updateStudentIdCard(long studentId, String idCard) {
        def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
        if (!re.success) {
            throw new HiiAdminException(re.message)
        }
        Student s = userEncodeInfoService.fetchStudentByTrueIdCard(idCard)
        if (s && s.id != studentId) {
            throw new HiiAdminException("身份证号码已存在")
        }

        userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.STUDENT.type, studentId, UserEncodeInfoType.ID_CARD.type)
    }

    List<String> getFerriesAndStudentRelation(Long ferriesId, Long campusId) {
        List<String> relationList = []
        List<FerriesStudent> ferriesStudentList = ferriesStudentService.fetchAllFerriesStudentByFerriesIdAndCampusId(campusId, ferriesId)
        Set<Long> studentIdSet = new HashSet<>()
        if (ferriesStudentList != null && ferriesStudentList.size() > 0) {
            ferriesStudentList.each {
                FerriesStudent ferriesStudent ->
                    if (!studentIdSet.contains(ferriesStudent.studentId)) {
                        studentIdSet.add(ferriesStudent.studentId)
                        Student student = fetchStudentByIdViaCache(ferriesStudent.studentId)
                        if (student?.status == 1 as byte) {
                            StringBuilder relation = new StringBuilder()
                            if (StringUtils.isNotBlank(student.name)) {
                                relation.append(student.name)
                            }
                            if (StringUtils.isNotBlank(ferriesStudent.relation)) {
                                relation.append(ferriesStudent.relation)
                            }
                            if (StringUtils.isNotBlank(relation.toString())) {
                                relationList << relation.toString()
                            }
                        }
                    }

            }
        }
        return relationList
    }


    def fetchAllUnitStudentByStudentIdList(Long campusId, List<Long> studentIds) {
        UnitStudent.findAllByCampusIdAndStudentIdInListAndUnitTypeAndStatus(campusId, studentIds, 1 as byte, 1 as byte)
    }

    def fetchAllUnitStudentBySectionId(Long campusId, Long sectionId) {
        UnitStudent.findAllByCampusIdAndSectionIdAndStatus(campusId, sectionId, 1 as byte)
    }

    def fetchAllUnitStudentByGradeId(Long campusId, Long gradeId) {
        UnitStudent.findAllByCampusIdAndGradeIdAndStatus(campusId, gradeId, 1 as byte)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_cleaningStudentIds", key = "#unitIds + '_' + #gender + '_' + #studentIds",
            cacheNullValue = true, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    String cleaningStudentIds(String unitIds, Boolean gender, String studentIds) {
        if (!studentIds) {
            return studentIds
        }

        if (unitIds && gender) {
            String HQL = """SELECT s.id FROM Student s 
                                WHERE s.id IN (${studentIds}) 
                                    AND s.gender = :gender 
                                    AND s.status = 1 
                                    AND EXISTS ( SELECT us.id FROM  UnitStudent us WHERE  us.unitId IN (${
                unitIds
            }) AND us.studentId = s.id AND us.status = 1)"""
            def re = Student.executeQuery(HQL, [gender: gender])
            List<Long> idList = re.collect { it as Long }
            return Joiner.on(",").join(idList)

        } else if (unitIds) {
            String HQL = "SELECT studentId FROM UnitStudent WHERE unitId IN (${unitIds}) AND status = 1 AND studentId IN (${studentIds})"
            def re = UnitStudent.executeQuery(HQL)
            List<Long> idList = re.collect { it as Long }
            return Joiner.on(",").join(idList)
        } else if (gender) {
            String HQL = "SELECT id FROM Student WHERE id IN (${studentIds}) AND status = 1  AND gender = :gender "
            def re = Student.executeQuery(HQL, [gender: gender])
            List<Long> idList = re.collect { it as Long }
            return Joiner.on(",").join(idList)
        } else {
            return studentIds
        }

    }

    /*def findStudentFaceByCampusIdAndSearchValuePage(Long campusId, Boolean gender, String searchValue, Long gradeId, Long unitId, Long doorPlateId, int p, int s) {
        List<Student> studentList = findAllStudentByCampusIdAndSearchValue(campusId, gender, searchValue, null, gradeId, unitId, doorPlateId, null, null, 99, p, s)
        int total = countStudentByCampusIdAndSearchValue(campusId, gender, searchValue, null, gradeId, unitId, doorPlateId, null, null, 99)
        List<FaceGroupVO> faceGroupVOList = []
        if (studentList != null && studentList.size() > 0) {
            studentList.each {
                Student student ->
                    Bed bed = bedService.fetchBedByStudentId(student.id)
                    FaceGroupVO faceGroupVO = new FaceGroupVO(
                            userType: UserTypeEnum.TEACHER.type,
                            userId: student.id,
                            name: student?.name,
                            pic: student?.pic,
                            code: student?.code,
                    )
                    if (bed) {
                        if (StringUtils.isNotBlank(bed?.buildingName) || StringUtils.isNotBlank(bed?.doorPlateName))
                            faceGroupVO.dormRoom = bed?.buildingName + bed?.doorPlateName
                    }
                    faceGroupVOList << faceGroupVO
            }
        }
        [list: faceGroupVOList, total: total]
    }*/

    List<Student> findAllStudentByCampusIdAndSearchValue(Long campusId, Boolean gender, String searchValue, Long sectionId, Long gradeId, Long unitId, Long doorPlateId, Long layerId, Long buildingId, int resultStatus, int p, int s, Long facultyId, Long majorId) {
        StringBuffer sb = new StringBuffer()
        Map map = buildHQLStudentFace(sb, campusId, gender, searchValue, sectionId, gradeId, unitId, doorPlateId, layerId, buildingId, facultyId, majorId)
        if (resultStatus == 99 && p > 0 && s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        List<Student> students = Student.executeQuery(sb.toString(), map) as List<Student>

        List<Student> studentList = []
        if (resultStatus != 99) {
            if (resultStatus == YrCheckFaceResultStatus.NOPIC.status) {
                studentList = fetchAllStudentWithoutPic(students*.id, p, s)
            } else {
//                studentList = fetchAllStudentByPicStatus(students*.id, campusId, resultStatus, p, s)
                List<CheckFace> checkFaceList = checkFaceService.fetchAllUniqueCheckFace(campusId, students*.id, UserTypeEnum.STUDENT.type, p, s)
                studentList = fetchAllStudentByStudentIdInList(checkFaceList*.userId)
            }
        } else {
            return students
        }

        studentList
    }

    List<Student> findAllStudentByCampusIdAndSearchValueAndUnitIdPage(Long campusId,String searchValue, Long unitId,  int p, int s) {
        StringBuffer sb = new StringBuffer()
        Map map = buildHQLStudentFaceV2(sb, campusId, searchValue, unitId)
        if ( p > 0 && s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        List<Student> students = Student.executeQuery(sb.toString(), map) as List<Student>

        return students
    }

    int countStudentByCampusIdAndSearchValueV2(Long campusId,  String searchValue, Long unitId) {
        StringBuffer sb = new StringBuffer()
        Map map = buildHQLStudentFaceV2(sb, campusId, searchValue, unitId)
        List<Student> students = Student.executeQuery(sb.toString(), map) as List<Student>

        int total = 0

        total = countStudentByPicStatus(students*.id, campusId)

        return total
    }

    private static Map buildHQLStudentFaceV2(StringBuffer sb, Long campusId,  String searchValue, Long unitId) {
        Map map = [:]
        sb.append(" FROM Student s WHERE s.status = 1 ")

        if (StringUtils.isNotBlank(searchValue)) {
            sb.append(""" AND ( s.name LIKE :searchValue OR s.code LIKE :searchValue )""")
            map.put("searchValue", "%" + searchValue + "%")
        }

        sb.append(" AND s.id IN (SELECT u_s.studentId from UnitStudent u_s where u_s.campusId= :campusId")
        map.put("campusId", campusId)
        String studentUnit = " "
        if (unitId ) {

            if (unitId && unitId != -1L) {
                studentUnit = studentUnit + " AND u_s.unitId =:unitId"
                map.put("unitId", unitId)
            }

        }
        sb.append(studentUnit)
        sb.append(" and u_s.status = 1 and u_s.unitType = 1) ")

        return map
    }

    int countStudentByPicStatus(List<Long> studentIds, Long campusId) {
        String HQL = """ SELECT COUNT(s.id) FROM Student s LEFT JOIN CheckFace cf ON s.id = cf.userId
                                    WHERE s.id IN :studentIds 
                                    AND cf.campusId = :campusId
                                    AND cf.userType = :userType 
                                    AND cf.status = 1 """

        Student.executeQuery(HQL, [userType: UserTypeEnum.STUDENT.type, campusId: campusId, studentIds: studentIds])[0] as Integer
    }

    List<Student> fetchAllStudentWithoutPic(List<Long> studentIds, int p, int s) {
        if (p > 0 && s > 0) {
            Student.findAllByIdInListAndPicIsNull(studentIds, [max: s, offset: (p - 1) * s])
        } else {
            Student.findAllByIdInListAndPicIsNull(studentIds)
        }
    }

    List<Student> fetchAllStudentByPicStatus(List<Long> studentIds, Long campusId, int resultStatus, int p, int s) {
        String HQL = """ SELECT s FROM Student s LEFT JOIN CheckFace cf ON s.id = cf.userId
                                    WHERE s.id IN :studentIds 
                                    AND cf.campusId = :campusId
                                    AND cf.userType = :userType 
                                    AND cf.resultStatus = :resultStatus 
                                    AND cf.status = 1 """

        Student.executeQuery(HQL, [userType: UserTypeEnum.STUDENT.type, campusId: campusId, resultStatus: resultStatus, studentIds: studentIds, max: s, offset: (p - 1) * s])
    }

    int countStudentByPicStatus(List<Long> studentIds, Long campusId, int resultStatus) {
        String HQL = """ SELECT COUNT(s.id) FROM Student s LEFT JOIN CheckFace cf ON s.id = cf.userId
                                    WHERE s.id IN :studentIds 
                                    AND cf.campusId = :campusId
                                    AND cf.userType = :userType 
                                    AND cf.resultStatus = :resultStatus 
                                    AND cf.status = 1 """

        Student.executeQuery(HQL, [userType: UserTypeEnum.STUDENT.type, campusId: campusId, resultStatus: resultStatus, studentIds: studentIds])[0] as Integer
    }

    List<Student> findAllStudentByCampusIdAndSearchValue(Long campusId, String searchValue) {
        StringBuffer sb = new StringBuffer()
        Map map = buildHQLStudentFace(sb, campusId, null, searchValue, null, null, null, null, null, null, null, null)
        Student.executeQuery(sb.toString(), map)
    }

    int countStudentByCampusIdAndSearchValue(Long campusId, Boolean gender, String searchValue, Long sectionId, Long gradeId, Long unitId, Long doorPlateId, Long layerId, Long buildingId, int resultStatus, Long facultyId, Long majorId) {
        StringBuffer sb = new StringBuffer()
        Map map = buildHQLStudentFace(sb, campusId, gender, searchValue, sectionId, gradeId, unitId, doorPlateId, layerId, buildingId, facultyId, majorId)
        List<Student> students = Student.executeQuery(sb.toString(), map) as List<Student>

        int total = 0
        if (resultStatus != 99) {
            if (resultStatus == YrCheckFaceResultStatus.NOPIC.status) {
                List<Student> studentList = fetchAllStudentWithoutPic(students*.id, 0, 0)
                total = studentList?.size() ?: 0
            } else {
                total = countStudentByPicStatus(students*.id, campusId, resultStatus)
            }
        } else {
            total = students?.size() ?: 0
        }

        return total
    }

    private static Map buildHQLStudentFace(StringBuffer sb, Long campusId, Boolean gender, String searchValue, Long sectionId, Long gradeId, Long unitId, Long doorPlateId, Long layerId, Long buildingId, Long facultyId, Long majorId) {
        Map map = [:]
        sb.append(" FROM Student s WHERE s.status = 1 ")

        if (StringUtils.isNotBlank(searchValue)) {
            sb.append(""" AND ( s.name LIKE :searchValue OR s.code LIKE :searchValue )""")
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (gender != null) {
            sb.append(" AND s.gender=:gender")
            map.put("gender", gender)
        }

        sb.append(" AND s.id IN (SELECT u_s.studentId from UnitStudent u_s where u_s.campusId= :campusId")
        map.put("campusId", campusId)
        String studentUnit = " "
        if (sectionId || gradeId || unitId || facultyId || majorId) {

            if (sectionId) {
                studentUnit = studentUnit + " AND u_s.sectionId=:sectionId"
                map.put("sectionId", sectionId)
            }

            if (gradeId && gradeId != -1L) {
                studentUnit = studentUnit + " AND u_s.gradeId=:gradeId"
                map.put("gradeId", gradeId)
            }

            if (unitId && unitId != -1L) {
                studentUnit = studentUnit + " AND u_s.unitId =:unitId"
                map.put("unitId", unitId)
            }

            if (facultyId) {
                studentUnit = studentUnit + " AND u_s.facultyId = :facultyId "
                map.put("facultyId", facultyId)
            }

            if (majorId) {
                studentUnit = studentUnit + " AND u_s.majorId = :majorId "
                map.put("majorId", majorId)
            }

        }
        sb.append(studentUnit)
        sb.append(" and u_s.status = 1 and u_s.unitType = 1) ")

        if (doorPlateId && doorPlateId != -1) {
            sb.append(" AND EXISTS (SELECT b.id from Bed b where s.id=b.studentId AND b.doorPlateId=:doorPlateId AND b.status = 1)")
            map.put("doorPlateId", doorPlateId)
        } else if (layerId && layerId != -1) {
            sb.append(" AND EXISTS (SELECT b.id FROM Bed b WHERE s.id=b.studentId AND b.buildingLayerId=:layerId AND b.status = 1)")
            map.put("layerId", layerId)
        } else if (buildingId) {
            sb.append(" AND EXISTS (SELECT b.id FROM Bed b WHERE s.id=b.studentId AND b.buildingId=:buildingId AND b.status = 1)")
            map.put("buildingId", buildingId)
        }

        return map
    }

    Student updateStudentParentCountByStudentId(Long studentId) {
        Student student = fetchStudentById(studentId)
        updateStudentParentCount4Student(student)
    }

    Student updateStudentParentCount4Student(Student student) {
        Integer parentCount = ParentStudent.countByStudentIdAndStatus(student.id, 1 as byte)
        student.parentCount = parentCount
        student.save(failOnError: true, flush: true)
    }

    Multimap<Long, Long> getAllStudentIdParentIdMap(Long unitId) {
        String HQL = """SELECT ps.studentId, ps.parentId FROM UnitStudent us LEFT JOIN ParentStudent ps ON us.studentId = ps.studentId 
                                WHERE us.classUnitId = :unitId 
                                    AND us.status = 1 
                                    AND ps.status = 1"""
        def list = UnitStudent.executeQuery(HQL, [unitId: unitId])
        Multimap<Long, Long> studentIdParentIdMultimap = HashMultimap.create()
        if (list) {
            list.each {
                Long studentId = it[0] as Long
                Long parentId = it[1] as Long
                studentIdParentIdMultimap.put(studentId, parentId)
            }
        }
        studentIdParentIdMultimap
    }

    Multimap<Long, ParentStudent> getAllStudentIdParentStudentMap(Long unitId, boolean graduate = false) {
        byte status = UnitStudent.NORMAL
        if (graduate) {
            status = UnitStudent.FINISH
        }
        String HQL = """SELECT ps.studentId, ps FROM UnitStudent us LEFT JOIN ParentStudent ps ON us.studentId = ps.studentId 
                                WHERE us.unitId = :unitId 
                                    AND us.status = :status 
                                    AND ps.status = :status """
        def list = UnitStudent.executeQuery(HQL, [unitId: unitId, status: status])
        Multimap<Long, ParentStudent> studentIdParentIdMultimap = HashMultimap.create()
        if (list) {
            list.each {
                Long studentId = it[0] as Long
                ParentStudent parentStudent = it[1] as ParentStudent
                studentIdParentIdMultimap.put(studentId, parentStudent)
            }
        }
        studentIdParentIdMultimap
    }

    def updateStudentPassword(String studentIds) {
        if (studentIds) {
            String[] strings = studentIds.split(",")
            strings.each {
                Student student = fetchStudentById(it as Long)
                if (student) {
                    student.password = UserService.createStoredPassWord(student.code)
                    student.save(failOnError: true, flush: true)
                }
            }
        }
    }

    def updateStudentPassword(Long gradeId) {
        List<Student> studentList = fetchAllStudentByGradeId(gradeId, false)
        GParsPool.withPool(20) {
            studentList.eachParallel { Student student ->
                student.password = UserService.createStoredPassWord(student.code)
            }
        }
        studentList*.save(failOnError: true, flush: true)
    }

    Map<Long, Map<String, Long>> fetchAllStudentIdUnitIdAndGradeIdByCampusId(Long campusId, boolean cache = true) {
        List<UnitStudent> unitStudentList = UnitStudent.findAllByCampusIdAndUnitTypeAndStatus(campusId, UnitType.ADMINISTRATIVE_CLASS.type, 1 as byte)
        Map<Long, Map<String, Long>> map = [:]
        unitStudentList.each {
            unitStudent ->
                map.put(unitStudent.studentId, [unitId: unitStudent.unitId, gradeId: unitStudent.gradeId, sectionId: unitStudent.sectionId])
        }
        map
    }

    /**
     * 查询班级学生列表
     * 不包含换班及已删除的。
     * @param schoolId 加上schoolId 可以走数据库索引
     * @param unitId
     * @param p
     * @param s
     * @return
     */
    List<Student> fetchAllStudentByUnitId(Long campusId, Long unitId, Integer p = null, int s = 30) {
        String HQL = """SELECT s FROM Student s 
                            WHERE s.status = :status
                            AND EXISTS ( SELECT u_s.id FROM UnitStudent u_s WHERE u_s.unitId = :unitId AND u_s.status = :status AND u_s.studentId = s.id AND u_s.campusId = :campusId)"""
        if (p) {
            return Student.executeQuery(HQL, [
                    campusId: campusId,
                    unitId  : unitId,
                    status  : UnitStudent.NORMAL,
                    max     : s,
                    offset  : (p - 1) * s
            ])
        } else {
            return Student.executeQuery(HQL, [
                    campusId: campusId,
                    unitId  : unitId,
                    status  : UnitStudent.NORMAL
            ])
        }
    }

    void saveBatchRecord(BatchRecord batchRecord) {
        batchRecord.save(failOnError: true)
    }

    Map<Long, List<Student>> getUnitIdStudentsMapByGradeId(Long gradeId) {
        String HQL = """SELECT u_s.unitId ,s FROM UnitStudent u_s ,Student s
                         WHERE u_s.studentId = s.id AND
                         u_s.unitType = :type AND
                         u_s.gradeId = :gradeId AND
                         u_s.status = :status"""
        def list = Unit.executeQuery(HQL, [
                type   : UnitType.ADMINISTRATIVE_CLASS.type,
                gradeId: gradeId,
                status : UnitStudent.NORMAL
        ])
        Map<Long, List<Student>> map = [:]
        list.each {
            Long unitId = it[0] as Long
            Student student = it[1] as Student
            if (!map[unitId]) {
                map[unitId] = []
            }
            !map.get(unitId).add(student)
        }
        map
    }

    List<Student> fetchAllStudentByUnitIdAndCourseId(long unitId, long courseId) {
        String HQL = """SELECT s FROM Student s, UnitStudent u WHERE s.id = u.studentId AND
                            u.unitId = :unitId AND
                            u.unitType IN :unitTypes AND
                            u.courseId = :courseId AND
                            u.status >= :status"""
        Student.executeQuery(HQL, [
                unitId   : unitId,
                courseId : courseId,
                unitTypes: [UnitType.ELECTIVE_CLASS.type, UnitType.NATURAL_CLASS.type],
                status   : UnitStudent.NORMAL
        ])
    }

    Integer countStudentByUnitIdInList(List<Long> unitIdList) {
        String HQL = """SELECT COUNT(s.id) FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status = :status"""
        Student.executeQuery(HQL, [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL,
        ])[0] as Integer
    }

    List<Student> fetchAllStudentByUnitIdInListLimit(List<Long> unitIdList, int p, int s) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status = :status"""

        Map params = [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status: UnitStudent.NORMAL
        ]
        if (p == 0) {
            params.put("max", s)
            params.put("offset", (p - 1) * s)
        }
        Student.executeQuery(HQL, params)
    }

    /**
     * 移除选科已确认的学生
     * @param unitIdList
     * @param p
     * @param s
     * @return
     */
    List<Student> fetchAllStudentByUnitIdInListLimitRemoveConfirmed(List<Long> unitIdList, Long optionId, int p, int s) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status >= :status AND 
                            NOT EXISTS (SELECT 1 FROM OptionRecord o WHERE o.studentId = s.id AND o.confirmState = 1 AND o.status = 1 AND o.optionId = :optionId) """
        Student.executeQuery(HQL, [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL,
                optionId: optionId,
                max     : s,
                offset  : (p - 1) * s
        ])
    }

    List<Student> fetchAllStudentByUnitIdInList(List<Long> unitIdList) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status >= :status"""
        Student.executeQuery(HQL, [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL,
        ])
    }

    /**
     * 正常学生
     * @param unitIdList
     * @return
     */
    List<Student> fetchAllNormalStudentByUnitIdInList(List<Long> unitIdList) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status = :status"""
        Student.executeQuery(HQL, [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL,
        ])
    }

    List<Student> fetchAllStudentByUnitIdInList4Suspend(List<Long> unitIdList) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIds AND
                            u.unitType = :unitType AND
                            u.status IN (-1, 0, 1, 6, 7)"""
        Student.executeQuery(HQL, [
                unitIds : unitIdList,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type
        ])
    }

    List<Student> fetchStudentListByStudentIdList(List<Long> longList, boolean normal = true) {
        if (longList == null || longList.size() < 1) {
            return Lists.newArrayList()
        }
        if (normal) {
            return Student.findAllByIdInListAndStatus(longList, 1 as Byte)
        } else {
            return Student.findAllByIdInListAndStatusInList(longList, [0 as byte, 1 as byte, 7 as byte])
        }
    }

    ListVM<Student> fetchAllStudentByLimit(Long schoolId, Long campusId, Long sectionId, Long gradeId, Long unitId, Byte type, int p, int s, boolean graduate = false, List<Long> manageUnitIds = []) {
        def list = UnitStudent.createCriteria().list(max: s, offset: (p - 1) * s) {
            eq("schoolId", schoolId)
            if (campusId) {
                eq("campusId", campusId)
            }
            if (sectionId) {
                eq("sectionId", sectionId)
            }
            if (gradeId) {
                eq("gradeId", gradeId)
            }
            if (unitId) {
                eq("unitId", unitId)
            } else if (manageUnitIds.size() > 0) {
                'in'("unitId", manageUnitIds)
            }
            if (type) {
                eq("unitType", type)
            } else if (schoolId || campusId || sectionId || gradeId) {
                eq("unitType", UnitType.ADMINISTRATIVE_CLASS.type)
            }

            if (graduate) {
                eq("status", UnitStudent.FINISH)
            } else {
                eq("status", UnitStudent.NORMAL)
            }


            order("campusId", "asc")
            order("sectionId", "asc")
            order("gradeId", "asc")
            order("unitId", "asc")
            order("studentId", "asc")
        }
        List<Long> studentIdList = list*.studentId
        Integer totalCount = list?.totalCount
        List<Student> studentList = fetchAllStudentByStudentIdInList(studentIdList)
        new ListVM<Student>(studentList, totalCount)
    }

    def fetchAllStudentByLimit(Long campusId, Long unitId, int p, int s) {
        def list = UnitStudent.createCriteria().list(max: s, offset: (p - 1) * s) {
            eq("campusId", campusId)
            if (unitId) {
                eq("unitId", unitId)
            }
            eq("unitType", UnitType.ADMINISTRATIVE_CLASS.type)
            eq("status", UnitStudent.NORMAL)
            order("studentId", "asc")
        }
        List<Long> studentIdList = list*.studentId
        Integer totalCount = list?.totalCount
        List<Student> studentList = fetchAllStudentByStudentIdInList(studentIdList)
        [list: studentList, total: totalCount]
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchSCUBUserId", key = "#studentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    CardUser fetchStudentCardUserByUserId(Long studentId, boolean cache = true) {
        CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 1 as byte)
    }

    List<CardUser> fetchStudentCardUserInStudentList(List<Long> studentIds) {
        CardUser.findAllByUserIdInList(studentIds)
    }

    Map<Long, String> getByStudentIdListAllCard(List<Long> userIdList) {
        List<CardUser> cardUserList = fetchStudentCardUserInStudentList(userIdList)
        Map<Long, String> map = [:]
        cardUserList.each {
            CardUser cardUser ->
                map.put(cardUser.userId, cardUser.cardNum)
        }
        return map
    }

    Map<Long, String> getAllStudentIdCard(boolean graduate = false) {
        byte status = 1 as byte
        if (graduate) {
            status = CardUser.USER_DELETE_STATUS as byte
        }
        String HQL = """SELECT userId, cardNum FROM CardUser WHERE type = :type AND status = :status"""
        def re = CardUser.executeQuery(HQL, [type: UserTypeEnum.STUDENT.type, status: status])
        Map<Long, String> map = [:]
        if (re) {
            re.each {
                Long userId = it[0] as Long
                String cardNum = it[1] as String
                map[userId] = cardNum
            }
        }
        map
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchSIdUBCampusId", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache", postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    Map<Long, Unit> fetchStudentIdUnitByCampusId(Long campusId, boolean cache = true) {
        String HQL = """SELECT u_s.studentId, u FROM UnitStudent u_s LEFT JOIN Unit u ON u_s.unitId = u.id WHERE u_s.campusId = :campusId AND u_s.unitType = 1 AND u_s.status = 1 AND u.campusId = :campusId AND u.type = 1 AND u.status = 1"""
        def list = UnitStudent.executeQuery(HQL, [campusId: campusId])
        Map<Long, Unit> map = [:]
        list.each {
            Long studentId = it[0] as Long
            Unit unit = it[1] as Unit
            map.put(studentId, unit)
        }
        map
    }

    List<Student> fetchAllStudentByStudentIdInList(List<Long> studentIdList) {
        if (!studentIdList || studentIdList.size() < 1) {
            return Lists.newArrayList()
        }
        return Student.findAllByIdInList(studentIdList)
    }

    Map<Long, Student> transformationStudentMap(List<Long> studentIdList) {
        List<Student> studentList = fetchAllStudentByStudentIdInList(studentIdList)
        Map<Long, Student> studentMap = [:]
        if (studentList == null || studentList.size() < 1) {
            return studentMap
        }
        studentList.each {
            Student student ->
                if (student?.id) {
                    studentMap.put(student.id, student)
                }
        }
        return studentMap
    }

    List<Student> fetchAllStudentByStudentIds(String studentIds) {
        if (studentIds) {
            String HQL = """FROM Student WHERE id IN (${studentIds}) AND status = 1"""
            return Student.executeQuery(HQL)
        }
        return []
    }

    /**
     * 清除班级与学生的关系介绍
     * 当前仅支持非行政班
     * @param unitId
     * @param studentId
     */
    void removeStudent4Unit(Long unitId, Long studentId) {
        UnitStudent unitStudent = UnitStudent.findByStudentIdAndUnitIdAndUnitTypeNotEqualAndStatus(
                studentId, unitId, UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.NORMAL)
        unitStudent.status = 0 as byte
        unitStudent.save(failOnError: true, flush: true)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchASBCampusId", key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 20, timeUnit = TimeUnit.MINUTES)
    List<Student> fetchAllStudentByCampusId(Long campusId, boolean cache = true) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.campusId = :campusId AND
                            u.unitType = :unitType AND
                            u.status = :status"""
        Student.executeQuery(HQL, [
                campusId: campusId,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL
        ])
    }

//    @Cached(
//            expire = REDIS_EXPIRE_HOUR_12, timeUnit = TimeUnit.SECONDS,
//            name = "hiiadmin_studentS_fetchSBStudentId", key = "#studentId",
//            cacheNullValue = false, cacheType = CacheType.REMOTE,
//            condition = "#cache eq null or #cache"
//    )
//    @CacheRefresh(
//            refresh = REDIS_EXPIRE_MINUTES_1, stopRefreshAfterLastAccess = REDIS_EXPIRE_MINUTES_10, timeUnit = TimeUnit.SECONDS
//    )
    Student fetchStudentById(Long studentId) {
        return Student.get(studentId)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentService_fetchStudentByStudentId_V2", key = "#studentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    Student fetchStudentByStudentId(Long studentId, boolean cache = true) {
        Student.findByIdAndStatus(studentId, 1 as Byte)
    }

    //TODO 优化，和上边合并
    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchStudentByIdViaCache", key = "#studentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, postCondition = "result ne null")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    Student fetchStudentByIdViaCache(Long studentId) {
        return Student.get(studentId)
    }

    Integer countNormalAdminStudentByCampusId(Long campusId) {
        UnitStudent.countByCampusIdAndUnitTypeAndStatus(campusId,
                UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.NORMAL)
    }

    Integer countNormalAdminStudentByCampusIdAndSectionId(Long campusId, Long sectionId, boolean graduate = false) {
        if (graduate) {
            UnitStudent.countByCampusIdAndSectionIdAndUnitTypeAndStatus(campusId, sectionId,
                    UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.FINISH)
        } else {
            UnitStudent.countByCampusIdAndSectionIdAndUnitTypeAndStatus(campusId, sectionId,
                    UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.NORMAL)
        }
    }

    /**
     * 查询年级学生列表
     * 不包含换班及已删除的。
     * 仅查询行政班
     * @param unitId
     * @return
     */
    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchASBGradeId", key = "#gradeId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 3, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    List<Student> fetchAllStudentByGradeId(Long gradeId, boolean cache = true) {
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.gradeId = :gradeId AND
                            u.unitType = :unitType AND
                            u.status = :status"""
        Student.executeQuery(HQL, [
                gradeId : gradeId,
                unitType: UnitType.ADMINISTRATIVE_CLASS.type,
                status  : UnitStudent.NORMAL
        ])
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchASBUnitId", key = "#unitId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 3, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    List<Student> fetchAllStudentByUnitId(Long unitId, boolean graduate = false, boolean cache = true) {
        byte status = UnitStudent.NORMAL
        if (graduate) {
            status = UnitStudent.FINISH
        }
        String HQL = """SELECT s FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId = :unitId AND
                            u.status = :status AND
                            u.unitType = 1"""
        Student.executeQuery(HQL, [
                unitId: unitId,
                status: status
        ])
    }

    List<ParentStudent> fetchAllParentStudentByStudentId(Long studentId) {
        return ParentStudent.findAllByStudentIdAndStatus(studentId, 1 as byte)
    }


    Integer countNormalAdminStudentByGradeId(Long gradeId, boolean graduate = false) {
        if (graduate) {
            UnitStudent.countByGradeIdAndUnitTypeAndStatus(gradeId,
                    UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.FINISH)
        } else {
            UnitStudent.countByGradeIdAndUnitTypeAndStatus(gradeId,
                    UnitType.ADMINISTRATIVE_CLASS.type, UnitStudent.NORMAL)
        }
    }

    Integer countStudentByUnitId(Long unitId, boolean graduate = false) {
        Byte status = UnitStudent.NORMAL
        String HQL = """SELECT COUNT(s.id) FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId = :unitId AND
                            u.status = :status"""
        if (graduate) {
            status = UnitStudent.FINISH
        }
        Student.executeQuery(HQL, [
                unitId: unitId,
                status: status,
        ])[0] as Integer
    }

    Map<Long, Integer> countStudentByUnitIdList(List<Long> unitIdList, boolean graduate = false) {
        Map<Long, Integer> studentCountMap = [:]
        if (unitIdList) {
            Byte status = UnitStudent.NORMAL
            String HQL = """SELECT u.unitId, COUNT(s.id) FROM Student s, UnitStudent u
                            WHERE s.id = u.studentId AND
                            u.unitId IN :unitIdList AND
                            u.status = :status AND
                            u.unitType = :unitType
                            GROUP BY u.unitId """
            if (graduate) {
                status = UnitStudent.FINISH
            }
            def result = Student.executeQuery(HQL, [
                    unitIdList: unitIdList,
                    status  : status,
                    unitType: UnitType.ADMINISTRATIVE_CLASS.type
            ])

            result?.each {
                Long unitId = it[0] as Long
                Integer count = it[1] as Integer
                studentCountMap.put(unitId, count)
            }
        }

        studentCountMap
    }

    Student fetchStudentByIdCardNoEqStudentId(String idCard, Long studentId) {
        Student.findByIdCardAndIdNotEqualAndStatus(idCard, studentId, 1 as byte)
    }

    // 上面student表里存在相同数据
    Student fetchStudentByIdCardNotEqualStudent(String idCard, Long studentId) {
        String HQL = """ SELECT s FROM Student s, UnitStudent us
                            WHERE s.id = us.studentId 
                            AND s.id = :studentId
                            AND s.status = 1
                            AND us.status = 1
                            AND s.idCard = :idCard """
        List<Student> studentList = Student.executeQuery(HQL, [studentId: studentId, idCard: idCard])
        if (studentList) {
            return studentList[0] as Student
        }
        null
    }

    Student createAndSaveNewStudent(Unit unit, String name, String eName, String code, Boolean gender, String avatar, String idCard) {
        long unitId = unit.id
        if (idCard) {
            //仅线上进行完全校检
            def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
            if (!re.success) {
                throw new HiiAdminException(re.message)
            }
            Preconditions.checkState(re.success, re.message)
            Student s = userEncodeInfoService.fetchStudentByTrueIdCard(idCard)
            if (s) {
                throw new HiiAdminException("该身份证号学生已存在")
            }
        }
        if (eName && !PatternUtils.isEName(eName)) {
            throw new HiiAdminException("""英文名仅能包含英文、数字、空格、'.'、'·'""")
        }
        Long schoolId = unit.schoolId
        Long campusId = unit.campusId
        Student s = fetchStudentByCampusIdIdAndCode(campusId, code)
        if (s) {
            throw new HiiAdminException("该校区当前学号已存在")
        }
        String password = userService.createStoredPassWord(code)
        Student student = new Student(
                code: code,
                eName: eName,
                name: name,
                simpleName: UserNameCorrectUtil.extractInitialsName(name),
                schoolId: schoolId,
                status: 1 as byte,
                gender: gender,
                password: password,
                pic: avatar ? PatternUtils.getFileName(avatar) : null
        )
        saveStudent(student)
        createUnitStudent(unit, student.id)
        userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.STUDENT.type, student.id, UserEncodeInfoType.ID_CARD.type)
        student
    }

    Student createAndSaveNewStudentV2(Unit unit, String name, String eName, String code, Boolean gender, String avatar, String idCard, String cardNum, String livePlace) {
        long unitId = unit.id
        if (idCard) {
            //仅线上进行完全校检
            def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
            if (!re.success) {
                throw new HiiAdminException(re.message)
            }
            Preconditions.checkState(re.success, re.message)
            Student s = userEncodeInfoService.fetchStudentByTrueIdCard(idCard)
            if (s) {
                throw new HiiAdminException("该身份证号学生已存在")
            }
        }
        if (eName && !PatternUtils.isEName(eName)) {
            throw new HiiAdminException("""英文名仅能包含英文、数字、空格、'.'、'·'""")
        }

        if (livePlace) {
            if (livePlace.length() > 200) {
                throw new HiiAdminException("家庭住址字数超出限制200")
            }
        }
        Long schoolId = unit.schoolId
        Long campusId = unit.campusId
        Student s = fetchStudentByCampusIdIdAndCode(campusId, code)
        if (s) {
            throw new HiiAdminException("该校区当前学号已存在")
        }
        String password = userService.createStoredPassWord(code)
        Student student = new Student(
                code: code,
                eName: eName,
                name: name,
                simpleName: UserNameCorrectUtil.extractInitialsName(name),
                schoolId: schoolId,
                status: 1 as byte,
                gender: gender,
                password: password,
                pic: avatar ? PatternUtils.getFileName(avatar) : null,
                livePlace: livePlace
        )
        saveStudent(student)
        createUnitStudent(unit, student.id)

        //完善checkFace信息
        if (student.pic) {
            CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUrlMd5AndName(campusId, student.pic, student.name)
            if (checkFace) {
                checkFace.userId = student.id
                checkFace.userName = student.name
                checkFaceService.saveCheckFace(checkFace)
            }
        }

        if (cardNum) {
            cardUserService.createOrUpdateCardUser4student(schoolId, student?.id, cardNum)
        }
        userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.STUDENT.type, student.id, UserEncodeInfoType.ID_CARD.type)
        student
    }

    void createUnitStudent(Unit unit, Long studentId) {
        UnitStudent unitStudent = UnitStudent.findOrCreateByUnitIdAndStudentIdAndStatus(unit.id, studentId, UnitStudent.NORMAL)
        if (!unitStudent.gradeId) {
            Grade grade = gradeService.fetchGradeById(unit.gradeId)
            unitStudent.schoolId = grade.schoolId
            unitStudent.campusId = grade.campusId
            unitStudent.sectionId = grade.sectionId
            unitStudent.schoolYearId = grade.schoolYearId
            unitStudent.gradeId = grade.id
            unitStudent.unitType = unit.type
            unitStudent.facultyId = unit.facultyId
            unitStudent.majorId = unit.majorId
            unitStudent.save(failOnError: true, flush: true)
        }
    }

    Student fetchStudentByCampusIdIdAndCode(Long campusId, String code) {
        log.info('find student by code not cached. campusId:{} code:{}', campusId, code)
        String HQL = """FROM Student s 
                            WHERE s.code = :code 
                                AND s.status = 1 
                                AND EXISTS ( SELECT 1 FROM UnitStudent us WHERE us.campusId = :campusId AND us.studentId = s.id AND us.status = 1 )"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId: campusId,
                code    : code
        ])
        if (list?.size() > 0) {
            return list[0] as Student
        }
        null
    }

    List<Student> fetchStudentByCampusIdIdAndName(Long campusId, String name) {
//        log.info('find student by code not cached. campusId:{} name:{}', campusId, name)
        String HQL = """FROM Student s 
                            WHERE s.name = :name 
                                AND s.status = 1 
                                AND EXISTS ( SELECT 1 FROM UnitStudent us WHERE us.campusId = :campusId AND us.studentId = s.id AND us.unitType = 1 AND us.status = 1 )"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId: campusId,
                name    : name
        ])
        list
    }

    List<Student> fetchStudentByCampusIdIdAndNameLikeOrCodeLike(Long campusId, String searchValue) {
        log.info('find student by code not cached. campusId:{} searchValue:{}', campusId, searchValue)
        String HQL = """FROM Student s 
                            WHERE (s.name LIKE :searchValue OR s.code LIKE :searchValue) 
                                AND s.status = 1 
                                AND EXISTS ( SELECT 1 FROM UnitStudent us WHERE us.campusId = :campusId AND us.studentId = s.id AND us.status = 1 )"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId   : campusId,
                searchValue: "%" + searchValue + "%"
        ])
        list
    }

    List<Student> fetchStudentByCampusIdIdAndCodeList(Long campusId, List<String> codeList) {
        String HQL = """FROM Student s 
                            WHERE s.code in :codeList 
                                AND s.status = 1 
                                AND EXISTS ( SELECT 1 FROM UnitStudent us WHERE us.campusId = :campusId AND us.studentId = s.id AND us.unitType = 1 AND us.status = 1 )"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId: campusId,
                codeList: codeList
        ])
        list
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_studentS_fetchStudentByCampusIdIdAndCode", key = "#campusId + '_' + #code",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 20, timeUnit = TimeUnit.MINUTES)
    Student fetchStudentByCampusIdIdAndCode4Cache(Long campusId, String code) {
        String HQL = """FROM Student s 
                            WHERE s.code = :code 
                                AND s.status = 1 
                                AND EXISTS ( SELECT 1 FROM UnitStudent us WHERE us.campusId = :campusId AND us.studentId = s.id AND us.status = 1 )"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId: campusId,
                code    : code
        ])
        if (list?.size() > 0) {
            return list[0] as Student
        }
        null
    }

    /**
     *
     * @param student
     * @param campusId 如果需要同步 需要指定校区
     */
    void saveStudent(Student student) {
        student.save(failOnError: true, flush: true)
    }

    void saveAllStudent(List<Student> studentList) {
        studentList*.save(failOnError: true, flush: true)
    }

    void checkAndChangeStudentUnit(long studentId, Unit unit) {
        long unitId = unit.id
        UnitStudent us = unitStudentService.fetchUnitStudentByStudentId(studentId)
        if (us.studentId != unitId) {
            changeStudent2NewUnit(studentId, unit)
        }
    }
    /**
     * 跨校区专班判断学号，不允许重复
     * @param studentId
     * @param newUnit
     */
    void changeStudent2NewUnit(Long studentId, Unit newUnit) {
        UnitStudent unitStudent = UnitStudent.findByStudentIdAndUnitTypeAndStatus(studentId,
                UnitType.ADMINISTRATIVE_CLASS.type, RelationStatus.NORMAL.status)
        Long oldCampusId = unitStudent.campusId
        if (newUnit.campusId != oldCampusId) {
            Student student = fetchStudentById(studentId)
            Student s = fetchStudentByCampusIdIdAndCodeAndStudentIdNotEq(newUnit.campusId, student.code, student.id)
            if (s) {
                throw new AlreadyExistException("新班级所在校区已存在相同学号，请订正后尝试")
            }
        }
        Bed bed = Bed.findByStudentIdAndStatus(studentId, 1 as byte)
        if (bed) {
            bed.unitId = newUnit.id
            bed.save(failOnError: true, flush: true)
        }
        unitStudent.status = UnitStudent.CHANGE
        unitStudent.save(failOnError: true, flush: true)
        createUnitStudent(newUnit, studentId)

        if (newUnit.campusId != oldCampusId) {
            pushStudentPersonEvent(oldCampusId, studentId)
        }
        pushStudentPersonEvent(newUnit.campusId, studentId)
    }

    Student fetchStudentByCampusIdIdAndCodeAndStudentIdNotEq(Long campusId, String code, Long studentId) {
        log.info('find student by code not cached. campusId:{} code:{}', campusId, code)
        String HQL = """SELECT s FROM UnitStudent u_s ,Student s WHERE u_s.studentId = s.id AND
                        u_s.campusId = :campusId AND
                        s.code = :code AND
                        s.status = :s_status AND
                        u_s.status = :u_s_status AND
                        s.id != :studentId"""
        List<Student> list = Student.executeQuery(HQL, [
                campusId  : campusId,
                code      : code,
                s_status  : 1 as byte,
                u_s_status: UnitStudent.NORMAL,
                studentId : studentId
        ])
        if (list) {
            return list[0] as Student
        }
        null
    }

    Long getStudentIdBySchoolIdAndCardNum(Long schoolId, String cardNum) {
        CardUser.findBySchoolIdAndCardNumAndTypeAndStatus(schoolId, cardNum, UserTypeEnum.STUDENT.type, 1 as byte)?.userId
    }

    List<CardUser> fetchAllCardUserViaUserIdListAndType(List<Long> userIdList, Byte type) {
        CardUser.findAllByTypeAndStatusAndUserIdInList(type, 1 as byte, userIdList)
    }

    /**
     * @param schoolId
     * @param studentId
     * @param cardNum
     */
//TODO 批量导入卡号不同步海康暂时使用这一个方法 CardUserService
    void createAndSaveCardUser(Long schoolId, Long studentId, String cardNum) {
        CardUser cardUser = CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 1 as byte)
        if (cardUser?.cardNum != cardNum) {
            if (cardUser) {
                cardUser.status = CardUser.CARD_DELETE_STATUS
                cardUser.save(failOnError: true, flush: true)
            }
            Long sId = getStudentIdBySchoolIdAndCardNum(schoolId, cardNum)
            //同一个学校下卡号不能重复
            if (sId && sId != studentId) {
                throw new AlreadyExistException("卡号已存在")
            }
            CardUser c = new CardUser(
                    schoolId: schoolId,
                    cardNum: cardNum,
                    userId: studentId,
                    type: UserTypeEnum.STUDENT.type,
                    status: 1 as byte
            )
            c.save(failOnError: true, flush: true)
        }
    }

    void saveCardUser(CardUser cardUser) {
        cardUser.save(failOnError: true, flush: true)
    }

    void verificationStudentCodeInCampus(String code, Long campusId, BatchRecord batchRecord) {
        Student student = fetchStudentByCampusIdIdAndCode(campusId, code)
        if (student) {
            batchRecord.status = -1 as byte
            batchRecord.msg = """学号有重复 学号：${code}"""
            batchRecord.save(failOnError: true)
        }
    }

    void verificationStudentIdCard(String idCard, BatchRecord batchRecord) {
        Student student = fetchStudentByIdCard(idCard)
        if (student) {
            batchRecord.status = -1 as byte
            batchRecord.msg = """身份证号：${idCard} 对应的身份证号有重复存在"""
            batchRecord.save(failOnError: true)
        }
    }

    Student fetchStudentByIdCard(String idCard) {
        Student.findByIdCardAndStatus(idCard, 1 as byte)
    }

    void upgradeAndCreateNewUnitStudent(Unit oldUnit, Unit newUnit) {
        List<UnitStudent> unitStudentList = UnitStudent.findAllByUnitIdAndStatus(oldUnit.id, UnitStudent.NORMAL)
        List<Long> studentIdList = []
        unitStudentList.each {
            it.status = UnitStudent.UPGRADE
            studentIdList << it.studentId
        }
        Long newUnitId = newUnit.id
        Grade newGrade = Grade.get(newUnit.gradeId)
        Long schoolId = newGrade.schoolId
        Long campusId = newGrade.campusId
        Long schoolYearId = newGrade.schoolYearId
        Long gradeId = newGrade.id
        Byte newUnitType = newUnit.type
        List<UnitStudent> newUnitStudent = []
        studentIdList.each {
            Long studentId ->
                UnitStudent unitStudent = new UnitStudent(
                        schoolYearId: schoolYearId,
                        schoolId: schoolId,
                        campusId: campusId,
                        sectionId: newGrade.sectionId,
                        gradeId: gradeId,
                        unitId: newUnitId,
                        unitType: newUnitType,
                        studentId: studentId,
                        status: UnitStudent.NORMAL,
                )
                newUnitStudent << unitStudent
        }
        unitStudentList*.save(failOnError: true, flush: true)
        newUnitStudent*.save(failOnError: true, flush: true)
    }

    @Async
    void createAndSaveStudentBatch(Long schoolId, Long campusId, Long sectionId, Long gradeId, String studentJson, BatchRecord batchRecord) {
        List<JSONStudent> jsonStudentList = JSON.parseArray(studentJson, JSONStudent.class)
        Set<Integer> unitCodeSet = Sets.newHashSet()
        Map<Integer, List<Student>> unitCodeStudentListMap = [:]
        Set<String> idCardSet = Sets.newHashSet()
        Set<String> studentCodeSet = []
        List<Student> oldStudentList = fetchAllStudentByGradeId(gradeId, false)
        if (oldStudentList && oldStudentList.size() > 0) {
            batchRecord.status = -1 as byte
            batchRecord.msg = "导入前需删除该年级下所有学生"
            batchRecord.save(failOnError: true)
            return
        }
//        gradeService.deleteAllStudent(gradeId)
        for (int i = 0; i < jsonStudentList?.size(); i++) {
            JSONStudent jsonStudent = jsonStudentList.get(i)
            String info = jsonStudent.unit
            if (!info.integer) {
                batchRecord.status = -1 as byte
                batchRecord.msg = """该年级code:${info}对应的班级不存在"""
                batchRecord.save(failOnError: true)
                return
            }
            Integer unitCode = Integer.valueOf(info)
            unitCodeSet.add(unitCode)
            String name = jsonStudent.name
            String code = jsonStudent.code
//            if (i == 0) {
//                baseStudentCode = code.substring(0, 2)
//            } else {
//                String studentCode = code.substring(0, 2)
//                if (baseStudentCode != studentCode) {
//                    batchRecord.status = -1 as byte
//                    batchRecord.msg = """学号：${code}，姓名：${name} 。学号格式异常"""
//                    batchRecord.save(failOnError: true)
//                    return
//                }
//            }
            studentCodeSet.add(code)
            if ((i + 1) != studentCodeSet.size()) {
                batchRecord.status = -1 as byte
                batchRecord.msg = """学号有重复 学号：${code}，姓名：${name}"""
                batchRecord.save(failOnError: true)
                return
            }
            verificationStudentCodeInCampus(code, campusId, batchRecord)
            Boolean gender = jsonStudent.gender == "男"
            String idCard = jsonStudent.idCard
            if (idCard) {
                if (idCard in idCardSet) {
                    batchRecord.status = -1 as byte
                    batchRecord.msg = """学号：${code}，姓名：${name} 对应的身份证号有重复存在"""
                    batchRecord.save(failOnError: true)
                    return
                } else {
                    verificationStudentIdCard(idCard, batchRecord)
                    idCardSet.add(idCard)
                }
                //仅线上进行完全校检
                def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
                if (!re.success) {
                    batchRecord.status = -1 as byte
                    batchRecord.msg = """学号：${code}，姓名：${name} 对应的身份证号不合法"""
                    batchRecord.save(failOnError: true)
                    return
                }
            }
            String password = userService.createStoredPassWord(code)
            Student student = new Student(
                    code: code,
                    name: name,
                    schoolId: schoolId,
                    campusId: campusId,
                    status: 1 as byte,
                    gender: gender,
                    password: password
            )
            student.save(failOnError: true, flush: true)
            if (!unitCodeStudentListMap[unitCode]) {
                unitCodeStudentListMap.put(unitCode, [])
            }
            unitCodeStudentListMap[unitCode] << student
            userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.STUDENT.type, student.id, UserEncodeInfoType.ID_CARD.type)
        }
        List<Long> studentIdList = []
        Grade grade = gradeService.fetchGradeById(gradeId)
        for (int j = 0; j < unitCodeSet?.size(); j++) {
            Integer unitCode = unitCodeSet[j]
            Unit unit = Unit.findBySchoolIdAndCampusIdAndGradeIdAndCodeAndStatus(schoolId, campusId,
                    gradeId, unitCode.toString(), YearAndGradeAndUnitStatus.NORMAL.status)
            if (!unit) {
                batchRecord.status = -1 as byte
                batchRecord.msg = """该年级code:${unitCode}对应的班级不存在"""
                batchRecord.save(failOnError: true)
                return
            }
            List<Student> studentList = unitCodeStudentListMap.get(unitCode)
            List<UnitStudent> unitStudentList = []
            studentList.each {
                Student student ->
                    UnitStudent unitStudent = new UnitStudent(
                            schoolYearId: grade.schoolYearId,
                            schoolId: schoolId,
                            campusId: campusId,
                            gradeId: grade.id,
                            sectionId: sectionId,
                            unitId: unit.id,
                            unitType: unit.type,
                            studentId: student.id,
                            status: UnitStudent.NORMAL,
                    )
                    unitStudentList << unitStudent
                    studentIdList << student.id
            }
            unitStudentList*.save(failOnError: true)
        }
        try {
            int changeBit = PersonDataChangeEnum.noGroupHandlerBit()
            personDataChangeService.studentsDataChange(campusId, studentIdList, null, null, changeBit, OperateType.ADD.type)
//            synUserV2Service.syncUpdateOrCreateUser2Firm(campusId, studentIdList, UserTypeEnum.STUDENT.type, null, null, DockingFirmSyncTask.OPERATION_TYPE_MANY, "批量导入学生")
        } catch (RuntimeException e) {
            log.error("同步平台异常${e.message}".toString())
        } catch (Exception e) {
            log.error("同步平台异常${e.message}".toString())
        }
        batchRecord.status = 1 as byte
        batchRecord.count = jsonStudentList.size()
        batchRecord.save(failOnError: true)
    }

    int updateStudentAvatarBatch(String avatarJson, Long campusId) {
        List<JSONAvatar> jsonAvatarList = JSON.parseArray(avatarJson, JSONAvatar.class)
        int i = 0
        List<Long> studentIdList = []

        jsonAvatarList.each {
            JSONAvatar jsonAvatar ->
                if (jsonAvatar.url) {
                    Student student = fetchStudentByCampusIdIdAndCode(campusId, jsonAvatar.code)
                    if (student) {
                        student.pic = jsonAvatar.url
                        i++
                        saveStudent(student)
                        studentIdList << student.id
                    }
                }
        }
        try {
            int changeBit = PersonDataChangeEnum.noGroupHandlerBit()
            personDataChangeService.studentsDataChange(campusId, studentIdList, null, null, changeBit, OperateType.ADD.type, "批量导入学生照片")
//            synUserV2Service.syncUpdateOrCreateUser2Firm(campusId, studentIdList, UserTypeEnum.STUDENT.type, null, null, DockingFirmSyncTask.OPERATION_TYPE_MANY, "批量导入学生照片")
        } catch (RuntimeException e) {
            log.error("同步平台异常${e.message}".toString())
        } catch (Exception e) {
            log.error("同步平台异常${e.message}".toString())
        }
        i
    }

    int updateStudentCardNumBatch(Long schoolId, String cardJson) {
        List<JSONCardNum> cardNumList = JSON.parseArray(cardJson, JSONCardNum.class)
        Map<Long, String> studentIdCardNumMap = [:]
        Set<String> cardNums = Sets.newHashSet()
        cardNumList.each {
            if (it.id && it.cardNum) {
                Long studentId = decodeId(it.id)
                String cardNum = it.cardNum
                studentIdCardNumMap.put(studentId, cardNum)
                cardNums.add(it.cardNum)
            }
        }
        if (cardNums.size() != studentIdCardNumMap.size()) {
            return 0
        }
        try {
            for (Map.Entry<Long, String> entry : studentIdCardNumMap) {
                CardUser cardUser = CardUser.findByUserIdAndTypeAndStatus(entry.key, UserTypeEnum.STUDENT.type, 1 as byte)
                if (!cardUser) {
                    createAndSaveCardUser(schoolId, entry.key, entry.value)
                } else if (cardUser.cardNum != entry.value) {
                    cardUser.status = CardUser.CARD_DELETE_STATUS
                    createAndSaveCardUser(schoolId, entry.key, entry.value)
                    cardUser.save(failOnError: true, flush: true)
                }
            }
        } catch (AlreadyExistException e) {
            throw new AlreadyExistException(e.message)
        }
        studentIdCardNumMap.size()
    }

    /**
     * 仅支持非行政班！！！
     * @param unitJSON
     * @param unitId
     */
    int updateStudentUnitBatch(String unitJSON, Unit unit) {
        Long unitId = unit.id
        Preconditions.checkState(unit.type != UnitType.ADMINISTRATIVE_CLASS.type, "仅支持行政班")
        List<JSONUnit> jsonUnitList = JSON.parseArray(unitJSON, JSONUnit.class)
        List<UnitStudent> unitStudentList = UnitStudent.findAllByUnitIdAndStatus(unitId, UnitStudent.NORMAL)
        if (unitStudentList) {
            unitStudentList*.status = RelationStatus.DELETE.status
            unitStudentList*.save(failOnError: true, flush: true)
        }
        Set<Long> studentIdSet = Sets.newHashSet()
        jsonUnitList.each {
            if (it.studentId) {
                Long studentId = decodeId(it.studentId)
                createUnitStudent(unit, studentId)
                studentIdSet.add(studentId)
            }
        }
        int changeBit = PersonDataChangeEnum.onGroupPersonHandlerBit()
        personDataChangeService.studentsDataChange(unit.campusId, studentIdSet.toList(), null, null, changeBit, OperateType.UPDATE.type)

        studentIdSet.remove(null)
        studentIdSet.size()
    }

    ServiceResult<Boolean> createParentStudent(Long parentId, String parentName, Long studentId, Byte appellation, String memo, Boolean exist = false) {
        if (!appellation && !memo) {
            appellation = AppellationType.OTHER.type
            memo = "家长"
        } else if (appellation == AppellationType.OTHER.type && !memo) {
            memo = "家长"
        } else if (memo) {
            if (!PatternUtils.isChineseOrWord(memo)) {
//                throw new HiiAdminException("家长称谓非汉字或字母")
                return ServiceResult.failure(BizErrorCode.NOT_CHINESE_WORD.code.toString(), BizErrorCode.NOT_CHINESE_WORD.msg)
            }
        }
        if (exist && !PatternUtils.isChineseOrWord(parentName)) {
//            throw new HiiAdminException("家长姓名非汉字或字母")
            return ServiceResult.failure(BizErrorCode.NOT_CHINESE_WORD.code.toString(), BizErrorCode.NOT_CHINESE_WORD.msg)
        }
        ParentStudent parentStudent = parentStudentService.fetchParentStudentByParentIdAndStudentId(parentId, studentId)
        //判断家长是否为接送人
        if (parentStudent && parentStudent.parentType == ParentType.PICK_UP_PERSON.type) {
            saveParentStudentByName(parentName, appellation, memo, parentStudent)
            return ServiceResult.success(true)
        }
        if (appellation != AppellationType.OTHER.type && parentStudentService.parentStudentWithStudentIdAndAppellation(studentId, appellation)) {
//            throw new HasBindException("该学生对应关系的家长已绑定")
            return ServiceResult.failure(BizErrorCode.STUDENT_PARENT_THIS_ROLE_EXISTS.code.toString(), BizErrorCode.STUDENT_PARENT_THIS_ROLE_EXISTS.msg)
        }
        if (parentStudent == null) {
            parentStudent = new ParentStudent(studentId: studentId, parentId: parentId, status: 1 as Byte)
        }
        if (!parentStudent.appellation) {
            parentStudent.appellation = 0 as byte
        }
        if (parentStudent.appellation != 0 as byte && parentStudent.appellation != appellation) {
//            throw new HasBindException("该手机号已绑定学生其他身份")
            if (exist) {
                return ServiceResult.failure(BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS_NOT_DEAL.code.toString(), BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS_NOT_DEAL.msg)
            } else {
                return ServiceResult.failure(BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS.code.toString(), BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS.msg)
            }
        }
        if (exist && (parentStudent.parentName || parentStudent.memo)) {
            return ServiceResult.failure(BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS_NOT_DEAL.code.toString(), BizErrorCode.MOBILE_STUDENT_PARENT_ANOTHER_ROLE_EXISTS_NOT_DEAL.msg)
        }
        saveParentStudentByName(parentName, appellation, memo, parentStudent)

        return ServiceResult.success(true)
    }

    def saveParentStudentByName(String parentName, Byte appellation, String memo, ParentStudent parentStudent) {
        parentStudent.status = 1 as byte
        parentStudent.parentName = parentName
        parentStudent.appellation = appellation
        parentStudent.memo = memo
        parentStudent.parentType = ParentType.PARENT.type
        parentStudent.save(failOnError: true, flush: true)
        studentParentCountAdd(parentStudent.studentId, 1)
    }

    @Async
    void studentParentCountAdd(Long studentId, Integer total) {
        Student student = fetchStudentById(studentId)
        student.parentCount += total
        if (student.parentCount < 0) {
            student.parentCount = 0
        }
        saveStudent(student)
    }

    List<Student> fetchAllStudentByNamesAndCampusId(List<String> nameList, Long campusId) {
        String HQL = """ SELECT s FROM Student s ,UnitStudent us WHERE s.id = us.studentId AND s.name in :nameList AND s.status = 1 AND us.status = 1
                AND us.campusId = :campusId"""
        Student.executeQuery(HQL, [nameList: nameList, campusId: campusId])
    }

    List<Student> fetchAllSuspendStudentByLimit(Long campusId, String searchValue, int p, int s) {
        StringBuilder stringBuilder = new StringBuilder()
        stringBuilder.append(" SELECT s")
        Map map = buildSuspendStudent(stringBuilder, campusId, searchValue, p, s)
        Student.executeQuery(stringBuilder.toString(), map)
    }

    Integer countAllSuspendStudent(Long campusId, String searchValue) {
        StringBuilder stringBuilder = new StringBuilder()
        stringBuilder.append(" SELECT COUNT(1) ")
        Map map = buildSuspendStudent(stringBuilder, campusId, searchValue, 0, 0)
        Student.executeQuery(stringBuilder.toString(), map)[0] as Integer
    }

    static Map buildSuspendStudent(StringBuilder stringBuilder, Long campusId, String searchValue, int p, int s) {
        Map map = [:]
        String HQL = """ FROM Student s, UnitStudent us 
                                WHERE s.id = us.studentId 
                                   AND s.status = :status AND us.status = :status
                                   AND us.campusId = :campusId"""
        stringBuilder.append(HQL)
        map.put("campusId", campusId)
        map.put("status", RelationStatus.SUSPEND.status)

        if (searchValue) {
            stringBuilder.append(""" AND (s.name LIKE :searchValue)""")
            map.put("searchValue", "%" + searchValue + "%")
        }

        if (s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }
        stringBuilder.append(""" ORDER BY s.lastUpdated DESC """)
        map
    }

    // 删除之前状态为 7 的学生数据
    def deleteSuspendStudentInfo(long studentId) {
        // 删除休学卡号数据
        CardUser suspendCardUser = CardUser.findByUserIdAndTypeAndStatus(studentId, UserTypeEnum.STUDENT.type, 7 as byte)
        if (suspendCardUser) {
            suspendCardUser.status = CardUser.CARD_DELETE_STATUS
            suspendCardUser.save(failOnError: true, flush: true)
        }

        // 删除身份证数据
        List<UserEncodeInfo> userEncodeInfoList = UserEncodeInfo.findAllByUserIdAndUserTypeAndTypeAndStatus(studentId, 1 as byte, 1 as byte, 7 as byte)
        if (userEncodeInfoList) {
            userEncodeInfoList*.status = 0
            userEncodeInfoList*.save(failOnError: true)
        }
    }

    def changeSuspendStudent2Normal(Student student, Unit unit) {
        long unitId = unit.id
        UnitStudent unitStudent = UnitStudent.findByStudentIdAndUnitTypeAndStatus(student.id, 1 as byte, 7 as byte)
        Long oldCampusId = unitStudent?.campusId
        if (unit.campusId != oldCampusId) {
            Student s = fetchStudentByCampusIdIdAndCodeAndStudentIdNotEq(unit.campusId, student.code, student.id)
            if (s) {
                throw new AlreadyExistException("新班级所在校区已存在相同学号，请订正后尝试")
            }
        }

        unitStudent.unitId = unitId
        Grade grade = gradeService.fetchGradeById(unit.gradeId, false)
        unitStudent.schoolId = grade.schoolId
        unitStudent.studentId = student?.id
        unitStudent.campusId = grade.campusId
        unitStudent.sectionId = grade.sectionId
        unitStudent.schoolYearId = grade.schoolYearId
        unitStudent.gradeId = grade.id
        unitStudent.unitType = unit.type
        unitStudent.facultyId = unit.facultyId
        unitStudent.majorId = unit.majorId
        unitStudent.status = 1 as byte
        unitStudent.save(failOnError: true, flush: true)
    }

    String appendStudentStatus(Student student) {
        String name = ''
        if (student) {
            name = student.name
            switch (student.status) {
                case RelationStatus.DELETE.status:
                    name = name + '[已删除]'
                    break
                case RelationStatus.SUSPEND.status:
                    name = name + '[已休学]'
                    break
                case RelationStatus.FINISH.status:
                    name = name + '[已毕业]'
                    break
                case RelationStatus.NORMAL.status:
                    break
                default:
                    name = name + '[已删除]'
            }
        }

        return name
    }

    String appendStudentStatusByStudentId(Long studentId) {
        Student student = fetchStudentById(studentId)

        String name = ''
        if (student) {
            name = appendStudentStatus(student)
        }
        return name
    }

    List<Student> analysisStudent(Long campusId, String filedName, List<String> valueList) {
        List<Student> studentList = []
        switch (filedName) {
            case "name":
                valueList.each {
                    if (PatternUtils.isNameLe50(it)) {
                        List<Student> list = fetchStudentByCampusIdIdAndName(campusId, it)
                        if (list && list.size() > 0 && list.size() <= 1) {
                            studentList.addAll(list)
                        }
                    }
                }
                break
            case "code":
                valueList.collate(100).each {
                    studentList.addAll(fetchStudentByCampusIdIdAndCodeList(campusId, it))
                }
                break
        }

        studentList
    }

    def transformUnitCodeStudentIdMultiMap(Long campusId, List<Long> studentIdList) {
        StringBuilder sb = new StringBuilder()
        Map<String, Object> map = [:]
        sb.append("SELECT s,u,g ")
        sb.append(""" FROM Student s 
                                LEFT JOIN UnitStudent us ON s.id = us.studentId AND us.status = :status
                                LEFT JOIN Unit u ON u.id = us.unitId AND u.status = :status
                                LEFT JOIN Grade g ON g.id = us.gradeId AND g.status = :status 
                        WHERE s.status = :status  AND us.campusId = :campusId AND  us.unitType = 1 """)
        map.put("status", 1 as byte)
        map.put("campusId", campusId)
        if (CollectionUtils.isNotEmpty(studentIdList)) {
            sb.append(" AND s.id IN :studentIdList")
            map.put("studentIdList", studentIdList)
        }

        List<Object> re = Student.executeQuery(sb.toString(), map)
        HashMultimap<String, Long> unitCodeStudentIdMultiMap = HashMultimap.create()
        re.each { obj ->
            Student student = obj[0] as Student
            Unit unit = obj[1] as Unit
            Grade grade = obj[2] as Grade

            if (student && unit && grade) {
                String unitCode = grade.sectionId + "-" + grade.code + "-" + unit.code
                unitCodeStudentIdMultiMap.put(grade.sectionId as String, student.id)
                unitCodeStudentIdMultiMap.put(grade.sectionId + "-" + grade.code, student.id)
                unitCodeStudentIdMultiMap.put(unitCode, student.id)
            }
        }
        unitCodeStudentIdMultiMap

    }

    def pushStudentPersonEvent(long campusId, long studentId) {
        StudentDataChange studentPerson = new StudentDataChange(
                campusId: campusId,
                userId: studentId,
                userType: UserTypeEnum.STUDENT.type
        )
        StudentPersonEvent personEvent = new StudentPersonEvent(this, studentPerson)
        personEvent.changeBit = PersonDataChangeEnum.getFullBit()
        applicationContext.publishEvent(personEvent)
    }

    static class JSONStudent implements Serializable {
        String unit
        String name
        String code
        String gender
        String idCard
    }

    static class JSONAvatar implements Serializable {
        String code
        String url
    }

    static class JSONCardNum implements Serializable {
        String id
        String cardNum
    }

    static class JSONUnit implements Serializable {
        String studentId
        String code
        String name
    }

}

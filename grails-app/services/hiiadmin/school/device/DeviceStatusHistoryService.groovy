package hiiadmin.school.device


import grails.gorm.transactions.Transactional
import org.joda.time.DateTime
import timetabling.device.Device
import timetabling.device.DeviceStatusHistory

@Transactional
class DeviceStatusHistoryService {

    def saveDeviceStatusHistory(Device device) {
        if (device) {
            DeviceStatusHistory deviceStatusHistory = new DeviceStatusHistory(
                    deviceId: device.id,
                    deviceName: device.name,
                    firm: device.firm,
                    onlineStatus: device.onlineStatus,
                    status: 1
            )
            deviceStatusHistory.save(failOnError: true)
        }
    }

    def fetchAllDeviceStatusHistory(Long deviceId, Long time, int p, int s) {
        def c = DeviceStatusHistory.createCriteria()
        def list = c.list(max: s, offset: (p - 1) * s) {
            eq("deviceId", deviceId)
            if (time) {
                DateTime dateTime = new DateTime(time)
                Date startDate = dateTime.withMillisOfDay(0).toDate()
                Date endDate = dateTime.millisOfDay().withMaximumValue().toDate()
                between("dateCreated", startDate, endDate)
            }
            eq("status", 1)
            order("dateCreated", "desc")
        }
        [list: list as List<DeviceStatusHistory>, total: list?.totalCount ?: 0]
    }

    /**
     * 获取设备状态统计数据
     * @param deviceId 设备ID
     * @param time 查询日期时间戳
     * @return 统计数据Map
     */
    Map<String, Object> getDeviceStatusStatistics(Long deviceId, Date startDate, Date endDate) {

        // 查询当天所有状态记录
        def c = DeviceStatusHistory.createCriteria()
        List<DeviceStatusHistory> historyList = c.list {
            eq("deviceId", deviceId)
            between("dateCreated", startDate, endDate)
            eq("status", 1)
            order("dateCreated", "asc")
        } as List<DeviceStatusHistory>

        return calculateDeviceStatistics(historyList, startDate, endDate)
    }

    /**
     * 获取设备状态时间轴数据
     * @param deviceId 设备ID
     * @param time 查询日期时间戳
     * @return 时间轴数据列表
     */
    List<Map<String, Object>> getDeviceTimelineData(Long deviceId, Date startDate, Date endDate) {


        def c = DeviceStatusHistory.createCriteria()
        List<DeviceStatusHistory> historyList = c.list {
            eq("deviceId", deviceId)
            between("dateCreated", startDate, endDate)
            eq("status", 1)
            order("dateCreated", "asc")
        } as List<DeviceStatusHistory>

        return buildDeviceTimelineData(historyList, startDate, endDate)
    }
    /**
     * 计算设备统计数据
     * 根据所选时间范围，展示对应的在离线状态色块图
     * 蓝色代表在线区间，红色代表离线区间
     */
    private Map<String, Object> calculateDeviceStatistics(List<DeviceStatusHistory> historyList, Date startDate, Date endDate) {
        Map<String, Object> statistics = [:]

        if (!historyList) {
            // 如果没有历史记录，默认整个时间段为在线状态
            return [
                onlineRate: "100%",
                offlineDuration: "0小时0分",
                statusChangeCount: 0,
                statusBlocks: [[
                    startTime: startDate.time,
                    endTime: endDate.time,
                    status: 1,
                    statusText: "在线",
                    statusClass: "online",
                    duration: formatDuration(endDate.time - startDate.time),
                    percentage: 100.0,
                    startTimeFormatted: formatTime(startDate),
                    endTimeFormatted: formatTime(endDate)
                ]]
            ]
        }

        long totalDayMillis = endDate.time - startDate.time
        long offlineMillis = 0
        int statusChangeCount = historyList.size()
        List<Map<String, Object>> statusBlocks = []

        // 构建状态区间块
        Date currentStart = startDate

        // 如果第一条记录不是从开始时间开始，需要推断初始状态
        if (historyList[0].dateCreated.after(startDate)) {
            // 假设开始时间到第一条记录之间为在线状态
            long blockDuration = historyList[0].dateCreated.time - startDate.time
            statusBlocks.add(createStatusBlock(
                startDate.time,
                historyList[0].dateCreated.time,
                1, // 在线状态
                blockDuration,
                totalDayMillis
            ))
        }

        // 处理每个状态变更记录
        for (int i = 0; i < historyList.size(); i++) {
            DeviceStatusHistory current = historyList[i]
            Date blockEnd = (i + 1 < historyList.size()) ? historyList[i + 1].dateCreated : endDate

            long blockDuration = blockEnd.time - current.dateCreated.time

            // 创建状态块
            statusBlocks.add(createStatusBlock(
                current.dateCreated.time,
                blockEnd.time,
                current.onlineStatus,
                blockDuration,
                totalDayMillis
            ))

            // 累计离线时长
            if (current.onlineStatus == 0) {
                offlineMillis += blockDuration
            }
        }

        // 计算在线率
        long onlineMillis = totalDayMillis - offlineMillis
        double onlineRate = totalDayMillis > 0 ? (onlineMillis * 100.0) / totalDayMillis : 100.0

        // 格式化离线时长
        String offlineDurationText = formatDuration(offlineMillis)

        statistics.put("onlineRate", String.format("%.1f%%", onlineRate))
        statistics.put("offlineDuration", offlineDurationText)
        statistics.put("statusChangeCount", statusChangeCount)
        statistics.put("statusBlocks", statusBlocks)

        return statistics
    }

    /**
     * 创建状态块数据
     * 用于生成色块图的每个区间数据
     */
    private Map<String, Object> createStatusBlock(long startTime, long endTime, int status, long duration, long totalDuration) {
        String statusText = getDeviceStatusText(status)
        String statusClass = getDeviceStatusClass(status)
        String durationText = formatDuration(duration)
        double percentage = totalDuration > 0 ? (duration * 100.0) / totalDuration : 0.0

        return [
            startTime: startTime,
            endTime: endTime,
            status: status,
            statusText: statusText,
            statusClass: statusClass,
            duration: durationText,
            percentage: percentage,
            startTimeFormatted: formatTime(new Date(startTime)),
            endTimeFormatted: formatTime(new Date(endTime))
        ]
    }

    /**
     * 格式化时长
     * 将毫秒转换为可读的时长格式
     */
    private String formatDuration(long millis) {
        if (millis <= 0) return "0分钟"

        long hours = millis / (1000 * 60 * 60)
        long minutes = (millis % (1000 * 60 * 60)) / (1000 * 60)

        if (hours > 0) {
            return "${hours}小时${minutes}分钟"
        } else {
            return "${minutes}分钟"
        }
    }

    /**
     * 格式化时间
     * 将Date对象格式化为HH:mm:ss格式
     */
    private String formatTime(Date date) {
        return date.format("HH:mm:ss")
    }

    /**
     * 构建设备时间轴数据
     */
    private List<Map<String, Object>> buildDeviceTimelineData(List<DeviceStatusHistory> historyList, Date startDate, Date endDate) {
        List<Map<String, Object>> timelineData = []

        if (!historyList) {
            return timelineData
        }

        // 添加第一条记录前的时间段（如果startDate早于第一条记录时间）
        if (startDate.before(historyList[0].dateCreated)) {
            String statusText = getDeviceStatusText(historyList[0].onlineStatus)
            String statusClass = getDeviceStatusClass(historyList[0].onlineStatus)

            timelineData.add([
                    startTime   : startDate.time,
                    endTime     : historyList[0].dateCreated.time,
                    onlineStatus: historyList[0].onlineStatus,
                    statusText  : statusText,
                    statusClass : statusClass,
                    message     : "",
                    deviceName  : historyList[0].deviceName ?: ""
            ])
        }

        // 处理状态变化记录
        for (int i = 0; i < historyList.size(); i++) {
            DeviceStatusHistory current = historyList[i]
            Date segmentStart = current.dateCreated
            Date segmentEnd = endDate

            // 如果有下一条记录，段结束时间为下一条记录时间
            if (i + 1 < historyList.size()) {
                segmentEnd = historyList[i + 1].dateCreated
            }

            String statusText = getDeviceStatusText(current.onlineStatus)
            String statusClass = getDeviceStatusClass(current.onlineStatus)

            timelineData.add([
                    startTime   : segmentStart.time,
                    endTime     : segmentEnd.time,
                    onlineStatus: current.onlineStatus,
                    statusText  : statusText,
                    statusClass : statusClass,
                    message     : current.message ?: "",
                    deviceName  : current.deviceName ?: ""
            ])
        }

        return timelineData
    }


    /**
     * 获取设备状态文本
     */
    private String getDeviceStatusText(Integer onlineStatus) {
        switch (onlineStatus) {
            case 1: return "在线"
            case 0: return "离线"
            default: return "未知"
        }
    }

    /**
     * 获取设备状态样式类
     */
    private String getDeviceStatusClass(Integer onlineStatus) {
        switch (onlineStatus) {
            case 1: return "online"
            case 0: return "offline"
            default: return "unknown"
        }
    }
}
package hiiadmin.school.device

import grails.gorm.transactions.Transactional
import timetabling.docking.DockingDevice

@Transactional
class DockingDeviceService {

    DockingDevice fetchDockingDeviceByParentIndexCodeAndChannelAndFirm(String parentResourceIndexCode, String channelNo, Integer firm) {
        DockingDevice.findByParentResourceIndexCodeAndResourceChannelNoAndFirmAndStatus(parentResourceIndexCode, channelNo, firm, 1 as byte)
    }

    DockingDevice fetchDockingDeviceByParentIndexCodeAndFirm(String parentResourceIndexCode, Integer firm) {
        DockingDevice.findByParentResourceIndexCodeAndFirmAndStatus(parentResourceIndexCode, firm, 1 as byte)
    }
}

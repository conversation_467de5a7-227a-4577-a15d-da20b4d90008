package hiiadmin.school

import com.alibaba.fastjson.JSON
import com.alicp.jetcache.anno.CacheRefresh
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import com.bugu.IdCardUtil
import com.google.common.base.Preconditions
import com.google.common.collect.*
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.EnvService
import hiiadmin.biz.StaffService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.listen.event.person.TeacherPersonEvent
import hiiadmin.listen.module.person.TeacherDataChange
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.pic.CheckFaceService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.UserNameCorrectUtil
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import timetabling.CardUser
import timetabling.Department
import timetabling.StaffDepartment
import timetabling.humanface.pic.CheckFace
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.org.UnitTeacher
import timetabling.user.Staff
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus
import timetabling.user.TeacherSubject
import timetabling.vocationalSchool.FacultyUser

import java.util.concurrent.TimeUnit

import static hiiadmin.ConstantEnum.*

@Slf4j
@Transactional
class TeacherService {

    EnvService envService

    @Lazy
    StaffService staffService

    @Lazy
    CardUserService cardUserService

    @Lazy
    UserEncodeInfoService userEncodeInfoService

    CheckFaceService checkFaceService

    UnitStudentService unitStudentService
    
    CampusService campusService

    @Autowired
    ApplicationContext applicationContext

    /**
     *
     * @param id
     * @param name
     * @param mobile
     * @param gender
     * @return
     */
    void updateTeacher(long id, String name, String mobile, Boolean gender) {
        boolean runFlag = false
        Teacher teacher = Teacher.get(id)
        if (name && teacher.name != name) {
            teacher.name = name
            runFlag = true
        }

        if (mobile && userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id) != mobile) {
            if (!PatternUtils.isMobile(mobile, envService)) {
                throw new HiiAdminException("请输入正确的手机号,errorMobile:${mobile}")
            }
            if (!hasTeacherByMobile(mobile, id)) {
                userEncodeInfoService.saveUserEncodeInfo(mobile, UserTypeEnum.TEACHER.type, id, UserEncodeInfoType.MOBILE.type)
                runFlag = true
            } else {
                throw new HiiAdminException("手机号已存在，请更换后尝试")
            }
        }
        if (gender && teacher.gender != gender) {
            teacher.gender = gender
            runFlag = true
        }
        if (runFlag) {
            teacher.save(failOnError: true, flush: true)
        }
    }

    TeacherSchoolCampus fetchTeacherSchoolCampusByCampusIdAndJobNum(Long campusId, String code) {
        TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(campusId, code, 1 as byte)
    }

    @Cached(expire = 1, cacheNullValue = false, timeUnit = TimeUnit.DAYS, name = "teacherService_fetchTeacherByid",
            key = "#teacherId", cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Teacher fetchTeacherById(Long teacherId, boolean cache = true) {
        Teacher.findByIdAndStatus(teacherId, 1 as byte)
    }

    Teacher fetchTeacherByIdNoCache(Long teacherId) {
        Teacher.findByIdAndStatus(teacherId, 1 as byte)
    }

    String getTeacherJobNumByByCampusIdAndTeacherId(Long campusId, Long teacherId) throws RuntimeException {
        TeacherSchoolCampus teacherSchoolCampus = fetchTeacherSchoolByCampusIdAndTeacherId(campusId, teacherId)
        if (teacherSchoolCampus) {
            if (StringUtils.isNotBlank(teacherSchoolCampus.jobNum)) {
                String code = teacherSchoolCampus.jobNum
                return code.substring(1, code.length())
            } else {
                throw new NullPointerException("工号不存在")
            }
        }
        throw new NullPointerException("此校区不存在该老师")
    }

    List<TeacherVO> findTeacherByCampusIdAndSearchValue(long campusId, Boolean gender, String searchValue, Long departmentId, int resultStatus = 99, byte userType = 99 as byte, Integer p = null, int s = 30) {
        StringBuilder sb = new StringBuilder()

        Map<String, Object> map = [:]
        sb.append("""SELECT t, tsc.jobNum FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  WHERE tsc.campusId = :campusId AND t.status = :status """)

        if (gender != null) {
            sb.append(""" AND t.gender = :gender """)
            map.put("gender", gender)
        }
        if (resultStatus == 4) {
            sb.append(""" AND t.avatar IS NULL """)
        }
        if (searchValue) {
            if (searchValue.isNumber()) {
                if (searchValue.length() < 4) {
                    return []
                } else {
                    List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                    sb.append(""" AND (t.id IN :userIdList OR tsc.jobNum LIKE :searchValue )""")
                    map.put("userIdList", userIdList)
                    map.put("searchValue", "%" + searchValue + "%")
                }
            } else {
                sb.append(""" AND (t.name LIKE :name OR tsc.jobNum LIKE :name)""")
                String value = "%" + searchValue + "%"
                map.put("name", value)
            }
        }

        Campus campus = Campus.get(campusId)
        if(campus.type ==1){
            if (departmentId == -1) {
                sb.append(""" AND NOT EXISTS ( SELECT s_d.id from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.teacherId = t.id AND s_d.status = :status ) """)
            } else if (departmentId) {
                sb.append(""" AND EXISTS ( SELECT s_d.id from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.departmentId = :departmentId AND s_d.teacherId = t.id AND s_d.status = :status ) """)
                map.put("departmentId", departmentId)
            }
        }else{
            if(departmentId){
                sb.append(""" AND EXISTS ( SELECT fu.id from FacultyUser fu WHERE fu.campusId = :campusId AND fu.facultyId = :departmentId AND fu.userId = t.id AND fu.userType = :userType AND fu.status = :status ) """)
                map.put("userType", UserTypeEnum.TEACHER.type)
                map.put("departmentId", departmentId)
            }
        }
        if (resultStatus == 3) {
            sb.append(""" AND t.avatar IS NOT NULL AND NOT EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND t.id=cf.userId AND cf.userType = 6 AND cf.resultStatus != :resultStatus AND cf.status = 1 ORDER BY cf.id DESC) """)
            map.put("campusId", campusId)
            map.put("resultStatus", resultStatus)
        }
        if (resultStatus && resultStatus != 4 && resultStatus != 3 && resultStatus != 99) {
            sb.append(""" AND EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND t.id=cf.userId AND cf.userType = 6 AND cf.resultStatus=:resultStatus AND t.avatar=cf.picUrl AND cf.status = 1 ORDER BY cf.id DESC) """)
            map.put("campusId", campusId)
            map.put("resultStatus", resultStatus)
        }
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("status", 1 as byte)
        map.put("campusId", campusId)
        if (p && p > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        def re = Teacher.executeQuery(sb.toString(), map)
        List<TeacherVO> teacherVOList = []
        re.each {
            Teacher teacher = it[0] as Teacher
            String jobNum = it[1] as String
            TeacherVO vo = new TeacherVO().buildVO(teacher, jobNum)
            teacherVOList << vo
        }
        teacherVOList
    }

    List<TeacherVO> findTeacherByCampusIdAndSearchValue(long campusId,  String searchValue,  Integer p = null, int s = 30) {
        StringBuilder sb = new StringBuilder()

        Map<String, Object> map = [:]
        sb.append("""SELECT t, tsc.jobNum FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  WHERE tsc.campusId = :campusId AND t.status = :status """)


        if (searchValue) {
            if (searchValue.isNumber()) {
                if (searchValue.length() < 4) {
                    return []
                } else {
                    List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                    sb.append(""" AND (t.id IN :userIdList OR tsc.jobNum LIKE :searchValue )""")
                    map.put("userIdList", userIdList)
                    map.put("searchValue", "%" + searchValue + "%")
                }
            } else {
                sb.append(""" AND (t.name LIKE :name OR tsc.jobNum LIKE :name)""")
                String value = "%" + searchValue + "%"
                map.put("name", value)
            }
        }


        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("status", 1 as byte)
        map.put("campusId", campusId)
        if (p && p > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        def re = Teacher.executeQuery(sb.toString(), map)
        List<TeacherVO> teacherVOList = []
        re.each {
            Teacher teacher = it[0] as Teacher
            String jobNum = it[1] as String
            TeacherVO vo = new TeacherVO().buildVO(teacher, jobNum)
            teacherVOList << vo
        }
        teacherVOList
    }

    Integer countTeacherBySchoolIdAndGender(long campusId, Boolean gender, String searchValue, Long departmentId, int resultStatus = 99) {
        StringBuilder sb = new StringBuilder()

        Map<String, Object> map = [:]
        sb.append("""SELECT COUNT(t.id) FROM 
                                Teacher t RIGHT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  WHERE t.status = :status """)

        if (gender != null) {
            sb.append(""" AND t.gender = :gender """)
            map.put("gender", gender)
        }
        if (resultStatus == 4) {
            sb.append(""" AND t.avatar IS NULL """)
        }
        if (searchValue) {
            if (searchValue.isNumber()) {
                if (searchValue.length() < 4) {
                    return 0
                } else {
                    List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                    sb.append(""" AND (t.id IN :userIdList OR tsc.jobNum LIKE :searchValue )""")
                    map.put("userIdList", userIdList)
                    map.put("searchValue", "%" + searchValue + "%")
                }
            } else {
                sb.append(""" AND (t.name LIKE :name OR tsc.jobNum LIKE :name)""")
                String value = "%" + searchValue + "%"
                map.put("name", value)
            }
        }
        Campus campus = Campus.get(campusId)
        if(campus.type ==1){
            if (departmentId == -1) {
                sb.append(""" AND NOT EXISTS ( SELECT s_d.id from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.teacherId = t.id AND s_d.status = :status ) """)
            } else if (departmentId) {
                sb.append(""" AND EXISTS ( SELECT s_d.id from StaffDepartment s_d WHERE s_d.campusId = :campusId AND s_d.departmentId = :departmentId AND s_d.teacherId = t.id AND s_d.status = :status ) """)
                map.put("departmentId", departmentId)
            }
        }else{
            if(departmentId){
                sb.append(""" AND EXISTS ( SELECT fu.id from FacultyUser fu WHERE fu.campusId = :campusId AND fu.facultyId = :departmentId AND fu.userId = t.id AND fu.userType = :userType AND fu.status = :status ) """)
                map.put("userType", UserTypeEnum.TEACHER.type)
                map.put("departmentId", departmentId)
            }
        }

        if (resultStatus == 3) {
            sb.append(""" AND t.avatar IS NOT NULL AND NOT EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND t.id=cf.userId AND cf.userType = 6 AND cf.resultStatus != :resultStatus AND cf.status = 1 ORDER BY cf.id DESC )""")
            map.put("resultStatus", resultStatus)
        }
        if (resultStatus && resultStatus != 4 && resultStatus != 3 && resultStatus != 99) {
            sb.append(""" AND EXISTS (SELECT DISTINCT(cf.id) FROM CheckFace cf WHERE cf.campusId = :campusId AND t.id=cf.userId AND cf.userType = 6 AND cf.resultStatus=:resultStatus AND t.avatar=cf.picUrl AND cf.status = 1 ORDER BY cf.id DESC )""")
            map.put("resultStatus", resultStatus)
        }
        map.put("status", 1 as byte)
        map.put("campusId", campusId)
        Teacher.executeQuery(sb.toString(), map)[0] as Integer
    }

    List<Long> findAllTeacherIdsNoAvatarByCampusId(long campusId) {
        StringBuilder sb = new StringBuilder()
        Map<String, Object> map = [:]
        sb.append("""SELECT t.id FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  
                                        WHERE tsc.campusId = :campusId AND t.status = :status AND t.avatar IS NULL """)
        map.put("status", 1 as byte)
        map.put("campusId", campusId)

        List<Long> teacherIds = Teacher.executeQuery(sb.toString(), map) as List<Long>

        teacherIds
    }

    /**
     * 根据教职工姓名，手机号，工号查询教职工
     * @param campusId
     * @param searchValue
     * @param p
     * @param s
     * @return
     */
    List<TeacherVO> fetchAllTeacherByNameOrMobileOrJobNum(Long campusId, String searchValue, int p, int s) {
        List<TeacherVO> teacherVOList = []
        Map<String, Object> map = [:]
        String HQL = """ SELECT t,tsc.jobNum FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  WHERE tsc.campusId = :campusId AND t.status = :status """
        StringBuilder stringBuilder = new StringBuilder(HQL)
        map.put("status", 1 as Byte)
        map.put("campusId", campusId)

        if (s > 0) {
            map.put("max", s)
            map.put("offset", (p - 1) * s)
        }

        if (searchValue) {
            stringBuilder.append(""" AND (t.name LIKE :searchValue OR t.mobile LIKE :searchValue OR tsc.jobNum LIKE :searchValue) """)
            map.put("searchValue", "%" + searchValue + "%")
        }

        stringBuilder.append(""" ORDER BY t.id DESC """)
        def re = Teacher.executeQuery(stringBuilder, map) as List<Teacher>
        re.each {
            Teacher teacher = it[0] as Teacher
            String jobNum = it[1] as String
            teacherVOList << new TeacherVO().buildVO(teacher, jobNum)
        }
        teacherVOList
    }

    /**
     * 根据校区、工号查询老师
     * @param campusId
     * @param code
     * @return
     */
    List<Teacher> fetchAllTeacherByCampusIdAndJobNum(Long campusId, String code) {
        Map<String, Object> map = [:]
        String HQL = """ SELECT t FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId = t.id AND tsc.campusId = :campusId AND tsc.jobNum = :code AND tsc.status = :status  
                                        WHERE t.status = :status """
        StringBuilder stringBuilder = new StringBuilder(HQL)
        map.put("campusId", campusId)
        map.put("status", 1 as Byte)
        map.put("code", code)
        Teacher.executeQuery(stringBuilder, map) as List<Teacher>
    }

    /**
     * 根据教职工姓名，手机号，工号返回count
     * @param campusId
     * @param searchValue
     * @param p
     * @param s
     * @return
     */
    Integer countAllTeacherByNameOrMobileOrJobNum(Long campusId, String searchValue) {
        Map<String, Object> map = [:]
        String HQL = """ SELECT count(t.id) FROM 
                                Teacher t LEFT JOIN TeacherSchoolCampus tsc 
                                        ON tsc.teacherId= t.id AND tsc.campusId = :campusId AND tsc.status = :status  WHERE tsc.campusId = :campusId AND t.status = :status """
        StringBuilder stringBuilder = new StringBuilder(HQL)
        map.put("campusId", campusId)
        map.put("status", 1 as Byte)
        if (searchValue) {
            stringBuilder.append(""" AND (t.name LIKE :searchValue OR t.mobile LIKE :searchValue OR tsc.jobNum LIKE :searchValue) """)
            map.put("searchValue", "%" + searchValue + "%")
        }
        Teacher.executeQuery(stringBuilder, map)[0] as Integer
    }

    @Cached(expire = 12, cacheNullValue = false, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherService_fetchAllTeacherByIdInList",
            key = "#teacherIdList", cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByIdInList(List<Long> teacherIdList, boolean cache = true) {
        if (!teacherIdList) {
            return []
        }
        Teacher.findAllByIdInListAndStatus(teacherIdList, 1 as byte)
    }

    /**
     * 批量导入老师照片
     * @param avatarJson
     * @param campusId
     */
    void batchTeacherAvatar(String avatarJson, Long campusId) {
        def avatars = JSON.parseArray(avatarJson)
        List<Teacher> teacherList = []
        avatars?.each {
            String url = it?.url
            String code = it?.code
            String jobNum = "T" + code
            TeacherSchoolCampus t = fetchTeacherSchoolCampusViaCampusIdAndJobNum(campusId, jobNum)
            Teacher teacher = fetchTeacherById(t?.teacherId)
            if (teacher) {
                teacher.avatar = url
                teacherList.add(teacher)
            }
        }
        if (teacherList && teacherList.size() > 0) {
            teacherList*.save(failOnError: true, flush: true)
        }
    }

    Teacher findTeacherById(Long teacherId) {
        return Teacher.get(teacherId)
    }

    @Cached(expire = 2, cacheNullValue = false, timeUnit = TimeUnit.HOURS, name = "teacherService_fetchAllTeacherByNameLike",
            key = "#name", cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByNameLike(String name) {
        if (!name) {
            return []
        }
        return Teacher.findAllByNameLike("%${name}%")
    }

    def findTeacherListByTeacherIdInList(List<Long> teacherIdList) {
        if (!teacherIdList || teacherIdList.size() < 1) {
            return []
        }
        Teacher.findAllByIdInListAndStatus(teacherIdList, 1 as Byte)
    }

    /**
     * 查询班级班主任
     * @param unitId
     * @return
     */
    Teacher fetchHeadmasterTeacherByUnitId(Long unitId, boolean graduate = false) {
        Long teacherId = null
        if (graduate) {
            teacherId = UnitTeacher.findByUnitIdAndHeadmasterAndTypeAndStatus(unitId, TeacherType.UNIT_DIRECTOR.type,
                    UnitTeacherType.ADMINISTRATION.type, UnitTeacher.FINISH)?.teacherId
        } else {
            teacherId = fetchHeadmasterTeacherIdByUnitId(unitId)
        }
        return findTeacherById(teacherId)
    }

    /**
     * 查询班级班主任Id
     * @param unitId
     * @return
     */
    Long fetchHeadmasterTeacherIdByUnitId(Long unitId) {
        UnitTeacher.findByUnitIdAndHeadmasterAndTypeAndStatus(unitId, TeacherType.UNIT_DIRECTOR.type,
                UnitTeacherType.ADMINISTRATION.type, UnitTeacher.NORMAL)?.teacherId
    }

    List<UnitTeacher> fetchHeadmasterTeacherIdByUnitIdInList(List<Long> unitIds) {
        UnitTeacher.findAllByUnitIdInListAndHeadmasterAndTypeAndStatus(unitIds, TeacherType.UNIT_DIRECTOR.type,
                UnitTeacherType.ADMINISTRATION.type, UnitTeacher.NORMAL)
    }

    /**
     * 查询学生当前时间段的班主任
     * @param studentId
     * @param date
     * @return
     */
    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiadmin_teacher_fetchHTBStudentId", key = "#studentId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 10, timeUnit = TimeUnit.MINUTES)
    Teacher fetchHeadmasterTeacherByStudentId(Long studentId, boolean cache = true) {
        Long unitId = unitStudentService.fetchAdministrativeUnitIdByStudentId(studentId)
        fetchHeadmasterTeacherByUnitId(unitId)
    }

    List<Long> fetchHeadmasterTeacherListByStudentIds(List<Long> studentIds) {
        List<Long> teacherIdList = []
        List<UnitStudent> unitStudentList = unitStudentService.fetchAdministrativeUnitByStudentIds(studentIds)
        if (unitStudentList) {
            List<Long> ids = unitStudentList*.unitId.unique()
            List<UnitTeacher> teacherList = UnitTeacher.findAllByUnitIdInListAndHeadmasterAndTypeAndStatus(ids, TeacherType.UNIT_DIRECTOR.type,
                    UnitTeacherType.ADMINISTRATION.type, UnitTeacher.NORMAL)
            if (teacherList) {
                teacherIdList = teacherList*.teacherId.unique()
            }
        }
        teacherIdList
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS,
            name = "hiiadmin_teacherS_countNormalTeacher", key = "#schoolId + '_' + #campusId + '_' + #sectionId + '_' + #gradeId + '_' + #unitId + '_' + #subjectId",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    Integer countNormalTeacher(Long schoolId, Long campusId, Long sectionId, Long gradeId, Long unitId, Long subjectId) {
        def count
        if (subjectId) {
            count = TeacherSubject.createCriteria().get {
                eq("schoolId", schoolId)
                if (campusId) {
                    eq("campusId", campusId)
                }
                if (sectionId) {
                    eq("sectionId", sectionId)
                }
                if (gradeId) {
                    eq("gradeId", gradeId)
                }
                if (subjectId) {
                    eq("subjectId", subjectId)
                }
                eq("status", TeacherSubject.NORMAL)
                projections {
                    countDistinct('teacherId')
                }
            }
        } else if (sectionId || gradeId || unitId) {
            count = UnitTeacher.createCriteria().get {
                eq("schoolId", schoolId)
                if (campusId) {
                    eq("campusId", campusId)
                }
                if (sectionId) {
                    eq("sectionId", sectionId)
                }
                if (gradeId) {
                    eq("gradeId", gradeId)
                }
                if (unitId) {
                    eq("unitId", unitId)
                }
                if (subjectId) {
                    eq("subjectId", subjectId)
                }
                eq("status", UnitTeacher.NORMAL)
                projections {
                    countDistinct('teacherId')
                }
            }
        } else {
            count = TeacherSchoolCampus.createCriteria().get {
                eq("schoolId", schoolId)
                if (campusId) {
                    eq("campusId", campusId)
                }
                eq("status", UnitTeacher.NORMAL)
                projections {
                    countDistinct('teacherId')
                }
            }
        }
        (count as Integer)
    }

    def fetchAllTeacherSchoolCampusByCampusIdMap(Long campusId) {
        Map<String, TeacherSchoolCampus> jobNumMap = [:]
        Map<Long, TeacherSchoolCampus> teacherIdMap = [:]
        List<TeacherSchoolCampus> teacherSchoolCampusList = fetchAllByCampusId(campusId)
        if (teacherSchoolCampusList) {
            teacherIdMap = teacherSchoolCampusList.collectEntries { TeacherSchoolCampus item -> [item.teacherId, item]
            }
            jobNumMap = teacherSchoolCampusList.collectEntries { TeacherSchoolCampus item -> [item.jobNum, item]
            }
        }
        ["jobNumMap": jobNumMap, "teacherIdMap": teacherIdMap]
    }

    List<TeacherSchoolCampus> fetchAllByCampusId(Long campusId) {
        TeacherSchoolCampus.findAllByCampusIdAndStatus(campusId, 1 as Byte)
    }

    List<Teacher> fetchAllNormalTeacher(long schoolId, int p, int s) {
        fetchAllNormalTeacher(schoolId, null, null, null, null, null, p, s)
    }

    List<Teacher> fetchAllNormalTeacher(long schoolId, Long campusId, int p, int s) {
        fetchAllNormalTeacher(schoolId, campusId, null, null, null, null, p, s)
    }

    List<Teacher> fetchAllNormalTeacher(long schoolId, Long campusId, Long sectionId, int p, int s) {
        fetchAllNormalTeacher(schoolId, campusId, sectionId, null, null, null, p, s)
    }

    List<Teacher> fetchAllNormalTeacher(long schoolId, Long campusId, Long sectionId, Long gradeId, int p, int s) {
        fetchAllNormalTeacher(schoolId, campusId, sectionId, gradeId, null, null, p, s)
    }

    List<Teacher> fetchAllNormalTeacher(long schoolId, Long campusId, Long sectionId, Long gradeId, Long unitId, int p, int s) {
        fetchAllNormalTeacher(schoolId, campusId, sectionId, gradeId, unitId, null, p, s)
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS,
            name = "hiiadmin_teacherS_fetchANormalTeacher", key = "#schoolId + '_' + #campusId + '_' + #sectionId + '_' + #gradeId + '_' + #unitId + '_' + #subjectId + '_' + #p + '_' + #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllNormalTeacher(long schoolId, Long campusId, Long sectionId, Long gradeId, Long unitId, Long subjectId, int p, int s) {
        StringBuffer sb = new StringBuffer()
        sb.append("""FROM Teacher t WHERE t.id IN (SELECT teacherId FROM""")
        if (subjectId) {
            sb.append(""" TeacherSubject ts WHERE ts.subjectId = """).append(subjectId).append(" AND ts.schoolId = ").append(schoolId)
        } else if (sectionId || gradeId || unitId) {
            sb.append(""" UnitTeacher ts WHERE ts.schoolId = """).append(schoolId)
        } else {
            sb.append(""" TeacherSchoolCampus ts WHERE ts.schoolId = """).append(schoolId)
        }
        if (campusId) {
            sb.append(" AND ts.campusId = ").append(campusId)
        }
        if (sectionId) {
            sb.append(" AND ts.sectionId = ").append(sectionId)
        }
        if (gradeId) {
            sb.append(" AND ts.gradeId = ").append(gradeId)
        }
        if (unitId && !subjectId) {
            sb.append(" AND ts.unitId = ").append(unitId)
        }
        sb.append(" AND ts.status = 1 ) AND t.status = 1 ORDER BY  t.lastUpdated DESC")
        Teacher.findAll(sb.toString(), [max   : s,
                                        offset: (p - 1) * s])
    }

    CardUser fetchTeacherCardUserByUserId(Long teacherId) {
        CardUser.findOrCreateByUserIdAndTypeAndStatus(teacherId, UserTypeEnum.TEACHER.type, 1 as byte)
    }

    TeacherSchoolCampus fetchTeacherSchoolBySchoolIdAndCampusIdAndTeacherId(Long schoolId, Long campusId, Long teacherId) {
        TeacherSchoolCampus.findBySchoolIdAndCampusIdAndTeacherIdAndStatus(schoolId, campusId, teacherId, 1 as byte)
    }

    TeacherSchoolCampus fetchTeacherSchoolCampusByCampusIdAndTeacherId(Long campusId, Long teacherId) {
        TeacherSchoolCampus.findByCampusIdAndTeacherIdAndStatus(campusId, teacherId, 1 as Byte)
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolCampusByTeacherIdInListAndCampusId(Long campusId, List<Long> teacherIds) {
        TeacherSchoolCampus.findAllByCampusIdAndTeacherIdInListAndStatus(campusId, teacherIds, 1 as Byte)
    }

    TeacherSchoolCampus fetchTeacherSchoolByCampusIdAndJobNum(Long campusId, String jobNum) {
        TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(campusId, jobNum, 1 as byte)
    }

    def getTeacherIdUnitTeacherListMapAndGradeIdListAndSubjectIdList(Long schoolId, Long campusId, List<Long> teacherIdList, Byte type) {
        Map<Long, List<UnitTeacher>> longListMap = [:]
        Set<Long> gradeIdList = []
        if (teacherIdList && type != null && schoolId) {
            List<UnitTeacher> unitTeacherList = UnitTeacher.findAllBySchoolIdAndCampusIdAndTeacherIdInListAndTypeAndStatus(schoolId, campusId, teacherIdList, type, UnitTeacher.NORMAL)
            unitTeacherList.each { UnitTeacher unitTeacher ->
                if (!longListMap[unitTeacher.teacherId]) {
                    longListMap[unitTeacher.teacherId] = []
                }
                longListMap[unitTeacher.teacherId] << unitTeacher
                gradeIdList << unitTeacher.gradeId
            }
        }
        [map: longListMap, gradeIds: gradeIdList.toList()]
    }

    Table<Long, Long, Unit> fetchTeacherIdUnitIdUnitByCampusId(Long campusId) {
        String HQL = """SELECT u_t.teacherId, u FROM UnitTeacher u_t LEFT JOIN Unit u ON u_t.unitId = u.id WHERE u_t.campusId = :campusId AND u_t.status = 1 AND u.campusId = :campusId AND u.status = 1"""
        def list = UnitTeacher.executeQuery(HQL, [campusId: campusId])
        Table<Long, Long, Unit> table = HashBasedTable.create()
        list.each {
            Long teacherId = it[0] as Long
            Unit unit = it[1] as Unit
            Long unitId = unit?.id
            table.put(teacherId, unitId, unit)
        }
        table
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBCampusId",
            key = "#campusId + '_' #searchValue + '_' + #p + '_' #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByCampusIdLimit(Long campusId, String searchValue, int p, int s) {
        if (!searchValue) {
            //不存在搜索条件下这个效率更高
            //没找到原因
            return fetchAllTeacherByCampusIdLimit(campusId, p, s)
        }
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("FROM Teacher t WHERE t.status = 1")
        if (searchValue) {
            sb.append(" AND (t.name LIKE :name OR mobile LIKE :mobile)")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("mobile", value)
        }
        sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("campusId", campusId)
        map.put("max", s)
        map.put("offset", (p - 1) * s)
        Teacher.executeQuery(sb.toString(), map)
    }

    List<Teacher> pageTeacherByCampusId(Long campusId, String searchValue, String roleIds, String departmentIds, int p, int s) {
        
        List<Long> teacherIdList = []
        
        if (roleIds || departmentIds) {
            Campus campus = campusService.fetchCampusByCampusId(campusId)
            List<Long> staffIdList = []
            if (roleIds) {
                List<Long> roleIdList = roleIds?.split(",")?.collect { it.toLong() }
                List<BgbStaffRole> bgbStaffRoleList = BgbStaffRole.findAllByRoleIdInListAndStatus(roleIdList, 1 as byte)
                List<Long> staffIdListByRole = bgbStaffRoleList*.staffId
                
                staffIdList.addAll(staffIdListByRole)
            }

            if (departmentIds) {
                List<Long> departmentIdList = departmentIds?.split(",")?.collect{it.toLong()}
                if (campus && campus.type == CampusType.K12.type) {
                    
                    if (departmentIdList.contains(-1l)) {
                        List<Teacher> teacherList = fetchAllNormalTeacher(campus.schoolId, campus.id, 1, 10000)
                        List<Long> allTeacherIds = teacherList*.id
                        List<Department> departmentList = Department.findAllByCampusIdAndStatus(campus.id, 1 as byte)
                        if (departmentList) {
                            List<StaffDepartment> allStaffDepartment = StaffDepartment.findAllByDepartmentIdInListAndStatus(departmentList*.id, 1 as byte)
                            List<Long> minusTeacherIds = allStaffDepartment*.teacherId
                            List<Long> noDepartmentTeacherIdList = allTeacherIds - minusTeacherIds
                            teacherIdList.addAll(noDepartmentTeacherIdList)
                        }
                    }
                    List<StaffDepartment> staffDepartmentList = StaffDepartment.findAllByDepartmentIdInListAndStatus(departmentIdList, 1 as byte)
                    List<Long> teacherIdListByDepartment = staffDepartmentList*.teacherId

                    teacherIdList.addAll(teacherIdListByDepartment)
                } else {
                    List<FacultyUser> facultyUserList = FacultyUser.findAllByFacultyIdInListAndUserTypeAndStatus(departmentIdList, 6 as byte, 1 as byte)
                    List<Long> teacherIdListByFaculty = facultyUserList*.userId
                    
                    teacherIdList.addAll(teacherIdListByFaculty)
                }
            }
            
            staffIdList?.removeAll([null])
            staffIdList?.unique()
            
            if (staffIdList?.size() > 0) {
                List<Staff> staffList = Staff.findAllByIdInListAndStatus(staffIdList, 1 as byte)
                List<Long> teacherIdListByStaff = staffList*.teacherId
                
                if (departmentIds) {
                    teacherIdList = CollectionUtils.intersection(teacherIdListByStaff, teacherIdList)
                } else {
                    teacherIdList.addAll(teacherIdListByStaff)
                }
            }

            teacherIdList?.removeAll([null])
            teacherIdList?.unique()
            if (teacherIdList?.size() > 0) {

                Map<String, Object> map = [:]
                StringBuilder sb = new StringBuilder()
                sb.append("FROM Teacher t WHERE t.id IN :teacherIdList AND t.status = 1")
                if (searchValue) {
                    if (searchValue.isNumber()) {
                        if (searchValue.length() < 4) {
                            return []
                        } else {
                            List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                            sb.append(" AND (t.id IN :userIdList)")
                            map.put("userIdList", userIdList)
                        }
                    } else {
                        sb.append(" AND (t.name LIKE :name OR t.simpleName LIKE :name)")
                        String value = searchValue + "%"
                        map.put("name", value)
                    }
                }
                sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
                sb.append(" ORDER BY t.lastUpdated DESC")
                map.put("teacherIdList", teacherIdList)
                map.put("campusId", campusId)
                map.put("max", s)
                map.put("offset", (p - 1) * s)
                Teacher.executeQuery(sb.toString(), map)
            } else {
                return []
            }
        }
        //原先逻辑不动
        else {
            if (!searchValue) {
                //不存在搜索条件下这个效率更高
                //没找到原因
                return fetchAllTeacherByCampusIdLimit(campusId, p, s)
            }
            Map<String, Object> map = [:]
            StringBuilder sb = new StringBuilder()
            sb.append("FROM Teacher t WHERE t.status = 1")
            if (searchValue) {
                if (searchValue.isNumber()) {
                    if (searchValue.length() < 4) {
                        return []
                    } else {
                        List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                        sb.append(" AND (t.id IN :userIdList)")
                        map.put("userIdList", userIdList)
                    }
                } else {
                    sb.append(" AND (t.name LIKE :name OR t.simpleName LIKE :name)")
                    String value = searchValue + "%"
                    map.put("name", value)
                }
            }
            sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
            sb.append(" ORDER BY t.lastUpdated DESC")
            map.put("campusId", campusId)
            map.put("max", s)
            map.put("offset", (p - 1) * s)
            Teacher.executeQuery(sb.toString(), map)
        }
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBCIdAIdNot",
            key = "#campusId + '_' #searchValue + '_' + #idList + '_' + #p + '_' #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByCampusIdAndIdNotInListLimit(Long campusId, String searchValue, List<Long> idList, int p, int s) {
        if (!searchValue) {
            //不存在搜索条件下这个效率更高
            //没找到原因
            return fetchAllTeacherByCampusIdAndIdNotInListLimit(campusId, idList, p, s)
        }
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("FROM Teacher t WHERE t.status = 1")
        if (searchValue) {
            sb.append(" AND (t.name LIKE :name OR mobile LIKE :mobile)")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("mobile", value)
        }
        sb.append(" AND t.id NOT IN :idList")
        map.put("idList", idList)
        sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("campusId", campusId)
        map.put("max", s)
        map.put("offset", (p - 1) * s)
        Teacher.executeQuery(sb.toString(), map)
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_countATBCIdAIdNotLimit",
            key = "#campusId + '_' #searchValue + '_' + #idList",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Integer countTeacherByCampusIdAndIdNotInList(Long campusId, String searchValue, List<Long> idList) {
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("SELECT count(t.id) FROM Teacher t WHERE t.status = 1")
        if (searchValue) {
            sb.append(" AND (t.name LIKE :name OR mobile LIKE :mobile)")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("mobile", value)
        }
        sb.append(" AND t.id NOT IN :idList")
        map.put("idList", idList)
        sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("campusId", campusId)
        map.put("max", 1)
        map.put("offset", 0)
        Teacher.executeQuery(sb.toString(), map)?.get(0) as Integer ?: 0
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_countATBCIdAndSearch",
            key = "#campusId + '_' #searchValue",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    Integer countTeacherByCampusIdAndSearchValue(Long campusId, String searchValue) {
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("SELECT count(t.id) FROM Teacher t WHERE t.status = 1")
        if (searchValue) {
            sb.append(" AND (t.name LIKE :name OR mobile LIKE :mobile)")
            String value = searchValue + "%"
            map.put("name", value)
            map.put("mobile", value)
            map.put("max", 1)
            map.put("offset", 0)
        }
        sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.campusId = :campusId AND t_s.status = 1)")
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("campusId", campusId)
        Teacher.executeQuery(sb.toString(), map)?.get(0) as Integer ?: 0
    }

    @Transactional(readOnly = true)
    Integer countTeacherByCampusId(Long campusId) {
        TeacherSchoolCampus.countByCampusIdAndStatus(campusId, 1 as byte)
    }

    Integer countTeacherByCampusIdAndAuth(Long campusId, String roleIds, String departmentIds) {
        
        List<Long> teacherIdList = []
        if (roleIds || departmentIds) {
            Campus campus = campusService.fetchCampusByCampusId(campusId)
            List<Long> staffIdList = []
            if (roleIds) {
                List<Long> roleIdList = roleIds.split(",").collect { it.toLong() }
                List<BgbStaffRole> bgbStaffRoleList = BgbStaffRole.findAllByRoleIdInListAndStatus(roleIdList, 1 as byte)
                List<Long> staffIdListByRole = bgbStaffRoleList*.staffId
                
                staffIdList.addAll(staffIdListByRole)
            }

            if (departmentIds) {
                List<Long> departmentIdList = departmentIds?.split(",")?.collect{it.toLong()}
                if (campus && campus.type == 1 as byte) {

                    if (departmentIdList.contains(-1l)) {
                        List<Teacher> teacherList = fetchAllNormalTeacher(campus.schoolId, campus.id, 1, 10000)
                        List<Long> allTeacherIds = teacherList*.id
                        List<Department> departmentList = Department.findAllByCampusIdAndStatus(campus.id, 1 as byte)
                        if (departmentList) {
                            List<StaffDepartment> allStaffDepartment = StaffDepartment.findAllByDepartmentIdInListAndStatus(departmentList*.id, 1 as byte)
                            List<Long> minusTeacherIds = allStaffDepartment*.teacherId
                            List<Long> noDepartmentTeacherIdList = allTeacherIds - minusTeacherIds
                            teacherIdList.addAll(noDepartmentTeacherIdList)
                        }
                    }
                    
                    List<StaffDepartment> staffDepartmentList = StaffDepartment.findAllByDepartmentIdInListAndStatus(departmentIdList, 1 as byte)
                    List<Long> teacherIdListByDepartment = staffDepartmentList*.teacherId
                    teacherIdList.addAll(teacherIdListByDepartment)
                } else {
                    List<FacultyUser> facultyUserList = FacultyUser.findAllByFacultyIdInListAndUserTypeAndStatus(departmentIdList, 6 as byte, 1 as byte)
                    List<Long> teacherIdListByFaculty = facultyUserList*.userId

                    teacherIdList.addAll(teacherIdListByFaculty)
                }
            }

            staffIdList?.removeAll([null])
            staffIdList?.unique()
            
            if (staffIdList?.size() > 0) {
                List<Staff> staffList = Staff.findAllByIdInListAndStatus(staffIdList, 1 as byte)
                List<Long> teacherIdListByStaff = staffList*.teacherId
                teacherIdList.addAll(teacherIdListByStaff)

                if (departmentIds) {
                    teacherIdList = CollectionUtils.intersection(teacherIdListByStaff, teacherIdList)
                } else {
                    teacherIdList.addAll(teacherIdListByStaff)
                }
            }
            teacherIdList?.removeAll([null])
            teacherIdList?.unique()

            if (teacherIdList?.size() > 0) {
                TeacherSchoolCampus.countByCampusIdAndTeacherIdInListAndStatus(campusId, teacherIdList, 1 as byte)
            } else {
                return 0
            }
        } else {
            countAllTeacherByNameOrMobileOrJobNum(campusId, null)
        }
    }


    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBSchoolId",
            key = "#schoolId + '_' #searchValue + '_' + #p + '_' #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherBySchoolIdLimit(Long schoolId, String searchValue, int p, int s) {
        if (!searchValue) {
            return fetchAllTeacherBySchoolIdLimit(schoolId, p, s)
        }
        Map<String, Object> map = [:]
        StringBuilder sb = new StringBuilder()
        sb.append("FROM Teacher t WHERE t.status = 1")
        if (searchValue) {
            if (searchValue.isNumber()) {
                if (searchValue.length() < 4) {
                    return []
                } else {
                    List<Long> userIdList = userEncodeInfoService.fetchUserIdListByTypeAndUserTypeAndDecodeInfo(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, searchValue)
                    sb.append(" AND (t.id IN :userIdList)")
                    map.put("userIdList", userIdList)
                }
            } else {
                sb.append(" AND (t.name LIKE :name OR t.simpleName LIKE :name)")
                String value = searchValue + "%"
                map.put("name", value)
            }
        }
        sb.append(" AND EXISTS(SELECT t_s.id FROM TeacherSchoolCampus t_s WHERE t_s.teacherId = t.id AND t_s.schoolId = :schoolId AND t_s.status = 1)")
        sb.append(" ORDER BY t.lastUpdated DESC")
        map.put("schoolId", schoolId)
        map.put("max", s)
        map.put("offset", (p - 1) * s)
        Teacher.executeQuery(sb.toString(), map)
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBSchoolId",
            key = "#schoolId + '_' + #p + '_' #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherBySchoolIdLimit(Long schoolId, Integer p = null, int s = 30) {
        String HQL = """SELECT t FROM Teacher t 
                                WHERE t.status = 1 
                                    AND EXISTS ( SELECT tsc.id FROM TeacherSchoolCampus tsc 
                                                        WHERE tsc.schoolId = :schoolId 
                                                            AND tsc.status = 1 
                                                            AND tsc.teacherId = t.id ) 
                                    ORDER BY t.lastUpdated DESC"""
        if (p) {
            Teacher.executeQuery(HQL, [schoolId: schoolId,
                                       max     : s,
                                       offset  : (p - 1) * s])
        } else {
            Teacher.executeQuery(HQL, [schoolId: schoolId])
        }
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBCampusId",
            key = "#campusId + '_' + #p + '_' '#s'",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByCampusIdLimit(Long campusId, int p, int s = 30) {

        String HQL = """ SELECT t FROM Teacher t, TeacherSchoolCampus tsc
                                    WHERE t.id = tsc.teacherId AND tsc.campusId = :campusId 
                                    AND t.status = 1 AND tsc.status = 1
                                    ORDER BY tsc.id DESC """


        Teacher.executeQuery(HQL, [campusId: campusId,
                                   max     : s,
                                   offset  : (p - 1) * s])

    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBCampusId",
            key = "#campusId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByCampusIdLimit(Long campusId, boolean cache = true) {

        String HQL = """ SELECT t FROM Teacher t, TeacherSchoolCampus tsc
                                    WHERE t.id = tsc.teacherId AND tsc.campusId = :campusId 
                                    AND t.status = 1 AND tsc.status = 1
                                    ORDER BY tsc.id DESC """

        Teacher.executeQuery(HQL, [campusId: campusId])
    }

    @Cached(expire = 12, timeUnit = TimeUnit.HOURS, name = "hiiadmin_teacherS_fetchATBCIdAIdNot",
            key = "#campusId + '_' + #idList + '_' + #p + '_' #s",
            cacheNullValue = false, cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    List<Teacher> fetchAllTeacherByCampusIdAndIdNotInListLimit(Long campusId, List<Long> idList, Integer p = null, int s = 30) {
        String HQL = """SELECT t FROM Teacher t 
                                WHERE t.status = 1 
                                AND t.id NOT IN :idList 
                                    AND EXISTS ( SELECT tsc.id FROM TeacherSchoolCampus tsc 
                                                        WHERE tsc.campusId = :campusId 
                                                            AND tsc.status = 1 
                                                            AND tsc.teacherId = t.id ) 
                                    ORDER BY t.id DESC"""
        if (p) {
            Teacher.executeQuery(HQL, [campusId: campusId,
                                       idList  : idList,
                                       max     : s,
                                       offset  : (p - 1) * s])
        } else {
            Teacher.executeQuery(HQL, [campusId: campusId])
        }
    }


    Integer countTeacherBySchoolId(Long schoolId) {
        def count = TeacherSchoolCampus.createCriteria().get {
            eq("schoolId", schoolId)
            eq("status", 1 as byte)
            projections {
                countDistinct('teacherId')
            }
        }
        count as Integer
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolBySchoolIdAndTeacherId(Long schoolId, Long teacherId) {
        TeacherSchoolCampus.findAllBySchoolIdAndTeacherIdAndStatus(schoolId, teacherId, 1 as byte)
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolByCampusId(Long campusId) {
        TeacherSchoolCampus.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolByTeacherId(Long teacherId) {
        TeacherSchoolCampus.findAllByTeacherIdAndStatus(teacherId, 1 as byte)
    }

    Map<Long, String> getTeacherNumByTeacherId(List<Long> teacherIdList, Long campusId) {
        fetchTeacherSchoolByTeacherIdListAndCampusId(teacherIdList, campusId)?.collectEntries {
            [it.teacherId, it.jobNum]
        }

    }

    List<TeacherSchoolCampus> fetchTeacherSchoolByTeacherIdListAndCampusId(List<Long> teacherIdList, Long campusId) {
        TeacherSchoolCampus.findAllByCampusIdAndTeacherIdInListAndStatus(campusId, teacherIdList, 1 as Byte)
    }

    Map<Long, String> fetchAnnTeacherIdJobNumByCampusId(Long campusId) {
        fetchAllTeacherSchoolByCampusId(campusId)?.collectEntries {
            [it.teacherId, it.jobNum]
        }
    }

    TeacherSchoolCampus fetchTeacherSchoolCampusViaCampusIdAndJobNum(Long campusId, String jobNum) {
        TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(campusId, jobNum, 1 as byte)
    }

    void updateTeacherAvatar(long teacherId, String avatar) {
        Teacher teacher = Teacher.get(teacherId)
        if (teacher) {
            teacher.avatar = avatar
            teacher.save(failOnError: true, flush: true)
        }
    }


    void updateTeacherIdCard(long teacherId, String idCard) {
        def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
        if (!re.success) {
            throw new HiiAdminException(re.message)
        }
        Teacher t = userEncodeInfoService.fetchTeacherByTrueIdCard(idCard)
        if (t && t.id != teacherId) {
            throw new HiiAdminException("身份证号码已存在")
        }

        userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.TEACHER.type, teacherId, UserEncodeInfoType.ID_CARD.type)
    }

    Teacher fetchTeacherByIdCardNoEqTeacherId(String idCard, Long teacherId) {
        Teacher.findByIdCardAndIdNotEqualAndStatus(idCard, teacherId, 1 as byte)
    }

    Teacher createAndSaveNewTeacher(Long schoolId, Long campusId, String name, String code, String mobile, String avatar, String cardNum, Boolean gender, String idCard) {
        if (!PatternUtils.isMobile(mobile, envService)) {
            throw new HiiAdminException("请输入正确的手机号,errorMobile:${mobile}")
        }
        Teacher teacher = fetchOrCreateByTrueMobileAndStatus(mobile)
        teacher.name = name
        teacher.simpleName= UserNameCorrectUtil.extractInitialsName(name)
        if (avatar) {
            teacher.avatar = PatternUtils.getFileName(avatar)
        }
        if (null != gender) {
            teacher.gender = gender
        }
        if (idCard) {
            def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
            if (!re.success) {
                throw new HiiAdminException(re.message)
            }
            Teacher t = userEncodeInfoService.fetchTeacherByTrueIdCard(idCard)
            if (t) {
                throw new HiiAdminException("身份证号已存在！")
            }
            userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.TEACHER.type, teacher.id, UserEncodeInfoType.ID_CARD.type)
        }
        saveTeacher(teacher)
        TeacherSchoolCampus t = fetchTeacherSchoolBySchoolIdAndCampusIdAndTeacherId(schoolId, campusId, teacher.id)
        if (t) {
            throw new HiiAdminException("手机号：" + mobile + "对应老师在该校区已存在，请勿重复创建")
        }
        if(code && !PatternUtils.validCardNum(code)){
            throw new HiiAdminException("工号仅可输入大小写字母和数字，上限20字符")
        }
        //工号是否已存在
        if (code?.startsWith("T")) {
            code = code.substring(1)
        }
        TeacherSchoolCampus isJobNum = fetchTeacherSchoolCampusViaCampusIdAndJobNum(campusId, "T" + code)
        if (isJobNum) {
            throw new HiiAdminException("该工号在当前校区下已存在，请勿重复创建")
        }

        if (cardNum) {
            if (!PatternUtils.validCardNum(cardNum)) {
                throw new HiiAdminException("卡号格式不支持")
            }
            cardUserService.createOrUpdateCardUser4teacher(schoolId, teacher.id, cardNum)
        }

        TeacherSchoolCampus teacherSchool = new TeacherSchoolCampus(teacherId: teacher.id,
                jobNum: "T" + code,
                schoolId: schoolId,
                campusId: campusId,
                status: 1 as byte)
        teacherSchool.save(failOnError: true, flush: true)

        //完善checkFace信息
        if (teacher.avatar) {
            CheckFace checkFace = checkFaceService.fetchLastOneCheckFaceByCampusIdAndUrlMd5AndName(campusId, teacher.avatar, null)
            if (checkFace) {
                checkFace.userId = teacher?.id
                checkFace.userName = teacher?.name
                checkFaceService.saveCheckFace(checkFace)
            }
        }

        teacher
    }

    /**
     * 删除老师所有教学科目
     * @param schoolId
     * @param teacherId
     */
    void deleteAllTeacherSubjectBySchoolIdAndTeacherId(Long schoolId, Long teacherId) {
        List<TeacherSubject> teacherSubjectList = TeacherSubject.findAllBySchoolIdAndTeacherId(schoolId, teacherId)
        if (teacherSubjectList) {
            teacherSubjectList*.status = TeacherSubject.DELETE
            teacherSubjectList*.delete(failOnError: true, flush: true)
        }
    }

    void deleteAllUnitTeacherBySchoolIdAndTeacherIdAndType(Long schoolId, Long campusId, Long teacherId, Byte type) {
        List<UnitTeacher> unitTeacherList = UnitTeacher.findAllBySchoolIdAndCampusIdAndTeacherIdAndType(schoolId, campusId, teacherId, type)
        if (unitTeacherList) {
            unitTeacherList*.status = UnitTeacher.DELETE
            unitTeacherList*.save(failOnError: true, flush: true)
        }
    }

    @Transactional(readOnly = true)
    boolean hasTeacherBySchoolIdAndCode(Long schoolId, String code, Long teacherId = null) {
        //工号是否已存在
        boolean flag = false
        if (code && !code.startsWith("T")) {
            code = "T" + code
        }
        TeacherSchoolCampus teacherSchool = TeacherSchoolCampus.findBySchoolIdAndJobNumAndStatus(schoolId, code, 1 as byte)
        if (teacherSchool) {
            flag = true
            if (teacherId == teacherSchool.teacherId) {
                flag = false
            }
        }
        flag
    }

    @Transactional(readOnly = true)
    boolean hasTeacherByCampusIdAndCode(Long campusId, String code, Long teacherId = null) {
        //工号是否已存在
        boolean flag = false
        if (code && !code.startsWith("T")) {
            code = "T" + code
        }
        TeacherSchoolCampus teacherSchool = TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(campusId, code, 1 as byte)
        if (teacherSchool) {
            flag = true
            if (teacherId == teacherSchool.teacherId) {
                flag = false
            }
        }
        flag
    }

    void saveTeacherSchool(TeacherSchoolCampus teacherSchool) {
        teacherSchool.save(failOnError: true, flush: true)
    }

    @Transactional(readOnly = true)
    boolean hasTeacherByMobile(String mobile, Long teacherId = null) {
        boolean flag = false
        Teacher teacher = userEncodeInfoService.fetchTeacherByTrueMobile(mobile)
        if (teacher) {
            flag = true
            if (teacherId == teacher.id) {
                flag = false
            }
        }
        flag
    }

    @Cached(expire = 1, cacheNullValue = false, timeUnit = TimeUnit.HOURS, name = "teacherService_fetchTeacherByMobile",
            key = "#mobile", cacheType = CacheType.REMOTE)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 3, timeUnit = TimeUnit.MINUTES)
    Teacher fetchTeacherByMobile(String mobile) {
        Teacher.findByMobileAndStatus(mobile, 1 as byte)
    }

    void saveTeacher(Teacher teacher) {
        teacher.save(failOnError: true, flush: true)
        staffService.updateStaffMobile(teacher.id, userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacher.id))
    }

    @Cached(expire = 1, timeUnit = TimeUnit.DAYS, name = "hiiAdmin_teacherS_fetchTSBCAndTId", key = "#campusId + '_' + #teacherId",
            cacheNullValue = false, cacheType = CacheType.REMOTE, condition = "#cache eq null or #cache")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 2, timeUnit = TimeUnit.MINUTES)
    TeacherSchoolCampus fetchTeacherSchoolByCampusIdAndTeacherId(Long campusId, Long teacherId, boolean cache = true) {
        TeacherSchoolCampus.findByCampusIdAndTeacherIdAndStatus(campusId, teacherId, 1 as byte)
    }

    Map<String, Teacher> createAndSaveNewTeacherBatch(String teacherJson, Campus campus) {
        Long campusId = campus.id
        Long schoolId = campus.schoolId
        List<JSONTeacher> jsonTeacherList = JSON.parseArray(teacherJson, JSONTeacher.class)
        Set<String> codeSet = Sets.newHashSet()
        Set<String> mobileSet = Sets.newHashSet()
        for (JSONTeacher jsonTeacher : jsonTeacherList) {
            if (!jsonTeacher.mobile) {
                throw new HiiAdminException("手机号存在空值，请检查后重新录入")
            }
            if (!PatternUtils.isMobile(jsonTeacher.mobile, envService)) {
                throw new HiiAdminException("手机号${jsonTeacher.mobile}存在错误，请检查后重新录入")
            }
            mobileSet.add(jsonTeacher.mobile)
            Preconditions.checkNotNull(jsonTeacher.code, "工号存在空值，请检查后重新录入")
            if (!jsonTeacher.code) {
                throw new HiiAdminException("工号存在空值，请检查后重新录入")
            }
            if (hasTeacherBySchoolIdAndCode(schoolId, jsonTeacher.code)) {
                throw new HiiAdminException("工号${jsonTeacher.code}在该学校已存在，请检查后重新录入")
            }
            codeSet.add(jsonTeacher.code)
            if (!jsonTeacher.name) {
                throw new HiiAdminException("姓名存在空值，请检查后重新录入")
            }
        }
        Integer jsonSize = jsonTeacherList.size()
        if (jsonSize != mobileSet.size()) {
            throw new HiiAdminException("手机号存在重复值，请检查后重新录入")
        }
        if (jsonSize != codeSet.size()) {
            throw new HiiAdminException("工号存在重复值，请检查后重新录入")
        }
        Map<String, Teacher> map = [:]
        jsonTeacherList.each { JSONTeacher jsonTeacher ->
            Teacher teacher = fetchOrCreateByTrueMobileAndStatus(jsonTeacher.mobile)
            teacher.name = jsonTeacher.name
            if (jsonTeacher.gender) {
                teacher.gender = jsonTeacher.gender == "男"
            } else {
                teacher.gender = true
            }
            teacher.save(failOnError: true, flush: true)
            map.put(jsonTeacher.code, teacher)
            TeacherSchoolCampus teacherSchool = TeacherSchoolCampus.findOrCreateBySchoolIdAndTeacherIdAndStatus(schoolId, teacher.id, 1 as byte)
            teacherSchool.jobNum = "T" + jsonTeacher.code
            teacherSchool.campusId = campusId
            teacherSchool.save(failOnError: true, flush: true)
            if (jsonTeacher.cardNum) {
                cardUserService.createOrUpdateCardUser4teacher(schoolId, teacher.id, jsonTeacher.cardNum)
            }
            pushTeacherPersonEvent(campusId, teacher.id)
        }
        map
    }

    Teacher fetchOrCreateByTrueMobileAndStatus(String trueMobile) {
        Teacher teacher = userEncodeInfoService.fetchTeacherByTrueMobile(trueMobile)
        if (!teacher) {
            teacher = new Teacher(
                    status: 1 as byte
            )
        }
        teacher.save(failOnError: true, flush: true)
        userEncodeInfoService.saveUserEncodeInfo(trueMobile, UserTypeEnum.TEACHER.type, teacher.id, UserEncodeInfoType.MOBILE.type)
        teacher
    }

    Map<Long, String> buildTeacherIdJobNumMap(Long schoolId, Long campusId, List<Long> teacherIds) {
        Map<Long, String> map = [:]
        List<TeacherSchoolCampus> teacherSchoolCampusList = TeacherSchoolCampus.findAllBySchoolIdAndCampusIdAndTeacherIdInListAndStatus(schoolId, campusId, teacherIds, 1 as byte)
        teacherSchoolCampusList.each {
            map.put(it.teacherId, it.jobNum?.startsWith("T") ? it.jobNum.replaceFirst("T", "") : it.jobNum)
        }

        map
    }

    def fetchTeacherCodeByCampusIdAndTeacherId(Long campusId, Long teacherId) {
        TeacherSchoolCampus.findByCampusIdAndTeacherIdAndStatus(campusId, teacherId, 1 as byte)
    }

    List<Long> fetchTeacherIdsByDepartmentId(Long campusId, Long departmentId) {
        List<Long> teacherIds = []
        List<StaffDepartment> staffDepartmentList = StaffDepartment.findAllByCampusIdAndDepartmentIdAndStatus(campusId, departmentId, 1 as Byte)
        if (staffDepartmentList) {
            teacherIds = staffDepartmentList*.teacherId.unique()
        }
        return teacherIds
    }

    Multimap<Long, Teacher> fetchUnitIdTeacherIdListMapByCampusId(Long schoolId, Long campusId) {
        List<Teacher> teacherList = fetchAllNormalTeacher(schoolId, campusId, 1, 2000)
        Map<Long, Teacher> teacherIdTeacherMap = [:]
        Multimap<Long, Teacher> unitIdTeacherMap = HashMultimap.create()
        teacherList.each {
            Teacher teacher ->
                teacherIdTeacherMap.put(teacher.id, teacher)
        }
        List<UnitTeacher> unitTeacherList = UnitTeacher.findAllByCampusIdAndStatus(campusId, 1 as byte)
        unitTeacherList.each {
            if (teacherIdTeacherMap.get(it.teacherId)) {
                unitIdTeacherMap.put(it.unitId, teacherIdTeacherMap.get(it.teacherId))
            }
        }
        unitIdTeacherMap
    }

    static class JSONTeacher implements Serializable {
        String mobile
        String code
        String name
        String gender
        String cardNum
    }

    List<Teacher> fetchTeacherByCampusIdAndNameLike(Long campusId, String name) {
        String HQL = "select a from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where a.name like :name and b.campusId = :campusId and a.status = 1 and b.status = 1"
        List<Teacher> teacherList = Teacher.executeQuery(HQL, [name: "%" + name + "%", campusId: campusId])
        teacherList
    }

    List<Teacher> fetchTeacherByCampusIdAndName(Long campusId, String name) {
        String HQL = "select a from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where a.name = :name and b.campusId = :campusId and a.status = 1 and b.status = 1"
        List<Teacher> teacherList = Teacher.executeQuery(HQL, [name: name, campusId: campusId])
        teacherList
    }


    List<Teacher> fetchTeacherByName(Long campusId, String name) {
        String HQL = "select a from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where a.name = :name and b.campusId = :campusId and a.status = 1 and b.status = 1"
        List<Teacher> teacherList = Teacher.executeQuery(HQL, [name: name, campusId: campusId])
        teacherList
    }

    List<Teacher> fetchTeacherByCodeList(Long campusId, List<String> codeList) {
        String HQL = "select a from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where b.jobNum in :codeList and b.campusId = :campusId and a.status = 1 and b.status = 1"
        List<Teacher> teacherList = Teacher.executeQuery(HQL, [codeList: codeList, campusId: campusId])
        teacherList
    }


    List<TeacherVO> analysisTeacher(Long schoolId, Long campusId, String filedName, List<String> valueList) {
        List<Teacher> teacherList = []

        switch (filedName) {
            case "name":
                valueList.each {
                    if (PatternUtils.isNameLe50(it)) {
                        List<Teacher> list = fetchTeacherByName(campusId, it)
                        if (list && list.size() > 0 && list.size() <= 1) {
                            teacherList.addAll(list)
                        }
                    }
                }
                break
            case "code":
                valueList.collate(100).each {
                    List<String> list = it.collect { value ->
                        if (!value.startsWith("T")) {
                            value = "T" + value
                        }
                        value
                    }
                    teacherList.addAll(fetchTeacherByCodeList(campusId, list))
                }

                break
        }
        Map<Long, String> jobNumMap = [:]
        if (teacherList) {
            jobNumMap = buildTeacherIdJobNumMap(schoolId, campusId, teacherList*.id)
        }
        List<TeacherVO> teacherVOList = teacherList.collect {
            TeacherVO teacherVO = new TeacherVO().buildVO(it)
            teacherVO.code = jobNumMap?.get(it.id)
            teacherVO
        }

        teacherVOList
    }


    def pushTeacherPersonEvent(long campusId, long teacherId) {
        TeacherDataChange teacherPerson = new TeacherDataChange(
                campusId: campusId,
                userId: teacherId,
                userType: UserTypeEnum.TEACHER.type
        )
        TeacherPersonEvent personEvent = new TeacherPersonEvent(this, teacherPerson)
        personEvent.changeBit = PersonDataChangeEnum.getFullBit()
        applicationContext.publishEvent(personEvent)
    }

    def pushTeacherListPersonEvent(long campusId, List<Long> teacherIds) {
        teacherIds.each {
            pushTeacherPersonEvent(campusId, it)
        }
    }

    List<Long> fetchTeacherIdByIdListAndGender(Long campusId, List<Long> teacherIdList, Boolean gender) {
        if (teacherIdList.size() > 0) {
            return Teacher.findAllByIdInListAndGenderAndStatus(teacherIdList, gender, 1 as byte)*.id
        } else {
            String HQL = "select a.id from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where a.gender = :gender and a.status = 1 and b.campusId = :campusId and b.status = 1"
            return Teacher.executeQuery(HQL, [gender: gender, campusId: campusId]) as List<Long>
        }
    }

    List<Long> fetchTeacherIdByCampusId(Long campusId) {
        String HQL = "select a.id from Teacher a left join TeacherSchoolCampus b on a.id = b.teacherId where a.status = 1 and b.campusId = :campusId and b.status = 1"
        return Teacher.executeQuery(HQL, [campusId: campusId]) as List<Long>
    }

    List<Teacher> fetchAllTeacherByIdInListLimit(List<Long> teacherIdList, int p, int s) {
        if (!teacherIdList) {
            return []
        }
        Teacher.findAllByIdInListAndStatus(teacherIdList, 1 as byte, [max: s, offset: (p - 1) * s, sort: "id", order: "desc"])
    }

    def transformUnitHeadMasterNameMap(Long campusId) {
        Map<Long, Teacher> unitHeadMasterMap = [:]
        String HQL = "SELECT ut,t FROM UnitTeacher ut LEFT JOIN Teacher t ON ut.teacherId = t.id" +
                " WHERE ut.campusId =:campusId AND ut.headmaster = 1 AND ut.type = 1 AND ut.status = 1 AND t.status =1 "
        def list = UnitTeacher.executeQuery(HQL, [campusId: campusId])
        list.each { obj ->
            def (UnitTeacher unitTeacher, Teacher teacher) = obj
            unitHeadMasterMap.put(unitTeacher.unitId, teacher)
        }
        unitHeadMasterMap
    }

    def updateTeacherPassword(Long teacherId, String password) {
        Teacher teacher = fetchTeacherById(teacherId,false)
        if (teacher == null) {
            log.info("teacher not exist,teacherId@${teacherId}".toString())
            return
        }
        log.info("teacher reset password success,teacherId@${teacher.id}")
        teacher.password = password
        saveTeacher(teacher)
    }

}

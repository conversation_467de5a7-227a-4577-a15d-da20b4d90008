package hiiadmin.school.repository


import grails.gorm.transactions.Transactional
import timetabling.user.TeacherSchoolCampus

@Transactional
class RepositoryTeacherSchoolCampusService {

    TeacherSchoolCampus fetchTeacherSchoolBySchoolIdAndCampusIdAndTeacherId(Long schoolId, Long campusId, Long teacherId) {
        TeacherSchoolCampus.findBySchoolIdAndCampusIdAndTeacherIdAndStatus(schoolId, campusId, teacherId, 1 as byte)
    }
    
    TeacherSchoolCampus fetchOrCreateByCampusAndTeacherId(Long campusId, Long teacherId) {
        return TeacherSchoolCampus.findOrSaveByCampusIdAndTeacherId(campusId, teacherId)
    }

    TeacherSchoolCampus fetchTeacherSchoolCampusByCampusIdAndTeacherId(Long campusId, Long teacherId) {
        TeacherSchoolCampus.findByCampusIdAndTeacherIdAndStatus(campusId, teacherId, 1 as Byte)
    }

    List<TeacherSchoolCampus> fetchAllTeacherSchoolCampusByTeacherIdInListAndCampusId(Long campusId, List<Long> teacherIds) {
        TeacherSchoolCampus.findAllByCampusIdAndTeacherIdInListAndStatus(campusId, teacherIds, 1 as Byte)
    }

    TeacherSchoolCampus fetchTeacherSchoolByCampusIdAndJobNum(Long campusId, String jobNum) {
        TeacherSchoolCampus.findByCampusIdAndJobNumAndStatus(campusId, jobNum, 1 as byte)
    }


    TeacherSchoolCampus saveTeacherSchoolCampus(TeacherSchoolCampus teacherSchoolCampus) {
        teacherSchoolCampus.save(flush: true, failOnError: true)
    }
    
    List<TeacherSchoolCampus> fetchAllByTeacherId(Long teacherId) {
        TeacherSchoolCampus.findAllByTeacherIdAndStatus(teacherId, 1 as Byte)
    }
    
    List<TeacherSchoolCampus> fetchAllByCampusIdListAndTeacherId(List<Long> campusIdList, Long teacherId) {
        
        if (!campusIdList) {
            return []
        }
        
        TeacherSchoolCampus.findAllByCampusIdInListAndTeacherIdAndStatus(campusIdList, teacherId, 1 as Byte)
    }
    
    def saveList(List<TeacherSchoolCampus> list) {
        list*.save(failOnError: true, flush: true)
    }
}

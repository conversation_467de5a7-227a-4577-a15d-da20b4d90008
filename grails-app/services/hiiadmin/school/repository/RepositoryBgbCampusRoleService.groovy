package hiiadmin.school.repository

import grails.gorm.transactions.Transactional
import timetabling.newMenu.BgbCampusRole

@Transactional
class RepositoryBgbCampusRoleService {
    
    BgbCampusRole fetchOrCreateByUnionCode(Long campusId, String unionCode, Integer syncType) {
        BgbCampusRole.findOrCreateByCampusIdAndUnionCodeAndSyncType(campusId, unionCode, syncType)
    }
    
    List<BgbCampusRole> fetchAllByUnionCode(String unionCode, Integer syncType) {
        BgbCampusRole.findAllByUnionCodeAndSyncTypeAndStatus(unionCode, syncType, 1 as byte)
    }
    
    def save(BgbCampusRole bgbCampusRole) {
        bgbCampusRole.save(failOnError: true, flush: true)
    }
    
    def saveAll(List<BgbCampusRole> bgbCampusRoleList) {
        bgbCampusRoleList*.save(failOnError: true, flush: true)
    }
    
    List<BgbCampusRole> fetchAllCampusRoleByCampusIds(List<Long> campusIds) {
        if (!campusIds) {
            return []
        }
        BgbCampusRole.findAllByCampusIdInListAndStatus(campusIds, 1 as byte)
    }
}

package hiiadmin.school.repository

import grails.gorm.transactions.Transactional
import timetabling.user.User

@Transactional
class RepositoryUserService {
    
    User fetchOrCreateByUid(Long uId, Byte userType) {
        User.findOrCreateByUIdAndType(uId, userType)
    }
    
    User fetchByUid(Long uId, Byte userType) {
        User.findByUIdAndType(uId, userType)
    }
    
    def save(User user) {
        user.save(failOnError: true, flush: true)
    }
}

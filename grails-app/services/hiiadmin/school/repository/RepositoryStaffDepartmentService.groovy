package hiiadmin.school.repository

import grails.gorm.transactions.Transactional
import timetabling.StaffDepartment

@Transactional
class RepositoryStaffDepartmentService {
    
    StaffDepartment fetchOrCreateByCampusIdAndDepartmentIdAndTeacherId(Long campusId, Long departmentId, Long teacherId) {
        
        return StaffDepartment.findOrCreateByCampusIdAndDepartmentIdAndTeacherId(campusId, departmentId, teacherId)
    }
    
    List<StaffDepartment> fetchAllByTeacherIdAndCampusId(Long teacherId, Long campusId) {
        return StaffDepartment.findAllByTeacherIdAndCampusIdAndStatus(teacherId, campusId, 1 as byte)
    }
    
    List<StaffDepartment> fetchAllStaffDepartmentByCampusIdsAndTeacherId(List<Long> campusIds, Long teacherId) {
        
        if (!campusIds) {
            return []
        }
        
        return StaffDepartment.findAllByCampusIdInListAndTeacherIdAndStatus(campusIds, teacherId, 1 as byte)
    }
    
    def save(StaffDepartment staffDepartment) {
        staffDepartment.save(failOnError: true, flush: true)
    }
    
    def saveList(List<StaffDepartment> staffDepartmentList) {
        staffDepartmentList*.save(failOnError: true, flush: true)
    }

    
    List<StaffDepartment> fetchAllStaffDepartmentByDepartmentIdList(List<Long> departmentIdList) {
        if (!departmentIdList) {
            return []
        }
        return StaffDepartment.findAllByDepartmentIdInListAndStatus(departmentIdList, 1 as byte)
    }

    List<StaffDepartment> fetchAllByCampusIdAndStatus(Long campusId){
        return StaffDepartment.findAllByCampusIdAndStatus(campusId, 1 as byte)
    }
}

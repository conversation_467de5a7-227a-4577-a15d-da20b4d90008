package hiiadmin

import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import grails.gorm.transactions.Transactional
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.CampusTeachingWeekVO
import hiiadmin.school.SchoolService
import org.apache.commons.collections.CollectionUtils
import org.joda.time.DateTime
import timetabling.CampusTeachingWeek
import timetabling.org.Semester

import java.util.concurrent.TimeUnit

@Transactional
class CampusTeachingWeekService {

    SchoolService schoolService

    /**
     * 自然周前端算
     * @param campusId
     * @param semester
     * @return
     */
    List<CampusTeachingWeekVO> fetchAllCampusTeachingWeek(Long campusId, Semester semester) {

        List<CampusTeachingWeekVO> weekVOList = []
        List<CampusTeachingWeek> campusTeachingWeekList = CampusTeachingWeek.findAllByCampusIdAndSemesterIdAndStatus(campusId, semester.id, 1 as byte, [sort: 'dayStamp', order: 'asc'])
        weekVOList = campusTeachingWeekList?.collect { campusTeachingWeek ->
            new CampusTeachingWeekVO().buildVO(campusTeachingWeek)
        }
        
        weekVOList
    }
    

    def createOrUpdateCampusTeachingWeek(Long campusId, Long semesterId, Long startTime, Integer dayLimit) {
        if (dayLimit < 2) {
            throw new HiiAdminException("周期应不少于2！")
        }
        DateTime startDayTime = new DateTime(startTime).withMillisOfDay(0)
        DateTime nowDayTime = new DateTime().withMillisOfDay(0)

        if (startTime < nowDayTime.millis) {
            throw new HiiAdminException("起始日期不能早于当前时间")
        }
        Semester semester = schoolService.fetchSemesterBySemesterId(semesterId)
        Date semesterStartDate = semester.startDate
        if (startTime < semesterStartDate.time) {
            throw new HiiAdminException("起始日期不能早于学期开始时间")
        }
        Date semesterEndDate = semester.endDate
        if (semesterEndDate.time < startTime) {
            throw new HiiAdminException("起始日期不能晚于学期结束时间")
        }
        CampusTeachingWeek thisCampusTeachingWeek = fetchCampusTeachingWeekBySemesterAndDay(semester.id, nowDayTime.millis)
        //判断今日有没有配置教学周
        if (thisCampusTeachingWeek) {
            int teachingWeekIndex = thisCampusTeachingWeek.teachingWeekIdx
            //获取今日教学周内最晚日期
            Long weekMaxDay = getMaxDay4weekIndex(semester.id, teachingWeekIndex)
            if (weekMaxDay >= startDayTime.millis) {
                throw new HiiAdminException("起始日期仅能选择本教学周之后日期")
            }
        }
        int newStartTeachingWeekIndex = 1
        CampusTeachingWeek campusTeachingWeek = fetchCampusTeachingWeekBySemesterAndDay(semester.id, startDayTime.millis)
        if (campusTeachingWeek) {
            newStartTeachingWeekIndex = campusTeachingWeek?.teachingWeekIdx
            //获取教学周内最早日期
            Long weekMinDay = getMinDay4weekIndex(semesterId, newStartTeachingWeekIndex)
            if (weekMinDay != startDayTime.millis) {
                newStartTeachingWeekIndex++
            }
        } else {
            CampusTeachingWeek lastDayCampusTeachingWeek = fetchCampusTeachingWeekBySemesterAndDay(semester.id, startDayTime.minusDays(1).millis)
            if (lastDayCampusTeachingWeek) {
                newStartTeachingWeekIndex = lastDayCampusTeachingWeek.teachingWeekIdx + 1
            }
        }
        removeAllCampusTeachingWeekByDayGenderThen(semester.id, startDayTime.millis)
//        DateTime dateTime = startDayTime
        for (DateTime dateTime = startDayTime; dateTime.millis <= semesterEndDate.time; dateTime = dateTime.plusDays(dayLimit)) {
            int flag = 0
            while (flag < dayLimit && dateTime.plusDays(flag).millis <= semesterEndDate.time) {
                CampusTeachingWeek teachingWeek = CampusTeachingWeek.findOrCreateWhere(
                        campusId: campusId,
                        semesterId: semester.id,
                        dayStamp: dateTime.plusDays(flag).millis
                )
                teachingWeek.status = 1 as byte
                teachingWeek.teachingWeekIdx = newStartTeachingWeekIndex

                teachingWeek.save(failOnError: true, flush: true)
                flag++
            }
            newStartTeachingWeekIndex++
        }

    }

    CampusTeachingWeek fetchCampusTeachingWeekBySemesterAndDay(Long semesterId, Long dayStamp) {
        CampusTeachingWeek.findBySemesterIdAndDayStampAndStatus(semesterId, dayStamp, 1 as byte)
    }

    def getTeachingWeekStartAndEndBySemesterAndIdx(Long semesterId, Integer weekIndex) {
        List<CampusTeachingWeek> teachingWeekList = fetchAllCampusTeachingWeekBySemesterAndWeekIndex(semesterId, weekIndex)
        [satrtDate: teachingWeekList?.first()?.dayStamp, endDate: teachingWeekList?.last()?.dayStamp]
    }

    List<CampusTeachingWeek> fetchAllCampusTeachingWeekBySemesterAndWeekIndex(Long semesterId, Integer weekIndex) {
        CampusTeachingWeek.findAllBySemesterIdAndTeachingWeekIdxAndStatus(semesterId, weekIndex, 1 as byte, [sort: "dayStamp", order: "desc"])
    }

    Long getMaxDay4weekIndex(Long semesterId, int teachingWeekIndex) {
        String HQL = """SELECT MAX(dayStamp) FROM CampusTeachingWeek WHERE semesterId = :semesterId AND teachingWeekIdx = :weekIndex AND status = 1"""
        List re = CampusTeachingWeek.executeQuery(HQL, [semesterId: semesterId, weekIndex: teachingWeekIndex])
        if (re?.size() > 0) {
            return re[0] as Long
        }
        return null
    }

    Long getMinDay4weekIndex(Long semesterId, int teachingWeekIndex) {
        String HQL = """SELECT MIN(dayStamp) FROM CampusTeachingWeek WHERE semesterId = :semesterId AND teachingWeekIdx = :weekIndex AND status = 1"""
        List re = CampusTeachingWeek.executeQuery(HQL, [semesterId: semesterId, weekIndex: teachingWeekIndex])
        if (re?.size() > 0) {
            return re[0] as Long
        }
        return null
    }

    Integer removeAllCampusTeachingWeekByDayGenderThen(Long semesterId, Long dayStamp) {
        String HQL = """UPDATE CampusTeachingWeek SET status = 0, lastUpdated = NOW(), version = version + 1 WHERE semesterId = :semesterId AND dayStamp > :dayStamp"""
        CampusTeachingWeek.executeUpdate(HQL, [semesterId: semesterId, dayStamp: dayStamp], [failOnError: true, flush: true])
    }
}

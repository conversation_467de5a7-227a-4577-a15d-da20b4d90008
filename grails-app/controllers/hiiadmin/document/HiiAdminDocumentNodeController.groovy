package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentNodeVO
import hiiadmin.utils.ToStringUnits
import timetabling.document.DocumentNode

class HiiAdminDocumentNodeController implements BaseExceptionHandler {

    DocumentTemplateService documentTemplateService

    DocumentService documentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long documentTemplateId = params.long("documentTemplateId")
        Long nodeNo = params.long("nodeNo")
        List<DocumentNode> documentNodeList = documentTemplateService.fetchDocumentNodeByDocumentTemplateIdAndNodeNo(documentTemplateId, nodeNo)
        List<DocumentNodeVO> documentNodeVOList = documentNodeList.collect { new DocumentNodeVO().buildVO(it) }
        resultVO.result.put("list", documentNodeVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long documentId = params.long("documentId")
        String title = params.title
        String userIds = params.stepUserIds
        Byte nodeType = params.byte("type")
        documentService.sendMessage4Document(documentId, campusId, 1 as byte, ToStringUnits.idsString2LongList(userIds), nodeType, title)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.imp.ImpRecordService
import hiiadmin.mapstruct.ImpRecordMapper
import hiiadmin.module.imp.ImpRecordVO
import hiiadmin.school.TeacherService
import org.springframework.beans.factory.annotation.Autowired
import timetabling.document.Document
import timetabling.document.DocumentForm
import timetabling.imp.ImpRecord
import timetabling.user.Teacher

@Slf4j
class HiiAdminDocumentExportController implements BaseExceptionHandler {

    DocumentService documentService

    ImpRecordService impRecordService

    TeacherService teacherService

    @Autowired
    ImpRecordMapper impRecordMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        def map = documentService.fetchDocumentExportRecordByCampusIdLimit(campusId, p, s)
        List<ImpRecord> impRecordList = map.list
        Integer total = map.total
        List<ImpRecordVO> impRecordVOList = impRecordList?.collect {
            Teacher teacher = teacherService.fetchTeacherById(it.teacherId)
            ImpRecordVO impRecordVO = impRecordMapper.convert2ImpRecordVO(it, teacher?.name)
            impRecordVO
        }
        resultVO.result.put("list", impRecordVOList)
        resultVO.result.put("total", total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String title = params.title
        Byte degreeOfUrgency = params.byte("degreeOfUrgency")
        Byte status = params.byte("status")
        Byte degreeOfSecrets = params.byte("degreeOfSecrets")
        String documentUnit = params.documentUnit
        String documentFont = params.documentFont
        String drafterName = params.drafterName
        Long categoryId = params.long("categoryId")
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        def map = documentService.fetchDocument4Export(campusId, title, degreeOfUrgency, degreeOfSecrets, status, documentUnit, documentFont, drafterName, categoryId)
        List<Document> documentList = map.list
        List<DocumentForm> documentFormList = documentService.fetchDocumentFormByCampusId(campusId)
        Integer total = documentList?.size() ?: 0
        ImpRecord impRecord = new ImpRecord(schoolId: schoolId,
                campusId: campusId,
                teacherId: teacherId,
                count: total,
                status: ConstantEnum.ImpStatus.IMPORTING.status,
                type: ConstantEnum.ImpType.DOCUMENT_EXPORT.type)
        Long id = impRecordService.saveImpRecord(impRecord)?.id
        Promise task = Promises.task {
            documentService.transformDocumentExcel(documentList, documentFormList, impRecord, categoryId, status)
        }
        task.onComplete {
            log.info("公文导出成功！")
        }
        task.onError { Exception e -> log.error("公文导出失败！", e)
        }
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentStepVO
import hiiadmin.module.document.DocumentVO
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api

@Api(value = "公文收发", tags = "总务办公", consumes = "admin")
class HiiAdminDocumentController implements BaseExceptionHandler {

    DocumentService documentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        Byte type = params.byte("type")
        String title = params.searchValue
        Byte degreeOfUrgency = params.byte("degreeOfUrgency")
        Byte degreeOfSecrets = params.byte("degreeOfSecrets")
        Byte status = params.byte("status")
        Long startTime = params.long("startTime")
        Long endTime = params.long('endTime')
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        def map = documentService.fetchDocumentLimit(schoolId, type, teacherId, title, degreeOfUrgency, degreeOfSecrets, status, startTime, endTime, p, s)
        List<DocumentVO> documentVOList = map.list?.collect {
            new DocumentVO().buildVO(it)
        }
        Integer total = map.total
        resultVO.result.put("list", documentVOList)
        resultVO.result.put("total", total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Boolean enclosure = params.boolean("enclosure")
        Long teacherId = request.getAttribute("userId") as Long
        DocumentVO documentVO = documentService.transformDocumentVOByDocumentId(id)
        if (enclosure) {
            resultVO.result.put("baseFormJson", documentVO.baseFormJson)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        List<DocumentStepVO> documentStepVOList = documentService.transformDocumentStepVOListByDocumentId(id)
        Boolean circulate = documentService.canCirculate(id, teacherId)
        Boolean canRead = documentService.canRead(id, teacherId)
        Boolean directFinish = documentService.directFinish4Document(documentVO.nodeNo, documentVO.status, teacherId)
        resultVO.result = ObjectUtils.objectToMap(documentVO)
        resultVO.result.put("stepList", documentStepVOList)
        resultVO.result.put("circulate", circulate)
        resultVO.result.put("read", !canRead)
        resultVO.result.put("directFinish", directFinish)
        resultVO.result.put("teacherId", teacherId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"

    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        Long documentTemplateId = params.long("documentTemplateId")
        String title = params.title
        Byte degreeOfUrgency = params.byte("degreeOfUrgency")
        String documentFont = params.documentFont
        Byte degreeOfSecrets = params.byte("degreeOfSecrets")
        String documentUnit = params.documentUnit
        String valueJson = params.valueJson
        String baseFormJson = params.baseFormJson
        Long documentId = documentService.createDocument(campusId, schoolId, teacherId, documentTemplateId, title, degreeOfUrgency, documentFont, degreeOfSecrets, documentUnit, valueJson, baseFormJson)
        resultVO.result.put("id", documentId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long teacherId = request.getAttribute("userId") as Long
        Long documentStepId = params.long("id")
        Byte status = params.byte("status")
        Long nodeId = params.long("nodeId")
        String suggestion = params.suggestion
        String autograph = params.autograph
        String personIds = params.personIds
        String title = params.title
        Byte degreeOfUrgency = params.byte("degreeOfUrgency")
        String documentFont = params.documentFont
        Byte degreeOfSecrets = params.byte("degreeOfSecrets")
        String documentUnit = params.documentUnit
        String valueJson = params.valueJson
        String baseFormJson = params.baseFormJson
        documentService.updateDocument(teacherId, documentStepId, status, nodeId, suggestion, autograph, personIds, title, degreeOfUrgency, documentFont, degreeOfSecrets, documentUnit, valueJson, baseFormJson)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long teacherId = request.getAttribute("userId") as Long
        Long id = params.long("id")
        Boolean read = params.boolean("read")
        String userIds = params.userIds
        if (userIds) {
            documentService.circulateDocument(id, userIds, teacherId)
        }
        if (read) {
            documentService.readDocument(id, teacherId)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"

    }
}

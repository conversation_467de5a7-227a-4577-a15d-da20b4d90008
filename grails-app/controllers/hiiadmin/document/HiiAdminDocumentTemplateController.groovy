package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentTemplateVO
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import timetabling.document.DocumentTemplate

@Api(value = "收文模版管理", tags = "总务办公", consumes = "admin")
class HiiAdminDocumentTemplateController implements BaseExceptionHandler {

    DocumentTemplateService documentTemplateService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        List<DocumentTemplate> documentTemplateList = documentTemplateService.fetchDocumentTemplateByCampusIdLimit(campusId, p, s)
        Integer count = documentTemplateService.countDocumentTemplateByCampusIdAndStatus(campusId)
        List<DocumentTemplateVO> documentTemplateVOList = documentTemplateList?.collect { new DocumentTemplateVO().buildVO(it) }
        resultVO.result.put("list", documentTemplateVOList)
        resultVO.result.put("total", count)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        DocumentTemplate documentTemplate = documentTemplateService.fetchDocumentTemplateById(id)
        DocumentTemplateVO documentTemplateVO = new DocumentTemplateVO().buildCompleteVO(documentTemplate)
        resultVO.result = ObjectUtils.objectToMap(documentTemplateVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long teacherId = request.getAttribute('userId') as Long
        String name = params.name
        String formJson = params.formJson
        Byte visibleType = params.byte("visibleType")
        String departmentIds = params.departmentIds
        String personIds = params.personIds
        Boolean canComment = params.boolean("canComment", false)
        Long mailmanId = params.long("mailmanId")
        String approvalJson = params.approvalJson
        String nodeJson = params.nodeJson
        Long id = documentTemplateService.createDocumentTemplate(schoolId, campusId, teacherId, name, formJson, visibleType, mailmanId, approvalJson, departmentIds, personIds, canComment, nodeJson)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String name = params.name
        Boolean canComment = params.boolean("canComment")
        String formJson = params.formJson
        Byte visibleType = params.byte("visibleType")
        String departmentIds = params.departmentIds
        String personIds = params.personIds
        Long mailmanId = params.long("mailmanId")
        String approvalJson = params.approvalJson
        String nodeJson = params.nodeJson
        documentTemplateService.updateDocumentTemplate(id, name, canComment, formJson, visibleType, mailmanId, approvalJson, departmentIds, personIds, nodeJson)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Byte status = params.byte("status")
        DocumentTemplate documentTemplate = documentTemplateService.fetchDocumentTemplateById(id)
        documentTemplate.status = status
        documentTemplateService.saveDocumentTemplate(documentTemplate)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

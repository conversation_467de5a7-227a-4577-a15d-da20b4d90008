package hiiadmin.document

import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import com.alibaba.fastjson.JSON

/**
 * <AUTHOR>
 * @Date 2023-05-08 14:14
 */

@Slf4j
class HiiAdminDocumentCommentController implements BaseExceptionHandler {

    DocumentService documentService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        Long documentId = params.long("documentId")
        String comment = params.comment
        Long id = documentService.createDocumentComment(campusId, teacherId, documentId, comment)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long documentCommentId = params.long("id")
        Long teacherId = request.getAttribute("userId") as Long
        documentService.deleteDocumentComment(documentCommentId, teacherId)

        resultVO.result.put("id", documentCommentId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long documentId = params.long("id")
        Long teacherId = request.getAttribute("userId") as Long
        Boolean isComment = documentService.isComment4Document(documentId, teacherId)

        resultVO.result.put("isComment", isComment)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentTemplateVO

class HiiAdminDocumentTemplate4RegisterController implements BaseExceptionHandler {

    DocumentTemplateService documentTemplateService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long teacherId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        List<DocumentTemplateVO> documentTemplateVOList = documentTemplateService.fetchDocumentTemplate4Register(schoolId, campusId, teacherId)
        resultVO.result.put("list", documentTemplateVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

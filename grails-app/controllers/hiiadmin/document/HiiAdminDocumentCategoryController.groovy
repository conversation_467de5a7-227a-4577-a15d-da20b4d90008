package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentCategoryVO
import io.swagger.annotations.Api
import timetabling.document.DocumentCategory

@Api(value = "公文库", tags = "总务办公", consumes = "admin")
class HiiAdminDocumentCategoryController implements BaseExceptionHandler {

    DocumentCategoryService documentCategoryService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<DocumentCategory> documentCategoryList = documentCategoryService.fetchDocumentCategoryByCampusId(campusId)
        List<DocumentCategoryVO> documentCategoryVOList = documentCategoryList?.collect { new DocumentCategoryVO().buildVO(it) }
        resultVO.result.put("list", documentCategoryVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long pId = params.long("pId")
        String name = params.name
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        if (documentCategoryService.hasCategoryName(campusId, name)) {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.msg = "目录名称不可重复"
        } else {
            Long id = documentCategoryService.createDocumentCategory(campusId, schoolId, pId, name)
            resultVO.result.put("id", id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        if (documentCategoryService.hasCategoryName(campusId, name)) {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.msg = "目录名称不可重复"
        } else {
            documentCategoryService.updateDocumentCategory(id, name)
            resultVO.result.put("id", id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        if (documentCategoryService.hasDocumentCategoryRecord(id)) {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.msg = "该分类下存在公文，无法删除！"
        } else {
            documentCategoryService.deleteDocumentCategory(id)
            resultVO.result.put("id", id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

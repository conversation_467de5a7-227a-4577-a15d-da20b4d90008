package hiiadmin.document

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.document.DocumentVO
import hiiadmin.utils.PatternUtils

class HiiAdminDocumentLibraryController implements BaseExceptionHandler {

    DocumentService documentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String title = params.searchValue
        Byte degreeOfUrgency = params.byte("degreeOfUrgency")
        Byte status = params.byte("status")
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        Byte degreeOfSecrets = params.byte("degreeOfSecrets")
        String documentUnit = params.documentUnit
        documentUnit = PatternUtils.filter(documentUnit)
        String documentFont = params.documentFont
        documentFont = PatternUtils.filter(documentFont)
        String drafterName = params.drafterName
        drafterName = PatternUtils.filter(drafterName)
        Long categoryId = params.long("categoryId")
        Long campusId = request.getAttribute("campusId") as Long
        def map = documentService.fetchDocument4Library(campusId, title, degreeOfUrgency, degreeOfSecrets, status, documentUnit, documentFont, drafterName, categoryId, p, s)
        List<DocumentVO> documentVOList = map.list?.collect { new DocumentVO().buildVO(it) }
        resultVO.result.put("list", documentVOList)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String ids = params.ids
        Long categoryId = params.long("categoryId")
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        documentService.placeToFile(ids, categoryId, schoolId, campusId)
        resultVO.result.put("id", categoryId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

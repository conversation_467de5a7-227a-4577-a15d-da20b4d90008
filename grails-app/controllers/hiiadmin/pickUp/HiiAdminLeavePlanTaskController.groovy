package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.pickUp.LeavePlanTaskVO
import hiiadmin.planAfterSchool.LeavePlanRecordService
import hiiadmin.planAfterSchool.LeavePlanTaskService
import io.swagger.annotations.Api

@Api(value = "放学计划", tags = "智慧教务", consumes = "admin")
@Deprecated
class HiiAdminLeavePlanTaskController implements BaseExceptionHandler {

    LeavePlanTaskService leavePlanTaskService

    LeavePlanRecordService leavePlanRecordService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<LeavePlanTaskVO> vos = leavePlanTaskService.transformLeavePlanTaskVO(campusId)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        String taskJson = params.json
        leavePlanTaskService.createOrUpdateLevelPlan(campusId, taskJson)
        if (!leavePlanRecordService.existTodayLeavePlanRecord4Client(campusId)) {
            leavePlanRecordService.initTodayLeavePlanRecord(campusId)
        }
        ResultVO resultVO = ResultVO.success()
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

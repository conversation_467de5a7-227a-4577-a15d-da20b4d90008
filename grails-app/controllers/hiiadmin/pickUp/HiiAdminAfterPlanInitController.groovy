package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.planAfterSchool.LeavePlanAuthGateService
import hiiadmin.planAfterSchool.LeavePlanRecordService

class HiiAdminAfterPlanInitController {
    LeavePlanAuthGateService leavePlanAuthGateService

    LeavePlanRecordService leavePlanRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String item = params.item
        switch (item) {
            case "initRecord":
                leavePlanRecordService.initTodayLeavePlanRecord(campusId)
                break
            case "initRecordLeave":
                Long levelId = params.long("levelId")
                leavePlanRecordService.createAndSaveLeavePlanRecord4Level(levelId, campusId)
                break
            case "initIssue":
                leavePlanAuthGateService.initIssueTodayAccess(campusId)
                break

        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

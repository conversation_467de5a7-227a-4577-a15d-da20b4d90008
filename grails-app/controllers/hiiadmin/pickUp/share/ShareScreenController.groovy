package hiiadmin.pickUp.share


import hiiadmin.BaseExceptionHandler
import hiiadmin.share.ShareScreenService
import org.springframework.beans.factory.annotation.Value
import timetabling.share.ShareScreen

class ShareScreenController implements BaseExceptionHandler {

    ShareScreenService shareScreenService

    @Value('${share.baseUrl}')
    private String baseUrl

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = shareScreenService.fetchShareScreenList(campusId, p, s)
        [list: map.list, total: map.total, baseUrl: baseUrl]
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        String link = params.url
        String title = params.title
        String pic = params.pic
        String url = shareScreenService.createShareScreen(schoolId, campusId, title, link, pic)
        [url: url]
    }



    def update() {
        String id = params.id
        String link = params.url
        String title = params.title
        String pic = params.pic
        shareScreenService.updateShareScreen(id, title, link, pic)
        [id: id]
    }

    def delete() {
        String id = params.id
        shareScreenService.deleteByEncodeId(id)
        [id: id]
    }
}

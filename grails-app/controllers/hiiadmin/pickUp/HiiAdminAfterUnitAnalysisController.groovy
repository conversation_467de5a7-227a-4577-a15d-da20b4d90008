package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import org.apache.commons.lang3.StringUtils

/**
 * <AUTHOR>
 * @Date 2023-03-13 10:54
 */
@Slf4j
@Deprecated
class HiiAdminAfterUnitAnalysisController implements BaseExceptionHandler {

    AfterUnitService afterUnitService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String values = params.values
        if (StringUtils.isEmpty(values)) {
            resultVO.result.put("list", [])
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        List<String> valueList = values.split(",")
        List<String> afterUnitNameList = afterUnitService.analysisAfterUnitNameList(campusId, valueList)

        resultVO.result.put("list", afterUnitNameList)
        resultVO.result.put("success", afterUnitNameList?.size() ?: 0)
        resultVO.result.put("failure", (valueList?.size() - afterUnitNameList?.size()) > 0 ?: 0 )
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

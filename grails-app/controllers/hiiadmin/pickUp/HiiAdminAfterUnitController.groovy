package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.module.pickUp.AfterUnitVO
import hiiadmin.planAfterSchool.LeavePlanAfterUnitService
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.device.Device
import timetabling.pickUp.AfterUnit

@Api(value = "放学班管理", tags = "智慧教务", consumes = "admin")
@Deprecated
class HiiAdminAfterUnitController implements BaseExceptionHandler {

    AfterUnitService afterUnitService

    ViewDOService viewDOService

    LeavePlanAfterUnitService leavePlanAfterUnitService

    DeviceService deviceService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        String name = params.searchValue


        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def map = afterUnitService.pageAfterUnit(campusId, name, p, s)
        List<AfterUnitVO> vos = []
        if (map.list?.size() > 0) {
            vos = viewDOService.buildAfterUnitVO(map.list)
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String unitName = params.unitName
        String unitNames = params.unitNames
        String teacherIds = PhenixCoder.decodeIds(params.teacherIds)
        Long campusId = request.getAttribute("campusId") as Long

        if (unitName) {
            unitName = afterUnitService.analysisAfterUnitName(unitName)
            AfterUnit afterUnit = afterUnitService.fetchAfterUnitByCampusIdAndUnitName(campusId, unitName)
            if (afterUnit) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.EDU_UNIT_NAME_ERROR.code
                resultVO.message = BizErrorCode.EDU_UNIT_NAME_ERROR.msg
                render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
                return
            }
        }

        afterUnitService.saveAfterUnit(campusId, unitName, unitNames, teacherIds)
        resultVO.result.put("success", "ok")
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"

    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        String unitName = params.unitName
        String teacherIds = PhenixCoder.decodeIds(params.teacherIds)

        Preconditions.checkNotNull(unitName, "班级名称不能为空！")
        unitName = afterUnitService.analysisAfterUnitName(unitName)
        AfterUnit afterUnit = afterUnitService.fetchAfterUnitByCampusIdAndUnitName(campusId, unitName)
        if (afterUnit && afterUnit.id != id) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.EDU_UNIT_NAME_ERROR.code
            resultVO.message = BizErrorCode.EDU_UNIT_NAME_ERROR.msg
            render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
            return
        }
        afterUnitService.updateAfterUnit(id, unitName, teacherIds)
        List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndType(campusId, ConstantEnum.DeviceType.PICK_SCREEN.type)
        deviceService.iotOperationPushByDeviceListAndType(deviceList, ConstantEnum.LeanCloudType.LEAVE_SCHOOL_DATA.type, null, ConstantEnum.DeviceType.PICK_SCREEN.type)
        resultVO.result.put("id", id)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        if (leavePlanAfterUnitService.existsLeavePlanLevelAfterUnit(id)) {
            resultVO.code = BizErrorCode.AFTER_UNIT_DELETE_ERROR.code
            resultVO.message = BizErrorCode.AFTER_UNIT_DELETE_ERROR.msg
            resultVO.status = 0
            render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }
        afterUnitService.deleteAfterUnit(id)
        List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndType(campusId, ConstantEnum.DeviceType.PICK_SCREEN.type)
        deviceService.iotOperationPushByDeviceListAndType(deviceList, ConstantEnum.LeanCloudType.LEAVE_SCHOOL_DATA.type, null, ConstantEnum.DeviceType.PICK_SCREEN.type)
        resultVO.result.put("id", id)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

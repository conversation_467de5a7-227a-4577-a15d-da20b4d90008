package hiiadmin.pickUp.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.module.startUpPlan.StartUpPlanVO
import hiiadmin.planAfterSchool.LeavePlanLevelScreenDeviceService
import hiiadmin.school.device.DeviceService
import hiiadmin.startUpPlan.StartUpPlanService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.ToStringUnits
import io.swagger.annotations.Api
import timetabling.device.Device
import timetabling.pickUp.LeavePlanLevelParentScreenDevice
import timetabling.pickUp.LeavePlanLevelScreenDevice

@Api(value = "接送大屏管控", tags = "总务办公", consumes = "admin")
class HiiAdminPickUpDeviceController implements BaseExceptionHandler {

    DeviceService deviceService

    StartUpPlanService startUpPlanService

    LeavePlanLevelScreenDeviceService leavePlanLevelScreenDeviceService
    

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        String searchValue = params.searchValue

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Byte useType = params.byte("useType")
        def map = deviceService.fetchAllByCampusIdLimit(campusId, [ConstantEnum.DeviceType.PICK_SCREEN.type], searchValue, useType, p, s)
        List<Device> deviceList = map.list

        // 同步在离线状态
        if (deviceList) {
            deviceService.updateIotStatus(deviceList, campusId)
        }
        List<DeviceVO> vos = []
        deviceList.each {
            Device device ->
                vos << new DeviceVO().buildVO(device)
        }
        resultVO.result.put("total", map?.total)
        resultVO.result.put("list", vos)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long deviceId = params.long("id")

        Integer type = params.int("type")

        Long campusId = request.getAttribute("campusId") as Long

        Device device = deviceService.fetchDeviceById(deviceId)

        DeviceVO deviceVO = new DeviceVO().buildVO(device)

        StartUpPlanVO startUpPlanVO = startUpPlanService.findDeviceStartupPlanByDeviceIdAndCampusId(deviceId, campusId, type, device?.type)

        deviceVO.startUpPlanVO = startUpPlanVO

        resultVO.result = deviceVO

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String name = params.name
        Long deviceId = params.long("id")

        Long campusId = request.getAttribute("campusId") as Long
        Byte useType = params.byte("useType")
        Device device = deviceService.fetchDeviceById(deviceId)

        Device existDevice = deviceService.fetchDeviceByCampusIdAndNameAndType(campusId, name, device?.type)


        if (existDevice && existDevice.id != deviceId) {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = "校区内设备名称已存在"
        } else {
            device.name = name
            if (device.useType != useType) {
                device.useType = useType
                
                // 签到大屏 -》 接送大屏
                if (useType == 1 as byte) {
                    List<LeavePlanLevelParentScreenDevice> parentScreenDeviceList = leavePlanLevelScreenDeviceService.fetchAllLeavePlanLevelParentScreenDeviceByDeviceId(device.id)
                    if (parentScreenDeviceList) {
                        parentScreenDeviceList*.status = 0
                        leavePlanLevelScreenDeviceService.saveParentScreenDeviceList(parentScreenDeviceList)
                        Map<Long, Long> levelIdMap = parentScreenDeviceList.collectEntries { [it.levelId, it.taskId] }
                        leavePlanLevelScreenDeviceService.saveScreenDeviceListByLevelIdList(levelIdMap, device)
                    }
                    // 接送大屏 -》 签到大屏
                } else if (useType == 2 as byte) {
                    List<LeavePlanLevelScreenDevice> screenDeviceList = leavePlanLevelScreenDeviceService.fetchAllLevelIdByDeviceId(device.id)
                    if (screenDeviceList) {
                        screenDeviceList*.status = 0
                        leavePlanLevelScreenDeviceService.saveScreenDeviceList(screenDeviceList)
                        Map<Long, Long> levelIdMap = screenDeviceList.collectEntries { [it.levelId, it.taskId] }
                        leavePlanLevelScreenDeviceService.saveParentScreenDeviceListByLevelIdList(levelIdMap, device)
                    }
                }
                
                deviceService.iotOperationPushByDeviceListAndType([device], ConstantEnum.LeanCloudType.SCREEN_DEVICE_CHANGE_USE.type, "${useType}".toString(), device.type)
            }
        }

        deviceService.saveDevice(device)

        resultVO.result = ObjectUtils.resultVOObject2Map(new DeviceVO().buildVO(device))

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String name = params.name
        String mac = params.mac
        Byte useType = params.byte("useType")
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long

        Preconditions.checkNotNull(name, "名称为空")
        Preconditions.checkNotNull(mac, "mac地址为空")
        Preconditions.checkNotNull(useType, "用途不能为空")
        Device deviceMac = deviceService.fetchDeviceByMac(mac)
        if (deviceMac) {
            throw new HiiAdminException("mac地址已存在，请重新输入！")
        }

        Device deviceName = deviceService.fetchDeviceByCampusIdAndNameAndType(campusId, name, ConstantEnum.DeviceType.PICK_SCREEN.type)

        if (deviceName) {
            throw new HiiAdminException("设备名称校区内已存在，请重新输入！")
        }

        def map = deviceService.saveScreenDevice(mac, name, campusId, schoolId, ConstantEnum.DeviceType.PICK_SCREEN.type, useType)

        resultVO.result.put("id", map.id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String ids = params.ids

        Preconditions.checkNotNull(ids, "必要参数为空，未选择设备")

        List<Long> idList = ToStringUnits.idsString2LongList(ids)

        idList.each {
            Long deviceId ->
                if (deviceService.existAfterUnitPlan(deviceId) || deviceService.existAfterUnitPlanParentScreen(deviceId)) {
                    Device device = deviceService.fetchDeviceById(deviceId)
                    throw new HiiAdminException("存在和放学班已关联的设备${device.name}")
                }
        }

        ServiceResult serviceResult = deviceService.deletePickUpScreenDevice(ids)

        if (!serviceResult.success) {
            resultVO.status = 0
            resultVO.message = serviceResult.message
            resultVO.code = serviceResult.code as int
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

}

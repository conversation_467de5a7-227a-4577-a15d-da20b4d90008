package hiiadmin.pickUp.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.device.bo.SelectDeviceBO
import hiiadmin.school.device.AppDeviceSelectService

@Deprecated
class LeavePlanLevelDeviceSelectController {

    AppDeviceSelectService appDeviceSelectService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        /**
         * 用来识别查询设备类型
         * */
        String selectDeviceName = params.selectDeviceName
        /**
         * ConstantEnum.DeviceType
         * */
        String types = params.types

        /**
         * 1 pad (班牌类) 2  摄像头类
         * */
        Integer deviceType = params.int("deviceType",2 as Integer)
        /**
         * ConstantEnum.DeviceFirm
         * */
        String firms = params.firms
        SelectDeviceBO bo = new SelectDeviceBO()
        bo.selectDeviceName = selectDeviceName

        bo.deviceType = deviceType

        bo.campusId = campusId
        bo.types=types
        bo.firms = firms
        def listMap  =  appDeviceSelectService.selectDevice(bo)
        resultVO.result.put("list",listMap.list)
        resultVO.result.put("totalCount",listMap.totalcount)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

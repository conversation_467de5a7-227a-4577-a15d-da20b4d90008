package hiiadmin.pickUp.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.pickUp.PlanDeviceVO
import hiiadmin.planAfterSchool.LeavePlanLevelAuthDeviceService

class EnterPlanAuthDeviceController {

    LeavePlanLevelAuthDeviceService leavePlanLevelAuthDeviceService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<PlanDeviceVO> deviceVOList = leavePlanLevelAuthDeviceService.transformPlanDeviceVO(campusId)
        ResultVO resultVO = ResultVO.success([list: deviceVOList])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

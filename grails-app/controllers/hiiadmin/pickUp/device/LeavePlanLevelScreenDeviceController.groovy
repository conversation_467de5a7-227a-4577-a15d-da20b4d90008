package hiiadmin.pickUp.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ConstantEnum
import hiiadmin.module.pickUp.PlanDeviceVO
import hiiadmin.school.device.DeviceService
import timetabling.device.Device

@Deprecated
class LeavePlanLevelScreenDeviceController {

    DeviceService deviceService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndType(campusId, ConstantEnum.DeviceType.PICK_SCREEN.type)
        List<PlanDeviceVO> vos = deviceList.collect {
            PlanDeviceVO deviceVO = new PlanDeviceVO().buildVO(it)
            deviceVO
        }
        ResultVO resultVO = ResultVO.success([list: vos])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

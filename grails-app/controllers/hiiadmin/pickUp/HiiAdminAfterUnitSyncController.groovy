package hiiadmin.pickUp

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import hiiadmin.BaseExceptionHandler
import hiiadmin.utils.PhenixCoder


class HiiAdminAfterUnitSyncController implements BaseExceptionHandler {

    AfterUnitService afterUnitService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String unitIds = PhenixCoder.decodeIds(params.unitIds)
        Promise task = Promises.task {
            afterUnitService.syncAfterUnitByCampusId(campusId, unitIds)
        }
        task.onComplete {
            log.info("同步行政班创建放学班完成")
        }
        task.onError { Throwable throwable ->
            log.error("同步行政班创建放学班异常", throwable)
        }
        resultVO.result.put("success", "ok")
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

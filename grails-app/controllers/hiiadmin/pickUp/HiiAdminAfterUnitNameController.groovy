package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

@Deprecated
class HiiAdminAfterUnitNameController implements BaseExceptionHandler {

    AfterUnitService afterUnitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long
        List<String> nameList = afterUnitService.fetchAfterUnitNameListByCampusId(campusId)
        resultVO.result.put("list", nameList)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

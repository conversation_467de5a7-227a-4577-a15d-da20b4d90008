package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.HashMultimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.school.TeacherService
import hiiadmin.utils.PhenixCoder
import timetabling.pickUp.AfterUnit

@Deprecated
class HiiAdminAfterUnitTeacherController implements BaseExceptionHandler {

    TeacherService teacherService

    AfterUnitTeacherService afterUnitTeacherService

    AfterUnitService afterUnitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String name = params.searchValue

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int('s', 30)

        List<TeacherVO> teacherList = teacherService.fetchAllTeacherByNameOrMobileOrJobNum(campusId, name, p, s)
        Integer count = teacherService.countAllTeacherByNameOrMobileOrJobNum(campusId, name)
        HashMultimap<Long, Long> teacherIdAfterUnitIdMap = afterUnitTeacherService.fetchAfterUnitTeacherMapByClientId(campusId)
        teacherList.each {
            TeacherVO teacherVO ->
                List<Long> afterUnitIdList = teacherIdAfterUnitIdMap?.get(PhenixCoder.decodeId(teacherVO.id)) as List<Long>
                List<AfterUnit> afterUnitList = []
                if (afterUnitIdList) {
                    afterUnitList = afterUnitService.fetchAfterUnitByIdInList(afterUnitIdList)
                    teacherVO.afterUnitNames = afterUnitList*.name.join(",")
                }
                String afterUnitIds = teacherIdAfterUnitIdMap.get(PhenixCoder.decodeId(teacherVO.id))?.join(",")
                teacherVO.afterUnitIds = afterUnitIds
        }
        resultVO.result.put("total", count)
        resultVO.result.put("list", teacherList)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long teacherId = PhenixCoder.decodeId(params.teacherId)
        String afterUnitIds = params.afterUnitIds
        Long campusId = request.getAttribute("campusId") as Long

        afterUnitTeacherService.updateAfterUnitTeacher(campusId, teacherId, afterUnitIds)

        resultVO.result.put("id", teacherId)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.pickUp

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.StudentPositionVO
import hiiadmin.utils.PhenixCoder
import timetabling.user.Student

@Deprecated
class HiiAdminAfterUnitStudentController implements BaseExceptionHandler {

    AfterUnitStudentService afterUnitStudentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long afterUnitId = params.long("afterUnitId")
        Long campusId = request.getAttribute("campusId") as Long

        String name = params.searchValue

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def map = afterUnitStudentService.fetchStudentByAfterUnitIdLimit(campusId, afterUnitId, name, p, s)

        Integer total = map.total
        List<Student> studentList = map.list as List<Student>
        List<StudentPositionVO> studentVOList = []

        studentList.each {
            Student student ->
                studentVOList << new StudentPositionVO(
                        id: PhenixCoder.encodeId(student.id),
                        name: student.name,
                        code: student.code,
                        avatar: student.pic
                )
        }
        resultVO.result.put("list", studentVOList)
        resultVO.result.put("total", total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String studentIds = PhenixCoder.decodeIds(params.studentIds)
        Long afterUnitId = params.long("afterUnitId")
        Long clientId = request.getAttribute("campusId") as Long
        afterUnitStudentService.addAfterUnitStudent(clientId, afterUnitId, studentIds)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long

        Long oldUnitId = PhenixCoder.decodeId(params.oldUnitId)
        Long studentId = PhenixCoder.decodeId(params.id)
        Long newUnitId = PhenixCoder.decodeId(params.newUnitId)

        afterUnitStudentService.changeAfterUnitStudent(studentId, oldUnitId, newUnitId)

        resultVO.result.put("id", newUnitId)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long afterUnitId = params.long("afterUnitId")
        String studentIds = PhenixCoder.decodeIds(params.studentIds)
        Preconditions.checkNotNull(studentIds, "未选择学生")
        Preconditions.checkNotNull(afterUnitId, "未选择放学班")
        afterUnitStudentService.deleteAfterUnitStudent(afterUnitId, studentIds)

        resultVO.result.put("success", "ok")
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}


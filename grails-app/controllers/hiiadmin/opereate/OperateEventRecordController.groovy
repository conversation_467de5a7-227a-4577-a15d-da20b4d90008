package hiiadmin.opereate

import com.alibaba.fastjson2.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.operate.OperateEventRecordService

class OperateEventRecordController implements BaseExceptionHandler {

    OperateEventRecordService operateEventRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        String searchValue = params.searchValue
        Long dateTime = params.long("dateTime")

        def map = operateEventRecordService.pageOperateEventRecord(campusId, searchValue, dateTime, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

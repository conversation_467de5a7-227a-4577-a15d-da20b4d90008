package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import groovy.util.logging.Slf4j
import hiiadmin.module.msg.CheckNoResult
import hiiadmin.msg.MsgService
import hiiadmin.utils.PatternUtils
import org.apache.commons.lang3.StringUtils

@Slf4j
class CheckNoController implements BaseExceptionHandler {

    MsgService msgService

    EnvService envService

    def index() {

        String mobile = params.mobile
        String t = params."t" as String

        ResultVO usVO = new ResultVO()

        CheckNoResult checkNoResult
        Preconditions.checkNotNull(mobile, "请输入手机号")
        Preconditions.checkState(PatternUtils.isMobile(mobile, envService), "请输入正确的手机号")
        if (StringUtils.equalsIgnoreCase("v", t)) {
            checkNoResult = msgService.sendVoiceCheckNO(mobile)
        } else {
            checkNoResult = msgService.sendCheckCodeAndCacheCodeT9(mobile, t)
        }
        if (checkNoResult.success) {
            usVO.status = 1
            log.info("---> checkNO.send success mobile:" + mobile)
            withFormat {
                json { render text: JSON.toJSONString(usVO), contentType: 'application/json;', encoding: "UTF-8" }
            }
        } else {
            usVO.status = 0
            usVO.code = checkNoResult.code
            log.warn("---> checkNO.send failed. mobile:{},code:{},msg{}", mobile, checkNoResult?.code, checkNoResult?.msg)
            String errorMsg = ConstantEnum.YunPianErrorEnum.getEnumByType(checkNoResult?.code)?.message
            if (StringUtils.isNotBlank(errorMsg)) {
                usVO.message = errorMsg
            } else {
                usVO.message = "验证码发送失败"
            }
            withFormat {
                json { render text: JSON.toJSONString(usVO), contentType: 'application/json;', encoding: "UTF-8" }
            }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String mobile = params.mobile
        String code = params.code
        msgService.createCode(mobile, code)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import io.swagger.annotations.Api
import timetabling.data.DataAssets
import timetabling.data.DataSubset

@Api(value = "数据资产管理", tags = "数据驾驶舱", consumes = "admin")
class HiiAdminDataAssetsController implements BaseExceptionHandler {

    MaxComputeService maxComputeService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<DataAssets> dataAssetsList = maxComputeService.fetchDataAssetsViaCampusId(campusId)
        Map<Long, List<DataSubset>> map = [:]
        dataAssetsList.each {
            DataAssets dataAssets ->
                List<DataSubset> list = maxComputeService.fetchDataSubsetViaDataAssetsId(dataAssets.id)
                map.put(dataAssets.id, list)
        }
        [list: dataAssetsList, map: map]
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = params.long("campusId")
        Long schoolId = params.long("schoolId")
        String url = params.url
        maxComputeService.readDataCenterModeExcel(schoolId, campusId, url)
        resultVO.result.put("id", 1)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = params.long("id")
        maxComputeService.deleteDataCenterFromModeExcel(campusId)
        resultVO.result.put("id", campusId)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.DepartmentVO

class DataDepartmentController {
    DepartmentService departmentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        //TODO 字段确认
        boolean t = params.boolean("t")
        String searchValue = params.searchValue
        List<DepartmentVO> departmentList = departmentService.getDepartmentVOList(campusId, t, searchValue)
        resultVO.result.put("list", departmentList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

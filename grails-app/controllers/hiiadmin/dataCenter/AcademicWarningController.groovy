package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class AcademicWarningController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String scoreTableName
        String punishTableName
        String dataCenterCode = campus.dataCenterCode + "_"
        switch (item) {
            case "num":
                scoreTableName = dataCenterCode + "GXXS0706"
                punishTableName = dataCenterCode + "GXXS0709"
                String studentTableName = dataCenterCode + "GXXS0701"
                def map = maxComputeService.getWarningStudentNum(scoreTableName, punishTableName, studentTableName)
                resultVO.result = map
                break
            case "list":
                scoreTableName = dataCenterCode + "GXXS0706"
                punishTableName = dataCenterCode + "GXXS0709"
                String studentStatusTableName = dataCenterCode + "GXXS0701"
                def list = maxComputeService.getWarningStudentList(scoreTableName, punishTableName, studentStatusTableName)
                resultVO.result.put("list", list)
                break
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

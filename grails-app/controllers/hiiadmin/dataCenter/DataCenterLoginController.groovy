package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.JwtUser
import hiiadmin.utils.CtJWT
import timetabling.data.DataCenterUser

@Transactional
class DataCenterLoginController implements BaseExceptionHandler {

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String username = params.username
        String password = params.password
        if (!username) {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.msg = "请输入正确的账号"
        } else if (!password) {
            resultVO.code = 200002
            resultVO.status = 0
            resultVO.msg = "请输入密码"
        } else {
            DataCenterUser dataCenterUser = DataCenterUser.findByNameAndStatus(username, 1 as byte)
            if (!dataCenterUser) {
                resultVO.code = 200003
                resultVO.status = 0
                resultVO.msg = "用户不存在"
            } else if (dataCenterUser.password != password) {
                resultVO.code = 200004
                resultVO.status = 0
                resultVO.msg = "密码错误"
            } else {
                dataCenterUser.lastLoginTime = new Date()
                dataCenterUser.save(failOnError: true)
                JwtUser jwtUser = new JwtUser()
                jwtUser.setId(dataCenterUser.id)
                String jwtToken = CtJWT.createJWT(dataCenterUser.id?.toString(), JSONObject.toJSONString(jwtUser), "DataCenter")
                resultVO.result.put("token", jwtToken)
            }
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

//    public static void main(String[] args) {
//        JwtUser jwtUser = new JwtUser()
//        jwtUser.setId(1L)
//        String jwtToken = CtJWT.createJWT(jwtUser.id?.toString(), JSONObject.toJSONString(jwtUser), "DataCenter")
//        println(jwtToken)
//    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.school.CampusService
import timetabling.org.Campus

class BookAssetsController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String bookTableName
        String dataCenterCode = campus.dataCenterCode + "_"
        switch (item) {
            case "num":
                bookTableName = dataCenterCode + "GXZC0604"
                Map<String, Long> map = maxComputeService.getBookNum(bookTableName)
                resultVO.result.put("updateTime", map.get("updateTime"))
                resultVO.result.put("bookTotal", map.get("bookTotal"))
                resultVO.result.put("paperTotal", map.get("paperTotal"))
                break
            case "classify":
                bookTableName = dataCenterCode + "GXZC0605"
                def list = maxComputeService.getBookClassify(bookTableName)
                resultVO.result.put("list", list)
                break
            case "department":
                Byte readerType = params.byte("readerType")
                bookTableName = dataCenterCode + "GXXX0301"
                def list = maxComputeService.getDepartment(bookTableName)
                if (readerType == 1) {
                    list = list.findAll { it.name.length() >= 5 }
                    list = list.findAll { it.name.substring(4, 5) == "系" }
                }
                resultVO.result.put("list", list)
                break
            case "grade":
                bookTableName = dataCenterCode + "GXXX0401"
                def list = maxComputeService.getGrade(bookTableName)
                resultVO.result.put("list", list)
                break
            case "hot":
                String departmentName = params.departmentName
                String gradeName = params.gradeName
                Byte readerType = params.byte("readerType", 1 as byte)
                Byte bookBorrowTimeType = params.byte("bookBorrowTimeType", ConstantEnum.BookBorrowTimeType.ONE_YEAR.type)
                bookTableName = dataCenterCode + "GXZC0606"
                def list = maxComputeService.getBookBorrowRecord(bookTableName, readerType, departmentName, gradeName, bookBorrowTimeType)
                list.sort { it.name }
                resultVO.result.put("list", list)
                break
            case "cold":
                Byte readerType = params.byte("readerType", 1 as byte)
                bookTableName = dataCenterCode + "GXZC0601"
                def list = maxComputeService.getColdBook(bookTableName, readerType)
                resultVO.result.put("list", list)
                break
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

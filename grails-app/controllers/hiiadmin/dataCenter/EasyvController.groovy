package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.PhoenixCoder
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class EasyvController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        String c = params.c
        Long campusId = PhoenixCoder.decodeId(c)
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String dataCode = campus.dataCenterCode
        String tableName = dataCode + "_" + "GXJG0101"
        String useTypeTable = dataCode + "_" + "GXJG0503"
        def list = []
        switch (item) {
        //教职工年龄分布
            case "age":
                list = maxComputeService.getTeacherAge4Easyv(tableName)
                break
                //学历情况
            case "education":
                list = maxComputeService.getTeacherEducation4Easyv(tableName)
                break
                //男女比例
            case "gender":
                Integer g = params.int("g", 1)
                list = maxComputeService.getTeacherGender4Easyv(tableName)
                if (g == 1) {
                    list = [list.get(0)]
                } else {
                    list = [list.get(1)]
                }
                break
                //总人数
            case "total":
                list = maxComputeService.getTeacherTotal4Easyv(tableName)
                break
                //用人方式
            case "useType":
                String useType = params.useType
                list = maxComputeService.getTeacherUseType4Easyv(useTypeTable, useType)
                break
                //近几年教职工学历
            case "latelyEducation":
                list = maxComputeService.getTeacherLatelyEducation4Easyv(tableName)
                break
                //政治面貌
            case "outlook":
                list = maxComputeService.getTeacherOutlook4Easyv(tableName)
                break
                //中央城市分布
            case "graduation":
                tableName = dataCode + "_" + "GXJG0106"
                list = maxComputeService.getTeacherGraduation(tableName)
                break
                //双肩挑
            case "double":
                list = maxComputeService.getDoubleTeacher(tableName)
                break
                //人员流动
            case "turnover":
                list = maxComputeService.getTeacherTurnover4Easyv(tableName)
                break
                //管理
            case "admin":
                tableName = dataCode + "_" + "GXJG0302"
                list = maxComputeService.countTeacher4Easyv(tableName)
                break
                //工勤
            case "diligence":
                tableName = dataCode + "_" + "GXJG0306"
                list = maxComputeService.countTeacher4Easyv(tableName)
                break
                //专业技术
            case "technology":
                tableName = dataCode + "_" + "GXJG0303"
                list = maxComputeService.countTeacher4Easyv(tableName)
                break
        }
        return render(text: JSON.toJSONString(list), contentType: 'application/json', encoding: "UTF-8")
    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.PositionCountVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.track.TrackService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import timetabling.track.Track

class HiiAdminUserTrackController implements BaseExceptionHandler {
    TrackService trackService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = params.long("userId")
        Long startDate = params.long("startDate")
        if (startDate == null) {
            startDate = TimeUtils.getDateTimeOfDay(System.currentTimeMillis()).millis
        }
        Long endDate = params.long("endDate")
        if (endDate == null) {
            endDate = System.currentTimeMillis()
        }
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")

        List<PositionCountVO> list = trackService.statisticsStudentPlaceTrack(campusId, userId, startDate, endDate, userType)
        resultVO.result.put("list", list)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = PhenixCoder.decodeId(params.userId)
        Long startDate = params.long("startDate")
        if (startDate == null) {
            startDate = TimeUtils.getDateTimeOfDay(System.currentTimeMillis()).millis
        }
        Long endDate = params.long("endDate")
        if (endDate == null) {
            endDate = System.currentTimeMillis()
        }
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        Long positionId = params.long("id")
        List<Track> trackList = trackService.fetchAllTrackByPositionIdAndCampusIdAndUserIdBetweenTime(campusId, userId, userType, startDate, endDate, positionId)
        List<TrackVO> trackVOList = []
        if (trackList) {
            trackList.each {
                Track track ->
                    TrackVO vo = new TrackVO().buildVO(track)
                    trackVOList << vo
            }
        }
        resultVO.result.put("list", trackVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

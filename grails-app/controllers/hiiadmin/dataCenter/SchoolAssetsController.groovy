package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.PhoenixCoder
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class SchoolAssetsController implements BaseExceptionHandler {

    CampusService campusService

    SchoolAssetsService schoolAssetsService

    def index() {
        String c = params.c
        Long campusId = PhoenixCoder.decodeId(c)
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String dataCode = campus.dataCenterCode
        def list = []
        switch (item) {
            case "houseUse":
                String tableName = dataCode + "_" + "GXZC0202"
                list = schoolAssetsService.getHouseUse4Easyv(tableName)
                break
            case "houseAllocation":
                String tableName = dataCode + "_" + "GXZC0202"
                list = schoolAssetsService.getHouseAllocation4Easyv(tableName)
                break
            case "deviceUse":
                String tableName = dataCode + "_" + "GXZC0501"
                list = schoolAssetsService.getDeviceUse4Easyv(tableName)
                break
            case "devicePercent":
                String tableName = dataCode + "_" + "GXZC0501"
                String type = params.type
                list = schoolAssetsService.getDevicePercent4Easyv(tableName, type)
                break
            case "softwareYear":
                String tableName = dataCode + "_" + "GXZC0507"
                list = schoolAssetsService.getSoftwareYear4Easyv(tableName)
                break
            case "softwarePercent":
                String tableName = dataCode + "_" + "GXZC0507"
                String tableName1 = dataCode + "_" + "GXZC0508"
                list = schoolAssetsService.getSoftwarePercent(tableName, tableName1)
                break
            case "book":
                String tableName = dataCode + "_" + "GXZC0604"
                String type = params.type
                if (!type) {
                    list = schoolAssetsService.getBookTotal(tableName)
                } else {
                    tableName = dataCode + "_" + "GXZC0605"
                    list = schoolAssetsService.getBookNum(tableName, type)
                }
                break
            case "softwareDevice":
                String tableName = dataCode + "_" + "GXZC0501"
                String tableName1 = dataCode + "_" + "GXZC0507"
                String type = params.type
                if (!type) {
                    list = schoolAssetsService.getSoftwareTotal(tableName, tableName1)
                } else {
                    list = schoolAssetsService.getSoftwareNum(tableName, tableName1, type)
                }
                break
            case "house":
                String tableName = dataCode + "_" + "GXZC0202"
                String tableName1 = dataCode + "_" + "GXZC0201"
                String type = params.type
                if (!type) {
                    list = schoolAssetsService.getHouseTotal(tableName)
                } else {
                    list = schoolAssetsService.getHouseNum(tableName, tableName1, type)
                }
                break
        }
        return render(text: JSON.toJSONString(list), contentType: 'application/json', encoding: "UTF-8")
    }
}

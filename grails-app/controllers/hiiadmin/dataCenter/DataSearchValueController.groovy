package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import timetabling.data.DataSearchValue

class DataSearchValueController implements BaseExceptionHandler {

    DataCenterService dataCenterService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Byte type = params.byte("type")
        List<DataSearchValue> dataSearchValueList = []
        if (!type) {
            dataSearchValueList = dataCenterService.fetchAllSearchValueViaCampusId(campusId)
        } else {
            dataSearchValueList = dataCenterService.fetchAllSearchValueViaCampusIdAndType(campusId, type)
        }
        [dataSearchValueList: dataSearchValueList]
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        dataCenterService.sendDataUpdateRecordMessage()
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

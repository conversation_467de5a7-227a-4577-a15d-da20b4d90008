package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.PhoenixCoder


class ResearchController {
    DataResearchService dataResearchService


    def index() {
        String c = params.c
        Long campusId = PhoenixCoder.decodeId(c)
        String item = params.item
        List list = dataResearchService.researchProcess(campusId, item)
        return render(text: JSON.toJSONString(list), contentType: 'application/json', encoding: "UTF-8")
    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.dataCenter.*
import hiiadmin.utils.ObjectUtils
import timetabling.data.*

class DataStudentArchivesController implements BaseExceptionHandler {

    DataStudentService dataStudentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String yxsmc = params.departmentName
        String sznj = params.gradeName
        String zyfx = params.majorName
        String szbh = params.unitName
        String xslbmc = params.studentType
        String xsdqztmc = params.schoolType
        String searchValue = params.searchValue

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        def map = dataStudentService.fetchDataStudentArchivesLimit(campusId, yxsmc, sznj, zyfx, szbh, xslbmc, xsdqztmc, searchValue, p, s)
        List<DataStudentArchives> list = map.get("list")
        Integer total = map.get("total")
        List<DataStudentArchivesVO> voList = list.collect { new DataStudentArchivesVO().buildVO(it) }
        resultVO.result.put("list", voList)
        resultVO.result.put("total", total)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long studentId = params.long("id")
        DataStudentArchives dataStudentArchives = dataStudentService.getDataStudentArchives(studentId)
        List<DataStudentAppellation> appellationList = dataStudentService.fetchAppellationByCode(dataStudentArchives.xh)
        List<DataStudentAward> awardList = dataStudentService.fetchAwardByCode(dataStudentArchives.xh)
        List<DataStudentDifficulty> difficultyList = dataStudentService.fetchDifficultyByCode(dataStudentArchives.xh)
        List<DataStudentExperience> experienceList = dataStudentService.fetchExperienceByCode(dataStudentArchives.xh)
        List<DataStudentGraduation> graduationList = dataStudentService.fetchGraduationByCode(dataStudentArchives.xh)
        List<DataStudentPunish> punishList = dataStudentService.fetchPunishByCode(dataStudentArchives.xh)
        List<DataStudentScholarship> scholarshipList = dataStudentService.fetchStudentScholarshipByCode(dataStudentArchives.xh)
        List<DataStudentStipend> stipendList = dataStudentService.fetchStudentStipendByCode(dataStudentArchives.xh)
        List<DataStudentWork> workList = dataStudentService.fetchStudentWorkByCode(dataStudentArchives.xh)
        DataStudentArchivesVO dataStudentArchivesVO = new DataStudentArchivesVO().buildVO(dataStudentArchives)
        dataStudentArchivesVO.appellationVOList = appellationList?.collect { new DataStudentAppellationVO().buildVO(it) }
        dataStudentArchivesVO.awardVOList = awardList?.collect { new DataStudentAwardVO().buildVO(it) }
        dataStudentArchivesVO.difficultyVOList = difficultyList?.collect { new DataStudentDifficultyVO().buildVO(it) }
        dataStudentArchivesVO.experienceVOList = experienceList?.collect { new DataStudentExperienceVO().buildVO(it) }
        dataStudentArchivesVO.graduationVOList = graduationList?.collect { new DataStudentGraduationVO().buildVO(it) }
        dataStudentArchivesVO.punishVOList = punishList?.collect { new DataStudentPunishVO().buildVO(it) }
        dataStudentArchivesVO.scholarshipVOList = scholarshipList?.collect { new DataStudentScholarshipVO().buildVO(it) }
        dataStudentArchivesVO.stipendVOList = stipendList?.collect { new DataStudentStipendVO().buildVO(it) }
        dataStudentArchivesVO.workVOList = workList?.collect { new DataStudentWorkVO().buildVO(it) }
        resultVO.result.putAll(ObjectUtils.objectToMap(dataStudentArchivesVO))
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

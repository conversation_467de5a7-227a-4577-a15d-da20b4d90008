package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler


class HumanMsgSendUserController implements BaseExceptionHandler {

    HumanMsgSendUserService humanMsgSendUserService


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String objJson = params.objJson
        Long msgId = params.long("msgId")
        Long campusId = request.getAttribute("campusId") as Long
        ServiceResult result = humanMsgSendUserService.saveMsgSendUser(objJson, campusId, msgId)
        if (result.success) {
            resultVO.result.put("id", msgId)
        } else {
            resultVO.status = 0
            resultVO.code = result.code as Integer
            resultVO.message = result.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

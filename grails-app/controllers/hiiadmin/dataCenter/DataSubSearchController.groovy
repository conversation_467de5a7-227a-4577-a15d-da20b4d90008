package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.dataCenter.DataSubSearchVO
import timetabling.data.DataAssets
import timetabling.data.DataField
import timetabling.data.DataSub
import timetabling.data.DataSubset

class DataSubSearchController implements BaseExceptionHandler {

    DataSubService dataSubService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte searchType = params.byte("searchType")
        String searchValue = params.searchValue
        List<DataSub> dataSubList = []
        List<DataSubSearchVO> list = []
        if (searchType == 1 as byte) {
            dataSubList = dataSubService.fetchSubViaName(campusId, searchValue)
            dataSubList?.each {
                DataSub dataSub ->
                    DataSubset dataSubset = dataSubService.getDataSubset(dataSub.dataSubsetId)
                    DataAssets dataAssets = dataSubService.getDataAssets(dataSubset.dataAssetsId)
                    DataSubSearchVO dataSubSearchVO = new DataSubSearchVO()
                    dataSubSearchVO.id = dataSub.id
                    dataSubSearchVO.name = dataSub.name
                    dataSubSearchVO.setName = dataSubset.name
                    dataSubSearchVO.assetsName = dataAssets.name
                    list.add(dataSubSearchVO)
            }
        } else {
            List<DataField> dataFieldList = dataSubService.fetchFieldViaName(campusId, searchValue)
            if (dataFieldList && dataFieldList.size() > 0) {
                List<Long> idList = dataFieldList*.dataSubId
                dataSubList = dataSubService.fetchSubViaIdInList(idList)
                dataSubList.removeAll([null])
                dataFieldList?.each {
                    DataField dataField ->
                        DataSub dataSub = dataSubList.find { it.id == dataField.dataSubId }
                        if (dataSub) {
                            DataSubset dataSubset = dataSubService.getDataSubset(dataSub.dataSubsetId)
                            DataAssets dataAssets = dataSubService.getDataAssets(dataSubset.dataAssetsId)
                            DataSubSearchVO dataSubSearchVO = new DataSubSearchVO()
                            dataSubSearchVO.id = dataSub.id
                            dataSubSearchVO.name = dataSub.name
                            dataSubSearchVO.setName = dataSubset.name
                            dataSubSearchVO.assetsName = dataAssets.name
                            dataSubSearchVO.fieldName = dataField.name
                            list.add(dataSubSearchVO)
                        }
                }
            }
        }
        resultVO.result.put("list", list)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

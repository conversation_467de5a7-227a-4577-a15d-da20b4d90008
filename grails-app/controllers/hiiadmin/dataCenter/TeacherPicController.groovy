package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class TeacherPicController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String dataCenterCode = campus.dataCenterCode + "_"
        String teacherTable = dataCenterCode + "GXJG0101"
        switch (item) {
            case "list":
                String departmentName = params.departmentName
                String jobStatus = params.jobStatus
                String teacherCode = params.teacherCode
                int p = params.int("p", 1)
                int s = params.int("s", 10)
                def list = maxComputeService.getTeacherList(teacherTable, departmentName, teacherCode, jobStatus, p, s)
                def total = maxComputeService.countTeacherList(teacherTable, departmentName, teacherCode, jobStatus)
                resultVO.result.put("list", list)
                resultVO.result.put("total", total)
                break
            case "show":
                String mobileTable = dataCenterCode + "GXJG0102"
                String studyTable = dataCenterCode + "GXJG0103"
                String contractTable = dataCenterCode + "GXJG0503"
                String professionalTable = dataCenterCode + "GXJG0303"
                String code = params.code
                def teacher = maxComputeService.getTeacher(teacherTable, mobileTable, studyTable, contractTable, professionalTable, code)
                resultVO.result = teacher
                break
            case "science":
                String textTable = dataCenterCode + "GXKY0303"
                String bookTable = dataCenterCode + "GXKY0302"
                String patentTable = dataCenterCode + "GXKY0307"
                String appraisalTable = dataCenterCode + "GXKY0306"
                String awardsTable = dataCenterCode + "GXKY0310"
                String softTable = dataCenterCode + "GXKY0311"
                String technologyTable = dataCenterCode + "GXKY0101"
                String code = params.code
                def teacherScience = maxComputeService.getTeacherScience(textTable, bookTable, patentTable, appraisalTable, awardsTable, softTable, technologyTable, code)
                resultVO.result = teacherScience
                break
            case "award":
                String awardTable = dataCenterCode + "GXJG0107"
                String code = params.code
                def allAwardList = maxComputeService.getTeacherAwardList(awardTable)
                def teacherAwardRecord = allAwardList.findAll { it.code == code }
                teacherAwardRecord?.sort { -it.awardDate }
                resultVO.result.put("list", allAwardList)
                resultVO.result.put("record", teacherAwardRecord)
                break
            case "course":
                String classTable = dataCenterCode + "GXJG0201"
                String code = params.code
                String courseTable = dataCenterCode + "GXJX0201"
                def teacherCourse = maxComputeService.getTeacherCourse(classTable, courseTable, code)
                resultVO.result = teacherCourse
                break
            case "work":
                String workTable = dataCenterCode + "GXJG0202"
                String code = params.code
                def teacherWork = maxComputeService.getTeacherWork(workTable, code)
                resultVO.result = teacherWork
                break
            case "unit":
                String unitTable = dataCenterCode + "GXJX0502"
                String code = params.code
                def teacherUnit = maxComputeService.getTeacherUnit(unitTable, code)
                resultVO.result.put("list", teacherUnit)
                break
            case "exam":
                String examTable = dataCenterCode + "GXJX1101"
                String code = params.code
                String examName = params.examName
                Long dateTime = params.long("dateTime")
                String examNo = params.examNo
                String courseName = params.courseName
                String placeName = params.placeName
                def examRecordList = maxComputeService.getTeacherExamRecord(examTable, code, examName, dateTime, examNo, courseName, placeName)
                resultVO.result.put("list", examRecordList)
                break
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"

    }
}

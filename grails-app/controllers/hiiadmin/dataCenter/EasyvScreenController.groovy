package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class EasyvScreenController implements BaseExceptionHandler {

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte type = params.byte("type")
        String url = maxComputeService.fetchEasyvUrlByCampusIdAndType(campusId, type)
        resultVO.result.put("url", url)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

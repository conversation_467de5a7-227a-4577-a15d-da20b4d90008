package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.dataCenter.HumanMessageContentVO


class DataHumanMessageController implements BaseExceptionHandler {
    DataHumanMessageService dataHumanMessageService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def contentPage = dataHumanMessageService.fetchMessageContentByCampusId(campusId, searchValue, p, s)
        List<HumanMessageContentVO> humanMessageContentList = dataHumanMessageService.buildHumanMessageVos(contentPage.list, campusId)
        resultVO.result.put("list", humanMessageContentList)
        resultVO.result.put("total", contentPage.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

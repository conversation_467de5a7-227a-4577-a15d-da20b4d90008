package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.dataCenter.DataMenuVO
import hiiadmin.school.CampusService
import timetabling.data.DataMenu
import timetabling.org.Campus

class DataMenuController implements BaseExceptionHandler {

    DataMenuService dataMenuService

    CampusService campusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        List<DataMenu> dataMenuList = dataMenuService.fetchDataMenuViaCampusId(campusId)
        List<DataMenu> mainMenu = dataMenuList.findAll { it.type == 1 as Byte }
        List<DataMenuVO> dataMenuVOList = mainMenu.collect { new DataMenuVO().buildVO(it) }
        dataMenuVOList?.each {
            DataMenuVO dataMenuVO ->
                //1级
                if (dataMenuVO.key != null) {
                    List<DataMenu> subDataMenuList = dataMenuList.findAll { it.parentKey == dataMenuVO.key }
                    if (subDataMenuList && subDataMenuList.size() > 0) {
                        List<DataMenuVO> subDataMenuVOList = subDataMenuList.collect { new DataMenuVO().buildVO(it) }
                        subDataMenuVOList.each {
                            DataMenuVO subDataMenuVO ->
                                //2级
                                if (subDataMenuVO.key != null) {
                                    List<DataMenu> subSubDataMenuList = dataMenuList.findAll { it.parentKey == subDataMenuVO.key }
                                    if (subSubDataMenuList && subSubDataMenuList.size() > 0) {
                                        List<DataMenuVO> subSubDataMenuVOList = subSubDataMenuList.collect { new DataMenuVO().buildVO(it) }
                                        subSubDataMenuVOList.each {
                                            DataMenuVO subSubDataMenuVO ->
                                                //3级
                                                if (subSubDataMenuVO.key != null) {
                                                    List<DataMenu> subSubSubDataMenuList = dataMenuList.findAll { it.parentKey == subSubDataMenuVO.key }
                                                    if (subSubSubDataMenuList && subSubSubDataMenuList.size() > 0) {
                                                        List<DataMenuVO> subSubSubDataMenuVOList = subSubSubDataMenuList.collect { new DataMenuVO().buildVO(it) }
                                                        subSubDataMenuVO.children = subSubSubDataMenuVOList
                                                    }
                                                }
                                        }
                                        subDataMenuVO.children = subSubDataMenuVOList
                                    }
                                }
                        }
                        dataMenuVO.children = subDataMenuVOList
                    }
                }
        }
        resultVO.result.put("name", campus.name)
        resultVO.result.put("logo", campus.logo)
        resultVO.result.put("menuList", dataMenuVOList)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

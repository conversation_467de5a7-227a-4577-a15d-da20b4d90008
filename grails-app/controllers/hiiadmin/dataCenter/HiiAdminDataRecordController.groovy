package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.module.dataCenter.DataFieldVO
import hiiadmin.school.CampusService
import timetabling.data.DataField
import timetabling.data.DataSub
import timetabling.org.Campus

class HiiAdminDataRecordController implements BaseExceptionHandler {

    MaxComputeService maxComputeService

    CampusService campusService

    DataSubService dataSubService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long dataSubId = params.long("dataSubId")
        String searchValue = params.searchJson
        Byte searchType = params.byte("searchType")
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        if (!campus.dataCenterCode) {
            resultVO.code = BizErrorCode.NO_SET_DATA.code
            resultVO.msg = BizErrorCode.NO_SET_DATA.msg
            resultVO.status = 0
        } else {
            List<Map<String, String>> reList = []
            Integer total = 0
            DataSub dataSub = maxComputeService.getDataSub(dataSubId)
            List<DataField> list = maxComputeService.fetchDataFieldViaDataSubId(dataSubId)
            String tableName = campus.dataCenterCode + "_" + dataSub.code
            if (searchValue && searchType) {
                reList = dataSubService.fetchDataRecordViaSearchValueLimit(tableName, searchValue, searchType, p, s)
                total = dataSubService.getDataRecordTotalViaSearchValue(tableName, searchValue, searchType)
            } else {
                reList = maxComputeService.dataMethodLimit(tableName, p, s)
                total = maxComputeService.countTotal(tableName)
            }
            List<DataFieldVO> vos = list.collect { new DataFieldVO().buildVO(it) }
            resultVO.result.put("fieldList", vos)
            resultVO.result.put("valueList", reList)
            resultVO.result.put("total", total)
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

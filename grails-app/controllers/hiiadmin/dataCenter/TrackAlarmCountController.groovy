package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.PositionService
import hiiadmin.module.dataCenter.TrackAlarmCountVO
import timetabling.Position
import timetabling.PositionDevice
import timetabling.track.TrackAlarm

class TrackAlarmCountController implements BaseExceptionHandler {

    TrackAlarmService trackAlarmService

    PositionService positionService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte type = params.byte("type", 1 as byte)
        Long campusId = request.getAttribute("campusId") as Long
        List<TrackAlarmCountVO> trackAlarmCountVOList = []
        switch (type) {
        //按场地维度
            case 1 as byte:
                Long startTime = params.long("startTime")
                List<TrackAlarm> trackAlarmList = trackAlarmService.fetchTrackAlarmViaStartTime(campusId, startTime)
                List<Position> positionList = positionService.fetchAllPositionByCampusId(campusId)
                List<PositionDevice> positionDeviceList = positionService.fetchAllPositionDeviceByCampusId(campusId)
                trackAlarmCountVOList = trackAlarmService.countTrackAlarmByPosition(trackAlarmList, positionList, positionDeviceList)
                trackAlarmCountVOList = trackAlarmCountVOList.findAll { it.num > 0 }
                trackAlarmCountVOList?.sort { -it.num }
                if (trackAlarmCountVOList && trackAlarmCountVOList.size() > 10) {
                    trackAlarmCountVOList = trackAlarmCountVOList.subList(0, 10)
                }
                break
                //按时间维度
            case 2 as byte:
                Byte timeType = params.byte("timeType")
                trackAlarmCountVOList = trackAlarmService.countTrackAlarmByDayTimeStamp(campusId, timeType)
                break
        }
        resultVO.result.put("list", trackAlarmCountVOList)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

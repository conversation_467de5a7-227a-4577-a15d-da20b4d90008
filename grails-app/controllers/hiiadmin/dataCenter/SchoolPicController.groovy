package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class SchoolPicController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String teacherTableName
        String studentTableName
        String departmentTableName
        String majorTableName
        String dataCenterCode = campus.dataCenterCode + "_"
        switch (item) {
            case "num":
                teacherTableName = dataCenterCode + "GXJG0101"
                studentTableName = dataCenterCode + "GXXS0701"
                departmentTableName = dataCenterCode + "GXXX0301"
                majorTableName = dataCenterCode + "GXXX0304"
                def map = maxComputeService.getSchoolPicNum(teacherTableName, studentTableName, departmentTableName, majorTableName)
                resultVO.result = map
                break
            case "title":
                teacherTableName = dataCenterCode + "GXJG0101"
                def list = maxComputeService.getSchoolPicTeacherTitle(teacherTableName)
                resultVO.result.put("list", list)
                break
            case "teacher":
                teacherTableName = dataCenterCode + "GXJG0101"
                def list = maxComputeService.getSchoolPicTeacherAge(teacherTableName)
                resultVO.result.put("list", list)
                break
            case "gender":
                studentTableName = dataCenterCode + "GXXS0701"
                String department = params.departmentName
                Integer m = maxComputeService.getStudentGender(studentTableName, department, null, "男")
                Integer w = maxComputeService.getStudentGender(studentTableName, department, null, "女")
                resultVO.result = [m: m, w: w]
                break
            case "grade":
                studentTableName = dataCenterCode + "GXXS0701"
                def list = maxComputeService.getGradeStudentStatusMap(studentTableName)
                resultVO.result.put("list", list)
                break
            case "major":
                majorTableName = dataCenterCode + "GXXX0304"
                def list = maxComputeService.getSchoolPicMajor(majorTableName)
                resultVO.result.put("list", list)
                break
            case "department":
                teacherTableName = dataCenterCode + "GXJG0101"
                studentTableName = dataCenterCode + "GXXS0701"
                majorTableName = dataCenterCode + "GXXX0304"
                def list = maxComputeService.getSchoolPicDepartment(studentTableName, teacherTableName, majorTableName)
                resultVO.result.put("list", list)
                break
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

//    public static void main(String[] args)  {
//        String src = "73.25"
//        try {
//            MessageDigest md2 = MessageDigest.getInstance("MD2");
//            byte[] digest = md2.digest(src.bytes);
//
//            int i = 0
//            while (i < 100000000) {
//                // 加密
//                digest = md2.digest(digest);
//                println(i)
//                i++
//            }
//            println(HexBin.encode(digest))
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//    }
}

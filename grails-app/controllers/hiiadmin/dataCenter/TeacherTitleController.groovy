package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.org.Campus

class TeacherTitleController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String dataCenterCode = campus.dataCenterCode + "_"
        String teacherTable = dataCenterCode + "GXJG0101"
        String courseTable = dataCenterCode + "GXJG0201"
        String textTable = dataCenterCode + "GXKY0303"
        String bookTable = dataCenterCode + "GXKY0302"
        String abilityTable = dataCenterCode + "GXJG0303"
        def list = maxComputeService.getTeacherAbility(teacherTable, courseTable, textTable, bookTable, abilityTable)
        resultVO.result.put("list", list)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

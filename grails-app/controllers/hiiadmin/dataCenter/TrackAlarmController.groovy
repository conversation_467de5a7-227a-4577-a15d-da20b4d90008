package hiiadmin.dataCenter


import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.PositionService
import hiiadmin.module.dataCenter.TrackAlarmVO
import timetabling.Position
import timetabling.PositionDevice
import timetabling.track.TrackAlarm

class TrackAlarmController implements BaseExceptionHandler {

    TrackAlarmService trackAlarmService

    PositionService positionService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long startTime = params.long("startTime")
        Long positionId = params.long("positionId")
        Long dayTimeStamp = params.long("dayTime")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = trackAlarmService.fetchTrackAlarmViaStartTimeLimit(campusId, startTime, positionId, dayTimeStamp, p, s)
        List<TrackAlarm> trackAlarmList = map.list
        Integer total = map.total
        List<PositionDevice> positionDeviceList = positionService.fetchAllPositionDeviceByCampusId(campusId)
        List<Position> positionList = positionService.fetchAllPositionByCampusId(campusId)
        List<TrackAlarmVO> voList = []
        trackAlarmList.each {
            TrackAlarm trackAlarm ->
                TrackAlarmVO trackAlarmVO = new TrackAlarmVO().buildVO(trackAlarm)
                List<Long> positionIdList = positionDeviceList.findAll { it.deviceId == trackAlarm.deviceId }?.collect { it.positionId }
                if (positionIdList && positionIdList.size() > 0) {
                    String positionName = positionList.findAll { it.id in positionIdList }*.name.join(",")
                    trackAlarmVO.positionName = positionName
                }
                voList.add(trackAlarmVO)
        }
        voList?.sort { -it.pushTime }
        resultVO.result.put("list", voList)
        resultVO.result.put("total", total)
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

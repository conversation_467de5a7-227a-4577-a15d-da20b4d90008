package hiiadmin.dataCenter

import com.alibaba.fastjson.JSON
import com.bugu.PhoenixCoder


class DataStudentWorkController {
    DataStudentWorkService dataStudentWorkService

    def index() {
        String c = params.c
        Long campusId = PhoenixCoder.decodeId(c)
        String item = params.item
        List list = dataStudentWorkService.getStudentWorkData(campusId, item)
        return render(text: JSON.toJSONString(list), contentType: 'application/json', encoding: "UTF-8")

    }

}

package hiiadmin.dataCenter

import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import timetabling.data.DataSub
import timetabling.org.Campus

class HiiAdminDataSubController implements BaseExceptionHandler {

    MaxComputeService maxComputeService

    CampusService campusService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        Long dataSubsetId = params.long("dataSubsetId")
        List<DataSub> list = maxComputeService.fetchDataSubViaDataSubsetId(dataSubsetId)
        Map<Long, Integer> dataSubIdCountMap = [:]
        list.each {
            DataSub dataSub ->
                Integer total = maxComputeService.countTotal(campus.dataCenterCode + "_" + dataSub.code)
                dataSubIdCountMap.put(dataSub.id, total)
        }
        [list: list, map: dataSubIdCountMap]
    }
}

package hiiadmin.dataCenter

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.school.CampusService
import hiiadmin.utils.ObjectUtils
import timetabling.org.Campus

class CourseAnalysisController implements BaseExceptionHandler {

    CampusService campusService

    MaxComputeService maxComputeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.getCampus(campusId)
        String item = params.item
        String tableName = campus.dataCenterCode + "_GXJX0201"
        String courseTable = campus.dataCenterCode + "_GXJG0201"
        switch (item) {
            case "top":
                Byte timeType = params.byte("timeType", ConstantEnum.BookBorrowTimeType.ONE_YEAR.type)
                Byte courseType = params.byte("courseType", ConstantEnum.DataCenterCourseType.ALL.type)
                def list = maxComputeService.getCourseTopList(tableName, timeType, courseType)
                resultVO.result.put("list", list)
                break
            case "show":
                String code = params.code
                def course = maxComputeService.showCourse(tableName, courseTable, code)
                resultVO.result.putAll(ObjectUtils.objectToMap(course))
                break
            case "cold":
                Byte timeType = params.byte("timeType", ConstantEnum.BookBorrowTimeType.ONE_YEAR.type)
                Byte courseType = params.byte("courseType", ConstantEnum.DataCenterCourseType.ALL.type)
                def list = maxComputeService.getColdCourse(tableName, timeType, courseType)
                resultVO.result.put("list", list)
                break
        }
        render text: JSONObject.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.pic

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ferries.FerriesService
import hiiadmin.oss.OssUtils
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import hiiadmin.visitor.VisitorService
import timetabling.gated.Ferries
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

@Slf4j
class CheckFaceController implements BaseExceptionHandler {

    CheckFaceService checkFaceService

    StudentService studentService

    TeacherService teacherService

    ParentService parentService

    FerriesService ferriesService

    VisitorService visitorService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Boolean create = params.boolean("create", false)
        String avatar = params.avatar
        String userIdStr = params.userId
        Long userId = PhenixCoder.decodeId(userIdStr)
        String code = params.code
        String mobile = params.mobile
        String userName = params.userName
        Byte userType = params.byte("userType")
        Long publisherId = request.getAttribute("userId") as Long
        Byte publisherType = request.getAttribute("type") as Byte
        Long campusId = request.getAttribute("campusId") as Long
        resultVO.result.put("success", true)

        Boolean onlyCheck = false
        if (!userId && !userName && !code && !mobile && !create) {
            onlyCheck = true
        } else if (!create) {
            switch (userType) {
                case ConstantEnum.UserTypeEnum.STUDENT.type:
                    Student student = null
                    if (userId) {
                        student = studentService.fetchStudentById(userId)
                    } else if (code) {
                        student = studentService.fetchStudentByCampusIdIdAndCode(campusId, code)
                    } else if (userName) {
                        List<Student> studentList = studentService.fetchStudentByCampusIdIdAndName(campusId, userName)
                        if (studentList.size() == 1) {
                            student = studentList[0]
                        }
                    }
                    if (student != null){
                        userId = student.id
                        userName = student.name
                    }
                    break

                case ConstantEnum.UserTypeEnum.TEACHER.type:
                    Teacher teacher = null
                    if (userId) {
                        teacher = teacherService.fetchTeacherById(userId)
                    } else if (code) {
                        if (!code.startsWith("T")) {
                            code = "T" + code
                        }
                        List<Teacher> list = teacherService.fetchAllTeacherByCampusIdAndJobNum(campusId, code)
                        if (list.size() == 1) {
                            teacher = list[0]
                        }
                    } else if (userName) {
                        List<Teacher> teachers = teacherService.fetchTeacherByCampusIdAndNameLike(campusId, userName)
                        if (teachers.size() == 1) {
                            teacher = teachers[0]
                        }
                    }
                    if (teacher != null) {
                        userId = teacher.id
                        userName = teacher.name
                    }
                    break

                case ConstantEnum.UserTypeEnum.PARENT.type:
                    Parent parent = null
                    if (userId) {
                        parent = parentService.fetchParentById(userId)
                    } else if (mobile) {
                        parent = parentService.fetchParentByTrueMobile(mobile)
                    }
                    if (parent != null) {
                        userId = parent.id
                        userName = parent.name
                    }
                    break

                case ConstantEnum.UserTypeEnum.FERRIES.type:
                    Ferries ferries = null
                    if (userId) {
                        ferries = ferriesService.fetchFerriesByFerriesId(userId)
                    } else if (mobile) {
                        ferries = ferriesService.fetchFerriesByTrueMobile(mobile)
                    }
                    if (ferries != null) {
                        userId = ferries.id
                        userName = ferries.name
                    }
                    break

                case ConstantEnum.UserTypeEnum.VISITORS.type:
                    Visitor visitor = null
                    if (userId) {
                        visitor = visitorService.fetchVisitorById(userId)
                    } else if (mobile) {
                        visitor = visitorService.fetchVisitorByTrueMobile(mobile)
                    }
                    if (visitor != null) {
                        userId = visitor.id
                        userName = visitor.visitorName
                    }
                    break
            }
        }

        String url = ""
        if (avatar.startsWith("https://res.buguk12.com")) {
            url = avatar
        } else {
            url = OssUtils.transformFilePath(PatternUtils.getFileName(avatar), 300)
        }

        JSONObject jsonObject = new JSONObject()
        jsonObject.put("onlyCheck", onlyCheck)
        jsonObject.put("create", create)
        jsonObject.put("campusId", campusId)
        jsonObject.put("imgUrl", url)
        jsonObject.put("publisherId", publisherId)
        jsonObject.put("publisherType", publisherType)
        jsonObject.put("userType", userType)
        jsonObject.put("avatar", avatar)
        if (userId) {
            jsonObject.put("userId", userId)
        }
        if (userName) {
            jsonObject.put("userName", userName)
        }
        ResultVO resultVO1 = checkFaceService.imageScore(jsonObject)
        if (resultVO1.status == 1) {
            resultVO.result.put("success", true)
        } else {
            resultVO.result.put("success", false)
            resultVO.result.put("msg", resultVO1.message)
        }

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

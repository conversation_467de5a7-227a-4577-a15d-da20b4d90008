package hiiadmin.pic

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.checkFace.CheckFaceVO
import hiiadmin.utils.PhenixCoder
import timetabling.humanface.pic.CheckFace

/**
 * <AUTHOR>
 * @Date 2022-11-14 11:19
 */
@Slf4j
class CheckFaceRecordController implements BaseExceptionHandler {

    CheckFaceService checkFaceService

    ViewDOService viewDOService

    def index () {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = PhenixCoder.decodeId(params.userId)
        String userName = params.userName
        Byte userType = params.byte("userType")
        Integer resultStatus = params.int("resultStatus")
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        def map = checkFaceService.fetchCheckFaceListLimit(campusId, userId, userName, userType, resultStatus, startTime, endTime, p, s)
        List<CheckFace> checkFaceList = map.list
        Integer total = map.total

        List<CheckFaceVO> checkFaceVOS = checkFaceList?.collect {viewDOService.buildCheckFaceVO(it)} ?: []

        resultVO.result.put("list", checkFaceVOS)
        resultVO.result.put("total", total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.pic

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.humanface.FaceGroupService
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.msg.MessageService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import org.joda.time.DateTime

/**
 * <AUTHOR>
 * @Date 2022-11-15 14:43
 */
@Slf4j
@Api(value = "人脸库", tags = "安防管控中心", consumes = "admin")
class CheckFaceDBController implements BaseExceptionHandler {

    FaceGroupService faceGroupService

    TeacherService teacherService

    ParentService parentService

    MessageService messageService

    CacheService cacheService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        byte userType = params.byte("userType")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long

        List<FaceGroupVO> checkFaceVOList = []
        int total = 0
        def resultMap
        switch (userType) {
            case ConstantEnum.UserTypeEnum.STUDENT.type:
                Boolean gender = params.boolean("gender")
                String searchValue = params.searchValue
                Long sectionId = PhenixCoder.decodeId(params.sectionId)
                Long gradeId = PhenixCoder.decodeId(params.gradeId)
                Long facultyId = PhenixCoder.decodeId(params.facultyId)
                Long majorId = PhenixCoder.decodeId(params.majorId)
                Long unitId = PhenixCoder.decodeId(params.unitId)
                Long layerId = PhenixCoder.decodeId(params.layerId)
                Long buildingId = PhenixCoder.decodeId(params.buildingId)
                Long doorPlateId = PhenixCoder.decodeId(params.doorPlateId)
                int resultStatus = params.int("resultStatus", 99)
                //暂时关闭筛选功能
                resultStatus = 99
                resultMap = faceGroupService.findStudentFaceByCampusIdAndSearchValuePage(campusId, gender, searchValue, sectionId, gradeId, unitId, buildingId, layerId, doorPlateId, resultStatus, userType, p, s, facultyId, majorId)
                checkFaceVOList = resultMap.list
                total = resultMap.total
                break

            case ConstantEnum.UserTypeEnum.TEACHER.type:
                Boolean gender = params.boolean("gender")
                String searchValue = params.searchValue
                Long departmentId = PhenixCoder.decodeId(params.departmentId)
                int resultStatus = params.int("resultStatus", 99)
                //暂时关闭筛选功能
                resultStatus = 99
                resultMap = faceGroupService.findTeacherFaceByCampusIdAndSearchValuePage(campusId, gender, searchValue, departmentId, resultStatus, userType, p, s)
                checkFaceVOList = resultMap.list
                total = resultMap.total
                break

            case ConstantEnum.UserTypeEnum.PARENT.type:
                String searchValue = params.searchValue
                Long sectionId = PhenixCoder.decodeId(params.sectionId)
                Long gradeId = PhenixCoder.decodeId(params.gradeId)
                Long facultyId = PhenixCoder.decodeId(params.facultyId)
                Long majorId = PhenixCoder.decodeId(params.majorId)
                Long unitId = PhenixCoder.decodeId(params.unitId)
                Long studentId = PhenixCoder.decodeId(params.studentId)
                int resultStatus = params.int("resultStatus", 99)
                //暂时关闭筛选功能
                resultStatus = 99
                resultMap = faceGroupService.findParentFaceByCampusIdAndSearchValuePage(campusId, searchValue, facultyId, majorId, sectionId, gradeId, unitId, studentId, resultStatus, userType, s, p)
                checkFaceVOList = resultMap.list
                total = resultMap.total
                break

            case ConstantEnum.UserTypeEnum.FERRIES.type:
                String searchValue = params.searchValue
                String studentName = params.studentName
                Long unitId = PhenixCoder.decodeId(params.unitId)
                int resultStatus = params.int("resultStatus", 99)
                resultMap = faceGroupService.findFerriesByCampusIdAndSearchValuePage(campusId, searchValue, studentName, null, unitId, resultStatus, userType, s, p)
                checkFaceVOList = resultMap.list
                total = resultMap.total
                break
        }

        resultVO.result.put("list", checkFaceVOList)
        resultVO.result.put("total", total)

        //上次照片上传提醒时间
        Long value = cacheService.sendUploadAvatarMessage.get("campusId_${campusId}_userType_${userType}")
        resultVO.result.put("lastSendTime", value)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        byte userType = params.byte("userType")
        Long campusId = request.getAttribute("campusId") as Long

        Long value = cacheService.sendUploadAvatarMessage.get("campusId_${campusId}_userType_${userType}")
        Long timeStamp = new Date().time
        if (value) {
            if (timeStamp - value < 24 * 60 * 60 * 1000) {
                String time = new DateTime(value).toString("yyyy-MM-dd HH:mm:ss")
                resultVO.status = 0
                resultVO.message = "最近一次提醒时间：" + time
                resultVO.result.put("lastSendTime", value)
                render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
                return
            }
        } else {
            cacheService.sendUploadAvatarMessage.put("campusId_${campusId}_userType_${userType}", timeStamp)
        }

        List<Long> userIdList = []
        if (userType == ConstantEnum.UserTypeEnum.TEACHER.type) {
            userIdList = teacherService.findAllTeacherIdsNoAvatarByCampusId(campusId)
        } else
            userIdList = parentService.findAllParentIdsNoAvatarByCampusId(campusId)

        Promise task = Promises.task {
            messageService.sendUploadAvatarMessage(campusId, userIdList.unique(), userType)
        }
        task.onComplete {
            log.info("照片上传通知成功")
        }
        task.onError { Exception e ->
            log.error("照片上传通知失败：", e)
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

}

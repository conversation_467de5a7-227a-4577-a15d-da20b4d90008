package hiiadmin

import com.buguk12.logging.TL
import grails.converters.JSON
import groovy.util.logging.Slf4j
import hiiadmin.exceptions.*
import org.springframework.cglib.proxy.UndeclaredThrowableException

/**
 * <AUTHOR> @date 2019/12/9 17:42
 * @version 1.0* @description
 */
@Slf4j
trait BaseExceptionHandler {

    // 输入参数错误
    def handle(IllegalArgumentException e) {
        error(410001, e, "参数错误")
    }

    // 状态不对
    def handle(IllegalStateException e) {
        error(410002, e, "状态错误")
    }

    def handle(PasswordInvalidException e) {
        error(100, e, e.message)
    }

    def handle(TimeChangeException e) {
        error(BizErrorCode.TIME_ERROR.code, e, BizErrorCode.TIME_ERROR.msg)
    }

    def handle(NoSuchUserException e) {
        error(BizErrorCode.NO_MATCHED_TYPE_USER_FOUND.code, e, BizErrorCode.NO_MATCHED_TYPE_USER_FOUND.msg)
    }

    def handle(HasPewClassException e) {
        error(BizErrorCode.HAS_BIND_PEW.code, e, BizErrorCode.HAS_BIND_PEW.msg)
    }

    def handle(HasUndoCardException e) {
        error(BizErrorCode.HAS_UNDO_CARD.code, e, BizErrorCode.HAS_UNDO_CARD.msg)
    }


    def handle(CannotDeleteException e) {
        error(BizErrorCode.CANNOT_DELETE.code, e, BizErrorCode.CANNOT_DELETE.msg)
    }

    def handle(AlreadyExistException e) {
        error(BizErrorCode.ALREADY_EXIST.code, e, BizErrorCode.ALREADY_EXIST.msg)
    }

    def handle(HasBindException e) {
        error(BizErrorCode.HAS_BIND.code, e, BizErrorCode.HAS_BIND.msg)
    }

    def handle(RepeatException e) {
        error(BizErrorCode.REPEAT_EXIST.code, e, BizErrorCode.REPEAT_EXIST.msg)
    }

    def handle(NoSettingsException e) {
        error(BizErrorCode.NO_SET_DATA.code, e, BizErrorCode.NO_SET_DATA.msg)
    }

    def handle(MobileErrorException e) {
        error(BizErrorCode.MOBILE_NO_ERROR.code, e, BizErrorCode.MOBILE_NO_ERROR.msg)
    }

    def handle(ApprovalUserTypeErrorException e) {
        error(BizErrorCode.APPROVAL_USER_TYPE_FORBID.code, e, BizErrorCode.APPROVAL_USER_TYPE_FORBID.msg)
    }

    //班牌错误
    def handle(DeviceErrorException e) {
        error(700001, e, "")
    }

    def handle(HiiAdminException e) {
        error(900000, e, e.message)
    }

    def handle(TimeException e) {
        error(410010, e, "时间异常")
    }
    // 数据库连接错误
    def handle(UndeclaredThrowableException e) {
        error(BizErrorCode.AUTHORITY_ERROR.code, e, BizErrorCode.AUTHORITY_ERROR.msg)
    }

    def handle(RuntimeException e) {
        error(BizErrorCode.SYSTEM_ERROR.code, e, BizErrorCode.SYSTEM_ERROR.msg)
    }

    def exceptionHandler(Exception e) {
        error(BizErrorCode.SYSTEM_ERROR.code, e, BizErrorCode.SYSTEM_ERROR.msg)
    }

    // 输入参数错误
    def handle(NullPointerException e) {
        error(410003, e, "参数错误")
    }

    def error(int code, Exception e, String message) {
        log.error("---> ${this.class?.name} , params : ${params?.toString()}")
        log.error(e.getMessage(), e)

        TL.build().appName("hiiadmin").error({
            tm ->
                log.error(tm, e.getMessage(), e)
        }, String.valueOf(code), message)

        def ret = [status: 0, code: code, msg: "${message}"]
//        def ret = [status: e, code: code, msg: "${message}\n${e.message}"]
        render ret as JSON
    }
}
package hiiadmin.docking.yunRui

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.dhYr.DockingAlarmEnumVO


class DockingAlarmEnumController implements BaseExceptionHandler {

    DockingAlarmEnumService dockingAlarmEnumService

    def index() {
        Integer firm = params.int("firm")
        List<DockingAlarmEnumVO> enumVOList = dockingAlarmEnumService.transformDockingAlarmEnumVO(firm)
        ResultVO resultVO = ResultVO.success([list: enumVOList])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

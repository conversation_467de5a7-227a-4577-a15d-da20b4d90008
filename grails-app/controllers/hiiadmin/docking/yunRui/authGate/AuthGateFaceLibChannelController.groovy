package hiiadmin.docking.yunRui.authGate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.device.AbstractDeviceVO
import timetabling.docking.yunrui.authGate.AuthGateFaceLib

@Deprecated
class AuthGateFaceLibChannelController implements BaseExceptionHandler {

    AuthGateFaceLibService authGateFaceLibService

    AuthGateFaceLibChannelService authGateFaceLibChannelService

    CacheService cacheService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long

        Long libId = params.long("libId")

        AuthGateFaceLib faceLib = authGateFaceLibService.fetchAuthGateFaceLibById(libId)
        Boolean exist = params.boolean("exist")
        List<AbstractDeviceVO> deviceVOList = []
        if (exist) {
            deviceVOList = authGateFaceLibChannelService.transformExistDeviceVOByLibId(libId)
        } else {
            deviceVOList = authGateFaceLibChannelService.transformUnExistDeviceVOByLibId(campusId, libId)
        }
        ResultVO resultVO = ResultVO.success([list: deviceVOList, dateCreated: faceLib.dateCreated.time])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long libId = params.long("libId")
        String addChannelIds = params.addChannelIds ?: null,
               delChannelIds = params.delChannelIds ?: null
        List<Long> addChannelIdList = addChannelIds?.split(",")?.collect { it as Long },
                   delChannelIdList = delChannelIds?.split(",")?.collect { it as Long }
        AuthGateFaceLib authGateFaceLib = authGateFaceLibService.fetchAuthGateFaceLibById(libId)

        
        ResultVO resultVO = new ResultVO()
        
        Boolean retry = cacheService.retryFailAuthGateFaceLibUserTask.get("auth_gate_face_lib_user_retry_${libId}")
        Boolean addOrDel = cacheService.authGateFaceLibChannelAddOrDel.get("auth_gate_face_lib_channel_addOrDel_${libId}")
        if (retry || addOrDel) {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = "当前人脸库正在处理数据中，请勿频繁操作"
            render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
            return 
        }
        
        if (addChannelIdList?.size() > 0) {
            authGateFaceLibChannelService.addAuthGateFaceLibChannel(libId, authGateFaceLib.dockingFaceLibId, addChannelIdList)
        } else if (delChannelIdList?.size() > 0) {
            authGateFaceLibChannelService.removeAuthGateFaceLibChannel(libId, authGateFaceLib.dockingFaceLibId, delChannelIdList)
        } else {
            throw new HiiAdminException("请选择设备")
        }
        authGateFaceLibService.updateAuthGateFaceLib(libId)

        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.docking.yunRui.authGate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

/**
 * 获取终端上的人脸库
 */
@Deprecated
class AuthGateFaceLibIvssController implements BaseExceptionHandler {

    AuthGateFaceLibIvssService authGateFaceLibIvssService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long channelId = params.long("channelId")

        List list = authGateFaceLibIvssService.transform(campusId, channelId)
        ResultVO resultVO = ResultVO.success(list: list)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        Long campusId = request.getAttribute("campusId") as Long
        Long channelId = params.long("id")

        authGateFaceLibIvssService.updateAuthGateFaceLibChannelGroupId(campusId)

        List list = authGateFaceLibIvssService.transform(campusId, channelId)
        ResultVO resultVO = ResultVO.success(list: list)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

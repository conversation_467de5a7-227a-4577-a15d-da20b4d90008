package hiiadmin.docking.yunRui.authGate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.docking.dhYr.authGate.AuthGateBaseUserVO
import timetabling.docking.yunrui.authGate.AuthGateFaceLib
import timetabling.docking.yunrui.authGate.AuthGateFaceLibUser

import java.util.concurrent.TimeUnit

@Deprecated
class AuthGateFaceLibUserController implements BaseExceptionHandler {

    AuthGateFaceLibService authGateFaceLibService

    AuthGateFaceLibUserService authGateFaceLibUserService

    AuthGateFaceLibChannelService authGateFaceLibChannelService

    CacheService cacheService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long libId = params.long("libId")
        AuthGateFaceLib authGateFaceLib = authGateFaceLibService.fetchAuthGateFaceLibById(libId)
        int p = params.int("p", 1),
            s = params.int("s", 30)

        Long sectionId = params.long("sectionId")
        Long gradeId = params.long("gradeId")
        Long unitId = params.long("unitId")
        Long buildingId = params.long("buildingId")
        Long layerId = params.long("layerId")
        Long departmentId = params.long("departmentId")
        Boolean gender = params.boolean("gender")
        Integer issueStatus = params.int("issueStatus")
//        String searchValue = params.searchValue
        Long searchUserId = params.long("userId")
        List<? extends AuthGateBaseUserVO> userVOS = authGateFaceLibUserService.fetchFaceLibUserListLimit(libId, authGateFaceLib.userType, issueStatus, searchUserId,
                sectionId, gradeId, unitId, buildingId, layerId, departmentId, gender, campusId, p, s)
        Integer total = authGateFaceLibUserService.countFaceLibUser(libId, authGateFaceLib.userType, issueStatus, searchUserId, sectionId, gradeId, unitId,
                buildingId, layerId, departmentId, gender, campusId,)


        Boolean retry = cacheService.retryFailAuthGateFaceLibUserTask.get("auth_gate_face_lib_user_retry_${libId}")
        
        ResultVO resultVO = ResultVO.success([list: userVOS, total: total])
        resultVO.result.put("retryTask", retry ?: false)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        Long libUserId = params.long("id")
        AuthGateFaceLibUser authGateFaceLibUser = authGateFaceLibUserService.fetchAuthGateFaceLibUserById(libUserId)
        AuthGateFaceLib authGateFaceLib = authGateFaceLibService.fetchAuthGateFaceLibById(authGateFaceLibUser.libId)
        def userVO = authGateFaceLibUserService.transformAuthGateUserVO(authGateFaceLibUser.libId, authGateFaceLibUser.userId, authGateFaceLib.userType, authGateFaceLib.campusId)
        userVO.id = libUserId
        ResultVO resultVO = ResultVO.success(userVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long libId = params.long("libId")

        String addUserIds = params.addUserIds ?: null,
               delUserIds = params.delUserIds ?: null

        List<Long> addUserIdList = addUserIds?.split(",")?.collect { it as Long },
                   delUserIdList = delUserIds?.split(",")?.collect { it as Long }

        AuthGateFaceLib authGateFaceLib = authGateFaceLibService.fetchAuthGateFaceLibById(libId)

        if (addUserIdList?.size() > 0) {
            Set<Long> deviceIdList = authGateFaceLibChannelService.getAllDeviceIdByLibId(libId)
            List<Long> libUserIdList = authGateFaceLibUserService.createAuthGateFaceLibUser(schoolId, campusId, libId, authGateFaceLib.userType, addUserIdList, deviceIdList.toList())

            authGateFaceLibUserService.issueUser2Yr(authGateFaceLib.dockingFaceLibId, libUserIdList)
        } else if (delUserIdList?.size() > 0) {
            delUserIdList.each { userId ->
                String key = "yrAuthGate${campusId}-${libId}-${userId}"
                cacheService.tryLockAndRun4lockCache(key, 1, TimeUnit.MINUTES, {
                    authGateFaceLibUserService.removeAuthGateFaceLibUser(libId, authGateFaceLib.dockingFaceLibId, userId)
                })
            }
        } else {
            throw new HiiAdminException("请选择人员")
        }
        authGateFaceLibService.updateAuthGateFaceLib(libId)

        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

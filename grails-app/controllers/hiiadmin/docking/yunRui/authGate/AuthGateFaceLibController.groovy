package hiiadmin.docking.yunRui.authGate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.AuthGateFaceMapper
import hiiadmin.module.docking.dhYr.authGate.AuthGateFaceLibVO
import org.springframework.beans.factory.annotation.Autowired
import timetabling.docking.yunrui.authGate.AuthGateFaceLib

@Deprecated
class AuthGateFaceLibController implements BaseExceptionHandler {

    AuthGateFaceLibService authGateFaceLibService

    AuthGateFaceLibUserService authGateFaceLibUserService

    CacheService cacheService

    @Autowired
    AuthGateFaceMapper authGateFaceMapper

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1),
            s = params.int("s", 30)
        List<AuthGateFaceLib> faceLibList = authGateFaceLibService.fetchAllAuthGateFaceLib(campusId, p, s)

        Integer total = authGateFaceLibService.countAuthGateFaceLibByCampusId(campusId)
        List<AuthGateFaceLibVO> vos = faceLibList.collect { faceLib ->
            authGateFaceMapper.convert2AuthGateFaceLibVO(faceLib)
        }
        ResultVO resultVO = ResultVO.success([list: vos, total: total])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        long schoolId = request.getAttribute("schoolId") as Long
        long campusId = request.getAttribute("campusId") as Long
        String channelIds = params.channelIds ?: null
        List<Long> channelIdList = channelIds?.split(",")?.collect { it as Long }

        Byte userType = params.byte("userType")
        String name = ""
        switch (userType) {
            case ConstantEnum.UserTypeEnum.STUDENT.type:
                name = "全体学生"
                break
            case ConstantEnum.UserTypeEnum.TEACHER.type:
                name = "全体教师"
                break
        }
        AuthGateFaceLib faceLib = authGateFaceLibService.fetchAuthGateFaceLibByUserType(campusId, userType)
        if (faceLib) {
            throw new HiiAdminException("该人员类型人脸库已创建，请勿重复创建")
        }

        faceLib = authGateFaceLibService.createdAuthGateFaceLib(schoolId, campusId, channelIdList, userType, name)
        //通过MQ将人员添加至人脸库
        authGateFaceLibUserService.initAuthGateFaceLibUser(schoolId, campusId, faceLib)
        authGateFaceLibService.updateAuthGateFaceLib(faceLib.id)
        AuthGateFaceLibVO vo = authGateFaceMapper.convert2AuthGateFaceLibVO(faceLib)
        ResultVO resultVO = ResultVO.success(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        Long libId = params.long("id")
        
        ResultVO resultVO = new ResultVO()
//        String name = params.name
//        if (!PatternUtils.isName(name)) {
//            throw new HiiAdminException("人脸库名称仅支持汉字、字母、数字")
//        }
//        authGateFaceLibService.updateAuthGateFaceLib(libId, name)
//        authGateFaceLibService.updateAuthGateFaceLib(libId)
        AuthGateFaceLib authGateFaceLib = authGateFaceLibService.fetchById(libId)
        if (!authGateFaceLib) {
            throw new HiiAdminException("未查询到人脸库！")
        }

        Boolean retry = cacheService.retryFailAuthGateFaceLibUserTask.get("auth_gate_face_lib_user_retry_${libId}")
        if (retry != null && retry) {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = "正在重试失败人员中，请勿重复操作"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" 
            return 
        }

        authGateFaceLibUserService.retryFailAuthGateFaceLibUserTask(authGateFaceLib)
        resultVO = ResultVO.success([id: libId])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        Long libId = params.long("id")
        authGateFaceLibService.deleteAuthGateFaceLib(libId)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([id: libId])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

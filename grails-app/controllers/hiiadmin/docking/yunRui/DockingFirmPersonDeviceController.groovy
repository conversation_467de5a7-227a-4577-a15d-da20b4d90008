package hiiadmin.docking.yunRui

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.docking.DockingFirmPersonDeviceService
import hiiadmin.school.device.DeviceService
import timetabling.device.Device

import static hiiadmin.ConstantEnum.DeviceFirm


class DockingFirmPersonDeviceController {

    DockingFirmPersonDeviceService dockingFirmPersonDeviceService

    DeviceService deviceService

    def save() {
        String userIds = params.userIds
        Byte userType = params.byte("userType")
        String deviceIds = params.deviceIds
        Integer timePlanIndex = params.int("timePlanIndex", 0)
        List<Long> deviceIdList = deviceIds.split(",").collect { it as Long }
        List<Device> deviceList = deviceService.fetchAllDeviceByIdInList(deviceIdList)
        List<String> deviceCodeList = []
        deviceList.each { device ->
            if (device.firm == DeviceFirm.DH_YR.firm && device.indexCode && device.status == 1 as byte) {
                deviceCodeList << device.indexCode
            }
        }
        List<Long> userIdList = userIds.split(",").collect { it as Long }
        dockingFirmPersonDeviceService.addAuths(deviceCodeList, userIdList, userType, timePlanIndex)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

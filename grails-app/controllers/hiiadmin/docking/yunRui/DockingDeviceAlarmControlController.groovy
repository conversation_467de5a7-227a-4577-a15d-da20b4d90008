package hiiadmin.docking.yunRui

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException

import static hiiadmin.YrConstantEnum.OperateType

/**
 * 布控/撤控
 */
class DockingDeviceAlarmControlController implements BaseExceptionHandler {

    DockingDeviceAlarmConfigService dockingDeviceAlarmConfigService

    def save() {
        ResultVO resultVO = ResultVO.success()
        Long channelId = params.long("channelId")

        if (!channelId) {
            throw new HiiAdminException("撤回失败请刷新后重试！")
        }
        Integer control = params.int("control")
        switch (control) {
            case OperateType.ADD.type:
                dockingDeviceAlarmConfigService.controlChannel(channelId)
                break
            case OperateType.DEL.type:
                dockingDeviceAlarmConfigService.cancelControlChannel(channelId)
                break
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

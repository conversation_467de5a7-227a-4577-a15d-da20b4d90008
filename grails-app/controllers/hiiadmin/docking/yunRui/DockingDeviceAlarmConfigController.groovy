package hiiadmin.docking.yunRui

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.BgbDockingApi
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.docking.dhYr.DockingDeviceAlarmConfigVO
import org.springframework.beans.factory.annotation.Autowired
import timetabling.docking.yunrui.DockingDeviceAlarmConfig

/**
 * 配置布控参数
 */
@Slf4j
class DockingDeviceAlarmConfigController implements BaseExceptionHandler {

    DockingDeviceAlarmConfigService dockingDeviceAlarmConfigService

    DockingAlarmEnumService dockingAlarmEnumService

    @Autowired
    BgbDockingApi bgbDockingApi

    def index() {
        Long campusId = request.getAttribute("campusId") as Long,
             channelId = params.long("channelId")
        List<DockingDeviceAlarmConfig> configList = dockingDeviceAlarmConfigService.fetchAllDockingDeviceAlarmConfigByDeviceId(channelId)
        List<DockingDeviceAlarmConfigVO> vos = configList.collect {
            DockingDeviceAlarmConfigVO vo = new DockingDeviceAlarmConfigVO().buildVO(it)
            if (vo.alarmType == ConstantEnum.TrackAlarmEventType.A_LI_BLACKLIST.event) {
                Map map = [campusId: campusId, channelId: channelId]
                ResultVO resultVO = bgbDockingApi.transform(map)
                if (resultVO.status == 1) {
                    vo.faceLibs = resultVO.result.get("list") as List
                } else {
                    throw new HiiAdminException(resultVO.message)
                }
            }

            vo.alarmName = dockingAlarmEnumService.getAlarmNameByType(vo.alarmType)
            vo
        }
        ResultVO resultVO = ResultVO.success([list: vos])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long,
             channelId = params.long("channelId")
        String ruleJson = params.ruleJson,
               name = params.name
        Boolean strangerAlarm = params.boolean("strangerAlarm")
        Integer alarmType = params.int("alarmType")
        DockingDeviceAlarmConfig deviceAlarmConfig = dockingDeviceAlarmConfigService.cratedAndSaveAlarmConfig(campusId, channelId, name, strangerAlarm, alarmType, ruleJson)
        ResultVO resultVO = ResultVO.success(new DockingDeviceAlarmConfigVO().buildVO(deviceAlarmConfig))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        Long configId = params.long("id")
        DockingDeviceAlarmConfig config = dockingDeviceAlarmConfigService.fetchDockingDeviceAlarmConfigByConfigId(configId)
        if (config.controlStatus) {
            throw new HiiAdminException("已布控事件不能编辑")
        }
        String ruleJson = params.ruleJson,
               name = params.name
        Boolean strangerAlarm = params.boolean("strangerAlarm")
        config.ruleJson = ruleJson
        config.name = name
        config.strangerAlarm = strangerAlarm
        DockingDeviceAlarmConfig deviceAlarmConfig = dockingDeviceAlarmConfigService.updateDockingDeviceAlarmConfig(config)
        ResultVO resultVO = ResultVO.success(new DockingDeviceAlarmConfigVO().buildVO(deviceAlarmConfig))

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        Long configId = params.long("id")
        dockingDeviceAlarmConfigService.deleteAlarmConfig(configId)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([id: configId])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingPlatformStatusStatisticsVO
import timetabling.docking.DockingPlatformCampus

/**
 * 对接平台状态统计控制器
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
class DockingPlatformStatusStatisticsController implements BaseExceptionHandler {

    DockingPlatformHistoryService dockingPlatformHistoryService
    DockingPlatformCampusService dockingPlatformCampusService

    /**
     * 获取平台状态统计数据
     */
    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        Long time = params.long("time", System.currentTimeMillis())
        
        // 获取平台信息
        DockingPlatformCampus platformCampus = dockingPlatformCampusService.fetchDockingPlatformCampusByCampusIdAndFirm(campusId, firm)
        Long platformId = platformCampus.platformId
        
        // 获取统计数据
        Map<String, Object> statistics = dockingPlatformHistoryService.getPlatformStatusStatistics(platformId, time)
        
        // 获取时间轴数据
        List<Map<String, Object>> timelineData = dockingPlatformHistoryService.getTimelineData(platformId, time)
        
        // 构建VO
        DockingPlatformStatusStatisticsVO vo = DockingPlatformStatusStatisticsVO.buildVO(statistics, timelineData)
        
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success(vo)), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    /**
     * 获取时间轴数据（单独接口）
     */
    def timeline() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        Long time = params.long("time", System.currentTimeMillis())
        
        // 获取平台信息
        DockingPlatformCampus platformCampus = dockingPlatformCampusService.fetchDockingPlatformCampusByCampusIdAndFirm(campusId, firm)
        Long platformId = platformCampus.platformId
        
        // 获取时间轴数据
        List<Map<String, Object>> timelineData = dockingPlatformHistoryService.getTimelineData(platformId, time)
        
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([timeline: timelineData])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.syn.SchoolSynFirmService
import timetabling.docking.DockingPlatform

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.DeviceFirm

class CheckDockingFirmPersonAvatarController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    SchoolSynFirmService schoolSynFirmService

    CacheService cacheService

    DockingPlatformCampusService dockingPlatformCampusService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
//        Long userId = request.getAttribute("userId") as Long  7_9itt1ghrwluwkhdo_411
//        Byte userType = request.getAttribute("type") as Byte
        Integer firm = params.int("firm")
        String personId = params.personId
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        switch (firm) {
            case DeviceFirm.HK_EDU.firm:
                resultVO = antennaApi.checkPersonAvatar(campusId, personId)
                break
            case DeviceFirm.HK_YM.firm:
                break
            case DeviceFirm.DH_DSS.firm:
                String key = """${campusId}_${personId}_${firm}"""
                Map cachePersonInfoMap = cacheService.campusIdPersonIdFirmCache.get(key)
                resultVO.result.put("avatar", cachePersonInfoMap.avatar)
                break
            case DeviceFirm.FS_CAR.firm:
                break
            case DeviceFirm.ALY_DEVICE.firm:
                DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, firm)

                String key = """${campusId}_${personId}_${firm}"""
                Map cachePersonInfoMap = cacheService.campusIdPersonIdFirmCache.get(key)
                String avatarUrl = schoolSynFirmService.getAliYunImgByAddressLinkAndImg(platform.addressLink, cachePersonInfoMap?.avatarUrl as String)
                resultVO.result.put("avatar", avatarUrl)
                break
            case DeviceFirm.ALY_EDGE_DEVICE.firm:
                DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, DeviceFirm.ALY_EDGE_DEVICE.firm)
                String key = """${platform.id}_${personId}_${firm}"""
                Map cachePersonInfoMap = cacheService.campusIdPersonIdFirmCache.get(key)
                String avatarUrl = cachePersonInfoMap?.avatar as String
                resultVO.result.put("avatar", avatarUrl)
                break
        }


        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

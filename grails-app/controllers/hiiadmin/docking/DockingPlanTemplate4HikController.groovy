package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.module.planTemplate.HikPlanDTO
import hiiadmin.module.planTemplate.PlanTemplateTimeVO
import hiiadmin.planTemplate.PlanTemplateTimeService
import io.swagger.annotations.Api

import javax.annotation.Resource

@Api(value = "时间模板详情", tags = "安防管控中心", consumes = "admin")
class DockingPlanTemplate4HikController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    PlanTemplateTimeService planTemplateTimeService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        String relatedTemplateId = params.relatedTemplateId
        ResultVO resultVO = antennaApi.planTemplateDetail([
                campusId  : campusId,
                templateId: relatedTemplateId
        ])
        if (resultVO.status == 1) {
            HikPlanDTO planDTO = resultVO.result as HikPlanDTO
            List<PlanTemplateTimeVO> vos = planTemplateTimeService.transformTimeVOList(planDTO.weekTime)
            resultVO = ResultVO.success([list: vos])
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

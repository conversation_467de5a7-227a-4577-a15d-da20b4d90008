package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingPlatformStatusHistoryVO
import timetabling.docking.DockingPlatformCampus
import timetabling.docking.DockingPlatformStatusHistory


/**
 *   对接平台在线历史记录控制器
 *
 * <AUTHOR>
 * @date 2022 /06/30
 */
class DockingPlatformStatusHistoryController implements BaseExceptionHandler {

    DockingPlatformHistoryService dockingPlatformHistoryService
    DockingPlatformCampusService dockingPlatformCampusService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        Long time = params.long("time")
        Integer firmStatus = params.int("firmStatus")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        DockingPlatformCampus platformCampus = dockingPlatformCampusService.fetchDockingPlatformCampusByCampusIdAndFirm(campusId, firm)
        Long platformId = platformCampus.platformId

        List<DockingPlatformStatusHistory> statusHistoryList = dockingPlatformHistoryService.fetchAllDockingPlatformHistory(platformId, time, firmStatus, p, s)
        List<DockingPlatformStatusHistoryVO> vos = statusHistoryList.collect {
            new DockingPlatformStatusHistoryVO().buildVO(it)
        }
        Integer count = dockingPlatformHistoryService.countDockingPlatformHistory(platformId, time, firmStatus)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([list: vos, total: count])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingFirmPersonConfigVO
import timetabling.docking.DockingFirmPersonConfig

class DockingFirmPersonConfigController implements BaseExceptionHandler {

    DockingFirmPersonConfigService dockingFirmPersonConfigService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = params.long("campusId")
        Integer firm = params.int("firm")
        List<DockingFirmPersonConfig> firmPeopleList = dockingFirmPersonConfigService.fetchAllDockingFirmPersonConfigByCampusIdAndFirm(campusId, firm)
        List<DockingFirmPersonConfigVO> vos = firmPeopleList.collect { firmPeople ->
            new DockingFirmPersonConfigVO().buildVO(firmPeople)
        }
        resultVO.result.put("list", vos)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

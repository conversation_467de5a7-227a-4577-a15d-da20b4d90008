package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingFirmSyncTaskInfoVO
import hiiadmin.module.docking.DockingFirmSyncTaskVO
import hiiadmin.module.docking.SynTaskResultVO
import hiiadmin.syn.SynUserV2Service
import io.swagger.annotations.Api
import timetabling.docking.DockingFirmSyncTask
import timetabling.docking.DockingFirmSyncTaskInfo

@Api(value = "人员组织同步记录", tags = "安防管控中心", consumes = "admin")
class DockingFirmSyncTaskController implements BaseExceptionHandler {

    DockingFirmSyncService dockingFirmSyncService

    SynUserV2Service synUserV2Service

    def index() {
        ResultVO resultVO = ResultVO.success()
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Integer operatingStatus = params.int("operatingStatus")
        def resultMap = dockingFirmSyncService.fetchAllDockingFirmSyncTask4FirmPage(campusId, firm, operatingStatus, p, s)
        List<DockingFirmSyncTask> list = resultMap.list
        Integer count = resultMap.total
        List<DockingFirmSyncTaskVO> vos = list.collect { task ->
            DockingFirmSyncTaskVO vo = new DockingFirmSyncTaskVO().buildVO(task)

            if (task?.operationUserId && task?.operationUserType) {
                vo.operationUserName = dockingFirmSyncService.getSynTaskOperationUserName(task?.operationUserId, task?.operationUserType)
            }
            vo
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def show() {
        ResultVO resultVO = ResultVO.success()
        Long taskId = params.long("id")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Byte operatingStatus = params.byte("operatingStatus")
        def resultMap = dockingFirmSyncService.fetchAllDockingFirmSyncTaskInfoByTaskIdPage(taskId, operatingStatus, p, s)
        List<DockingFirmSyncTaskInfo> list = resultMap.list
        Integer count = resultMap.total
        SynTaskResultVO synTaskResultVO = dockingFirmSyncService.getSynTaskResult(taskId)
        List<DockingFirmSyncTaskInfoVO> vos = list.collect { taskInfo ->
            new DockingFirmSyncTaskInfoVO().buildVO(taskInfo)
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", count)
        resultVO.result.put("synTaskResultVO", synTaskResultVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        Long taskId = params.long("id")
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        synUserV2Service.syncRetrySync2FirmByTaskId(taskId, userId, userType)
        ResultVO resultVO = ResultVO.success([id: taskId])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

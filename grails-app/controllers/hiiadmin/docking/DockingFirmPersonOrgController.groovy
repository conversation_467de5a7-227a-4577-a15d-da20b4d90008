package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.module.docking.DockingFirmPersonOrgVO
import io.swagger.annotations.Api

import java.util.concurrent.TimeUnit

/**
 * 人员组织同步相关
 */
@Api(value = "人员组织同步管理", tags = "安防管控中心", consumes = "admin")
class DockingFirmPersonOrgController implements BaseExceptionHandler {

    DockingFirmService dockingFirmService

    CacheService cacheService

    def index() {
        ResultVO resultVO = ResultVO.success()
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        List<DockingFirmPersonOrgVO> vos = dockingFirmService.transformDockingFirmPersonOrgVO(campusId, firm)
        resultVO.result.put("list", vos)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = ResultVO.success()
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        Long orgId = params.long("orgId")
        String key = "syncPersonOrg${campusId}_${firm}".toString()
        cacheService.tryLockAndRun4lockCache(key, 30, TimeUnit.SECONDS, { dockingFirmService.syncPersonOrg(campusId, firm,orgId) })
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

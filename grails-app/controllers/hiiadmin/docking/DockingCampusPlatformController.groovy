package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingCampusPlatformVO


class DockingCampusPlatformController implements BaseExceptionHandler {

    DockingPlatformCampusService dockingPlatformCampusService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<DockingCampusPlatformVO> campusPlatformVOS = dockingPlatformCampusService.fetchAllCampusPlatformVOByCampus(campusId)
        ResultVO resultVO = ResultVO.success([list: campusPlatformVOS])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

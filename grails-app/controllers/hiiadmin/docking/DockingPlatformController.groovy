package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.docking.DockingPlatformVO
import timetabling.docking.DockingPlatform
import timetabling.docking.DockingPlatformCampus


/**
 *   对接平台控制器
 *
 * <AUTHOR>
 * @date 2022 /06/30
 */
class DockingPlatformController implements BaseExceptionHandler {

    DockingPlatformService dockingPlatformService

    DockingPlatformCampusService dockingPlatformCampusService

    def show() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("id")
        DockingPlatformCampus platformCampus = dockingPlatformCampusService.fetchDockingPlatformCampusByCampusIdAndFirm(campusId, firm)
        DockingPlatform platform = dockingPlatformService.getDockingPlatform(platformCampus.platformId)
        DockingPlatformVO vo = new DockingPlatformVO().buildVO(platform)
        ResultVO resultVO = ResultVO.success(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def patch() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("id")
        DockingPlatformCampus platformCampus = dockingPlatformCampusService.fetchDockingPlatformCampusByCampusIdAndFirm(campusId, firm)
        dockingPlatformService.checkPlatformStatus(platformCampus.platformId)
        DockingPlatform platform = dockingPlatformService.getDockingPlatform(platformCampus.platformId)
        DockingPlatformVO vo = new DockingPlatformVO().buildVO(platform)
        ResultVO resultVO = ResultVO.success(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


}

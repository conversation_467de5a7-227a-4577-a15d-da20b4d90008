package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.ferries.FerriesService
import hiiadmin.group.PersonGroupRelatedService
import hiiadmin.group.PersonGroupService
import hiiadmin.module.docking.hk.AuthConfigSearchVO
import hiiadmin.module.docking.vo.AuthConfigVO
import hiiadmin.planTemplate.PlanTemplateRelatedService
import hiiadmin.planTemplate.PlanTemplateService
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.DockingDeviceService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.utils.PhenixCoder
import org.joda.time.DateTime
import timetabling.device.Device
import timetabling.docking.DockingDevice
import timetabling.docking.RelatedUser
import timetabling.gated.Ferries
import timetabling.group.PersonGroup
import timetabling.group.PersonGroupRelated
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.planTemplate.PlanTemplate
import timetabling.planTemplate.PlanTemplatePersonGroup
import timetabling.user.Student
import timetabling.user.Teacher

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*
import static hiiadmin.HkConstantEnum.AuthConfigPerson

/**
 * 安防管控中心
 * -通行权限查询
 * -海康
 */
class AuthConfigSearch4HikController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    DeviceService deviceService

    DockingDeviceService dockingDeviceService

    RelatedUserService relatedUserService

    PlanTemplateService planTemplateService

    PersonGroupService personGroupService

    PersonGroupRelatedService personGroupRelatedService

    PlanTemplateRelatedService planTemplateRelatedService

    StudentService studentService

    UnitService unitService

    GradeService gradeService

    FerriesService ferriesService

    TeacherService teacherService

    UnitStudentService unitStudentService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("deviceId")
        Long userId = PhenixCoder.decodeId(params.userId)
        Long userType = params.byte("userType")
        String personType = params.personType ?: AuthConfigPerson.person.name()
        Long planGroupId = params.long("planGroupId")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        String indexCode = null
        String personId = null
        if (userId) {
            RelatedUser relatedUser = relatedUserService.fetchRelatedUserByStudentId(userId, DeviceFirm.HK_EDU.firm)
            if (!relatedUser) {
                throw new HiiAdminException("人员未同步海康平台")
            }
            personId = relatedUser.personId

        } else if (planGroupId) {
            personId = planTemplateRelatedService.fetchPlanTemplateRelated4hkGroup(planGroupId)?.relatedId
            if (!personId) {
                throw new HiiAdminException("分组不存在")
            }
        } else if (!deviceId) {
            throw new HiiAdminException("人员和和门禁点必须选择一个")
        }

        Device device = deviceService.fetchDeviceById(deviceId)
        if (device && device.firm != DeviceFirm.HK_EDU.firm) {
            throw new HiiAdminException("设备不支持查询")
        } else {
            indexCode = device?.indexCode
        }
        ResultVO resultVO = antennaApi.authConfigSearch([campusId  : campusId,
                                                         personIds : personId,
                                                         personType: personType,
                                                         indexCode : indexCode,
                                                         startTime : startTime,
                                                         endTime   : endTime,
                                                         p         : p,
                                                         s         : s])

        if (resultVO.status == 1) {
            Integer total = resultVO.result.total as Integer
            List<AuthConfigSearchVO> searchVOList = resultVO.result.list as List<AuthConfigSearchVO>
            List<AuthConfigVO> vos = []
            searchVOList.each { configSearch ->
                AuthConfigVO authConfigVO = new AuthConfigVO()

                String relatedTemplateId = configSearch.templateId
                authConfigVO.relatedTemplateId = relatedTemplateId
                if (relatedTemplateId == "1") {
                    authConfigVO.templateName = "全天模板"
                } else {
                    PlanTemplate planTemplate = planTemplateService.fetchPlanTemplateByRelatedTemplateId(configSearch.templateId)
                    authConfigVO.templateName = planTemplate?.name
                    authConfigVO.templateId = planTemplate?.id
                }
                if (configSearch.startTime) {
                    authConfigVO.startTime = new DateTime(configSearch.startTime).millis
                }
                if (configSearch.endTime) {
                    authConfigVO.endTime = new DateTime(configSearch.endTime).millis
                }
                DockingDevice dockingDevice = dockingDeviceService.fetchDockingDeviceByParentIndexCodeAndChannelAndFirm(configSearch.resourceIndexCode, configSearch.channelNo as String, DeviceFirm.HK_EDU.firm)
                authConfigVO.parentResourceIndexCode = configSearch.resourceIndexCode
                if (dockingDevice) {
                    authConfigVO.parentResourceName = dockingDevice.parentResourceName
                    authConfigVO.resourceIndexCode = dockingDevice.resourceIndexCode
                    authConfigVO.resourceName = dockingDevice.resourceName
                }
                String personDataId = configSearch.personDataId
                switch (configSearch.personDataType) {
                    case AuthConfigPerson.person.name():
                        RelatedUser relatedUser = relatedUserService.fetchByCampusIdAndPersonIdAndFirm(campusId, personDataId, DeviceFirm.HK_EDU.firm)
                        if (relatedUser) {
                            authConfigVO.userId = PhenixCoder.encodeId(relatedUser.userId)
                            authConfigVO.userType = relatedUser.userType
                            authConfigVO.personId = relatedUser.personId
                            switch (relatedUser.userType) {
                                case UserTypeEnum.TEACHER.type:
                                    Long teacherId = relatedUser.userId
                                    Teacher teacher = teacherService.fetchTeacherById(teacherId)
                                    authConfigVO.userName = teacher?.name
                                    break
                                case UserTypeEnum.STUDENT.type:
                                    Long studentId = relatedUser.userId
                                    Student student = studentService.fetchStudentById(studentId)
                                    authConfigVO.userName = student?.name
                                    authConfigVO.userCode = student?.code
                                    UnitStudent unitStudent = unitStudentService.fetchAdministrativeUnitStudentByStudentId(relatedUser.userId)
                                    if (unitStudent) {
                                        Unit unit = unitService.fetchUnitById(unitStudent.unitId)
                                        Grade grade = gradeService.fetchGradeById(unitStudent.gradeId)
                                        authConfigVO.memo = """${SectionType.getEnumByType(grade?.sectionId)?.name}${grade.name}${unit.name}"""
                                    }
                                    break
                                case UserTypeEnum.FERRIES.type:
                                    Long ferriesId = relatedUser.userId
                                    Ferries ferries = ferriesService.fetchFerriesByFerriesId(ferriesId)
                                    authConfigVO.userName = ferries?.name
                                    break
                            }
                        }
                        break
                    case AuthConfigPerson.personGroup.name():

                        PersonGroupRelated groupRelated = personGroupRelatedService.fetchPersonGroupRelatedByRelated(personDataId)
                        PersonGroup personGroup = personGroupService.fetchPersonGroupById(groupRelated?.groupId)
                        authConfigVO.userType = personGroup?.userType
                        authConfigVO.planGroupId = PhenixCoder.encodeId(personGroup?.id)
                        authConfigVO.planGroupName = personGroup?.name

                        authConfigVO.planGroupRelatedId = personDataId
                        break
                }
                vos << authConfigVO
            }
            resultVO = ResultVO.success([list: vos, total: total])
            withFormat {
                json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
            }
        } else {
            withFormat {
                json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
            }
        }
    }
}

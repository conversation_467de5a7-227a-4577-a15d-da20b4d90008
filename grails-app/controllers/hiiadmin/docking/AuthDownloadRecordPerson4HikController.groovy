package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.module.docking.hk.DownloadRecordDetailVO
import hiiadmin.module.docking.vo.AuthDownloadDetailVO
import io.swagger.annotations.Api

import javax.annotation.Resource

@Api(value = "权限更新记录详情", tags = "安防管控中心", consumes = "admin")
class AuthDownloadRecordPerson4HikController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    AuthDownload4HikService authDownload4HikService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        String downloadResultId = params.downloadResultId
        Integer downloadResult = params.int("downloadResult")
        int p = params.int("p", 1)
        int s = params.int("s", 30)

        String parentResourceIndexCode = params.parentResourceIndexCode

        ResultVO resultVO = antennaApi.downloadRecordPersonDetail([
                campusId        : campusId,
                downloadResultId: downloadResultId,
                downloadResult  : downloadResult,
                p               : p,
                s               : s
        ])
        if (resultVO.status == 1) {
            Integer total = resultVO.result.total as Integer
            List<DownloadRecordDetailVO> recordDetailVOList = (resultVO.result.list as JSONArray).toJavaList(DownloadRecordDetailVO.class)
            List<AuthDownloadDetailVO> vos = []
//            TODO 判断结果为空
            recordDetailVOList.each { DownloadRecordDetailVO recordDetail ->
                vos << authDownload4HikService.transformDetailVO(campusId, parentResourceIndexCode, recordDetail)
            }
            resultVO = ResultVO.success([list: vos, total: total])
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

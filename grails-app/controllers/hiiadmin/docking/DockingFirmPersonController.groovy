package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.ALiYunApiInvoker
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.apiCloud.EdgeALiYunApiInvoker
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.ferries.FerriesService
import hiiadmin.module.docking.DockJsonParseAliYunVO
import hiiadmin.module.docking.DockingFirmPersonVO
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.syn.SynUserV2Service
import hiiadmin.utils.PhenixCoder
import hiiadmin.visitor.VisitorService
import io.swagger.annotations.Api
import timetabling.docking.DockingFirmSyncTask
import timetabling.docking.DockingPlatform
import timetabling.docking.RelatedUser
import timetabling.gated.Ferries
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.DeviceFirm
import static hiiadmin.ConstantEnum.UserTypeEnum

/**
 * 人员同步
 */
@Slf4j
@Api(value = "人员同步管理", tags = "安防管控中心", consumes = "admin")
class DockingFirmPersonController implements BaseExceptionHandler {

    DockingPersonService dockingPersonService

    SynUserV2Service synUserV2Service

    RelatedUserService relatedUserService

    @Resource
    AntennaApi antennaApi

    @Resource
    ALiYunApiInvoker aLiYunApiInvoker

    @Resource
    EdgeALiYunApiInvoker edgeALiYunApiInvoker

    StudentService studentService

    ParentService parentService

    TeacherService teacherService

    FerriesService ferriesService

    VisitorService visitorService

    DockingPlatformCampusService dockingPlatformCampusService

    def index() {
        ResultVO resultVO = ResultVO.success()
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long

        Byte userType = params.byte("userType")
        String searchValue = params.searchValue
        Boolean gender = params.boolean("gender")

        Integer firm = params.int("firm")
        Boolean firmStatus = params.boolean("firmStatus")
        int p = params.int("p", 1)
        int s = params.int("s", 30)

        switch (userType) {
            case UserTypeEnum.STUDENT.type:
                String sectionIdStr = params.sectionId
                Long sectionId = PhenixCoder.decodeId(sectionIdStr)

                String gradeIdStr = params.gradeId
                Long gradeId = PhenixCoder.decodeId(gradeIdStr)

                String unitIdStr = params.unitId
                Long unitId = PhenixCoder.decodeId(unitIdStr)

                String facultyIdStr = params.facultyId
                Long facultyId = PhenixCoder.decodeId(facultyIdStr)

                String majorIdStr = params.majorId
                Long majorId = PhenixCoder.decodeId(majorIdStr)
                def re = dockingPersonService.transformDockingFirmPersonVOList4student(schoolId, campusId, sectionId, gradeId, unitId,facultyId,majorId,searchValue, gender, firm, firmStatus, p, s)
                resultVO.result = re
                break
            case UserTypeEnum.PARENT.type:
                def re = dockingPersonService.transformDockingFirmPersonVOList4Parent(campusId, searchValue, firm, firmStatus, p, s)
                resultVO.result = re
                break
            case UserTypeEnum.TEACHER.type:
                String departmentIdStr = params.departmentId
                Long departmentId =PhenixCoder.decodeId(departmentIdStr)
                def re = dockingPersonService.transformDockingFirmPersonVOList4teacher(campusId, departmentId, searchValue, gender, firm, firmStatus, p, s)
                resultVO.result = re
                break
            case UserTypeEnum.FERRIES.type:
                def re = dockingPersonService.transformDockingFirmPersonVOList4Ferries(campusId, searchValue, firm, firmStatus, s, p)
                resultVO.result = re
                break
            case UserTypeEnum.VISITORS.type:
                Long daytimeStamp = params.long("daytimeStamp")
                def re = dockingPersonService.transformDockingFirmPersonVOList4Visitor(campusId, searchValue, daytimeStamp, firm, firmStatus, s, p)
                resultVO.result = re
                break
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = ResultVO.success()
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        switch (userType) {
            case UserTypeEnum.STUDENT.type:
                DockingFirmPersonVO vo = dockingPersonService.transformDockingFirmPersonVOInfo4student(campusId, id)
                resultVO.result = vo
                break
            case UserTypeEnum.PARENT.type:
                DockingFirmPersonVO vo = dockingPersonService.transformDockingFirmPersonVOInfo4parent(campusId, id)
                resultVO.result = vo
                break
            case UserTypeEnum.TEACHER.type:
                DockingFirmPersonVO vo = dockingPersonService.transformDockingFirmPersonVOInfo4teacher(campusId, id)
                resultVO.result = vo
                break
            case UserTypeEnum.FERRIES.type:
                DockingFirmPersonVO vo = dockingPersonService.transformDockingFirmPersonVOInfo4Ferries(campusId, id)
                resultVO.result = vo
                break
            case UserTypeEnum.VISITORS.type:
                DockingFirmPersonVO vo = dockingPersonService.transformDockingFirmPersonVOInfo4Visitors(campusId, id)
                resultVO.result = vo
                break
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def patch() {
        ResultVO resultVO = ResultVO.success()
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        Integer firm = params.int("firm")
        String operating = params.operating
        switch (operating) {
            case "sync":
                String syncUserIdStr = params.userId
                Long syncUserId = PhenixCoder.decodeId(syncUserIdStr)
                Byte syncUserType = params.byte("userType")
                String option = "单个人员同步"
                switch (syncUserType) {
                    case UserTypeEnum.STUDENT.type:
                        Student student = studentService.fetchStudentById(syncUserId)
                        option = "单个学生同步【${student?.name}】"
                        break
                    case UserTypeEnum.PARENT.type:
                        Parent parent = parentService.fetchParentById(syncUserId)
                        option = "单个家长同步【${parent?.name}】"
                        break
                    case UserTypeEnum.TEACHER.type:
                        Teacher teacher = teacherService.fetchTeacherById(syncUserId)
                        option = "单个教职工同步【${teacher?.name}】"
                        break
                    case UserTypeEnum.FERRIES.type:
                        Ferries ferries = ferriesService.fetchFerriesByFerriesId(syncUserId)
                        option = "单个接送人同步【${ferries?.name}】"
                        break
                    case UserTypeEnum.VISITORS.type:
                        Visitor visitor = visitorService.fetchVisitorById(syncUserId)
                        option = "单个访客同步【${visitor?.visitorName}】"
                        break
                }
                synUserV2Service.syncUpdateOrCreateUser2Firm(campusId, [syncUserId], syncUserType, userId, userType, DockingFirmSyncTask.OPERATION_TYPE_ONE, option, firm)
                break
            case "refresh":
                String syncUserIdStr = params.userId
                Long syncUserId = PhenixCoder.decodeId(syncUserIdStr)
                Byte syncUserType = params.byte("userType")
                switch (firm) {
                    case DeviceFirm.HK_EDU.firm:
                        try {
                            antennaApi.syncHkCardCache(campusId)
                        } catch (RuntimeException e) {
                            log.warn("同步人员卡号失败${e.message}".toString())
                        }
                        RelatedUser relatedUser = relatedUserService.fetchRelatedUserByUserIdAndUserType(syncUserId, syncUserType, DeviceFirm.HK_EDU.firm)
                        try {
                            antennaApi.syncHkPersonInfo(campusId, relatedUser?.personId)
                        } catch (RuntimeException e) {
                            log.warn("同步人员信息失败${e.message}".toString())
                            throw new HiiAdminException("同步人员信息失败:${e.message}")
                        }
                        break
                    case DeviceFirm.HK_YM.firm:
                        break
                    case DeviceFirm.DH_DSS.firm:
                        antennaApi.syncDHPersonInfo(campusId, syncUserId, syncUserType)
                        break
                    case DeviceFirm.FS_CAR.firm:
                        RelatedUser relatedUser = relatedUserService.fetchOrCreateRelatedUserByUserIdAndUserType(syncUserId, syncUserType, DeviceFirm.FS_CAR.firm)
                        if (relatedUser) {
                            antennaApi.synFsStaffByIdToCache([campusId: campusId, id: relatedUser.personId])
                        }
                        break
                    case DeviceFirm.ALY_DEVICE.firm:
                        RelatedUser relatedUser = relatedUserService.fetchOrCreateRelatedUserByUserIdAndUserType(syncUserId, syncUserType, DeviceFirm.ALY_DEVICE.firm)
                        if (relatedUser) {
                            DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, firm)
                            DockJsonParseAliYunVO aliYunVO = dockingPlatformCampusService.getDockJsonParseAliYunVO(platform.dockingJson)
                            if (aliYunVO.version) {
                                edgeALiYunApiInvoker.synDetailsFaceCacheToALiYun([campusId: campusId, id: relatedUser.personId, version: aliYunVO.version])
                            } else {
                                aLiYunApiInvoker.synDetailsFaceCacheToALiYun([campusId: campusId, id: relatedUser.personId])
                            }

                        }
                        break
                    case DeviceFirm.ALY_EDGE_DEVICE.firm:
                        RelatedUser relatedUser = relatedUserService.fetchOrCreateRelatedUserByUserIdAndUserType(syncUserId, syncUserType, DeviceFirm.ALY_EDGE_DEVICE.firm)
                        if (relatedUser) {
                            DockingPlatform platform = dockingPlatformCampusService.fetchDockingPlatformByCampusIdAndFirm(campusId, DeviceFirm.ALY_EDGE_DEVICE.firm)
                            aLiYunApiInvoker.synUserDetailsCache([campusId: platform.id, id: relatedUser.personId])
                        }
                        break
                }
                break
            case "init":
                synUserV2Service.syncDefaultSync4UpdateOrCreateUserToFirm(campusId, firm, userId, userType)
                break
            case "delete":
                String syncUserIdStr = params.userId
                Long syncUserId = PhenixCoder.decodeId(syncUserIdStr)
                Byte syncUserType = params.byte("userType")
                RelatedUser relatedUser = relatedUserService.fetchOrCreateRelatedUserByUserIdAndUserType(syncUserId, syncUserType, firm)
                relatedUser.status = 0
                relatedUserService.saveRelatedUser(relatedUser)
            default:
                break
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

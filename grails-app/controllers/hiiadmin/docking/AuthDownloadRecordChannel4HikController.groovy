package hiiadmin.docking

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.docking.hk.DownloadRecordChannelVO
import hiiadmin.module.docking.vo.AuthDownloadVO
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.DockingDeviceService
import io.swagger.annotations.Api
import org.joda.time.DateTime
import timetabling.device.Device
import timetabling.docking.DockingDevice

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.DeviceFirm
import static hiiadmin.HkConstantEnum.DownloadTaskType

@Api(value = "权限更新记录", tags = "安防管控中心", consumes = "admin")
class AuthDownloadRecordChannel4HikController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    DeviceService deviceService

    DockingDeviceService dockingDeviceService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("deviceId")
        Long downloadResult = params.int("downloadResult")

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")

        String indexCode = null
        if (deviceId) {
            Device device = deviceService.fetchDeviceById(deviceId)
            if (device?.firm != DeviceFirm.HK_EDU.firm) {
                throw new HiiAdminException("设备不支持查询")
            } else {
                indexCode = device?.indexCode
            }
        }
        ResultVO resultVO = antennaApi.downloadRecordChannelList([
                campusId      : campusId,
                indexCode     : indexCode,
                downloadResult: downloadResult,
                startTime     : startTime,
                endTime       : endTime,
                p             : p,
                s             : s
        ])
        if (resultVO.status == 1) {
            Integer total = resultVO.result.total as Integer
            List<DownloadRecordChannelVO> recordChannelVOList = resultVO.result.list as List<DownloadRecordChannelVO>
            List<AuthDownloadVO> vos = []
            recordChannelVOList.each { recordChannel ->
                String resourceIndexCode = recordChannel.resourceInfo.resourceIndexCode
                AuthDownloadVO authDownloadVO = new AuthDownloadVO(
                        parentResourceIndexCode: resourceIndexCode,
                        downloadResultId: recordChannel.downloadResultId,
                        tagId: recordChannel.tagId,
                        taskId: recordChannel.taskId,
                        taskType: recordChannel.taskType,
                        taskTypeName: DownloadTaskType.bit2Name(recordChannel.taskType),
                        taskOptType: recordChannel.taskOptType,
                        downloadResult: recordChannel.downloadResult,
                        errorCode: recordChannel.errorCode,
                        downloadPersonCount: recordChannel.downloadPersonCount,
                        successedPersonCount: recordChannel.successedPersonCount,
                        failedPersonCount: recordChannel.failedPersonCount
                )
                if (recordChannel.startTime) {
                    authDownloadVO.startTime = new DateTime(recordChannel.startTime).millis
                }
                if (recordChannel.endTime) {
                    authDownloadVO.endTime = new DateTime(recordChannel.endTime).millis
                }
                DockingDevice dockingDevice = dockingDeviceService.fetchDockingDeviceByParentIndexCodeAndFirm(resourceIndexCode, DeviceFirm.HK_EDU.firm)
                if (dockingDevice) {
                    authDownloadVO.parentResourceName = dockingDevice.parentResourceName + "（" + dockingDevice.resourceName + "）"
                }
                vos << authDownloadVO
            }
            resultVO = ResultVO.success([list: vos, total: total])
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.gated

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.track.TrackService


class TrackPicController {

    TrackService trackService


    def show() {
        Long alarmId = params.long("id")
        TrackVO trackVO = new TrackVO()
        ResultVO resultVO = trackService.getTrackPic(alarmId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.gated

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.history.AppTrackService
import hiiadmin.humanface.FaceGroupService
import hiiadmin.mapConfig.service.CampusMapConfigService
import hiiadmin.module.gated.TrackVO
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.track.TrackService
import hiiadmin.track.TrackVODOService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import org.apache.commons.lang3.StringUtils
import timetabling.history.HistoryTrack
import timetabling.mapConfig.CampusMapConfig
import timetabling.track.Track

import static hiiadmin.ConstantEnum.DeviceFirm

@Api(value = "人员通行记录", tags = "校园安全", consumes = "admin")
class HiiAdminTrackController implements BaseExceptionHandler {

    TrackService trackService

    TrackVODOService trackVODOService

    DockingPlatformCampusService dockingPlatformCampusService

    FaceGroupService faceGroupService

    CampusMapConfigService campusMapConfigService

    AppTrackService appTrackService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long

        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
//        if (startDate && endDate) {
//            startDate = new DateTime(startDate).withMillisOfDay(0).millis
//            endDate = new DateTime(endDate).withMillisOfDay(0).plusDays(1).millis
//        }
        Long departmentId = PhenixCoder.decodeId(params.departmentId)
        String searchValue = params.searchValue
        String studentName = params.studentName
        Long unitId = PhenixCoder.decodeId(params.unitId)
        //是否选择查看历史通行记录
        boolean history = params.boolean("history", false)
        List<TrackVO> vos = []
        //家长/接送人聚合，使用string
        String userType = params.userType
        String deviceIds = params.deviceIds

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        if (history) {
            List<HistoryTrack> historyTrackList = trackService.fetchAllHistoryTrackByCampusId(campusId, deviceIds, searchValue, userType, startDate, endDate, departmentId, unitId, history, studentName, p, s)
            historyTrackList?.each { track ->
                TrackVO vo = trackVODOService.transformHistoryTrackVOV2(track)
                vos << vo
            }
            Integer historyCount = trackService.countHistoryTrackByCampusId(campusId, deviceIds, searchValue, userType, startDate, endDate, departmentId, unitId, history, studentName)
            resultVO.result.put("list", vos)
            resultVO.result.put("total", historyCount ?: 0)
        } else {
            List<Track> trackList = trackService.fetchAllTrackByCampusId(campusId, deviceIds, searchValue, userType, startDate, endDate, departmentId, unitId, history, studentName, p, s)
            trackList.each { track ->
                TrackVO vo = trackVODOService.transformTrackVO(track)
                if (track.firm == DeviceFirm.ALY_DEVICE.firm) {
                    vo.faceImg = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, campusId)
                }
                vos << vo
            }
            Integer count = trackService.countTrackByCampusId(campusId, deviceIds, searchValue, userType, startDate, endDate, departmentId, unitId, history, studentName)
            resultVO.result.put("list", vos)
            resultVO.result.put("total", count ?: 0)
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long id = params.long("id")
        Long userId = PhenixCoder.decodeId(params.userId)
        Long campusId = request.getAttribute("campusId") as Long
        Boolean isAsc = params.boolean("isAsc", false)

        //userId不为空查用户的的轨迹记录
        if (userId != null) {
            Long startTime = params.long("startTime")
            Long endTime = params.long("endTime")
            Byte userType = params.byte("userType")
            FaceGroupVO faceGroupVO

            try {
                faceGroupVO = faceGroupService.getFaceGroupByUserIdAndUserType(userId, userType, campusId)
                CampusMapConfig campusMapConfig = campusMapConfigService.fetchCampusMapConfigByCampusId(campusId)
                if (campusMapConfig && StringUtils.isNotBlank(campusMapConfig?.mapPic)) {
                    faceGroupVO.mapPic = campusMapConfig?.mapPic
                }
                List<TrackVO> vos = appTrackService.getAllTrackVORecord(campusId, userId, userType, startTime, endTime,isAsc)
                trackVODOService.trackVOAddBuilding(vos, campusId)
                //添加序号
                if (isAsc){
                    vos.eachWithIndex { vo, idx ->
                        vo.idx = idx + 1
                    }
                }else {
                    vos.eachWithIndex { vo, idx ->
                        vo.idx = vos.size() - idx
                    }
                }
                //添加活动频次分析
                Map<String, List<TrackVO>> activityFrequencyAnalysis = vos.groupBy { it.viaName}
                Map<String, Integer> frequencyAnalysis = activityFrequencyAnalysis.collectEntries { String viaName, List<TrackVO> tracks ->
                    [viaName, tracks.size()]
                } as Map<String, Integer>
                //根据value降序
                frequencyAnalysis = frequencyAnalysis.sort { a, b -> b.value <=> a.value }.take(10)

               faceGroupVO.frequencyAnalysis = frequencyAnalysis

                faceGroupVO.trackList = vos
                resultVO.result.put("trackAlarm", faceGroupVO)
            } catch (Exception e) {
                resultVO.status = 0
                resultVO.message = e.message
            }
        } else {
            Track track = trackService.fetchById(id)
            if (track) {
                TrackVO trackVO = trackVODOService.transformTrackVO(track)
                if (track.firm == DeviceFirm.ALY_DEVICE.firm) {
                    trackVO.faceImg = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, campusId)
                }
                trackVODOService.trackVOAddBuilding([trackVO], campusId)
                resultVO.result.put("track", trackVO)
            } else {
                resultVO.status = 0
                resultVO.message = "轨迹不存在"
            }
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.gated

import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.docking.RelatedUserService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.ferries.FerriesService
import hiiadmin.file.XssfExcelService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.visitor.VisitorService
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import timetabling.gated.Ferries
import timetabling.gated.GatedTask
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import javax.annotation.Resource

class HiiAdminGatedDaDuaExcelController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    GatedService gatedService

    RelatedUserService relatedUserService

    XssfExcelService xssfExcelService

    StudentService studentService

    ParentService parentService

    FerriesService ferriesService

    TeacherService teacherService

    VisitorService visitorService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        Long taskId = params.long("taskId")
        Integer stat = params.int("stat")
        GatedTask task = gatedService.fetchGatedTaskById(taskId)
        if (!task.faceLibId) {
            throw new HiiAdminException("关联大华人脸库数据异常，请联系客服处理")
        }
        int p = params.int("p", 1)
        int s = params.int("total")

        ResultVO result = antennaApi.searchPerson4FaceLib([campusId: task.campusId, faceLibId: task.faceLibId, stat: stat, p: p, s: s])
        List<String> list = []
        if (result.status == 1) {
            result.result.list.each {
                list << it.id
            }
        } else {
            throw new HiiAdminException("获取大华人员下发详情异常")
        }
        log.info("listsize:${list.size()}")

        List<Long> userIdList = relatedUserService.fetchAllRelatedUserByPersonIdsAndUserType(list, task.userType, ConstantEnum.DeviceFirm.DH_DSS.firm, task.campusId)*.userId

        String fileName = "下发失败人员名单.xlsx"
        XSSFWorkbook workbook = new XSSFWorkbook()

        log.info("userIdListsize:${userIdList.size()}")
        Map<Integer, String> titleMap = [:]
        Map<Long, String> userIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, task.userType, userIdList)
        switch (task.userType) {
            case ConstantEnum.UserTypeEnum.STUDENT.type:

                titleMap = [
                        0: "姓名",
                        1: "学号",
                        2: "性别",
                ]
                List<Student> studentList = studentService.fetchAllStudentByStudentIdInList(userIdList)
                workbook = xssfExcelService.transformPersonIssueFailureExport(studentList, titleMap, task.userType)
                fileName = "下发失败人员名单（学生）.xlsx"
                break
            case ConstantEnum.UserTypeEnum.PARENT.type:
                titleMap = [
                        0: "姓名",
                        1: "学生关系",
                        2: "手机号",
                ]
                List<Parent> parentList = parentService.fetchAllParentByIds(userIdList)
                Map<Long, String> relationMap = parentService.getParentAndStudentRelationListByParentIdList(userIdList)
                List resultList = []
                parentList.each {
                    resultList << [name: it.name, relation: relationMap.get(it.id), mobile: userIdMobileMap.get(it.id)]
                }
                workbook = xssfExcelService.transformPersonIssueFailureExport(resultList, titleMap, task.userType)
                fileName = "下发失败人员名单（接送人）.xlsx"
                break
            case ConstantEnum.UserTypeEnum.FERRIES.type:

                titleMap = [
                        0: "姓名",
                        1: "学生关系",
                        2: "手机号",
                ]
                List<Ferries> ferries = ferriesService.fetchAllByFerriesIdList(userIdList)
                Map<Long, String> relationMap = ferriesService.getFerriesAndStudentRelationListByFerriesIdList(userIdList)
                List resultList = []
                ferries.each {
                    resultList << [name: it.name, relation: relationMap.get(it.id), mobile: userIdMobileMap.get(it.id)]
                }
                workbook = xssfExcelService.transformPersonIssueFailureExport(resultList, titleMap, task.userType)
                fileName = "下发失败人员名单（接送人）.xlsx"
                break
            case ConstantEnum.UserTypeEnum.TEACHER.type:

                titleMap = [
                        0: "姓名",
                        1: "工号",
                        2: "性别",
                ]

                List<Teacher> teacherList = teacherService.fetchAllTeacherByIdInList(userIdList)
                Map<Long, String> teacherJobNum = teacherService.getTeacherNumByTeacherId(userIdList, task.campusId)

                List resultList = []
                teacherList.each {
                    resultList << [name: it.name, code: teacherJobNum.get(it.id), gender: it.gender]
                }
                workbook = xssfExcelService.transformPersonIssueFailureExport(resultList, titleMap, task.userType)
                fileName = "下发失败人员名单（教师）.xlsx"
                break
            case ConstantEnum.UserTypeEnum.VISITORS.type:
                titleMap = [
                        0: "姓名",
                        1: "手机号"
                ]
                List<Visitor> visitorList = visitorService.fetchVisitorByIds(userIdList)
                List resultList = []
                visitorList.each {
                    resultList << [name: it.visitorName, mobile: userIdMobileMap.get(it.id)]
                }
                workbook = xssfExcelService.transformPersonIssueFailureExport(visitorList, titleMap, task.userType)
                fileName = "下发失败人员名单（访客）.xlsx"
                break

        }

        File file = xssfExcelService.getExcelFile(workbook, fileName)
        fileName = URLEncoder.encode(fileName, "UTF-8")
        response.setContentType("multipart/form-data")
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName)
        OutputStream out = null
        try {
            out = response.getOutputStream()
            out << file?.bytes
            try {
                file.delete()
            } catch (Exception e) {
                log.error("文件删除错误：{}", e)
            }
        } catch (IOException e) {
            log.error("文件下载错误:{}", e)
        } finally {
            out.flush()
            out.close()
        }
    }
}

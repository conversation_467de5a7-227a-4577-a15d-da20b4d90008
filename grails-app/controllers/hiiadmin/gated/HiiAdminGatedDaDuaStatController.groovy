package hiiadmin.gated

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.exceptions.HiiAdminException
import timetabling.gated.GatedTask

import javax.annotation.Resource

class HiiAdminGatedDaDuaStatController implements BaseExceptionHandler {

    @Resource
    AntennaApi antennaApi

    GatedService gatedService


    def index() {
        Long taskId = params.long("taskId")
        Integer stat = params.int("stat")
        GatedTask task = gatedService.fetchGatedTaskById(taskId)
        if (!task.faceLibId) {
            throw new HiiAdminException("关联大华人脸库数据异常，请联系客服处理")
        }
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        ResultVO result = antennaApi.searchPerson4FaceLib([campusId: task.campusId, faceLibId: task.faceLibId, stat: stat, p: p, s: s])
        ResultVO resultVO = new ResultVO()
        if (result.status == 1) {
            Integer total = result.result.total as Integer
            List list = []
            result.result.list.each {
                list << [name: it.name, gender: it.gender == 1, code: it.code, avatar: it.photoUrl]
            }
            resultVO.status = 1
            resultVO.result.put("total", total)
            resultVO.result.put("list", list)
        } else {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = "获取大华人员下发详情异常"
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long taskId = params.long("id")
        GatedTask task = gatedService.fetchGatedTaskById(taskId)
        antennaApi.againIssued4FaceLib([campusId: task.campusId, faceLibId: task.faceLibId])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

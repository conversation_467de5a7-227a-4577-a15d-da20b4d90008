package hiiadmin.gated

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.module.gated.GatedDeviceVO
import hiiadmin.module.gated.GatedPersonVO
import hiiadmin.module.gated.GatedTaskVO
import hiiadmin.module.gated.GatedTimeVO
import hiiadmin.school.GradeService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.device.DeviceService
import hiiadmin.school.user.ParentService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.Department
import timetabling.building.DoorPlate
import timetabling.device.Device
import timetabling.gated.GatedDevice
import timetabling.gated.GatedPerson
import timetabling.gated.GatedTask
import timetabling.gated.GatedTime
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import static com.google.common.base.Preconditions.checkNotNull
import static hiiadmin.ConstantEnum.UserTypeEnum

@Api(value = "门控配置", tags = "校园安全", consumes = "admin")
class HiiAdminGatedController implements BaseExceptionHandler {

    GatedService gatedService

    UnitService unitService

    GradeService gradeService

    StudentService studentService

    DepartmentService departmentService

    TeacherService teacherService

    DeviceService deviceService

    DoorPlateService doorPlateService

    ParentService parentService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<GatedTask> gatedTaskList = []
        Integer count = 0
        if (userType) {
            gatedTaskList.addAll(gatedService.fetchAllGatedTaskByCampusIdAndUserType(campusId, userType, p, s))
            count = gatedService.countGatedTaskByCampusIdAndUserType(campusId, userType)
        } else {
            gatedTaskList.addAll(gatedService.fetchAllGatedTaskByCampusId(campusId, p, s))
            count = gatedService.countGatedTaskByCampusId(campusId)
        }

        List<GatedTaskVO> vos = gatedTaskList.collect {
            task ->
                GatedTaskVO taskVO = new GatedTaskVO().buildVO(task)
                Integer total = gatedService.countGatedPersonByTaskId(task.id)
                taskVO.personTotal = total
                String channelNames = gatedService.getTaskDeviceNameByTaskId(task.id)
                taskVO.deviceNames = channelNames
                taskVO
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        GatedTask task = gatedService.fetchGatedTaskById(taskId)
        GatedTaskVO vo = new GatedTaskVO().buildVO(task)
        String c = params.c
        switch (c) {
            case "time":
                List<GatedTime> gatedTimeList = gatedService.fetchAllGatedTimeByTackId(taskId)
                vo.times = gatedTimeList.collect {
                    gatedTime ->
                        new GatedTimeVO().buildVO(gatedTime)
                }
                break
            case "person":
                Long gradeId = PhenixCoder.decodeId(params.gradeId)
                Byte gender = params.byte("gender")
                Long layerId = PhenixCoder.decodeId(params.layerId)
                Byte userType = params.byte("userType")
                String searchValue = params.searchValue
                switch (userType) {
                    case UserTypeEnum.STUDENT.type:
                        List<GatedPersonVO> voList = []
                        if (searchValue) {
                            voList.addAll(gatedService.transformTaskStudent(taskId, searchValue))
                        } else if (gradeId) {
                            voList.addAll(gatedService.transformTaskStudentByGradeId(taskId, gradeId))
                        } else {
                            int p = params.int("p", 1)
                            int s = params.int("s", 30)
                            Integer count = gatedService.countGatedPersonByTaskId(taskId, null, gender, layerId)
                            List<GatedPerson> gatedPersonList = gatedService.fetchAllGatedPersonByTaskId(taskId, null, gender, layerId, p, s)
                            Map<Long, Unit> unitIdMap = unitService.fetchAllNormalAdminUnitIdMapByCampusId(task.campusId)
                            Map<Long, Grade> gradeIdMap = gradeService.fetchAllNormalGradeIdMapByCampusId(task.campusId)
                            gatedPersonList.each {
                                gatedPerson ->
                                    Student student = studentService.fetchStudentById(gatedPerson.userId)
                                    if (student) {
                                        GatedPersonVO personVO = new GatedPersonVO().buildVO(student, gatedPerson.id, true)
                                        personVO.unitId = PhenixCoder.encodeId(gatedPerson.unitId)
                                        Unit unit = unitIdMap.get(gatedPerson.unitId)
                                        Grade grade = gradeIdMap.get(gatedPerson.gradeId)
                                        DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(gatedPerson.doorPlateId)
                                        personVO.unitName = unit?.name
                                        personVO.alias = unit?.alias
                                        personVO.gradeId = PhenixCoder.encodeId(grade?.id)
                                        personVO.gradeName = grade?.name
                                        personVO.buildingId = PhenixCoder.encodeId(doorPlate?.buildingId)
                                        personVO.layerId = PhenixCoder.encodeId(doorPlate?.layerId)
                                        personVO.doorPlateName = doorPlate?.memo
                                        voList << personVO
                                    }
                            }
                            vo.personTotal = count
                        }
                        vo.persons = voList
                        break
                    case UserTypeEnum.TEACHER.type:
                        List<GatedPerson> gatedPersonList = gatedService.fetchAllGatedPerson4teacher(campusId)
                        Map<Long, Long> teacherIdPersonIdMap = [:]
                        Map<Long, Boolean> teacherIdGatedMap = [:]
                        gatedPersonList.each {
                            teacherIdGatedMap[it.userId] = true
                            if (it.taskId == taskId) {
                                teacherIdPersonIdMap[it.userId] = it.id
                            }
                        }
                        List<Department> departmentList = departmentService.fetchAllDepartmentByCampusId(campusId)
                        List<Teacher> teacherList = teacherService.fetchAllTeacherByCampusIdLimit(campusId, false)
                        Multimap<Long, Long> longLongMultimap = departmentService.transformDepartmentIdStaffDepartment(campusId)
                        Map<Long, Teacher> longTeacherMap = teacherList.collectEntries { teacher ->
                            [teacher.id, teacher]
                        }
                        List<Long> teacherIdSet = longTeacherMap.keySet().collect { it }
                        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacherIdSet)
                        List<DepartmentVO> vos = departmentList.collect { department ->
                            Set<Long> teacherIdList = longLongMultimap.get(department.id)
                            DepartmentVO departmentVO = new DepartmentVO(
                                    id: department.id,
                                    name: department.name,
                            )
                            departmentVO.persons = teacherIdList.collect { teacherId ->
                                teacherIdSet.remove(teacherId)
                                Teacher teacher = longTeacherMap.get(teacherId)
                                GatedPersonVO personVO = new GatedPersonVO().buildVO4Teacher(teacher, teacherIdPersonIdMap[teacherId], teacherIdGatedMap[teacherId])
                                if (personVO) {
                                    personVO.mobile = teacherIdMobileMap.get(teacherId)
                                }
                                personVO
                            }
                            departmentVO
                        }
                        DepartmentVO departmentVO = new DepartmentVO(
                                id: PhenixCoder.encodeId(-1L),
                                name: "未分组"
                        )
                        departmentVO.persons = teacherIdSet.collect { teacherId ->
                            Teacher teacher = longTeacherMap.get(teacherId)
                            GatedPersonVO personVO = new GatedPersonVO().buildVO4Teacher(teacher, teacherIdPersonIdMap[teacherId], teacherIdGatedMap[teacherId])
                            if (personVO) {
                                personVO.mobile = teacherIdMobileMap.get(teacherId)
                            }
                            personVO
                        }
                        vos << departmentVO
                        vo.departments = vos
                        break
                    case UserTypeEnum.FERRIES.type:
                        List<GatedPersonVO> voList = []
                        if (searchValue) {
                            voList.addAll(gatedService.transformTaskStudent(taskId, searchValue))
                        } else if (gradeId) {
                            voList.addAll(gatedService.transformTaskStudentByGradeId(taskId, gradeId))
                        } else {
                            int p = params.int("p", 1)
                            int s = params.int("s", 30)
                            Integer count = gatedService.countGatedPersonByTaskId(taskId, null, gender, layerId)
                            List<GatedPerson> gatedPersonList = gatedService.fetchAllGatedPersonByTaskId(taskId, null, gender, layerId, p, s)

                            Map<Long, Unit> unitIdMap = unitService.fetchAllNormalAdminUnitIdMapByCampusId(task.campusId)
                            Map<Long, Grade> gradeIdMap = gradeService.fetchAllNormalGradeIdMapByCampusId(task.campusId)
                            gatedPersonList.each { gatedPerson ->
                                Student student = studentService.fetchStudentById(gatedPerson.userId)
                                if (student) {
                                    GatedPersonVO personVO = new GatedPersonVO().buildVO(student, gatedPerson.id, true)
                                    personVO.unitId = PhenixCoder.encodeId(gatedPerson.unitId)
                                    Unit unit = unitIdMap.get(gatedPerson.unitId)
                                    Grade grade = gradeIdMap.get(gatedPerson.gradeId)
                                    DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(gatedPerson.doorPlateId)
                                    personVO.unitName = unit?.name
                                    personVO.alias = unit?.alias
                                    personVO.gradeId = PhenixCoder.encodeId(grade?.id)
                                    personVO.gradeName = grade?.name
                                    personVO.buildingId = PhenixCoder.encodeId(doorPlate?.buildingId)
                                    personVO.layerId = PhenixCoder.encodeId(doorPlate?.layerId)
                                    personVO.doorPlateName = doorPlate?.memo
                                    voList << personVO
                                }
                            }
                            vo.personTotal = count
                        }
                        vo.persons = voList
                        break
                    case UserTypeEnum.PARENT.type:
                        int p = params.int("p", 1)
                        int s = params.int("s", 30)
                        if (searchValue) {
                            Integer count = gatedService.countGatedPerson4ParentS(taskId, searchValue)
                            List<GatedPersonVO> voList = gatedService.transformTaskParent(taskId, searchValue, p, s)
                            vo.persons = voList
                            vo.personTotal = count
                        } else {
                            Integer count = gatedService.countGatedPersonByTaskId(taskId)
                            List<GatedPerson> gatedPersonList = gatedService.fetchAllGatedPersonByTaskId(taskId, p, s)
                            List<GatedPersonVO> voList = []
                            Map<Long, String> parentIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.PARENT.type, gatedPersonList*.userId)
                            gatedPersonList.each { gatedPerson ->
                                Parent parent = parentService.fetchParentById(gatedPerson.userId)
                                if (parent) {
                                    GatedPersonVO personVO = new GatedPersonVO().buildVO(parent, gatedPerson.id)
                                    personVO.mobile = parentIdMobileMap.get(parent.id)
                                    voList << personVO
                                }
                            }
                            vo.persons = voList
                            vo.personTotal = count
                        }
                        break
                    case UserTypeEnum.VISITORS.type:
                        int p = params.int("p", 1)
                        int s = params.int("s", 30)
                        if (searchValue) {
                            Integer count = gatedService.countGatedPerson4VisitorS(taskId, searchValue)
                            List<GatedPersonVO> voList = gatedService.transformTaskVisitor(taskId, searchValue, p, s)
                            vo.persons = voList
                            vo.personTotal = count
                        } else {
                            Integer count = gatedService.countGatedPersonByTaskId(taskId)
                            List<GatedPerson> gatedPersonList = gatedService.fetchAllGatedPersonByTaskId(taskId, p, s)
                            List<GatedPersonVO> voList = []
                            Map<Long, String> visitorIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, UserTypeEnum.VISITORS.type, gatedPersonList*.userId)
                            gatedPersonList.each { gatedPerson ->
                                Visitor visitor = Visitor.get(gatedPerson.userId)
                                if (visitor) {
                                    GatedPersonVO personVO = new GatedPersonVO().buildVO(visitor, gatedPerson.id)
                                    personVO.mobile = visitorIdMobileMap.get(visitor.id)
                                    voList << personVO
                                }
                            }
                            vo.persons = voList
                            vo.personTotal = count
                        }
                        break
                }
                break
            case "device":
                List<GatedDevice> gatedDeviceList = gatedService.fetchAllGatedDeviceByTaskId(taskId)
                Map<Long, GatedDevice> deviceIdTaskIdMap = gatedDeviceList.collectEntries {
                    [it.deviceId, it]
                }
                List<Device> deviceList = deviceService.fetchAllDeviceByCampusId(campusId)
                List<GatedDeviceVO> voList = []
                deviceList.each {
                    Device device ->
                        voList << new GatedDeviceVO().buildVO(device, deviceIdTaskIdMap[device.id]?.id, deviceIdTaskIdMap[device.id]?.channel)
                }
                vo.devices = voList
                break
        }
        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        String name = params.name
        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
        if (gatedService.hasGatedTask4CampusIdAndName(campusId, name)) {
            throw new HiiAdminException("已有该名称的配置，不能重复创建")
        }
        ServiceResult taskServiceResult = gatedService.createAndSaveGatedTask(schoolId, campusId, name, userType, startDate, endDate)
        if (taskServiceResult.success) {
            resultVO.result.put("id", taskServiceResult.result.id)
        } else {
            resultVO.status = 0
            resultVO.code = taskServiceResult.code as Integer
            resultVO.message = taskServiceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long('id')
        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
        String memo = params.memo
        if (memo) {
            gatedService.changeMemo(taskId, memo)
        }

        Boolean gated = params.boolean('gated')
        if (gated != null) {
            gatedService.changeGated(taskId, gated)
        }

        if (startDate && endDate) {
            ServiceResult serviceResult = gatedService.changeGatedTaskTime(taskId, startDate, endDate)
            if (!serviceResult.success) {
                resultVO.status = 0 as byte
                resultVO.code = serviceResult.code as Integer
                resultVO.message = serviceResult.message
            }
        }

        String c = params.c
        switch (c) {
            case "time":
                //是否快速设置
                String days = params.days
                ServiceResult serviceResult = gatedService.setGatedTimeV2(taskId, days)
                if (!serviceResult.success) {
                    resultVO.status = 0 as byte
                    resultVO.code = serviceResult.code as Integer
                    resultVO.message = serviceResult.message
                }
//                redirect(action: "show", id: taskId, params: [c: "time"])
                break
            case "personPlus":
                String ids = params.ids
                ids = PhenixCoder.decodeIds(ids)
                ServiceResult serviceResult = gatedService.pushPerson(taskId, ids)
                if (!serviceResult.success) {
                    resultVO.status = 0 as byte
                    resultVO.code = serviceResult.code as Integer
                    resultVO.message = serviceResult.message
                }
//                redirect(action: "show", id: taskId, params: [c: "person"])
                break
            case "personMinus":
                String ids = params.ids
                ids = PhenixCoder.decodeIds(ids)
                checkNotNull(ids, "请选择人员")
                gatedService.minusPerson(taskId, ids)
                break
            case "device":
                String ids = params.ids
                ServiceResult serviceResult = gatedService.setTaskDevice(taskId, ids)
                if (!serviceResult.success) {
                    resultVO.status = 0 as byte
                    resultVO.code = serviceResult.code as Integer
                    resultVO.message = serviceResult.message
                }
                break
        }
        resultVO.result.put("id", taskId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long('id')
        gatedService.deleteGatedTask(taskId)
        resultVO.result.put("id", taskId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long taskId = params.long('id')
        String c = params.c
        switch (c) {
            case "time":
                ServiceResult serviceResult = gatedService.patch2DaHua(taskId)
                if (!serviceResult.success) {
                    resultVO.code = serviceResult.code as Integer
                    resultVO.message = serviceResult.message
                }
                break

        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.file

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.oss.OssUtils

/**
 * 用fileName换取临时url
 */
class SmartCampusOssUrlController implements BaseExceptionHandler {

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String fileNames = params.fileNames
        Integer time = params.int("time")
        List<String> fileNameList = fileNames?.split(",")
        List<String> urlList = OssUtils.transformFilePathList(fileNameList, time)
        resultVO.result.put("list", urlList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

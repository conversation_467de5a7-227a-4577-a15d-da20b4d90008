package hiiadmin.file

import com.alibaba.fastjson.JSON
import com.aliyuncs.auth.sts.AssumeRoleResponse
import com.bugu.ResultVO
import hiiadmin.oss.OssUtils
import hiiadmin.utils.ObjectUtils

class OssTokenController {

    def index() {
        ResultVO resultVO = ResultVO.success()
        AssumeRoleResponse.Credentials c = OssUtils.STS()
        if (c == null) {
            resultVO.status = 0
        } else {
            resultVO.result = ObjectUtils.objectToMap(c)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

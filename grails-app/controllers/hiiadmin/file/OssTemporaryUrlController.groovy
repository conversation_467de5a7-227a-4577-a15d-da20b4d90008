package hiiadmin.file

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.oss.OssConfig
import hiiadmin.oss.OssPathService

/**
 * 用fileName换取临时url
 */
class OssTemporaryUrlController implements BaseExceptionHandler {

    OssPathService ossPathService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String fileName = params.fileName
        String bucketName = params.bucketName
        if (!bucketName) {
            bucketName = OssConfig.bucketName
        }
        String url = ossPathService.getFilePath(fileName, 300, bucketName)
        resultVO.result.put("url", url)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

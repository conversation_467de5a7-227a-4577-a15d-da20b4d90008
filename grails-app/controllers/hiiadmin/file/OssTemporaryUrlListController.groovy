package hiiadmin.file

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.oss.OssUtils

class OssTemporaryUrlListController implements BaseExceptionHandler {

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List<String> fileNames = request.JSON["fileNames"] as List<String>
        Integer time = request.JSON["time"] as Integer
        List<String> urlList = OssUtils.transformFilePathList(fileNames, time)
        resultVO.result.put("list", urlList)
        header('Access-Control-Allow-Origin', '*')
        header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        header('Access-Control-Expose-Headers', 'Content-Length,Content-Range')
        header('Access-Control-Allow-Headers', 'authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range')
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

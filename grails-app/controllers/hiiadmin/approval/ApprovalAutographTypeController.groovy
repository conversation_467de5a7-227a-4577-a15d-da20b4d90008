package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import timetabling.oa.ApprovalAutographType

class ApprovalAutographTypeController implements BaseExceptionHandler {

    ApprovalV2Service approvalV2Service

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<ApprovalAutographType> approvalAutographTypeList = approvalV2Service.fetchApprovalAutographTypeByCampusId(campusId)
        [approvalAutographTypeList: approvalAutographTypeList]
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String json = params.s
        Integer size = approvalV2Service.saveApprovalAutographType(json)
        resultVO.result.put("id", size)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

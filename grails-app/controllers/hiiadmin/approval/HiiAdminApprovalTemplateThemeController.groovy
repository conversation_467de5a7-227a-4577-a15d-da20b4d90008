package hiiadmin.approval

import hiiadmin.BaseExceptionHandler
import io.swagger.annotations.Api
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import timetabling.ApprovalTemplateTheme

@Api(value = "审批模板主题", tags = "总务办公", consumes = "admin")
class HiiAdminApprovalTemplateThemeController implements BaseExceptionHandler {

    static Logger logger = LoggerFactory.getLogger(HiiAdminApprovalTemplateThemeController.class)

    ApprovalService approvalService

    def index() {
        Long staffId = request.getAttribute("staffId")
        Long campusId = request.getAttribute("campusId") as Long
        if (!campusId || campusId == -1) {
            campusId = params.long("campusId")
        }
        Byte status = params.byte('status')
        def approvalTemplateThemeList
        if (status) {
            approvalTemplateThemeList = approvalService.findAllApprovalTemplateThemeByCampusIdAndStatus(campusId, status)
        } else {
            approvalTemplateThemeList = approvalService.findAllApprovalTemplateThemeByCampusId(campusId)
        }

        [approvalTemplateThemeList: approvalTemplateThemeList]
    }

    def show() {
        Long campusId = request.getAttribute("campusId")
        Long schoolId = request.getAttribute("schoolId")
        Long staffId = request.getAttribute("staffId")
        Long approvalTemplateThemeId = params.long('approvalTemplateThemeId')
        ApprovalTemplateTheme approvalTemplateTheme = approvalService.findApprovalTemplateThemeById(approvalTemplateThemeId)
        def approvalTemplateList = approvalService.findAllApprovalSubListByApprovalRootId(approvalTemplateTheme.approvalTemplateId)

        [approvalTemplateList: approvalTemplateList]
    }

    def save() {
        Long staffId = request.getAttribute("staffId")
        Byte promoterType = params.byte('promoterType')
        Byte userType = params.byte('userType')
        Byte targetType = params.byte('targetType')
        Byte approvalType = params.byte('approvalType')
        Integer weight = params.int('weight')
        String name = params.name
        Long campusId = params.long("campusId")
        Long schoolId = params.long("schoolId")
        ApprovalTemplateTheme approvalTemplateTheme = approvalService.createApprovalTemplateTheme(schoolId, campusId, name, approvalType, promoterType, userType, targetType, weight)

        [approvalTemplateTheme: approvalTemplateTheme]
    }

    def update() {
        Long staffId = request.getAttribute("staffId")
        Byte status = params.byte('status')
        Long id = params.long('id')
        ApprovalTemplateTheme approvalTemplateTheme = approvalService.changeApprovalTemplateThemeStatus(id, status)

        [approvalTemplateTheme: approvalTemplateTheme]
    }
}

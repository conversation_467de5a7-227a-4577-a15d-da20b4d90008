package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.approval.affair.AffairService
import hiiadmin.approval.repository.RepositoryApprovalFormService
import hiiadmin.approval.repository.RepositoryApprovalTemplateNewService
import hiiadmin.approval.repository.RepositoryApprovalTemplateThemeNewService
import hiiadmin.module.approval.ApprovalTemplateNewVO
import hiiadmin.module.approval.ApprovalTemplateThemeNewVO
import hiiadmin.newMenu.BgbCampusMenuService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.oa.ApprovalForm
import timetabling.oa.ApprovalTemplateNew
import timetabling.oa.ApprovalTemplateThemeNew

@Api(value = "审批流程配置", tags = "总务办公", consumes = "admin")
class HiiAdminApprovalTemplateThemeV2Controller implements BaseExceptionHandler {

    ApprovalV2Service approvalV2Service

    RepositoryApprovalTemplateThemeNewService repositoryApprovalTemplateThemeNewService

    RepositoryApprovalTemplateNewService repositoryApprovalTemplateNewService

    RepositoryApprovalFormService repositoryApprovalFormService

    ApprovalNewVOService approvalNewVOService

    BgbCampusMenuService bgbCampusMenuService

    AffairService affairService

    ApprovalV2TemplateChangeService approvalV2TemplateChangeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte promoterType = params.byte("promoterType")
        Integer formType = params.int("formType")
        String searchValue = params.searchValue
        List<ApprovalTemplateThemeNew> approvalTemplateThemeNewList = repositoryApprovalTemplateThemeNewService.fetchApprovalTemplateThemeByCampusIdAndPromoterTypeAndFormTypeAndName(campusId, promoterType, formType, searchValue)
        List<ApprovalTemplateThemeNewVO> approvalTemplateThemeNewVOList = approvalTemplateThemeNewList.collect { approvalNewVOService.buildApprovalTemplateThemeNewVO(it) }
        approvalTemplateThemeNewVOList?.sort { it.weight }
        resultVO.result.put("list", approvalTemplateThemeNewVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long teacherId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        ApprovalTemplateThemeNew approvalTemplateThemeNew = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(id)
        ApprovalTemplateThemeNewVO vo = approvalNewVOService.buildApprovalTemplateThemeNewVO(approvalTemplateThemeNew, false)
        if (approvalTemplateThemeNew.jumpTemplateId) {
            vo.jumpTemplateName = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(approvalTemplateThemeNew.jumpTemplateId)?.name
        }
        List<ApprovalTemplateNew> approvalTemplateNewList = repositoryApprovalTemplateNewService.fetchApprovalTemplateViaApprovalTemplateId(approvalTemplateThemeNew.rootApprovalTemplateId)
        List<ApprovalTemplateNewVO> approvalTemplateNewVOList = approvalTemplateNewList.collect { approvalNewVOService.transformApprovalTemplateNewVO(it) }
        resultVO.result = ObjectUtils.objectToMap(vo)
        resultVO.result.put("templateList", approvalTemplateNewVOList)
        resultVO.result.put("encodeId", PhenixCoder.encodeId(id))
        resultVO.result.put("encodeTeacherId", PhenixCoder.encodeId(teacherId))
        resultVO.result.put("encodeCampusId", PhenixCoder.encodeId(campusId))
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Long id = params.long("id")
        String name = params.name
        String tips = params.tips
        String buttonText = params.buttonText
        Byte autograph = params.byte("autograph")
        Byte comments = params.byte("comments")
        Byte promoterAutoApproval = params.byte("promoterAutoApproval")
        Byte printTime = params.byte("printTime")
        Long leaveCategoryId = params.long("leaveCategoryId")
        Long jumpTemplateId = params.long("jumpTemplateId")
        String f = params.f
        String conditionJson = params.conditionJson
        Integer formType = params.int("formType")
        Boolean updateProcess = params.boolean("updateProcess")
        String icon = params.icon
        String color = params.color
        Long groupId = params.long("groupId")
        String s = params.s
        ApprovalTemplateThemeNew approvalTemplateNew = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(id)
        ApprovalTemplateThemeNew old = repositoryApprovalTemplateThemeNewService.fetchApprovalTemplateThemeByCampusIdAndName(approvalTemplateNew.campusId, name, approvalTemplateNew.promoterType)
        if (old && old.id != id) {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.msg = "审批名称重复"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        approvalTemplateNew.name = name
        approvalTemplateNew.tips = tips
        approvalTemplateNew.buttonText = buttonText
        approvalTemplateNew.autograph = autograph
        approvalTemplateNew.comments = comments
        approvalTemplateNew.promoterAutoApproval = promoterAutoApproval
        approvalTemplateNew.printTime = printTime
        approvalTemplateNew.icon = icon
        approvalTemplateNew.color = color
        approvalTemplateNew.groupId = groupId
        approvalTemplateNew.menuUrl = (formType == ConstantEnum.ApprovalFormType.VISITOR_APPOINTMENT.type) ? "/${approvalTemplateNew.promoterType}/approvalsV2/check?id=${PhenixCoder.encodeId(approvalTemplateNew.id)}&name=${name}&formType=${formType}" : "/${approvalTemplateNew.promoterType}/approvalsV2/check?id=${approvalTemplateNew.id}&name=${name}"
        if (approvalTemplateNew.formId) {
            ApprovalForm approvalForm = repositoryApprovalFormService.getApprovalForm(approvalTemplateNew.formId)
            if (approvalForm.formJson != f) {
                ApprovalForm approvalForm1 = new ApprovalForm(
                        campusId: approvalTemplateNew.campusId,
                        schoolId: approvalTemplateNew.schoolId,
                        formJson: f,
                        status: 1 as byte
                )
                approvalForm1 = repositoryApprovalFormService.saveApprovalForm(approvalForm1)
                approvalTemplateNew.formId = approvalForm1.id
            }
        } else {
            ApprovalForm approvalForm1 = new ApprovalForm(
                    campusId: approvalTemplateNew.campusId,
                    schoolId: approvalTemplateNew.schoolId,
                    formJson: f,
                    status: 1 as byte
            )
            approvalForm1 = repositoryApprovalFormService.saveApprovalForm(approvalForm1)
            approvalTemplateNew.formId = approvalForm1.id
        }
        approvalTemplateNew.formJson = f
        approvalTemplateNew.conditionJson = conditionJson
        approvalTemplateNew.jumpTemplateId = jumpTemplateId
        if (updateProcess) {
            approvalTemplateNew.processJson = null
        }
        approvalTemplateNew.leaveCategoryId = leaveCategoryId
        approvalTemplateNew.formType = formType
        approvalTemplateNew = repositoryApprovalTemplateThemeNewService.saveApprovalTemplateThemeNew(approvalTemplateNew)
        approvalV2TemplateChangeService.saveApprovalTemplateV2(schoolId, campusId, userId, id, s)
        String parameter = (formType == ConstantEnum.ApprovalFormType.VISITOR_APPOINTMENT.type) ? "id=${PhenixCoder.encodeId(approvalTemplateNew.id)}&name=${name}&formType=${formType}" : "id=${approvalTemplateNew.id}&name=${name}"
        bgbCampusMenuService.updateBgbCampusMenuByWorkSource(approvalTemplateNew.campusId, ConstantEnum.BgbCampusMenuSourceType.APPROVAL.type, approvalTemplateNew.id, parameter, icon, color)
        affairService.sendAffairEvent(approvalTemplateNew, "update")
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Byte status = params.byte("status")
        Long groupId = params.long("groupId")
        ApprovalTemplateThemeNew approvalTemplateNew = repositoryApprovalTemplateThemeNewService.getApprovalTemplateTheme(id)
        if (status != null) {
            if (status == 1 as byte) {
                affairService.sendAffairEvent(approvalTemplateNew, "add")
            } else if (status == 0 as byte) {
                affairService.sendAffairEvent(approvalTemplateNew, "delete")
            }
            approvalTemplateNew.status = status
        } else {
            approvalTemplateNew.groupId = groupId
        }
        approvalTemplateNew = repositoryApprovalTemplateThemeNewService.saveApprovalTemplateThemeNew(approvalTemplateNew)
        resultVO.result.put("id", approvalTemplateNew.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

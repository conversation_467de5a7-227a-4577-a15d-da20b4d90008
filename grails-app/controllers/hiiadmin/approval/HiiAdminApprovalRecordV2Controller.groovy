package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.approval.repository.RepositoryApprovalStepNewService
import hiiadmin.module.approval.ApprovalNewVO
import hiiadmin.module.approval.ApprovalNewVOV2
import hiiadmin.module.approval.ApprovalStepVO
import hiiadmin.school.TeacherService
import hiiadmin.utils.ObjectUtils
import timetabling.oa.*
import timetabling.user.Teacher

import static grails.async.Promises.task

@Slf4j
class HiiAdminApprovalRecordV2Controller implements BaseExceptionHandler {

    ApprovalV2Service approvalV2Service

    ApprovalV2ExcelService approvalV2ExcelService
    ApprovalNewVOService approvalNewVOService

    RepositoryApprovalStepNewService repositoryApprovalStepNewService

    TeacherService teacherService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte promoterType = params.byte("promoterType")
        Long themeId = params.long("themeId")
        Byte status = params.byte("status")
        Long createStartTime = params.long("createStartTime")
        Long createEndTime = params.long("createEndTime")
        Long updateStartTime = params.long("updateStartTime")
        Long updateEndTime = params.long("updateEndTime")
        String promoterName = params.promoterName
        String approvalNo = params.approvalNo
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        def map = approvalV2Service.fetchApprovalListLimit(campusId, promoterType, themeId, status, createStartTime, createEndTime, updateStartTime, updateEndTime, promoterName, approvalNo, p, s)
        List<ApprovalNew> approvalNewList = map.list
        Integer total = map.total

        List<ApprovalStepNew> approvalStepList = repositoryApprovalStepNewService.fetchApprovalStepByApprovalIdList(approvalNewList*.id)
        Multimap<Long, ApprovalStepNew> approvalIdStepMap = HashMultimap.create()
        approvalStepList.each {
            approvalIdStepMap.put(it.approvalId, it)
        }
        List<ApprovalNewVOV2> approvalNewVOV2List = []
        approvalNewList.each { ApprovalNew approvalNew ->
            ApprovalNewVOV2 approvalNewVOV2 = approvalNewVOService.buildApprovalNewVOV2(approvalNew, approvalIdStepMap)
            approvalNewVOV2List.add(approvalNewVOV2)
        }

        resultVO.result.put("total", total)
        resultVO.result.put("list", approvalNewVOV2List)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String ids = params.ids
        Long userId = request.getAttribute("userId") as Long
        Byte type = request.getAttribute("type") as Byte
        Long campusId = request.getAttribute("campusId") as Long
        List<ApprovalAutographType> approvalAutographTypeList = approvalV2Service.fetchApprovalAutographTypeByCampusId(campusId)
        if (ids) {
            List<Long> idList = ids.split(",").collect { it.toLong() }
            List<ApprovalNew> approvalNewList = approvalV2Service.getAllApproval(idList)
            List<ApprovalNewVO> approvalNewVOList = []
            approvalNewList.each { ApprovalNew approvalNew ->
                ApprovalStepNew approvalStepNew
                if (approvalNew.status == ConstantEnum.ApprovalStatus.APPROVALED.status) {
                    approvalStepNew = repositoryApprovalStepNewService.getApprovalStep(approvalNew.finalStepId)
                } else {
                    approvalStepNew = repositoryApprovalStepNewService.fetchApprovalStepByApprovalIdAndStatus(id, approvalNew.status)
                }
                ApprovalRecordNew approvalRecordNew = approvalV2Service.fetchApprovalRecordByStepIdAndUserId(approvalStepNew?.id, userId)
                Integer byMe = 0
                if (approvalNew.promoterType == type && approvalNew.promoterId == userId) {
                    byMe = 1
                }
                ApprovalNewVO approvalNewVO = approvalNewVOService.buildApprovalNewVO(approvalNew, approvalStepNew, approvalRecordNew, byMe)
                List<ApprovalStepNew> approvalStepList = repositoryApprovalStepNewService.fetchApprovalStepByApprovalId(approvalNew.id)
                List<ApprovalStepVO> approvalStepVOList = approvalStepList.collect { approvalNewVOService.buildApprovalStepVO(it, approvalNew, approvalAutographTypeList) }
                approvalNewVO.stepList = approvalStepVOList
                approvalNewVOList.add(approvalNewVO)
            }
            resultVO.result.put("list", approvalNewVOList)
        } else if (id) {
            ApprovalNew approvalNew = approvalV2Service.getApproval(id)
            ApprovalStepNew approvalStepNew
            if (approvalNew.status == ConstantEnum.ApprovalStatus.APPROVALED.status) {
                approvalStepNew = repositoryApprovalStepNewService.getApprovalStep(approvalNew.finalStepId)
            } else {
                approvalStepNew = repositoryApprovalStepNewService.fetchApprovalStepByApprovalIdAndStatus(id, approvalNew.status)
            }

            ApprovalRecordNew approvalRecordNew = approvalV2Service.fetchApprovalRecordByStepIdAndUserId(approvalStepNew?.id, userId)
            Integer byMe = 0
            if (approvalNew.promoterType == type && approvalNew.promoterId == userId) {
                byMe = 1
            }
            ApprovalNewVO approvalNewVO = approvalNewVOService.buildApprovalNewVO(approvalNew, approvalStepNew, approvalRecordNew, byMe)
            List<ApprovalStepNew> approvalStepList = repositoryApprovalStepNewService.fetchApprovalStepByApprovalId(id)
            List<ApprovalStepVO> approvalStepVOList = approvalStepList.collect { approvalNewVOService.buildApprovalStepVO(it, approvalNew, approvalAutographTypeList) }
            approvalNewVO.stepList = approvalStepVOList
            resultVO.result = ObjectUtils.objectToMap(approvalNewVO)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte promoterType = params.byte("promoterType")
        Long themeId = params.long("themeId")
        Byte status = params.byte("status")
        Long createStartTime = params.long("createStartTime")
        Long createEndTime = params.long("createEndTime")
        Long updateStartTime = params.long("updateStartTime")
        Long updateEndTime = params.long("updateEndTime")
        String promoterName = params.promoterName
        String approvalNo = params.approvalNo
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Teacher teacher = teacherService.findTeacherById(userId)
        ApprovalTemplateThemeNew approvalTemplateThemeNew = approvalV2Service.getApprovalTemplateTheme(themeId)
        List<ApprovalNew> approvalNewList = approvalV2Service.fetchApprovalList(campusId, promoterType, themeId, status, createStartTime, createEndTime, updateStartTime, updateEndTime, promoterName, approvalNo)
        if (approvalNewList && approvalNewList.size() > 0) {
            ApprovalExportRecord approvalExportRecord = new ApprovalExportRecord(schoolId: schoolId,
                    campusId: campusId,
                    exporterId: userId,
                    exporterName: teacher?.name,
                    approvalTemplateThemeId: themeId,
                    approvalTemplateThemeName: approvalTemplateThemeNew?.name,
                    recordNum: approvalNewList.size(),
                    type: 1 as byte)
            approvalExportRecord = approvalV2Service.saveApprovalExportRecord(approvalExportRecord)


            def t1 = task {
                List<ApprovalForm> approvalFormList = approvalV2Service.fetchApprovalFormByCampusId(campusId)
                List<ApprovalStepNew> approvalStepList = repositoryApprovalStepNewService.fetchApprovalStepByApprovalIdList(approvalNewList*.id)
                approvalV2ExcelService.transformApprovalExcel(approvalNewList, approvalExportRecord, approvalFormList, approvalStepList)
            }
            t1.onComplete {
                log.info("审批记录导出成功！")
            }
            resultVO.result.put("id", approvalExportRecord.id)
        } else {
            resultVO.code = 200001
            resultVO.msg = "无审批记录可导出"
            resultVO.status = 0
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.Constants
import hiiadmin.UserService
import hiiadmin.module.JwtUser
import hiiadmin.utils.CtJWT
import timetabling.user.User

class ApprovalTokenController implements BaseExceptionHandler {

    UserService userService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        User user = userService.fetchUserByUIdAndType(userId, ConstantEnum.UserTypeEnum.TEACHER.type)
        if (!user) {
            resultVO.code = 200001
            resultVO.msg = "用户不存在"
            resultVO.status = 0
        } else {
            JwtUser jwtUser = new JwtUser()
            jwtUser.setId(user.id)
            jwtUser.setUserId(userId)
            jwtUser.setCampusId(campusId)
            jwtUser.setSchoolId(schoolId)
            jwtUser.setType(ConstantEnum.UserTypeEnum.TEACHER.type)
            String token = CtJWT.createJWT(user.id?.toString(), JSONObject.toJSONString(jwtUser))
            resultVO.result.put(Constants.JWT_TOKEN, token)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.approval.leave

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ViewDOService
import hiiadmin.approval.LeaveCategoryService
import hiiadmin.approval.LeaveRecordService
import hiiadmin.module.approval.LeaveRecordVO
import hiiadmin.utils.PhenixCoder
import timetabling.oa.LeaveRecord

class LeaveRecordListController {

    LeaveRecordService leaveRecordService

    LeaveCategoryService leaveCategoryService

    ViewDOService viewDOService

    def studentRecord() {
        Long campusId = request.getAttribute("campusId") as Long
        String studentIdStr = params.studentId
        Long studentId = PhenixCoder.decodeId(studentIdStr)
        def leaveCategoryMap = leaveCategoryService.fetchLeaveCategoryMapByCampusId(campusId)
        def leaveDirectionMap = leaveCategoryService.fetchLeaveDirectionMapByCampusId(campusId)
        List<LeaveRecord> list = leaveRecordService.fetchAllLeaveRecord4student(campusId, studentId)
        List<LeaveRecordVO> leaveRecordVOList = list?.collect {
            viewDOService.buildLeaveRecordVO(it, leaveCategoryMap?.get(it.leaveCategoryId), leaveDirectionMap?.get(it.leaveDirectionId))
        }
        render text: JSON.toJSONString(ResultVO.success([list: leaveRecordVOList])), contentType: 'application/json;', encoding: "UTF-8"
    }
}

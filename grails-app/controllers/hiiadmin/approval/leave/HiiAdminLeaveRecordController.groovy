package hiiadmin.approval.leave

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.approval.LeaveCategoryService
import hiiadmin.approval.LeaveRecordService
import hiiadmin.module.approval.LeaveRecordVO
import io.swagger.annotations.Api
import timetabling.oa.LeaveRecord

@Api(value = "学生请假记录", tags = "行为德育", consumes = "admin")
class HiiAdminLeaveRecordController implements BaseExceptionHandler {

    LeaveRecordService leaveRecordService

    LeaveCategoryService leaveCategoryService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String searchValue = params.searchValue
        Long leaveCategoryId = params.long("leaveCategoryId")
        Long leaveDirectionId = params.long("leaveDirectionId")
        Byte status = params.byte("status")
        String promoterName = params.promoterName
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Long applyStartTime = params.long("applyStartTime")
        Long applyEndTime = params.long("applyEndTime")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Boolean gender = params.boolean('gender')
        Long campusId = request.getAttribute("campusId") as Long
        def map = leaveRecordService.fetchLeaveRecordLimit(campusId, searchValue, leaveCategoryId, leaveDirectionId, leaveStartTime, leaveEndTime, status, promoterName, applyStartTime, applyEndTime, gender, p, s)
        def leaveCategoryMap = leaveCategoryService.fetchLeaveCategoryMapByCampusId(campusId)
        def leaveDirectionMap = leaveCategoryService.fetchLeaveDirectionMapByCampusId(campusId)
        List<LeaveRecord> leaveRecordList = map.list as List<LeaveRecord>
        List<LeaveRecordVO> leaveRecordVOList = leaveRecordList?.collect {
            viewDOService.buildLeaveRecordVO(it, leaveCategoryMap?.get(it.leaveCategoryId), leaveDirectionMap?.get(it.leaveDirectionId))
        }
        Integer total = map.total
        resultVO.result.put("total", total)
        resultVO.result.put("list", leaveRecordVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

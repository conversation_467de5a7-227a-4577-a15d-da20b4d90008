package hiiadmin.approval.leave

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.ApiHii
import hiiadmin.approval.LeaveRecordService
import hiiadmin.approval.structure.LeaveRecordStructureService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.ToStringUnits
import timetabling.oa.LeaveRecord

import javax.annotation.Resource

class HiiAdminLeaveRevokeController {

    LeaveRecordService leaveRecordService

    LeaveRecordStructureService leaveRecordStructureService

    TeacherService teacherService

    StudentService studentService

    ParentService parentService

    @Resource
    ApiHii apiHii

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String ids = params.ids

        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        Long campusId = request.getAttribute("campusId") as Long

        List<Long> idList = ToStringUnits.idsString2LongList(ids)

        idList?.each { leaveRecordId ->
            LeaveRecord leaveRecord = leaveRecordService.fetchLeaveRecordById(leaveRecordId)
            if (userId != leaveRecord.promoterId) {
                if (userType == ConstantEnum.UserTypeEnum.TEACHER.type) {
                    Long teacherId = teacherService.fetchHeadmasterTeacherByStudentId(leaveRecord.studentId, false)?.id
                    if (userId != teacherId) {
                        resultVO.status = 0
                        resultVO.code = 200001
                        resultVO.msg = "${leaveRecord.studentName}的请假撤销失败，仅提交人或班主任可撤销"
                    } else {
                        leaveRecordStructureService.cancelLeaveRecord(leaveRecordId)
                        resultVO.result.put("id", leaveRecord.id)
                    }
                } else {
                    resultVO.status = 0
                    resultVO.code = 200001
                    resultVO.msg = "${leaveRecord.studentName}的请假撤销失败，仅提交人或班主任可撤销"
                }
            } else {
                leaveRecordStructureService.cancelLeaveRecord(leaveRecordId)
                resultVO.result.put("id", leaveRecord.id)
            }
        }

        if (campusId in ConstantEnum.CampusCitizenInfo.getAllCampusIds()) {
            String cancelName = ""
            if (userType == ConstantEnum.UserTypeEnum.TEACHER.type) {
                cancelName = teacherService.fetchTeacherById(userId).name
            } else if (userType == ConstantEnum.UserTypeEnum.STUDENT.type) {
                cancelName = studentService.fetchStudentById(userId).name
            } else if (userType == ConstantEnum.UserTypeEnum.PARENT.type) {
                cancelName = parentService.fetchParentById(userId).name
            }
            apiHii.dealLeaveRecordList(ids, cancelName)
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "utf-8"
    }
}

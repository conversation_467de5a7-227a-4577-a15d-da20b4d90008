package hiiadmin.approval.leave

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.approval.ApprovalV2Service
import hiiadmin.approval.TeacherLeaveRecordService
import hiiadmin.module.approval.TeacherLeaveRecordVO
import hiiadmin.school.TeacherService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import io.swagger.annotations.Api
import timetabling.oa.ApprovalExportRecord
import timetabling.oa.TeacherLeaveRecord
import timetabling.user.Teacher

@Slf4j
@Api(value = "教师请假记录", tags = "总务办公", consumes = "admin")
class HiiAdminTeacherLeaveRecordController implements BaseExceptionHandler {

    TeacherLeaveRecordService teacherLeaveRecordService

    TeacherService teacherService

    ApprovalV2Service approvalV2Service

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String encodeDepartmentId = params.departmentId
        Long departmentId = PhenixCoder.decodeId(encodeDepartmentId)
        Long leaveCategoryId = params.long("leaveCategoryId")
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Long applyStartTime = params.long("applyStartTime")
        Long applyEndTime = params.long("applyEndTime")
        Byte status = params.byte("status")
        
        String encodeTeacher = params.teacherId
        Long teacherId = PhenixCoder.decodeId(encodeTeacher)
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        Long campusId = request.getAttribute("campusId") as Long
        def map = teacherLeaveRecordService.fetchTeacherLeaveRecordListLimit(campusId, teacherId, departmentId, leaveCategoryId, leaveStartTime, leaveEndTime, applyStartTime, applyEndTime, status, p, s)
        List<TeacherLeaveRecordVO> teacherLeaveRecordVOList = viewDOService.transformTeacherLeaveRecordVOList(map.list, campusId)
        resultVO.result.put("list", teacherLeaveRecordVOList)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String encodeDepartmentId = params.departmentId
        Long departmentId = PhenixCoder.decodeId(encodeDepartmentId)
        Long leaveCategoryId = params.long("leaveCategoryId")
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Long applyStartTime = params.long("applyStartTime")
        Long applyEndTime = params.long("applyEndTime")
        Byte status = params.byte("status")
        Long userId = request.getAttribute("userId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        String encodeTeacher = params.teacherId
        Long teacherId = PhenixCoder.decodeId(encodeTeacher)
        Teacher teacher = teacherService.findTeacherById(userId)
        if(!applyStartTime&&!applyEndTime){
            applyEndTime = new Date().time
            applyStartTime = TimeUtils.getDateAfterMonth(new Date(),-6)
        }
        List<TeacherLeaveRecord> teacherLeaveRecordList = teacherLeaveRecordService.fetchTeacherLeaveRecordList(campusId, teacherId, departmentId, leaveCategoryId, leaveStartTime, leaveEndTime, applyStartTime, applyEndTime, status)
        if (teacherLeaveRecordList && teacherLeaveRecordList.size() > 0) {
            ApprovalExportRecord approvalExportRecord = new ApprovalExportRecord(
                    schoolId: schoolId,
                    campusId: campusId,
                    exporterId: userId,
                    exporterName: teacher?.name,
                    recordNum: teacherLeaveRecordList.size(),
                    type: ConstantEnum.ApprovalExportRecordType.TEACHER_LEAVE.type
            )
            approvalExportRecord = approvalV2Service.saveApprovalExportRecord(approvalExportRecord)
            Promise task = Promises.task {
                teacherLeaveRecordService.transformTeacherLeaveRecordExcel(teacherLeaveRecordList, campusId, approvalExportRecord)
            }
            task.onComplete {
                log.info("教师请假记录导出完成")
            }
            task.onError { Exception e ->
                log.error("教师请假记录导出失败", e)
            }
            resultVO.result.put("id", approvalExportRecord.id)
        } else {
            resultVO.code = 200001
            resultVO.msg = "无请假记录可导出"
            resultVO.status = 0
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.approval.leave

import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.LeaveCategoryService

class HiiAdminLeaveCategoryController implements BaseExceptionHandler {

    LeaveCategoryService leaveCategoryService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        def leaveCategoryList = leaveCategoryService.fetchLeaveCategoryByCampusIdAndStatus(campusId)
        def leaveDirectionList = leaveCategoryService.fetchLeaveDirectionByCampusIdAndStatus(campusId)
        [leaveCategoryList: leaveCategoryList, leaveDirectionList: leaveDirectionList]
    }
}

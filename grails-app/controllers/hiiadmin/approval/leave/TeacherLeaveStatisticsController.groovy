package hiiadmin.approval.leave

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.approval.ApprovalV2Service
import hiiadmin.approval.TeacherLeaveRecordService
import hiiadmin.module.approval.TeacherLeaveRecordVO
import hiiadmin.school.TeacherService
import hiiadmin.utils.PhenixCoder
import timetabling.oa.ApprovalExportRecord
import timetabling.user.Teacher

@Slf4j
class TeacherLeaveStatisticsController implements BaseExceptionHandler {

    TeacherLeaveRecordService teacherLeaveRecordService

    TeacherService teacherService

    ApprovalV2Service approvalV2Service

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String departmentIdStr = params.departmentId
        Long departmentId = PhenixCoder.decodeId(departmentIdStr)
        String encodeTeacher = params.teacherId
        Long teacherId = PhenixCoder.decodeId(encodeTeacher)
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Boolean gender = params.boolean("gender")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        def map = teacherLeaveRecordService.transformTeacherLeaveRecordStatisticsLimit(schoolId, campusId, teacherId, departmentId, leaveStartTime, leaveEndTime, gender, p, s)
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long departmentId = params.long("departmentId")
        Long teacherId = params.long("teacherId")
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Boolean gender = params.boolean("gender")
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Teacher teacher = teacherService.findTeacherById(userId)
        def map = teacherLeaveRecordService.transformTeacherLeaveRecordStatistics(schoolId, campusId, teacherId, departmentId, leaveStartTime, leaveEndTime, gender)
        List<TeacherLeaveRecordVO> teacherLeaveRecordVOList = map.statisticsList
        ApprovalExportRecord approvalExportRecord = new ApprovalExportRecord(
                schoolId: schoolId,
                campusId: campusId,
                exporterId: userId,
                exporterName: teacher?.name,
                recordNum: teacherLeaveRecordVOList.size(),
                type: ConstantEnum.ApprovalExportRecordType.TEACHER_LEAVE_STATISTICS.type
        )
        approvalExportRecord = approvalV2Service.saveApprovalExportRecord(approvalExportRecord)
        Promise task = Promises.task {
            teacherLeaveRecordService.transformTeacherLeaveRecordStatisticsExcel(teacherLeaveRecordVOList, map.recordList, campusId, leaveStartTime, leaveEndTime, approvalExportRecord)
        }
        task.onComplete {
            log.info("教师请假统计导出完成")
        }
        task.onError { Exception e ->
            log.error("教师请假统计导出失败", e)
        }
        resultVO.result.put("id", approvalExportRecord.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

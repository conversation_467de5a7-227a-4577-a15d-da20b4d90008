package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.approval.repository.RepositoryApprovalTemplateThemeGroupService
import hiiadmin.module.approval.ApprovalTemplateThemeGroupVO
import timetabling.oa.ApprovalTemplateThemeGroup

class ApprovalTemplateThemeGroupController implements BaseExceptionHandler {

    RepositoryApprovalTemplateThemeGroupService repositoryApprovalTemplateThemeGroupService

    ApprovalNewVOService approvalNewVOService

    ApprovalV2TemplateChangeService approvalV2TemplateChangeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        List<ApprovalTemplateThemeGroup> approvalTemplateThemeGroupList = repositoryApprovalTemplateThemeGroupService.fetchAllApprovalTemplateThemeGroupByCampusIdAndUserType(campusId, userType)
        List<ApprovalTemplateThemeGroupVO> approvalTemplateThemeGroupVOList = approvalTemplateThemeGroupList.collect { approvalNewVOService.buildApprovalTemplateThemeGroupVO(it) }
        resultVO.result.put("list", approvalTemplateThemeGroupVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        String name = params.name
        if (repositoryApprovalTemplateThemeGroupService.canUseGroupName(campusId, name)) {
            resultVO = resultVO.failure(BizErrorCode.MENU_CATEGORY_NAME_ERROR.code, BizErrorCode.MENU_CATEGORY_NAME_ERROR.msg)
        } else {
            ApprovalTemplateThemeGroup approvalTemplateThemeGroup = approvalV2TemplateChangeService.createApprovalTemplateThemeGroup(schoolId, campusId, userType, name)
            resultVO.result.put("id", approvalTemplateThemeGroup.id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        ApprovalTemplateThemeGroup oldGroup = repositoryApprovalTemplateThemeGroupService.canUseGroupName(campusId, name)
        if (oldGroup && oldGroup.id != id) {
            resultVO = resultVO.failure(BizErrorCode.MENU_CATEGORY_NAME_ERROR.code, BizErrorCode.MENU_CATEGORY_NAME_ERROR.msg)
        } else {
            ApprovalTemplateThemeGroup approvalTemplateThemeGroup = repositoryApprovalTemplateThemeGroupService.fetchApprovalTemplateThemeGroupById(id)
            approvalTemplateThemeGroup.name = name
            repositoryApprovalTemplateThemeGroupService.saveApprovalTemplateThemeGroup(approvalTemplateThemeGroup)
            resultVO.result.put("id", approvalTemplateThemeGroup.id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        approvalV2TemplateChangeService.deleteApprovalTemplateThemeGroup(id)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

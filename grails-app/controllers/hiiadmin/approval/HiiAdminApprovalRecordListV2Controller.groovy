package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.approval.ApprovalNewVO
import timetabling.oa.ApprovalNew

class HiiAdminApprovalRecordListV2Controller {

    ApprovalV2Service approvalV2Service



    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String ids = params.ids
        Long userId = request.getAttribute("userId") as Long
        Byte type = request.getAttribute("type") as Byte
        Long campusId = request.getAttribute("campusId") as Long
        List<Long> idList = ids.split(",").collect { it.toLong() }
        List<ApprovalNew> approvalNewList = approvalV2Service.getAllApproval(idList)
        List<ApprovalNewVO> approvalNewVOList = []
        approvalNewList.each {
            ApprovalNew approvalNew ->

                ApprovalNewVO approvalNewVO =new ApprovalNewVO()
                approvalNewVO.status = approvalNew.status
                approvalNewVO.id = approvalNew.id
                approvalNewVOList.add(approvalNewVO)
        }
        resultVO.result.put("list", approvalNewVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

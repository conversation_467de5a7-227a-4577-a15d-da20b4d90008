package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.apiCloud.ApiHii
import io.swagger.annotations.Api
import org.springframework.beans.factory.annotation.Autowired

import static hiiadmin.ConstantEnum.UserTypeEnum

@Api(value = "我的审批", tags = "总务办公", consumes = "admin")
class HiiAdminApprovalRecord4MeController {

    @Autowired
    ApiHii apiHii

    def index() {
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
//        1、待我审批 3、我发起的
        Byte type = params.byte("type", 1)
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        ResultVO resultVO = apiHii.allApproval4User(campusId, userId, UserTypeEnum.TEACHER.type, type, p, s)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

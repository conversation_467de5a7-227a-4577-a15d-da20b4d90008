package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class ApprovalTemplateThemeSortController implements BaseExceptionHandler {

    ApprovalV2TemplateChangeService approvalV2TemplateChangeService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long groupId = params.long("groupId")
        String sortJson = params.sortJson
        approvalV2TemplateChangeService.sortApprovalTemplateTheme(groupId, sortJson)
        resultVO.result.put("id", groupId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.school.TeacherService
import timetabling.oa.ApprovalExportRecord
import timetabling.oa.ViolationRecord
import timetabling.user.Teacher

@Slf4j
class ViolationExportController implements BaseExceptionHandler {

    TeacherService teacherService

    ViolationRecordService violationRecordService

    ApprovalV2Service approvalV2Service

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        String searchValue = params.searchValue
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Long userId = request.getAttribute("userId") as Long
        Teacher teacher = teacherService.findTeacherById(userId)
        List<ViolationRecord> violationRecordList = violationRecordService.fetchViolationRecord(campusId, searchValue, startTime, endTime)
        Long maxNum = violationRecordService.fetchMaxDemoteNum(campusId, searchValue, startTime, endTime)
        if (violationRecordList && violationRecordList?.size() > 0) {
            ApprovalExportRecord approvalExportRecord = new ApprovalExportRecord(
                    schoolId: schoolId,
                    campusId: campusId,
                    exporterId: userId,
                    exporterName: teacher?.name,
                    recordNum: violationRecordList.size(),
                    type: ConstantEnum.ApprovalExportRecordType.STUDENT_VIOLATION.type
            )
            approvalExportRecord = approvalV2Service.saveApprovalExportRecord(approvalExportRecord)
            Promise task = Promises.task {
                violationRecordService.transformViolationRecordExcel(violationRecordList, approvalExportRecord, maxNum, startTime, endTime)
            }
            task.onComplete {
                log.info("学生违纪记录导出完成")
            }
            task.onError { Exception e ->
                log.error("学生违纪记录导出失败", e)
            }
            resultVO.result.put("id", approvalExportRecord.id)
        } else {
            resultVO.code = 200001
            resultVO.msg = "无违纪记录可导出"
            resultVO.status = 0
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

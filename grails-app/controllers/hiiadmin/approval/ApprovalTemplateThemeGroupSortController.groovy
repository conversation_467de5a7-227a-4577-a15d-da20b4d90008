package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class ApprovalTemplateThemeGroupSortController implements BaseExceptionHandler {

    ApprovalV2TemplateChangeService approvalV2TemplateChangeService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String sortJson = params.sortJson
        Long campusId = request.getAttribute("campusId") as Long
        approvalV2TemplateChangeService.sortApprovalTemplateThemeGroup(campusId, sortJson)
        resultVO.result.put("id", campusId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

}

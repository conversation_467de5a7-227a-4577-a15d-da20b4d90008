package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.approval.ViolationRecordVO
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api

@Api(value = "学生违纪管理", tags = "行为德育", consumes = "admin")
class HiiAdminViolationRecordController implements BaseExceptionHandler {

    ViolationRecordService violationRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long studentId = params.long("studentId")
        def map = violationRecordService.pageViolationRecordV2(campusId, searchValue, startTime, endTime,studentId, p, s)
        List<ViolationRecordVO> list = violationRecordService.transformViolationRecordVOList(map.list)
        resultVO.result.put('list', list)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        ViolationRecordVO vo = violationRecordService.transformViolationRecordVO(id)
        resultVO.result = ObjectUtils.objectToMap(vo)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long demotionResultId = params.long("demotionResultId")
        Long demoteTime = params.long("demoteTime")
        Long teacherId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        violationRecordService.updateViolationRecord(id, demotionResultId, demoteTime, teacherId, campusId)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        violationRecordService.deleteViolationRecord(id)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

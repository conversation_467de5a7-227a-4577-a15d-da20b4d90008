package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.VisitorVO
import hiiadmin.visitor.VisitorService
import org.joda.time.DateTime
import timetabling.visitor.Visitor

class HiiAdminVisitorRecordController implements BaseExceptionHandler {

    VisitorService visitorService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long daytimeStamp = params.long("daytimeStamp")
        String searchValue = params.searchValue
        Integer timeType = params.int("timeType")

        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")

        Boolean visited = params.boolean("visited")

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        Long campusId = request.getAttribute("campusId") as Long

        def map = visitorService.fetchVisitorByCampusIdAndSearchValuePageV2(campusId, searchValue, daytimeStamp, startTime, endTime, timeType, visited, p, s)
        List<Visitor> visitorList = map.list as List<Visitor>
        Integer total = map.total ?: 0

        List<VisitorVO> voList = visitorService.transformVisitorToVisitorVO(visitorList, true)

        resultVO.result.put("list", voList)
        resultVO.result.put("total", total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def todayVisitor() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long dayTimeStamp = params.long("daytimeStamp", new DateTime().withTimeAtStartOfDay().millis)
        List<Visitor> visitorList = visitorService.fetchAllVisitorByCampusIdAndDay(campusId, dayTimeStamp)
        Integer totalCount = visitorList?.size() ?: 0
        Integer visitedCount = visitorList?.findAll { it?.comeTime != null }?.size() ?: 0

        resultVO.result.put("totalCount", totalCount)
        resultVO.result.put("visitedCount", visitedCount)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

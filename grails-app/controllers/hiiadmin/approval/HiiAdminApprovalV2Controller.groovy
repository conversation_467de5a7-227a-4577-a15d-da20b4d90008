package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.approval.repository.RepositoryApprovalRecordService
import hiiadmin.approval.repository.RepositoryApprovalStepNewService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.approval.ApprovalNewVO
import hiiadmin.module.approval.ApprovalStepVO
import hiiadmin.module.auditLog.AuditLogDTO
import hiiadmin.mq.producer.AuditLogProducerService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PhenixCoder
import timetabling.oa.ApprovalNew
import timetabling.oa.ApprovalRecordNew
import timetabling.oa.ApprovalStepNew

@Slf4j
class HiiAdminApprovalV2Controller implements BaseExceptionHandler {

    ApprovalV2Service approvalV2Service

    RepositoryApprovalRecordService repositoryApprovalRecordService

    RepositoryApprovalStepNewService repositoryApprovalStepNewService

    ApprovalNewVOService approvalNewVOService

    AuditLogProducerService auditLogProducerService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int("p", 1)
        int s = params.int("s", 10)
        String searchValue = params.searchValue
        Byte approvalStatus = params.byte("approvalStatus")
        Integer type = params.int("type")
        String templateThemeIds = params.templateThemeIds
        Byte userType = request.getAttribute("type") as Byte
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
        def (List<ApprovalNew> list, Integer total, List<ApprovalStepNew> approvalStepNewList) = repositoryApprovalRecordService.fetchApprovalViaPromoterIdAndPromoterTypeLimitNoOpenSearch(campusId, userId, userType, searchValue, approvalStatus, templateThemeIds, type, startDate, endDate, p, s)
        [list: list ?: [], total: total ?: 0, approvalStepNewList: approvalStepNewList ?: []]
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        String encode = params.encode
        Long id
        if (encode != null) {
            id = PhenixCoder.decodeId(idStr)
        } else {
            id = idStr.toLong()
        }
        Long userId = request.getAttribute("userId") as Long
        Byte type = request.getAttribute("type") as Byte
        ApprovalNew approvalNew = approvalV2Service.getApproval(id)
        ApprovalStepNew approvalStepNew
        if (approvalNew?.status == ConstantEnum.ApprovalStatus.APPROVALED.status) {
            approvalStepNew = repositoryApprovalStepNewService.getApprovalStep(approvalNew.finalStepId)
        } else {
            approvalStepNew = repositoryApprovalStepNewService.fetchApprovalStepByApprovalIdAndStatus(id, approvalNew.status)
        }

        ApprovalRecordNew approvalRecordNew = approvalV2Service.fetchApprovalRecordByStepIdAndUserId(approvalStepNew?.id, userId)
        Integer byMe = 0
        if (approvalNew.promoterType == type && approvalNew.promoterId == userId) {
            byMe = 1
        }
        ApprovalNewVO approvalNewVO = approvalNewVOService.buildApprovalNewVO(approvalNew, approvalStepNew, approvalRecordNew, byMe)
        List<ApprovalStepNew> approvalStepList = repositoryApprovalStepNewService.fetchApprovalStepByApprovalId(id)
        List<ApprovalStepVO> approvalStepVOList = approvalStepList.collect {
            approvalNewVOService.buildApprovalStepVO(it, approvalNew)
        }
        approvalNewVO.stepList = approvalStepVOList
        resultVO.result = ObjectUtils.objectToMap(approvalNewVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long approvalTemplateThemeId = params.long("approvalTemplateThemeId")
        String valueJson = params.valueJson
        Long userId = request.getAttribute("userId") as Long
        String baseFormJson = params.baseFormJson
        String templateJson = params.templateJson
        String idJson = params.idJson
        String invoiceJson = params.invoiceJson
        String ding = params.ding
        String formValueJson = params.formValueJson

        try {
            ApprovalNew approvalNew = approvalV2Service.createApprovalV2(approvalTemplateThemeId, valueJson, userId, baseFormJson, templateJson, idJson, invoiceJson, ding, formValueJson)
            resultVO.result.put("id", approvalNew.id)
            AuditLogDTO auditLogDTO = new AuditLogDTO(
                    schoolId: approvalNew.schoolId,
                    campusId: approvalNew.campusId,
                    dateTime: System.currentTimeMillis(),
                    userId: approvalNew.promoterId,
                    userType: approvalNew.promoterType,
                    eventObject: ConstantEnum.AuditLogEventObject.APPROVAL.type,
                    eventType: ConstantEnum.AuditLogEventType.APPROVAL_SAVE.type,
                    eventDetails: approvalNew.approvalNo + approvalNew.approvalTemplateThemeName
            )
            auditLogProducerService.sendMessage("approval", "approval", JSON.toJSONString(auditLogDTO).bytes)

        } catch (HiiAdminException e) {
            resultVO.status = 0
            resultVO.code = 200001
            resultVO.message = e.message
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long stepId = params.long("id")
        Byte status = params.byte("status")
        String suggestion = params.suggestion
        String pics = params.pics
        String autograph = params.autograph
        String deliverIds = params.deliverIds
        String ding = params.ding
        String formValueJson = params.formValueJson
        String updateRecordJson = params.updateRecordJson
        String invoiceJson = params.invoiceJson
        String idJson = params.idJson
        String baseFormJson = params.baseFormJson
        String valueJson = params.valueJson


        try {
            ApprovalNew approvalNew = approvalV2Service.updateApproval(stepId, userId, status, suggestion, pics, autograph, deliverIds, ding, formValueJson, updateRecordJson, invoiceJson, idJson, baseFormJson, valueJson)
            resultVO.result.put("id", approvalNew.id)
        } catch (HiiAdminException e) {
            resultVO.status = 0
            resultVO.code = 200001
            resultVO.message = e.message
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

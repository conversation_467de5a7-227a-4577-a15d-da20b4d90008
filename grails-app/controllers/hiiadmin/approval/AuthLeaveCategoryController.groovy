package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.approval.repository.RepositoryApprovalTemplateNewService
import hiiadmin.module.approval.LeaveCategoryVO
import timetabling.oa.ApprovalTemplateThemeNew
import timetabling.oa.LeaveCategory

import static hiiadmin.ConstantEnum.ApprovalUserType
import static hiiadmin.ConstantEnum.UserTypeEnum

class AuthLeaveCategoryController implements BaseExceptionHandler {

    LeaveTypeService leaveTypeService

    RepositoryApprovalTemplateNewService repositoryApprovalTemplateNewService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType")
        List<LeaveCategory> leaveCategoryList = []
        if (userType) {
            leaveCategoryList = leaveTypeService.fetchLeaveTypeByCampusIdAndUserType(campusId, userType)
        } else {
            leaveCategoryList = leaveTypeService.fetchLeaveTypeByCampusId(campusId)
        }
        List<Long> leaveTypeIdList = leaveCategoryList*.id
        List<LeaveCategoryVO> leaveTypeVOList = []
        if (leaveTypeIdList && leaveTypeIdList.size() > 0) {
            List<ApprovalTemplateThemeNew> approvalTemplateThemeNewList = repositoryApprovalTemplateNewService.fetchApprovalTemplateThemeByLeaveCategoryIdInList(leaveTypeIdList)
            Multimap<Long, String> approvalTemplateIdNameMap = HashMultimap.create()
            approvalTemplateThemeNewList.each {
                approvalTemplateIdNameMap.put(it.leaveCategoryId, "【${ApprovalUserType.getEnumByType(it.promoterType)?.name}端】" + it.name)
            }
            leaveCategoryList.each { LeaveCategory leaveCategory ->
                Set<String> approvalTemplateNameSet = approvalTemplateIdNameMap.get(leaveCategory.id)
                String approvalTemplateName = approvalTemplateNameSet?.join(",")
                leaveTypeVOList.add(viewDOService.buildLeaveTypeVO(leaveCategory, approvalTemplateName))
            }
        }
        leaveTypeVOList.sort { -it.id }
        resultVO.result.put("list", leaveTypeVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType", UserTypeEnum.TEACHER.type)
        String name = params.name
        Byte leaveUnitType = params.byte("leaveUnitType")
        List<LeaveCategory> leaveCategoryList = leaveTypeService.fetchLeaveTypeByCampusIdAndUserType(campusId, userType)
        List<String> nameList = leaveCategoryList*.name
        if (nameList.contains(name)) {
            resultVO.status = 0
            resultVO.code = 200001
            resultVO.msg = "名称不可重复"
        } else {
            LeaveCategory leaveCategory = new LeaveCategory(schoolId: schoolId,
                    campusId: campusId,
                    name: name,
                    leaveUnitType: leaveUnitType,
                    userType: userType,
                    status: 1 as byte)
            leaveCategory = leaveTypeService.saveLeaveType(leaveCategory)
            resultVO.result.put("id", leaveCategory.id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String name = params.name
        Byte leaveUnitType = params.byte("leaveUnitType")
        Byte status = params.byte("status")
        Byte userType = params.byte("userType", UserTypeEnum.TEACHER.type)
        LeaveCategory leaveCategory = leaveTypeService.getLeaveType(id)
        if (name && name != leaveCategory.name) {
            List<LeaveCategory> leaveCategoryList = leaveTypeService.fetchLeaveTypeByCampusIdAndUserType(leaveCategory.campusId, userType)
            List<String> nameList = leaveCategoryList*.name
            if (nameList.contains(name)) {
                resultVO.status = 0
                resultVO.code = 200001
                resultVO.msg = "名称不可重复"
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
            leaveCategory.name = name
        }
        leaveCategory.leaveUnitType = leaveUnitType
        if (status != null) {
            List<ApprovalTemplateThemeNew> approvalTemplateThemeNewList = repositoryApprovalTemplateNewService.fetchApprovalTemplateThemeByLeaveCategoryId(id)
            if (approvalTemplateThemeNewList && approvalTemplateThemeNewList.size() > 0) {
                resultVO.code = 300001
                resultVO.msg = "该请假类型已关联审批"
                resultVO.status = 0
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
            leaveCategory.status = status
        }
        leaveTypeService.saveLeaveType(leaveCategory)
        resultVO.result.put("id", leaveCategory.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        List<ApprovalTemplateThemeNew> approvalTemplateThemeNewList = repositoryApprovalTemplateNewService.fetchApprovalTemplateThemeByLeaveCategoryId(id)
        if (approvalTemplateThemeNewList && approvalTemplateThemeNewList.size() > 0) {
            resultVO.code = 300001
            resultVO.msg = "该请假类型已关联审批"
            resultVO.status = 0
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        LeaveCategory leaveCategory = leaveTypeService.getLeaveType(id)
        leaveCategory.status = -1 as byte
        leaveTypeService.saveLeaveType(leaveCategory)
        resultVO.result.put("id", leaveCategory.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.school.TeacherService
import hiiadmin.utils.TimeUtils
import timetabling.oa.ApprovalExportRecord
import timetabling.oa.LeaveRecord
import timetabling.user.Teacher

@Slf4j
class LeaveExportController implements BaseExceptionHandler {

    LeaveRecordService leaveRecordService

    TeacherService teacherService

    ApprovalV2Service approvalV2Service

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String searchValue = params.searchValue
        Long leaveCategoryId = params.long("leaveCategoryId")
        Long leaveDirectionId = params.long("leaveDirectionId")
        Byte status = params.byte("status")
        String promoterName = params.promoterName
        Long leaveStartTime = params.long("leaveStartTime")
        Long leaveEndTime = params.long("leaveEndTime")
        Long applyStartTime = params.long("applyStartTime")
        Long applyEndTime = params.long("applyEndTime")
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Boolean gender = params.boolean('gender')
        Boolean approvalDetail = params.boolean('approvalDetail')
        Teacher teacher = teacherService.findTeacherById(userId)
        if(!applyStartTime&&!applyEndTime){
            applyEndTime = new Date().time
            applyStartTime = TimeUtils.getDateAfterMonth(new Date(),-6)
        }
        List<LeaveRecord> leaveRecordList = leaveRecordService.fetchLeaveRecord(campusId, searchValue, leaveCategoryId, leaveDirectionId, leaveStartTime, leaveEndTime, status, promoterName, applyStartTime, applyEndTime, gender)
        if (leaveRecordList && leaveRecordList.size() > 0) {
            ApprovalExportRecord approvalExportRecord = new ApprovalExportRecord(
                    schoolId: schoolId,
                    campusId: campusId,
                    exporterId: userId,
                    exporterName: teacher?.name,
                    recordNum: leaveRecordList.size(),
                    type: ConstantEnum.ApprovalExportRecordType.STUDENT_LEAVE.type
            )
            approvalExportRecord = approvalV2Service.saveApprovalExportRecord(approvalExportRecord)
            Promise task = Promises.task {
                leaveRecordService.transformLeaveExcel(leaveRecordList,approvalDetail, approvalExportRecord)
            }
            task.onComplete {
                log.info("学生请假记录导出完成")
            }
            task.onError { Exception e ->
                log.error("学生请假记录导出失败", e)
            }
            resultVO.result.put("id", approvalExportRecord.id)
        } else {
            resultVO.code = 200001
            resultVO.msg = "无请假记录可导出"
            resultVO.status = 0
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

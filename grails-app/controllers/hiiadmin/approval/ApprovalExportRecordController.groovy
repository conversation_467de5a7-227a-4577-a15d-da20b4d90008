package hiiadmin.approval

import hiiadmin.BaseExceptionHandler
import timetabling.oa.ApprovalExportRecord

class ApprovalExportRecordController implements BaseExceptionHandler {

    ApprovalV2Service approvalV2Service

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        String type = params.type
        if (type == null || type == "") {
            type = "1"
        }
        List<ApprovalExportRecord> approvalExportRecordList = approvalV2Service.fetchApprovalExportRecordViaCampusIdAndType(campusId, type)
        approvalExportRecordList?.sort { -it.id }
        [approvalExportRecordList: approvalExportRecordList]
    }
}

package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class HiiAdminCheckInvoiceController implements BaseExceptionHandler {

    OcrInvoiceService ocrInvoiceService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String url = params.url
        Long campusId = request.getAttribute("campusId") as Long
        def map = ocrInvoiceService.checkInvoice(campusId, url)
        if (map.code == 200) {
            resultVO.result.put("invoiceCode", map.invoiceCode)
        } else {
            resultVO.status = 0
            resultVO.code = 200001
            resultVO.msg = "检测失败,请上传正确发票"
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

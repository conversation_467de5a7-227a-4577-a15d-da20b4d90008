package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.module.approval.HonorDeclarationSelectionVO
import timetabling.oa.HonorDeclarationSelection

class HonorDeclarationSelectionController implements BaseExceptionHandler {

    HonorDeclarationService honorDeclarationService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = params.byte("userType", ConstantEnum.UserTypeEnum.STUDENT.type)
        List<HonorDeclarationSelection> honorDeclarationSelectionList = honorDeclarationService.fetchAllHonorDeclarationSelectionByCampusIdAndUserType(campusId, userType)
        List<HonorDeclarationSelectionVO> honorDeclarationSelectionVOList = honorDeclarationSelectionList.collect {
            viewDOService.buildHonorDeclarationSelectionVO(it)
        }
        resultVO.result.put("list", honorDeclarationSelectionVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

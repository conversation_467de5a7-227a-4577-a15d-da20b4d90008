package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.approval.ViolationVO
import timetabling.oa.Violation

/**
 * 违纪等第列表
 */
class ViolationListController implements BaseExceptionHandler {

    ViolationRecordService violationRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        List<Violation> violationList = violationRecordService.fetchAllViolationByCampus(campusId)

        List<ViolationVO> vos = []

        violationList.each {
            ViolationVO vo = new ViolationVO()
            vo.id = it.id
            vo.campusId = it.campusId
            vo.name = it.name
            vo.score = it.score
            vo.level = it.level

            vos << vo
        }

        resultVO.result.put("list", vos)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

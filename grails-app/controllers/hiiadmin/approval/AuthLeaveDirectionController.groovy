package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.approval.LeaveDirectionVO
import timetabling.oa.LeaveDirection
import timetabling.oa.LeaveDirectionDevice

class AuthLeaveDirectionController implements BaseExceptionHandler{

    LeaveTypeService leaveTypeService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<LeaveDirectionVO> leaveDirectionVOList = []
        List<LeaveDirection> leaveDirectionList = leaveTypeService.fetchLeaveDirectionByCampusId(campusId)
        if (leaveDirectionList && leaveDirectionList.size() > 0) {
            List<LeaveDirectionDevice> leaveDirectionDeviceList = leaveTypeService.fetchLeaveDirectionDeviceByLeaveDirectionIdInList(leaveDirectionList*.id)
            Multimap<Long, Long> directionIdDeviceIdMap = HashMultimap.create()
            if (leaveDirectionDeviceList && leaveDirectionDeviceList.size() > 0) {
                leaveDirectionDeviceList.each {
                    LeaveDirectionDevice leaveDirectionDevice ->
                        directionIdDeviceIdMap.put(leaveDirectionDevice.leaveDirectionId, leaveDirectionDevice.deviceId)
                }
            }
            leaveDirectionList.each {
                LeaveDirection leaveDirection ->
                    leaveDirectionVOList.add(viewDOService.buildLeaveDirectionVO(leaveDirection, directionIdDeviceIdMap?.get(leaveDirection.id)?.join(",")))
            }
        }
        leaveDirectionVOList?.sort { -it.id }
        resultVO.result.put("list", leaveDirectionVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

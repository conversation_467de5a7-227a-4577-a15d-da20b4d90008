package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.approval.WenYinApprovalRecordVO
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PatternUtils
import io.swagger.annotations.Api
import timetabling.oa.ApprovalTemplateThemeNew
import timetabling.oa.WenYinApprovalRecord

@Api(value = "文印室管理（新）", tags = "总务办公", consumes = "admin")
class HiiAdminWenYinApprovalRecordController implements BaseExceptionHandler {

    WenYinApprovalRecordService wenYinApprovalRecordService

    ApprovalV2Service approvalV2Service

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long approvalTemplateThemeId = params.long("approvalTemplateThemeId")
        String subjectName = params.subjectName
        subjectName = PatternUtils.filter(subjectName)
        String teacherName = params.teacherName
        teacherName = PatternUtils.filter(teacherName)
        Byte status = params.byte("status")
        Byte printStatus = params.byte("printStatus")
        Long startUseTime = params.long("startUseTime")
        Long endUseTime = params.long("endUseTime")
        Long startApprovalTime = params.long("startApprovalTime")
        Long endApprovalTime = params.long("endApprovalTime")
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = wenYinApprovalRecordService.pageWenYinApprovalRecord(campusId, approvalTemplateThemeId, subjectName, teacherName, status, printStatus, startUseTime, endUseTime, startApprovalTime, endApprovalTime, p, s)
        List<WenYinApprovalRecordVO> wenYinApprovalRecordVOList = map.list?.collect { new WenYinApprovalRecordVO().buildVO(it) }
        resultVO.result.put("list", wenYinApprovalRecordVOList)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordById(id)
        WenYinApprovalRecordVO wenYinApprovalRecordVO = new WenYinApprovalRecordVO().buildVO(wenYinApprovalRecord)
        ApprovalTemplateThemeNew approvalTemplateThemeNew = approvalV2Service.getApprovalTemplateTheme(wenYinApprovalRecord.approvalTemplateThemeId)
        wenYinApprovalRecordVO.approvalTemplateThemeName = approvalTemplateThemeNew?.name
        resultVO.result = ObjectUtils.objectToMap(wenYinApprovalRecordVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Byte printStatus = params.byte("printStatus")
        WenYinApprovalRecord wenYinApprovalRecord = wenYinApprovalRecordService.fetchWenYinApprovalRecordById(id)
        if (printStatus != 1 as byte) {
            wenYinApprovalRecord.printStatus = 1 as byte
            wenYinApprovalRecord.printTime = new Date()
            wenYinApprovalRecordService.updateWenYinApprovalRecord(wenYinApprovalRecord)
        }
        wenYinApprovalRecordService.sendWenYinPrintMessage(wenYinApprovalRecord)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

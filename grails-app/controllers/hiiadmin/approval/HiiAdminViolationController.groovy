package hiiadmin.approval

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.approval.ViolationVO
import org.apache.commons.lang3.StringUtils
import timetabling.oa.Violation

/**
 * 学生违纪评分管理
 */
class HiiAdminViolationController implements BaseExceptionHandler {

    ViolationService violationService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        List<Violation> violationList = violationService.fetchAllViolationByCampusId(campusId)
        
        List<ViolationVO> vos = []
        
        violationList.each {
            vos << violationService.buildViolationVO(it)
        }
        
        resultVO.result.put("list", vos)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        

        Long id = params.long("id")
        
        String score = params.score
        
        if (StringUtils.isBlank(score)) {
            throw new HiiAdminException("分数未填写！")
        }
        
        Violation violation = violationService.fetchById(id)
        
        if (violation) {
            BigDecimal bigDecimal = new BigDecimal(score)
            violation.score = bigDecimal
            violationService.save(violation)
        }
        
        resultVO.result.put("id", id)


        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        String ids = params.ids
        
        if (StringUtils.isBlank(ids)) {
            throw new HiiAdminException("未选择评分项，排序出错")
        }
        
        violationService.sortViolation(ids)
        
        resultVO.result.put("ids", ids)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

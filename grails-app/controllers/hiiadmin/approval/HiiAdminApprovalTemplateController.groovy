package hiiadmin.approval


import grails.gorm.transactions.Transactional
import hiiadmin.BaseExceptionHandler
import io.swagger.annotations.Api
import org.assertj.core.util.Lists
import timetabling.ApprovalTemplate
import timetabling.ApprovalTemplateTheme

@Deprecated
@Transactional
@Api(value = "审批模板", tags = "总务办公", consumes = "admin")
class HiiAdminApprovalTemplateController implements BaseExceptionHandler {

    ApprovalService approvalService

    def index() {
        Long campusId = request.getAttribute("campusId")
        Long schoolId = request.getAttribute("schoolId")
        Long staffId = request.getAttribute("staffId")

        List<List<ApprovalTemplate>> approvalTemplateLists = Lists.newArrayList()

        def rootApprovalTemplateList = approvalService.findAllRootApprovalList(campusId)
        rootApprovalTemplateList.each {
            ApprovalTemplate approvalTemplate ->
                def approvalTemplates = approvalService.findApprovalListById(approvalTemplate.id)
                approvalTemplateLists.add(approvalTemplates)
        }

        [approvalTemplateLists: approvalTemplateLists]
    }

    def save() {
        Long campusId = request.getAttribute("campusId")
        Long schoolId = request.getAttribute("schoolId")
        Long staffId = request.getAttribute("staffId")
        Long approvalTemplateThemeId = params.long('approvalTemplateThemeId')
        String mapStr = params.t as String
        ApprovalTemplateTheme approvalTemplateTheme = approvalService.findApprovalTemplateThemeById(approvalTemplateThemeId)
        if (approvalTemplateTheme.approvalTemplateId) {
            //失效原先的模板
            List<ApprovalTemplate> approvalTemplateList = approvalService.findApprovalListById(approvalTemplateTheme.approvalTemplateId)
            approvalTemplateList.each {
                ApprovalTemplate approvalTemplate ->
                    approvalTemplate.status = 0 as Byte
                    approvalTemplate.save(failOnError: true)
            }
        }
        Long rootId = approvalService.saveApprovalTemplate(mapStr, schoolId, campusId, staffId, approvalTemplateTheme)

        def approvalTemplates = approvalService.findApprovalListById(rootId)

        [approvalTemplates: approvalTemplates]
    }

    def show() {
        Long id = params.long('id')
        ApprovalTemplateTheme approvalTemplateTheme = ApprovalTemplateTheme.findById(id)
        def approvalTemplateList = approvalService.findAllApprovalSubListByApprovalRootId(approvalTemplateTheme.approvalTemplateId)

        [approvalTemplateList: approvalTemplateList]
    }

//    public static void main(String[] args) {
//        Map<Integer, ApprovalTemplateVO> map = Maps.newHashMap()
//        ApprovalTemplateVO approvalTemplate = new ApprovalTemplateVO()
//        approvalTemplate.promoterType = ConstantEnum.ApprovalUserType.TEACHER.type
//        approvalTemplate.promoterIds = "122,133"
//        approvalTemplate.targetType = ConstantEnum.ApprovalUserType.STUDENT.type
//        approvalTemplate.approvalType = ConstantEnum.ApprovalType.ASK_FOR_LEAVE_ILL.type
//        approvalTemplate.indexId = 0l
//        approvalTemplate.approvalStepType = ConstantEnum.ApprovalStepType.PROMOTE.type//发起者
//        approvalTemplate.name = '学生病假'
//        approvalTemplate.approvalTemplateParentId = 0l
//        approvalTemplate.conditionScript = '{return true}'
//
//        map.put(0, approvalTemplate)
//
//        ApprovalTemplateVO approvalTemplate1 = new ApprovalTemplateVO()
//        approvalTemplate1.approvalType = ConstantEnum.ApprovalType.ASK_FOR_LEAVE_ILL.type
//        approvalTemplate1.parentIndexId = approvalTemplate.indexId
//        approvalTemplate1.approvalStepType = ConstantEnum.ApprovalStepType.APPROVAL.type
//        approvalTemplate1.requireMemo = "提交医生开具的确诊证明及图片"
//        approvalTemplate1.name = "第一环节name"
//        approvalTemplate1.approvalStepUserType = ConstantEnum.ApprovalUserType.TEACHER.type
//        approvalTemplate1.approvalStepUserIds = "295,309"
//        approvalTemplate1.indexId = 1l
//        approvalTemplate1.conditionScript = '{return true}'
//
//        map.put(1, approvalTemplate1)
//
//        ApprovalTemplateVO approvalTemplate2 = new ApprovalTemplateVO()
//        approvalTemplate2.approvalStepType = ConstantEnum.ApprovalStepType.COPY_TO.type
//        approvalTemplate2.approvalType = ConstantEnum.ApprovalType.ASK_FOR_LEAVE_ILL.type
//        approvalTemplate2.approvalStepUserType = ConstantEnum.ApprovalUserType.TEACHER.type
//        approvalTemplate2.approvalStepUserIds = "272,290,457"
//        approvalTemplate2.conditionScript = '{return true}'
//        approvalTemplate2.name = "第二阶段name"
//        approvalTemplate2.parentIndexId = 1l
//        approvalTemplate2.indexId = 2l
//
//        map.put(2, approvalTemplate2)
//
//        ApprovalTemplateVO approvalTemplateVO = new ApprovalTemplateVO()
//        approvalTemplateVO.approvalStepType = ConstantEnum.ApprovalStepType.COPY_TO.type
//        approvalTemplateVO.approvalType = ConstantEnum.ApprovalType.ASK_FOR_LEAVE_ILL.type
//        approvalTemplateVO.approvalStepUserType = ConstantEnum.ApprovalUserType.PARENT.type
//        approvalTemplateVO.conditionScript = '{return true}'
//        approvalTemplateVO.parentIndexId = 2l
//        approvalTemplateVO.indexId = 3l
//        approvalTemplateVO.name = "第三阶段name"
//
//        map.put(3, approvalTemplateVO)
//        System.out.println(JSON.toJSONString(map))
//
//        String p = '{0:{"approvalStepType":1,"approvalTemplateParentId":0,"approvalType":1,"conditionScript":"{return true}","indexId":0,"name":"学生病假","promoterType":6,"status":1,"targetType":1,"weight":2},1:{"approvalStepType":2,"approvalStepUserIds":"12,13","approvalStepUserType":6,"approvalType":1,"conditionScript":"{return true}","indexId":1,"parentIndexId":0,"status":1},2:{"approvalStepType":3,"approvalStepUserIds":"15,16","approvalStepUserType":6,"approvalType":1,"conditionScript":"{return true}","indexId":2,"parentIndexId":1,"status":1},3:{"approvalStepType":3,"approvalStepUserType":2,"approvalType":1,"conditionScript":"{return true}","indexId":3,"parentIndexId":2,"status":1}}'
//        Map<Integer, ApprovalTemplateVO> mapResult = (Map) JSON.parse(p)
//        System.out.println(mapResult)

//    }
}

package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.jogging.JoggingRecordVO
import hiiadmin.school.SchoolService
import hiiadmin.utils.PhenixCoder
import org.joda.time.DateTime

/**
 * <AUTHOR>
 * @Date 2022-12-05 19:36
 */

@Slf4j
class JoggingCompletionRateController implements BaseExceptionHandler {

    JoggingStudentCylinderService joggingStudentCylinderService

    SchoolService schoolService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte type = params.byte("type", 2 as byte)

        String encodeSectionId = params.sectionId
        String encodeGradeId = params.gradeId
        String encodeUnitId = params.unitId

        Long sectionId = PhenixCoder.decodeId(encodeSectionId)
        Long gradeId = PhenixCoder.decodeId(encodeGradeId)
        Long unitId = PhenixCoder.decodeId(encodeUnitId)
        List<JoggingRecordVO> vos = []
        Integer total = 0
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        Long semesterId = schoolService.fetchCurrentSemesterByCampusId(campusId)?.id
        Long dayStamp = new DateTime().minusDays(1).withTimeAtStartOfDay().millis
        switch (type) {
            case ConstantEnum.JoggingCompletionRateType.STUDENT_COUNT.type:
                String searchValue = params.searchValue
                def map = joggingStudentCylinderService.fetchAllStudentJoggingCompletionRate(semesterId, dayStamp, campusId, sectionId, gradeId, unitId, searchValue, p, s)
                total = map.total
                vos = joggingStudentCylinderService.transformStudentJoggingRate(map.list)
                break

            case ConstantEnum.JoggingCompletionRateType.UNIT_COUNT.type:
                def map = joggingStudentCylinderService.fetchAllJoggingUnitCompletionRate(semesterId, dayStamp, campusId, sectionId, gradeId, unitId, p, s)
                total = map.total
                vos = joggingStudentCylinderService.transformUnitJoggingRate(map.list)
                break
        }

        resultVO.result.put("list", vos)
        resultVO.result.put("total", total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

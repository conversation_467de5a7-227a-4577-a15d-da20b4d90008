package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.jogging.PointDeviceVO
import timetabling.jog.Point

/**
 * <AUTHOR>
 * @Date 2022-12-01 09:49
 */

@Slf4j
class PointController implements BaseExceptionHandler {

    PointDeviceService pointDeviceService

    JoggingPointService joggingPointService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<Point> pointList = pointDeviceService.fetchAllPointByCampusId(campusId)
        List<PointDeviceVO> vos = pointList?.collect {viewDOService.buildPointDeviceVO4Point(it)}

        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        Point point = new Point(
                campusId: campusId,
                name: name,
                status: 1 as byte
        )
        pointDeviceService.savePoint(point)

        resultVO.result.put("id", point.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        joggingPointService.deleteJoggingPointByCampusIdAndPointId(campusId, id)
        pointDeviceService.deletePointDeviceByCampusIdAndPointId(campusId, id)
        pointDeviceService.deletePoint(id)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

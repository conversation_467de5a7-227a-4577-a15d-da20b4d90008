package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.BureauStudentVO
import hiiadmin.module.jogging.JoggingRecordVO
import hiiadmin.module.jogging.JoggingStudentRecordVO
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PhenixCoder
import timetabling.jog.JoggingRecord
import timetabling.jog.JoggingStudent
import timetabling.user.Student

/**
 * <AUTHOR>
 * @Date 2022-12-03 16:25
 */

@Slf4j
class JoggingRecordController implements BaseExceptionHandler {

    JoggingRecordService joggingRecordService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long dayTimeStamp = params.long("dayTimeStamp")
        Long sectionId = PhenixCoder.decodeId(params.sectionId)
        Long gradeId = PhenixCoder.decodeId(params.gradeId)
        Long unitId = PhenixCoder.decodeId(params.unitId)
        Long joggingPlanId = params.long("joggingPlanId")
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = joggingRecordService.fetchAllJoggingRecordLimit(schoolId, campusId, searchValue, dayTimeStamp, sectionId, gradeId, unitId, joggingPlanId, p, s)

        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long joggingStudentId = params.long("id")
        JoggingStudent joggingStudent = JoggingStudent.findById(joggingStudentId)
        Long studentId = joggingStudent?.studentId
        Long joggingPlanId = joggingStudent?.joggingPlanId
        Long dayTimeStamp = joggingStudent?.dayTimeStamp
        List<JoggingRecord> joggingRecordList = joggingRecordService.fetchAllJoggingRecordByCampusIdAndJoggingTime(campusId, studentId, dayTimeStamp, joggingPlanId)
        List<JoggingStudentRecordVO> vos = viewDOService.buildJoggingStudentRecordVO(joggingRecordList, joggingPlanId)
        vos?.sort {-it?.createTime}

        Student student = Student.findById(studentId)

        BureauStudentVO studentVO = viewDOService.buildBureauStudentVO(student)
        studentVO.joggingStudentRecordVOList = vos
        resultVO.result = ObjectUtils.resultVOObject2Map(studentVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.jogging.PointDeviceVO
import hiiadmin.utils.ToStringUnits
import timetabling.jog.PointDevice

/**
 * <AUTHOR>
 * @Date 2022-11-30 18:31
 */

@Slf4j
class PointDeviceController implements BaseExceptionHandler {

    PointDeviceService pointDeviceService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long joggingPlanId = params.long("joggingPlanId")
        List<PointDeviceVO> vos = pointDeviceService.fetchPointDeviceVOSByCampusIdAndJoggingPlanId(campusId, joggingPlanId)

        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        List<PointDevice> pointDeviceList = pointDeviceService.fetchAllPointDeviceByCampusId(campusId)
        List<Long> deviceIdList = []
        if (pointDeviceList.size() > 0) {
            deviceIdList = pointDeviceList*.deviceId
        }

        resultVO.result.put("list", deviceIdList.toSet().toList())
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long pointId = params.long("id")
        String deviceIds = params.deviceIds
        List<Long> list = []
        List<Long> deviceIdList = ToStringUnits.idsString2LongList(deviceIds)
        pointDeviceService.deletePointDeviceByCampusIdAndPointId(campusId, pointId)
        deviceIdList?.each {
            deviceId ->
                PointDevice pointDevice = new PointDevice(
                        campusId: campusId,
                        pointId: pointId,
                        deviceId: deviceId,
                        status: 1 as byte
                )
                pointDeviceService.savePointDevice(pointDevice)
                list << pointDevice.id
        }

        resultVO.result.put("list", list)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        Long deviceId = params.long("deviceId")
        pointDeviceService.deletePointDeviceByPointIdAndDeviceId(campusId, id, deviceId)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

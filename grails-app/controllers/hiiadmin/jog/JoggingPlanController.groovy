package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.jogging.JoggingPlanVO
import hiiadmin.module.jogging.dto.JoggingTimeDTO
import hiiadmin.module.jogging.dto.PointDeviceDTO
import timetabling.jog.JoggingPlan

/**
 * <AUTHOR>
 * @Date 2022-11-30 14:34
 */

@Slf4j
@Transactional
class JoggingPlanController implements BaseExceptionHandler {

    JoggingPlanService joggingPlanService

    JoggingTimeService joggingTimeService

    JoggingPointService joggingPointService

    PointDeviceService pointDeviceService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<JoggingPlan> joggingPlans = joggingPlanService.fetchAllJoggingPlanByCampusId(campusId)
        List<JoggingPlanVO> vos = joggingPlans?.collect {viewDOService.buildJoggingPlanVO(it)}

        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        JoggingPlan joggingPlan = joggingPlanService.getJoggingPlan(id)
        JoggingPlanVO vo = viewDOService.buildJoggingPlanVO(joggingPlan)

        resultVO.result.put("vo", vo)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long promoterId = request.getAttribute("userId") as Long

        String name = params.name
        String unitIdCodes = params.unitIdCodes
        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
        Integer highTime = params.int("highTime")
        Integer lowTime = params.int("lowTime")
        // cylinderNumber -> totalNumber
        String cylinderNumber = params.cylinderNumber

        String timeJson = params.timeJson
        String pointDeviceJson = params.pointDeviceJson

        List<JoggingTimeDTO> joggingTimeDTOS = joggingTimeService.parseJoggingTimeDto(timeJson)
        if (joggingTimeDTOS.size() < 1) {
            resultVO.status = 0
            resultVO.message = "未配置跑操时间,请至少配置一个跑操时间段"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }

        List<PointDeviceDTO> pointDeviceDTOS = pointDeviceService.parsePointDeviceDto(pointDeviceJson)
        if (pointDeviceDTOS == null) {
            resultVO.status = 0
            resultVO.message = "未关联点位打卡设备"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }

        JoggingPlan joggingPlan = new JoggingPlan()
        joggingPlan.campusId = campusId
        joggingPlan.promoterId = promoterId
        joggingPlan.name = name
        if (!unitIdCodes) {
            throw new HiiAdminException("未选择班级")
        }
        joggingPlan.unitIdCodes = unitIdCodes
        joggingPlan.startDate = new Date(startDate)
        joggingPlan.endDate = new Date(endDate)
        joggingPlan.highTime = highTime
        joggingPlan.lowTime = lowTime
        joggingPlan.pointNumber = pointDeviceDTOS.size() ?: 0
        if (!cylinderNumber) {
            throw new HiiAdminException("未设置跑操圈数")
        }
        joggingPlan.totalNumber = new BigDecimal(cylinderNumber)
        joggingPlan.timeJson = timeJson
        joggingPlan.pointDeviceJson = pointDeviceJson
        joggingPlan.status = 1 as byte
        joggingPlanService.saveJoggingPlan(joggingPlan)

        joggingTimeService.createJoggingTime(joggingTimeDTOS, campusId, promoterId, joggingPlan.id)

        pointDeviceDTOS?.each {
            dto ->
                joggingPointService.createJoggingPoint(campusId, joggingPlan.id, dto?.pointDeviceId, dto.idx)
        }

        resultVO.result.put("id", joggingPlan.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long promoterId = request.getAttribute("userId") as Long

        Long id = params.long("id")
        String name = params.name
        String unitIdCodes = params.unitIdCodes
        Long startDate = params.long("startDate")
        Long endDate = params.long("endDate")
        Integer highTime = params.int("highTime")
        Integer lowTime = params.int("lowTime")
        String cylinderNumber = params.cylinderNumber

        String timeJson = params.timeJson
        String pointDeviceJson = params.pointDeviceJson

        Integer pointNumber = 0

        JoggingPlan joggingPlan = joggingPlanService.getJoggingPlan(id)
        if (!joggingPlan) {
            resultVO.status = 0
            resultVO.message = "没有该跑操计划"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }

        List<JoggingTimeDTO> joggingTimeDTOS = joggingTimeService.parseJoggingTimeDto(timeJson)
        if (joggingTimeDTOS.size() > 0) {
            joggingTimeService.updateJoggingTime(timeJson, campusId, promoterId, id)
        }

        List<PointDeviceDTO> pointDeviceDTOS = pointDeviceService.parsePointDeviceDto(pointDeviceJson)
        if (pointDeviceDTOS.size() > 0) {
            joggingPointService.updateJoggingPoint(campusId, id, pointDeviceDTOS)
            pointNumber = pointDeviceDTOS.size()
        }

        joggingPlanService.updateJoggingPlan(joggingPlan, promoterId, name, unitIdCodes, startDate, endDate, highTime, lowTime, pointNumber, cylinderNumber, timeJson, pointDeviceJson)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        joggingPlanService.deleteJoggingPlanById(campusId, id)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

}

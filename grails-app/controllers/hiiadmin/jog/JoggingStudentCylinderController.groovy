package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.jogging.JoggingStudentCylinderVO
import hiiadmin.module.jogging.dto.JoggingStudentDaysDTO
import hiiadmin.school.SchoolService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import timetabling.jog.JoggingStudentCylinder
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.user.Student

/**
 * <AUTHOR>
 * @Date 2022-12-01 19:40
 */

@Slf4j
class JoggingStudentCylinderController implements BaseExceptionHandler {

    StudentService studentService

    JoggingStudentCylinderService joggingStudentCylinderService

    UnitService unitService

    SchoolService schoolService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<JoggingStudentCylinderVO> vos = []
        List<JoggingStudentCylinder> joggingStudentCylinders = joggingStudentCylinderService.fetchAllJoggingStudentCylinderByCampusId(campusId)
        if (joggingStudentCylinders.size() > 0) {
            Map<Integer, List<JoggingStudentCylinder>> idxCylinderMap = joggingStudentCylinders.groupBy {it.idx}
            idxCylinderMap?.each{  entry ->
                List<JoggingStudentCylinder> joggingStudentCylinderList = entry.value
                if (joggingStudentCylinderList.size() > 0) {
                    JoggingStudentCylinderVO vo = new JoggingStudentCylinderVO()
                    List<UnitStudent> unitStudentList = studentService.fetchAllUnitStudentByStudentIdList(campusId, joggingStudentCylinderList*.studentId)
                    List<Unit> units = unitService.fetchAllNormalUnitByIdList(unitStudentList*.unitId.toSet().toList())
                    vo.id = entry.key
                    vo.unitIds = PhenixCoder.encodeIds(units*.id.join(","))
                    vo.unitNames = units*.name.join(",")
                    vo.totalDays = joggingStudentCylinderList[0].totalDays
                    vo.idx = joggingStudentCylinderList[0].idx

                    vos << vo
                }
            }
        }

        resultVO.result.put("vos", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long promoterId = request.getAttribute("userId") as Long
        String studentDaysJson = params.studentDaysJson
        List<JoggingStudentDaysDTO> joggingStudentDaysDTOS = joggingStudentCylinderService.parseJoggingStudentCylinderDto(studentDaysJson)
        if (joggingStudentDaysDTOS == null) {
            resultVO.status = 0
            resultVO.message = "学生跑操天数参数为空"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }

        if (joggingStudentDaysDTOS.size() > 0) {
            joggingStudentCylinderService.checkJoggingStudentDaysDTOStandard(joggingStudentDaysDTOS)
        }

        Long semesterId = schoolService.fetchCurrentSemesterByCampusId(campusId).id

        joggingStudentCylinderService.deleteJoggingStudentDaysByCampusId(campusId, semesterId)

        joggingStudentDaysDTOS?.each {
            dto ->
                List<Long> unitIdList = ToStringUnits.idsString2LongList(PhenixCoder.decodeIds(dto.unitIds))
                if (unitIdList.size() > 0) {
                    List<Student> students = studentService.fetchAllNormalStudentByUnitIdInList(unitIdList)
                    if (students.size() > 0) {
                        ServiceResult serviceResult = joggingStudentCylinderService.createJoggingStudentCylinder(campusId, students*.id, promoterId, dto.totalDays, dto.idx, semesterId)
                        if (serviceResult.success) {
                            resultVO.message = "success"
                        } else {
                            resultVO.status = 0
                            resultVO.code = serviceResult.code as int
                            resultVO.message = serviceResult.message
                        }
                    }
                }
        }

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.jog

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import hiiadmin.EnvService
import lombok.extern.slf4j.Slf4j
import org.joda.time.DateTime
import timetabling.org.Campus

/**
 * 跑操脚本
 */
@Slf4j
class JoggingJobController {

    EnvService envService

    JoggingPlanService joggingPlanService

    JoggingRecordService joggingRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long stamp = params.long("stamp")
        List<Campus> campusList = Campus.findAllByStatus(1 as byte)
        campusList.each {
            joggingPlanService.calculateStudentJogging(it.id, stamp)
        }

        resultVO.result.put("msg", "success")
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String stamps = params.stamps
        List<Long> stampList = stamps.split(",").collect { Long.parseLong(it) }
        Long campusId = request.getAttribute("campusId") as Long
        Promise promise = Promises.task {
            stampList.each {
                Long stamp ->
                    joggingPlanService.calculateStudentJogging(campusId, stamp)
                    String date = new DateTime(stamp).toString("yyyy-MM-dd")
                    log.info("杭高跑操率生成成功，${date}".toString())
            }
        }

        promise.onError { Throwable throwable ->
            throwable.printStackTrace()
            log.error("======== 杭高跑操率生成失败 ${throwable.message} ========".toString())
        }
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long stamp = params.long("stamp")

        Long campusId = request.getAttribute("campusId") as Long
        joggingPlanService.delete(campusId, stamp)
        

        resultVO.result.put("msg", "删除成功")

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    // 日常使用
    def patch() {
        Long campusId = request.getAttribute("campusId") as Long

        Long stamp = params.long("stamp", System.currentTimeMillis())
        if (envService.isDev() || envService.isTest()) {
            joggingRecordService.fixRecord(campusId, stamp)
        }
        render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json;', encoding: "UTF-8"

    }
}

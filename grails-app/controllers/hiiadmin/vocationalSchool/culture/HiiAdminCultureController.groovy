package hiiadmin.vocationalSchool.culture

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.vocationalSchool.CultivationLevelService
import io.swagger.annotations.Api
import timetabling.vocationalSchool.CultivationLevel

@Api(value = "培养层次管理", tags = "职校", consumes = "admin")
@Deprecated
class HiiAdminCultureController {
    
    CultivationLevelService cultivationLevelService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        def map = cultivationLevelService.fetchAllCultivationLevelVO(campusId, p , s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")
        
        Byte status = params.byte("status")
        String name = params.name
        
        Long campusId = request.getAttribute("campusId") as Long

        ServiceResult<Long> serviceResult = cultivationLevelService.updateOrSave(campusId, id, status, name)
        
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.byte("id")

        CultivationLevel cultivationLevel = cultivationLevelService.fetchCultivationLevelById(id)
        if (cultivationLevel){
            cultivationLevel.status = -1 as byte
            cultivationLevelService.save(cultivationLevel)
            resultVO.result.put("id", id)
        } else {
            resultVO.status = 0
            resultVO.message = "学制不存在"
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
}

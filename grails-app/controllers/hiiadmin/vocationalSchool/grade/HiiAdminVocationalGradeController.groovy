package hiiadmin.vocationalSchool.grade

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.school.GradeService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.vocationalSchool.VocationalGradeService
import io.swagger.annotations.Api
import timetabling.org.Grade

@Api(value = "年级管理", tags = "职校", consumes = "admin")
@Deprecated
class HiiAdminVocationalGradeController implements BaseExceptionHandler{
    
    VocationalGradeService vocationalGradeService
    
    GradeService gradeService

    UnitStudentService unitStudentService

    PersonDataChangeService personDataChangeService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        boolean normal = params.boolean("normal", true)
        
        def map = vocationalGradeService.fetchAllGradeVO(campusId, searchValue, p ,s, normal)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        
        String name = params.name

        ServiceResult<Long> serviceResult = vocationalGradeService.addVocationalGrade(schoolId, campusId, name)
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        
        String name = params.name
        
        Byte status = params.byte("status")
        
        vocationalGradeService.update(campusId, name, id, status)

        List<Long> studentIdList = unitStudentService.fetchAllStudentIdByGradeId(id)
        int changeBit = PersonDataChangeEnum.onGroupPersonHandlerBit()
        personDataChangeService.studentsDataChange(campusId, studentIdList, null, null, changeBit, ConstantEnum.OperateType.UPDATE.type)

        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    
    def delete()  {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 
        
        Long id = params.long("id")

        Long campusId = request.getAttribute("campusId") as Long

        Grade grade = gradeService.fetchGradeById(id, false)
        grade.status = -1
        
        gradeService.saveGrade(grade)

        List<Long> studentIdList = unitStudentService.fetchAllStudentIdByGradeId(id)
        int changeBit = PersonDataChangeEnum.onGroupPersonHandlerBit()
        personDataChangeService.studentsDataChange(campusId, studentIdList, null, null, changeBit, ConstantEnum.OperateType.UPDATE.type)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.vocationalSchool.Faculty

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.vocationalSchool.FacultyService
import io.swagger.annotations.Api

@Api(value = "院系/人员排序", tags = "职校", consumes = "admin")
@Deprecated
class HiiAdminFacultySortController implements BaseExceptionHandler{
    
    FacultyService facultyService
    
    def save(){
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        String ids = params.ids
        // 1 部门排序 2人员排序
        Byte sortType = params.byte("sortType")
        
        facultyService.sortFaculty(sortType, ids)
        
        resultVO.result.put("msg", "success")
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

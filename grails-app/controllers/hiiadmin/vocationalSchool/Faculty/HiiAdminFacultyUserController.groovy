package hiiadmin.vocationalSchool.Faculty

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.vocationalSchool.FacultyUserService
import timetabling.vocationalSchool.FacultyUser

@Deprecated
class HiiAdminFacultyUserController {
    
    FacultyUserService facultyUserService
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long userId = params.long("userId")
        Byte userType = params.byte("userType", ConstantEnum.UserTypeEnum.TEACHER.type)
        Long id = params.byte("id")
        
        Long campusId = request.getAttribute("campusId") as Long
        
        facultyUserService.addFacultyUser(campusId, userId, userType, id)
        resultVO.result.put("msg", 'success')
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")
        
        Long campusId = request.getAttribute("campusId") as Long

        FacultyUser facultyUser = facultyUserService.fetchById(id)
        if (!facultyUser){
            throw new HiiAdminException("教师不存在！")
        }
        facultyUserService.deleteUser(campusId, facultyUser.userId, facultyUser.facultyId)
        
        resultVO.result.put("msg","success")
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

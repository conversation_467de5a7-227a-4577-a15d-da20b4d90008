package hiiadmin.vocationalSchool.Faculty

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.vocationalSchool.FacultyVO
import hiiadmin.vocationalSchool.FacultyService
import io.swagger.annotations.Api

// 适配hiiAdminDepartmentController
@Api(value = "院系管理" ,tags = "职校", consumes = "admin")
@Deprecated
class HiiAdminFacultyController implements BaseExceptionHandler{
    
    FacultyService facultyService
    
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        Byte type = params.byte("type")
        String searchValue = params.searchValue
        
        boolean t = params.boolean("t", true)
        boolean normal = params.boolean("normal", true)
        
        List<FacultyVO> facultyVOList = []
        Integer total = 0
        if (t) {
            def map = facultyService.fetchAllFactoryVO(campusId, searchValue, type)
            facultyVOList = map.list
            total = map.total
        } else {
            facultyVOList = facultyService.buildSimpleFacultyVO(campusId, searchValue, type, normal)
            total = facultyVOList?.size() ?: 0
        }
        resultVO.result.put("list", facultyVOList)
        resultVO.result.put("total", total)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        String name = params.name
        Byte type = params.byte("type")
        ServiceResult<Long> serviceResult = facultyService.saveFaculty(campusId, name, type)
        
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        
        String name = params.name
        
        facultyService.update(campusId, id, name)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        
        facultyService.delete(campusId, id)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def patch(){
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        Long id = params.long("id")
        Long userId = params.long("userId")
        Byte userType = params.byte("userType", ConstantEnum.UserTypeEnum.TEACHER.type)
        
        boolean superVisor = params.boolean("superVisor", true)
        
        facultyService.updateSuperVisor(campusId, id , userId, userType, superVisor)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

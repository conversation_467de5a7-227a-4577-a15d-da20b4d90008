package hiiadmin.vocationalSchool.unit

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.vocationalSchool.FacultyVO
import hiiadmin.vocationalSchool.VocationalUnitService
import io.swagger.annotations.Api

@Api(value = "班级树形结构", tags = "职校", consumes = "admin")
class HiiAdminVocationalUnitTreeController implements BaseExceptionHandler{
    
    VocationalUnitService vocationalUnitService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        Integer hideGrade = params.int("hideGrade", 0)
        
        List<FacultyVO> facultyVOList = vocationalUnitService.treeUnitV2(campusId, hideGrade)
        
        resultVO.result.put("list", facultyVOList)
        resultVO.result.put("total", facultyVOList ? facultyVOList.size() : 0)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.vocationalSchool.unit

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.school.org.structure.UnitDataStructureService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.VocationalUnitService
import io.swagger.annotations.Api
import timetabling.vocationalSchool.Faculty


@Api(value = "班级管理", tags = "职校", consumes = "admin")
class HiiAdminVocationalUnitController implements BaseExceptionHandler {

    VocationalUnitService vocationalUnitService

    FacultyService facultyService

    UnitDataStructureService unitDataStructureService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        
        String encodeFacultyId = params.facultyId
        String encodeMajorId = params.majorId
        String encodeCultureId = params.cultureId
        String encodeSchoolSystemId = params.schoolSystemId
        String encodeGradeId = params.gradeId
        
        
        Long facultyId = PhenixCoder.decodeId(encodeFacultyId)
        Long majorId = PhenixCoder.decodeId(encodeMajorId)
        Long cultureId = PhenixCoder.decodeId(encodeCultureId)
        Long schoolSystemId = PhenixCoder.decodeId(encodeSchoolSystemId)
        Long gradeId = PhenixCoder.decodeId(encodeGradeId)
        String searchValue = params.searchValue

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def map = vocationalUnitService.fetchAllUnitByCampus(campusId, facultyId, majorId, cultureId, schoolSystemId, gradeId, searchValue, p, s)
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    // 批量毕业班级
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        String encodeIds = params.ids
        String ids = PhenixCoder.decodeIds(encodeIds)
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        
        vocationalUnitService.graduateUnit(campusId, userId, userType, ids)
        
        resultVO.result.put("ids", ids)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    // 修改，添加
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        String  encodeId = params.id
        String encodeFacultyId = params.facultyId
        String encodeMajorId = params.majorId
        String encodeGradeId = params.gradeId
        
        
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        
        String unitCode = params.unitCode
        String name = params.name
        
        
        Long id = PhenixCoder.decodeId(encodeId)
        Long facultyId = PhenixCoder.decodeId(encodeFacultyId)
        Long majorId = PhenixCoder.decodeId(encodeMajorId)
        Long gradeId = PhenixCoder.decodeId(encodeGradeId)
        Faculty faculty = facultyService.fetchFacultyById(facultyId)
        
        if (!faculty || faculty?.type == 2) {
            throw new HiiAdminException("非教学院系不能添加！")
        }

        ServiceResult<Long> serviceResult = vocationalUnitService.saveOrUpdate(id, schoolId, campusId, unitCode, name, facultyId, majorId, gradeId)
        
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8" 
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        String encodeId = params.id
        Long id = PhenixCoder.decodeId(encodeId, true)
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        unitDataStructureService.deleteUnit(id, userId, userType)
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

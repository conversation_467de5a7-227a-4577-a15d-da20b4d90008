package hiiadmin.vocationalSchool.SchoolSystem

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.vocationalSchool.SchoolSystemService
import io.swagger.annotations.Api
import timetabling.vocationalSchool.SchoolSystem


@Deprecated
@Api(value = "学制管理", tags = "职校", consumes = "admin")
class HiiAdminSchoolSystemController implements BaseExceptionHandler{
    
    SchoolSystemService schoolSystemService
	
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = schoolSystemService.fetchAllSchoolSystem(campusId, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        Long id = params.long("id")
        Byte status = params.byte("status")
        String name = params.name

        ServiceResult<Long> serviceResult = schoolSystemService.updateOrSave(campusId, id, status, name)
        
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete(){
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")

        SchoolSystem schoolSystem = schoolSystemService.fetchSchoolSystemById(id)
        schoolSystem.status = -1 as byte
        schoolSystemService.save(schoolSystem)
        
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.vocationalSchool.major

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.module.vocationalSchool.MajorVO
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.vocationalSchool.MajorService
import io.swagger.annotations.Api
import timetabling.vocationalSchool.Major

@Api(value = "专业管理", tags = "职校", consumes = "admin")
@Deprecated
class HiiAdminMajorController implements BaseExceptionHandler{
    
    MajorService majorService

    UnitStudentService unitStudentService

    PersonDataChangeService personDataChangeService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long facultyId = params.long("facultyId")
        Long cultureId = params.long("cultureId")
        Long schoolSystemId = params.long("schoolSystemId")
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        boolean normal = params.boolean("normal", true)
        
        Long campusId = request.getAttribute("campusId") as Long
        
        List<Major> majorList = majorService.fetchAllMajorBySearch(campusId, facultyId, cultureId, schoolSystemId, searchValue, normal)
        
        List<MajorVO> majorVOList = majorService.buildMajorVOS(campusId, majorList, normal)
        
        
        resultVO.result.put("list", majorVOList)
        resultVO.result.put("total", majorVOList?.size())

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")
        
        String code = params.code
        String name = params.name
        Long facultyId = params.long("facultyId")
        Long cultureId = params.long("cultureId")
        Long schoolSystemId = params.long("schoolSystemId")
        
        Long campusId = request.getAttribute("campusId") as Long

        ServiceResult<Long> serviceResult = majorService.saveOrUpdateMajor(id, campusId, code, name, facultyId, cultureId, schoolSystemId)
        
        resultVO.result.put("id", serviceResult.result)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long("id")
        
        Major major = majorService.fetchMajorById(id)
        
        major.status = -1
        
        majorService.save(major)
        List<Long> studentIdList = unitStudentService.fetchAllStudentIdByMajorId(id)
        int changeBit = PersonDataChangeEnum.onGroupPersonHandlerBit()
        personDataChangeService.studentsDataChange(campusId, studentIdList, null, null, changeBit, ConstantEnum.OperateType.UPDATE.type)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
}

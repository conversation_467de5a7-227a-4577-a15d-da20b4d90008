package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.biz.StaffService
import hiiadmin.school.TeacherService
import hiiadmin.utils.PasswordRegex
import org.springframework.security.crypto.password.PasswordEncoder
import timetabling.user.Staff

import javax.annotation.Resource

class HiiAdminUpdatePasswordController implements BaseExceptionHandler {

    @Resource
    PasswordEncoder passwordEncoder

    StaffService staffService

    TeacherService teacherService

    def save() {
        String password = params.password
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Staff staff
        Long staffId = request.getAttribute('staffId') as Long
        if (!PasswordRegex.checkPass(password)) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.PASSWORD_NOT_VALID_ERROR.code
            resultVO.msg = BizErrorCode.PASSWORD_NOT_VALID_ERROR.msg
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        } else {
            staff = staffService.fetchStaffById(staffId)
        }
        if (null == staff) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.NO_USER_ERROR.code
            resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
        } else {
            staff.passwordHash = passwordEncoder.encode(password)
            staff.save(failOnError: true)
            teacherService.updateTeacherPassword(staff.teacherId,  staff.passwordHash)
            //找回密码时候，对于多个校到同一个手机号，都更新密码，对于用户来说是一个可见账户
            List<Staff> staffList = staffService.findSameMobileStaffListByStaff(staff)
            staffList.each {
                Staff staff1 ->
                    staff1.passwordHash = staff.passwordHash
                    staff1.save(failOnError: true)
            }
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }
}

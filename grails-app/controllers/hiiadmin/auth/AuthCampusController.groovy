package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.CampusVO
import hiiadmin.school.CampusService
import timetabling.org.Campus
import timetabling.org.School

import static com.bugu.PhoenixCoder.decodeId

/**
 * 校区列表
 * 传 searchValue 不分页
 */
@Deprecated
class AuthCampusController implements BaseExceptionHandler {

    CampusService campusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String searchValue = params.searchValue
        String cityStr = params.cityId
        Long cityId = decodeId(cityStr)
        List<Campus> campusList = []
        if (searchValue) {
            campusList = campusService.fetchAllCampusByNameLike(searchValue)
        } else {
            campusList = campusService.fetchAllCampusViaCityId(cityId)
        }
        Map<Long, School> longSchoolMap = campusService.fetchAllSchoolMap()
        List<CampusVO> vos = []
        campusList.each {
            School school = longSchoolMap.get(it.schoolId)
            CampusVO vo = new CampusVO().buildVO(it, school)
            vos << vo
        }
        resultVO.result.put('list', vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

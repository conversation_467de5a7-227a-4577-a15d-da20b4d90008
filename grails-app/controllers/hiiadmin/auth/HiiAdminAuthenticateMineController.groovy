package hiiadmin.auth

import com.bugu.ResultVO
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.Constants
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.newMenu.BgbCampusRoleVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.apache.commons.lang3.StringUtils
import timetabling.Department
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.Staff
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus

/**
 *   获取当前token的类登录接口返回信息
 *   用于前端有token 但是需要获取用户信息
 *
 * <AUTHOR>
 * @date 2022 /06/21
 */
@Slf4j
class HiiAdminAuthenticateMineController implements BaseExceptionHandler {

    StaffService staffService

    CampusService campusService

    SchoolService schoolService

    TeacherService teacherService

    DepartmentService departmentService

    BgbStaffRoleService bgbStaffRoleService

    BgbCampusRoleService bgbCampusRoleService

    UserEncodeInfoService userEncodeInfoService

    /**
     *
     * @return { @link Object  }
     */
    def index() {

        Long staffId = request.getAttribute('staffId') as Long

        Staff staff = staffService.fetchStaffById(staffId)
        String mobile =  userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.STAFF.type, staff.id)
        String username = mobile

        School school = schoolService.fetchSchoolById(staff.schoolId)
        Campus campus = campusService.fetchCampusByCampusId(staff.campusId)
        List<BgbCampusRoleVO> bgbCampusRoleVOList
        Map<School, List<Campus>> schoolCampusListMap = Maps.newHashMap()
        ResultVO resultVO = ResultVO.success()
        boolean pswd = false

        List<Staff> campusStaffList = []
        String lowercaseLogin = ''
        if (StringUtils.isNotBlank(username)) {
            lowercaseLogin = username.toLowerCase(Locale.ENGLISH)
            boolean appAdmin = staffService.appAdminStaff(lowercaseLogin)
            String appId = null
            if (appAdmin || StringUtils.equalsIgnoreCase('admin', lowercaseLogin)) {
                appId = Constants.ADMIN_SYS_APP_ID
            } else {
                appId = Constants.ADMIN_USER_APP_ID
            }

            campusStaffList = userEncodeInfoService.fetchAllStaffByTrueMobile(mobile)
        } else if (staff) {
            campusStaffList.add(staff)
        }
        

        if (campusStaffList && campusStaffList.size() > 0) {
            
            if (StringUtils.isNotBlank(lowercaseLogin)) {
                schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminMobile(campusStaffList.collect { it.schoolId }, lowercaseLogin)
            } else {
                schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminAndStaffList(campusStaffList.collect {it.schoolId}, campusStaffList)
            }
            
        } else {
            throw new HiiAdminException("该账号未开通-请联系学校管理员")
        }

        String avatar = null
        List<Department> departmentList
        if (staff) {
            Teacher teacher = teacherService.fetchTeacherById(staff.teacherId)
            departmentList = departmentService.findDepartmentByTeacherIdAndCampusId(teacher?.id, staff.campusId)
            avatar = teacher?.avatar
            List<BgbStaffRole> bgbStaffRoleList = bgbStaffRoleService.fetchBgbStaffRoleByStaffId(staff.id)
            List<Long> roleIdList = bgbStaffRoleList*.roleId
            bgbCampusRoleVOList = bgbCampusRoleService.fetchBgbCampusRoleVOByIdList(roleIdList)

        }
        TeacherSchoolCampus teacherSchoolCampus = teacherService.fetchTeacherSchoolByCampusIdAndTeacherId(campus?.id, staff?.teacherId, false)
        String jobNum = teacherSchoolCampus?.jobNum
        if (StringUtils.isNotBlank(staff.passwordHash)) {
            pswd = true
        }
        [
                username           : username,
                jobNum             : jobNum,
                pswd               : pswd,
                school             : school,
                campus             : campus,
                schoolCampusListMap: schoolCampusListMap,
                nick               : staff?.nick,
                staff              : staff,
                resultVO           : resultVO,
                avatar             : avatar,
                departmentList     : departmentList,
                bgbCampusRoleVOList: bgbCampusRoleVOList
        ]
    }
}

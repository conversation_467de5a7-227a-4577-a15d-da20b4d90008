package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.Claim
import com.auth0.jwt.interfaces.DecodedJWT
import com.bugu.ResultVO
import hiiadmin.*
import hiiadmin.biz.StaffService
import hiiadmin.module.RemoteRequestParamVO
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import timetabling.user.Staff
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus

import javax.annotation.Resource

class HiiAdminThirdTokenChangeController implements BaseExceptionHandler {

    static Logger logger = LoggerFactory.getLogger(HiiAdminThirdTokenChangeController.class)

    TeacherService teacherService

    RemoteService remoteService

    @Autowired
    BgbApiCore bgbApiCore

    EnvService envService

    @Resource
    PasswordEncoder passwordEncoder

    StaffService staffService

    UserEncodeInfoService userEncodeInfoService

    def save() {

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String token = params.token as String

        JWTVerifier verifier = JWT.require(Algorithm.HMAC256("ydkyyhblsqt")).build()
        DecodedJWT jwtHl = verifier.verify(token)
        Map<String, Claim> claims = jwtHl.getClaims()
        String haiLiangTeacherCode = claims.get("staffId").asString()
        logger.info("解析海亮token,获取code:{}", haiLiangTeacherCode)

        Long campusId = 7l
        if (envService.prod) {
            campusId = 71l
        }
        String username
        String jwt
        Staff staff

        TeacherSchoolCampus teacherSchoolCampus = teacherService.fetchTeacherSchoolCampusByCampusIdAndJobNum(campusId, 'T' + haiLiangTeacherCode)
        if (teacherSchoolCampus) {
            Teacher teacher = teacherService.fetchTeacherById(teacherSchoolCampus.teacherId)
            if (teacher) {
                staff = staffService.fetchStaffByCampusIdAndTeacherIdAndAppId(campusId, teacher.id, ConstantEnum.AppIdEnum.PC.appId)
                if (null == staff) {
                    resultVO.status = 0
                    resultVO.code = BizErrorCode.USER_NO_ASYNC.code
                    resultVO.msg = BizErrorCode.USER_NO_ASYNC.msg
                } else {
                    username = staff.name
                    String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.STAFF.type, staff.id)
                    String fixUserName = mobile + Constants.MULTI_APP_SPLIT + staff.appId + Constants.MULTI_APP_SPLIT + staff.campusId
                    RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
                    ResultVO result = bgbApiCore.fetchToken(requestParamVO)
                    if (result.status == 1) {
                        jwt = result.result.get(Constants.JWT_TOKEN)
                    }
                    logger.info("调用glad获取token staff.id:{},fixUserName:{}, result:{}".toString(), staff.id, fixUserName, JSON.toJSONString(result))
                    response.addHeader(Constants.JWT_TOKEN, jwt)
                }
            } else {
                resultVO.status = 0
                resultVO.code = BizErrorCode.USER_NO_ASYNC.code
                resultVO.msg = BizErrorCode.USER_NO_ASYNC.msg
            }

        } else {
            resultVO.status = 0
            resultVO.code = BizErrorCode.USER_NO_ASYNC.code
            resultVO.msg = BizErrorCode.USER_NO_ASYNC.msg
        }

        [
                jwt     : jwt,
                username: username,
                resultVO: resultVO,
        ]
    }
}

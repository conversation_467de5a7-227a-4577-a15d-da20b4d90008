package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.Claim
import com.auth0.jwt.interfaces.DecodedJWT
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import hiiadmin.*
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.RemoteRequestParamVO
import hiiadmin.module.newMenu.BgbCampusRoleVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PasswordRegex
import hiiadmin.utils.PatternUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import timetabling.CampusCustomMade
import timetabling.Client
import timetabling.Department
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.Staff
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus

import javax.annotation.Resource

@Slf4j
class HiiAdminAuthenticateController implements BaseExceptionHandler {

    @Autowired
    BgbApiCore bgbApiCore

    RemoteService remoteService

    StaffService staffService

    CampusService campusService

    TeacherService teacherService

    DepartmentService departmentService

    Auth3Service auth3Service

    BgbStaffRoleService bgbStaffRoleService

    BgbCampusRoleService bgbCampusRoleService

    SinglePointLoginService singlePointLoginService

    UserEncodeInfoService userEncodeInfoService

    LoginService loginService

    LoginRecordService loginRecordService


    @Resource
    PasswordEncoder passwordEncoder

    def save(String username, String password, boolean rememberMe) {
        String hlToken = params.tpToken as String
        String referer = request.getHeader("Referer")

        log.info("登录 hlToken:{},referer:{},requestURL:{}", hlToken, referer)
        String clientId
        Staff staff
        boolean pswd = false
        String jwt
        School school
        Campus campus
        Client client
        List<BgbCampusRoleVO> bgbCampusRoleVOList
        Map<School, List<Campus>> schoolCampusListMap = Maps.newHashMap()
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String appId = params.appId
        String corpId = params.corpId
        if (StringUtils.isBlank(hlToken)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(username), '请输入用户名')
            Preconditions.checkArgument(StringUtils.isNotBlank(password), '请输入密码')

            String lowercaseLogin = username.toLowerCase(Locale.ENGLISH)

            if (!PatternUtils.isMobile(lowercaseLogin)) {
                throw new HiiAdminException("手机号验证不通过！")
            }

            boolean appAdmin = staffService.appAdminStaff(lowercaseLogin)
            if (appAdmin || StringUtils.equalsIgnoreCase('admin', lowercaseLogin)) {
                appId = Constants.ADMIN_SYS_APP_ID
            } else {
                appId = Constants.ADMIN_USER_APP_ID
            }
            log.info("lowercaseLogin:${lowercaseLogin},appId:${appId},appAdmin:${appAdmin},corpId:${corpId}".toString())

            List<Staff> campusStaffList
            if (appAdmin) {
                staff = Staff.findByLoginAndAppIdAndStatus(lowercaseLogin, appId, 1 as Byte)
            } else {
                Staff staffLastLogin = null
                //todo 兼容中策登入
                if (referer) {
                    Long campusId = singlePointLoginService.getCampusIdByDomainName(referer)
                    if (campusId) {
                        staffLastLogin = staffService.findStaffByCampusIdAndTrueMobile(campusId, lowercaseLogin)
                    }
                }
                //为空的话在缓存中拿
                if (staffLastLogin == null) {
                    Long lastLoginId = staffService.fetchLastLoginStaffId(lowercaseLogin)
                    staffLastLogin = staffService.fetchStaffById(lastLoginId)
                }


                //查询上次登录的缓存用户
                campusStaffList = userEncodeInfoService.fetchAllStaffByTrueMobile(lowercaseLogin)
                if (staffLastLogin) {
                    staff = staffLastLogin
                    schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminMobile(campusStaffList.collect {
                        it.schoolId
                    }, lowercaseLogin)
                } else {
                    if (campusStaffList && campusStaffList.size() > 0) {
                        staff = campusStaffList.find { it.campusId != null }
                        schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminMobile(campusStaffList.collect {
                            it.schoolId
                        }, lowercaseLogin)
                    } else {
                        throw new HiiAdminException("该账号未开通-请联系学校管理员")
                    }
                }
            }
            //如果是在钉钉工作台，则根据corpid确定组织-campus
            if (StringUtils.isNotBlank(corpId)) {
                log.info("ding corp login find campus and staff,corpid:{},username:{}", corpId, username)
                campus = campusService.findCampusByDingCorpId(corpId)
                if (campus) {
                    staff = userEncodeInfoService.fetchAllStaffByTrueMobile(lowercaseLogin)?.find { it.campusId == campus.id }
                }
            }
            log.info("staff id:${staff?.id}".toString())
            if (!staff) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.NO_USER_ERROR.code
                resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
            } else {

                loginRecordService.loginCheckAndCheckPassword(password, staff, lowercaseLogin)
                if (!staff.activated) {
                    resultVO.status = 0
                    resultVO.code = BizErrorCode.USER_NOT_ACTIVE_ERROR.code
                    resultVO.msg = BizErrorCode.USER_NOT_ACTIVE_ERROR.msg
                } else {
                    staffService.recordLastLoginStaffId(lowercaseLogin, staff.id)
                    Teacher teacher = userEncodeInfoService.fetchTeacherByTrueMobile(username)
                    if (teacher && !staff.teacherId) {
                        staff.teacherId = teacher.id
                        staff.save(failOnError: true)
                    }

                    List<BgbStaffRole> bgbStaffRoleList = bgbStaffRoleService.fetchBgbStaffRoleByStaffId(staff.id)
                    if (bgbStaffRoleList?.size() < 1) {
                        resultVO.status = 0
                        resultVO.code = BizErrorCode.USER_NO_ROLE.code
                        resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
                    } else {
                        List<Long> roleIdList = bgbStaffRoleList*.roleId
                        bgbCampusRoleVOList = bgbCampusRoleService.fetchBgbCampusRoleVOByIdList(roleIdList)
                    }
                    RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
                    ResultVO result = bgbApiCore.fetchToken(requestParamVO)
                    if (result.status == 1) {
                        jwt = result.result.get(Constants.JWT_TOKEN)
                        log.info("调用glad获取token staff.id:${staff.id},fixUserName:${}fixUserName, result:${JSON.toJSONString(result)}".toString())
                        response.addHeader(Constants.JWT_TOKEN, jwt)
                        if (staff.schoolId) {
                            school = School.get(staff.schoolId)
                        }
                        if (staff.campusId) {
                            campus = Campus.get(staff.campusId)
                        }
                    } else {
                        resultVO.status = 0
                        resultVO.msg = result.msg
                        resultVO.code = result.code
                        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                        return
                    }
                }
            }
            if (null == staff) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.USER_NO_ROLE.code
                resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
            } else {
                if (StringUtils.isNotBlank(staff.passwordHash)) {
                    pswd = true
                }
                //处理SSO相关
                clientId = params.clientId
                if (StringUtils.isNotBlank(clientId)) {
                    client = Client.findByClientId(clientId)
                    String auth3Token = auth3Service.genThirdPartToken(client, staff)
                    resultVO.result.put("o3Token", auth3Token)
                    resultVO.result.put("client", client)
                }
            }
        } else {
            //联合登录token解析
            appId = Constants.ADMIN_USER_APP_ID
            log.info("海亮联合登录token:${hlToken}".toString())
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256("abxdtr")).build()
            DecodedJWT jwtHl = verifier.verify(hlToken)
            Map<String, Claim> claims = jwtHl.getClaims()
            String haiLiangStaffId = claims.get("staffId").asString()
            def aResult = staffService.asyncHaiLiangUserReturnStaff(haiLiangStaffId)
            staff = staffService.findStaffByTrueMobileAndCodeAndAppId(aResult.mobile, haiLiangStaffId, appId)
            if (null == staff) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.USER_NO_ASYNC.code
                resultVO.msg = BizErrorCode.USER_NO_ASYNC.msg
            } else {
                String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.STAFF.type, staff.id)
                username = mobile
                pswd = true//联合登录不提示密码
                if (staff.schoolId) {
                    school = School.get(staff.schoolId)
                }
                if (staff.campusId) {
                    campus = Campus.get(staff.campusId)
                }

                List<BgbStaffRole> bgbStaffRoleList = bgbStaffRoleService.fetchBgbStaffRoleByStaffId(staff.id)
                if (bgbStaffRoleList?.size() < 1) {
                    resultVO.status = 0
                    resultVO.code = BizErrorCode.USER_NO_ROLE.code
                    resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
                } else {
                    List<Long> roleIdList = bgbStaffRoleList*.roleId
                    bgbCampusRoleVOList = bgbCampusRoleService.fetchBgbCampusRoleVOByIdList(roleIdList)
                }
                String fixUserName = mobile + Constants.MULTI_APP_SPLIT + staff.appId + Constants.MULTI_APP_SPLIT + staff.campusId
                RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
                ResultVO result = bgbApiCore.fetchToken(requestParamVO)
                if (result.status == 1) {
                    jwt = result.result.get(Constants.JWT_TOKEN)
                } else {
                    log.info("海亮登录，调用glad获取token识别,mobile:${aResult?.mobile},code:${aResult?.code},staffId:${staff?.id}")
                    resultVO.status = 0
                    resultVO.msg = result.msg
                    resultVO.code = result.code
                    render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                    return
                }
                log.info("调用glad获取token mobile:${aResult?.mobile},staff.id:${staff.id},fixUserName:${fixUserName}, result:${JSON.toJSONString(result)}".toString())
                response.addHeader(Constants.JWT_TOKEN, jwt)
                List<Staff> campusStaffList
                campusStaffList = userEncodeInfoService.fetchAllStaffByTrueMobile(mobile)
                if (campusStaffList && campusStaffList.size() > 0) {
                    schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminMobile(campusStaffList.collect {
                        it.schoolId
                    }, mobile)
                } else {
                    throw new HiiAdminException("该账号未开通-请联系学校管理员")
                }
            }
        }

        String avatar = null
        List<Department> departmentList
        if (staff) {
            Teacher teacher = teacherService.fetchTeacherById(staff.teacherId)
            departmentList = departmentService.findDepartmentByTeacherIdAndCampusId(teacher?.id, staff.campusId)
            avatar = teacher?.avatar
        }

        TeacherSchoolCampus teacherSchoolCampus = teacherService.fetchTeacherSchoolByCampusIdAndTeacherId(campus?.id, staff?.teacherId, false)
        String jobNum = teacherSchoolCampus?.jobNum

        //绑定钉钉userid
        String authCode = params.authCode
        if (StringUtils.isNotBlank(corpId) && StringUtils.isNotBlank(authCode)) {
            staffService.bindDingUser(staff, appId, corpId, authCode, params.long("dingAppId"))
        }

        //TODO  迁移到service
        CampusCustomMade actionHide = CampusCustomMade.findByCampusIdAndTypeAndStatus(campus?.id, 1, 1)
        List<Long> supervisorIdList = departmentService.findDepartmentSupervisorByTeacherId(campus?.id, staff?.teacherId, campus?.type)

        [
                jwt                : jwt,
                clientId           : clientId,
                client             : client,
                username           : username,
                jobNum             : jobNum,
                pswd               : pswd,
                school             : school,
                campus             : campus,
                schoolCampusListMap: schoolCampusListMap,
                staff              : staff,
                resultVO           : resultVO,
                avatar             : avatar,
                departmentList     : departmentList,
                bgbCampusRoleVOList: bgbCampusRoleVOList,
                customMade         : actionHide?.customMade,
                supervisorIdList   : supervisorIdList
        ]
    }

    def update() {
        String password = params.password
        String mobile = params.mobile
        String code = params.code
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Staff staff

        if (!PasswordRegex.checkPass(password)) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.PASSWORD_NOT_VALID_ERROR.code
            resultVO.msg = BizErrorCode.PASSWORD_NOT_VALID_ERROR.msg
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        } else {
            if (loginRecordService.checkResetPassword(mobile, code)) {
                staff = userEncodeInfoService.fetchStaffByTrueMobile(mobile)
            } else {
                resultVO.status = 0
                resultVO.code = BizErrorCode.CHECK_NO_ERROR.code
                resultVO.msg = BizErrorCode.CHECK_NO_ERROR.msg
                render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
                return
            }
        }
        if (null == staff) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.NO_USER_ERROR.code
            resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
        } else {
            //找回密码时候，对于多个校到同一个手机号，都更新密码，对于用户来说是一个可见账户
            String passwordHash = passwordEncoder.encode(password)
            List<Staff> staffList = staffService.findSameMobileStaffListByStaff(staff)
            teacherService.updateTeacherPassword(staff.teacherId, passwordHash)
            staffService.updateStaffPassword(staffList, passwordHash)
        }

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        String userName = params.userName
        String password = params.password
        resultVO = loginService.login4UserInfo(userName, password)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }
}

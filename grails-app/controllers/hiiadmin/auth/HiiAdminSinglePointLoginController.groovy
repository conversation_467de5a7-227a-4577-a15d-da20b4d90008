package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.ToStringUnits

class HiiAdminSinglePointLoginController {
    SinglePointLoginService singlePointLoginService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as long
        Long userId = request.getAttribute("userId") as Long
        Byte type = params.byte("type") as Byte
        String referer = request.getHeader("Referer")
        String url = ToStringUnits.urlTransformationReferer(referer)
        log.info("[singlePointLogin]campusId@${campusId},type@${type},referer@${referer}".toString())
        ServiceResult<TeacherVO> serviceResult = singlePointLoginService.singlePointLogin(campusId, userId, type, url)
        if (serviceResult.success) {
            resultVO.result = ObjectUtils.resultVOObject2Map(serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.code = serviceResult.code as int
            resultVO.message = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

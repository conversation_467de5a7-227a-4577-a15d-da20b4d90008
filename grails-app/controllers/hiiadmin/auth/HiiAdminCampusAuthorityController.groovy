package hiiadmin.auth

import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import timetabling.Authority

class HiiAdminCampusAuthorityController implements BaseExceptionHandler {

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<Authority> authorityList = Authority.findAllByCampusIdAndStatus(campusId, 9 as byte)
        [list: authorityList]
    }
}

package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.StaffService
import hiiadmin.bureau.BureauService
import hiiadmin.module.bugu.BureauVO
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.ObjectUtils
import timetabling.org.Bureau
import timetabling.user.Staff

import static com.bugu.PhoenixCoder.decodeId
import static hiiadmin.ConstantEnum.UserEncodeInfoType
import static hiiadmin.ConstantEnum.UserTypeEnum

/**
 * 教育局列表
 */
@Deprecated
@Slf4j
class AuthBureauController implements BaseExceptionHandler {

    BureauService bureauService

    StaffService staffService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        List<Bureau> bureauList = bureauService.fetchAllBureau(p, s)
        List<BureauVO> list = bureauList.collect { bureau ->
            BureauVO vo = new BureauVO().buildVO(bureau)
            Staff staff = staffService.fetchStaffByBureauId(bureau.id)
            String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.STAFF.type, staff.id)
            vo.authMobile = mobile
            vo.authName = staff.name
            vo
        }
        list?.sort {
            -it.lastUpdated
        }
        Integer total = bureauService.countBureau()
        resultVO.result.put('list', list)
        resultVO.result.put('total', total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String authMobile = params.authMobile
        Staff staff = staffService.fetchBureauStaffByTrueMobile(authMobile)
        if (staff) {
            resultVO.status = 0 as byte
            resultVO.code = 100
            resultVO.msg = "教育局账号已存在"
        } else {
            String bureauName = params.bureauName
            String cityStr = params.cityId
            String provinceStr = params.provinceId
            Long cityId = decodeId(cityStr)
            Long provinceId = decodeId(provinceStr)
            Bureau bureau = bureauService.createAndSaveBureau(bureauName, provinceId, cityId)
            String authName = params.authName
            Staff staff1 = staffService.createBureauStaff(authMobile, authName, bureau.id)
            BureauVO vo = new BureauVO().buildVO(bureau)
            String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.STAFF.type, staff1.id)
            vo.authMobile = mobile
            vo.authName = staff1.name
            resultVO.result.putAll(ObjectUtils.objectToMap(vo))
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String bureauIdStr = params.id
        Long bureauId = decodeId(bureauIdStr)
        String name = params.bureauName
        bureauService.changeBureauName(bureauId, name)
        resultVO.result.put('id', bureauIdStr)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

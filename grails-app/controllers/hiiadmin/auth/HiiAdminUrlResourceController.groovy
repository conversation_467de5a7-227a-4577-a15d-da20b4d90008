package hiiadmin.auth

import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.Constants
import org.apache.commons.lang3.StringUtils
import timetabling.UrlResources

class HiiAdminUrlResourceController implements BaseExceptionHandler {

    def index() {
        String appId = params.appId
        if (StringUtils.isBlank(appId)) {
            appId = Constants.ADMIN_SYS_APP_ID
        }
        def c = UrlResources.createCriteria()

        int p = params.int('p', 1)  // 分页, 从0开始
        int s = params.int('s', 10)

        def urlResources = c.list(max: s, offset: (p - 1) * s) {
            if (params.status) {
                eq('status', params.byte('status'))
            }
            eq('appId', appId)
            order("id", "desc")
        }

        [
                urlResources: urlResources,
                total       : urlResources?.totalCount,

        ]
    }

    def show() {
        Preconditions.checkNotNull(params.id, "id 不存在")

        UrlResources resources = UrlResources.findById(params.long('id'))

        Preconditions.checkNotNull(resources)

        [
                resources: resources
        ]
    }

    def update() {
        Preconditions.checkNotNull(params.id, "id 不存在")

        UrlResources resources = UrlResources.findById(params.long('id'))

        Preconditions.checkNotNull(resources)

        resources.description = params.description
        resources.category = params.category
        resources.status = params.byte("status", 0 as byte)

        resources.save(failOnError: true)

        [resources: resources]
    }
}

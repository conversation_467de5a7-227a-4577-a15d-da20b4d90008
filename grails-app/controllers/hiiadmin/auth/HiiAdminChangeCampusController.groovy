package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.CampusService
import org.springframework.beans.factory.annotation.Autowired
import timetabling.CampusCustomMade

@Deprecated
class HiiAdminChangeCampusController implements BaseExceptionHandler {

    @Autowired
    BgbApiCore bgbApiCore

    CampusService campusService

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long staffId = request.getAttribute('staffId') as Long
        Long campusId = params.long('id')
        ResultVO result = bgbApiCore.changeCampus(staffId, campusId)
//TODO  迁移到service
        CampusCustomMade actionHide = CampusCustomMade.findByCampusIdAndTypeAndStatus(campusId, 1, 1)

        if (result.status == 1) {
            resultVO.result = result.result.get('result')
            if (actionHide) {
                resultVO.result.put("customMade", JSON.parseObject(actionHide.customMade))
            }
            String corpId = campusService.fetchCampusById(campusId)?.corpId
            resultVO.result.put("corpId", corpId)
        } else {
//            resultVO.status = 0
//            resultVO.code = 80001
//            resultVO.msg = '切换校区出错'
            // glad会返回错误码和信息
            resultVO.result = result.result.get("result")
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

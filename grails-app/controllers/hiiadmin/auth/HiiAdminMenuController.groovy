package hiiadmin.auth

import com.google.common.base.Preconditions
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.Constants
import org.apache.commons.lang3.StringUtils
import timetabling.Menu

@Deprecated
class HiiAdminMenuController implements BaseExceptionHandler {

    def index() {
        String appId = params.appId
        Long schoolId = params.long('schoolId', 0l)
        int level = params.int('level', 1)
        if (StringUtils.isBlank(appId)) {
            appId = Constants.ADMIN_SYS_APP_ID
        }
        List<Long> parentIdListLong = Lists.newArrayList()
        String parentIds = params.parentId
        if (params.parentId) {
            List<String> parentIdList = Lists.newArrayList(parentIds.split(","))
            parentIdList.each {
                parentIdListLong.add(Long.valueOf(it))
            }
        }
        def c = Menu.createCriteria()

        int p = params.int('p', 1)  // 分页, 从0开始
        int s = params.int('s', 10)

        def menus = c.list(max: s, offset: (p - 1) * s) {
            eq('appId', appId)
            if (params.level) {
                eq('level', level)
            }
            if (params.parentId) {
                'in'("parentId", parentIdListLong)
            }
            if (params.schoolId) {
                eq('schoolId', schoolId)
            }

            order("id", "desc")
        }
        Map<Long, List<Menu>> parentIdMenuListMap = Maps.newHashMap()
        menus?.each {
            Menu menu ->
                List<Menu> menuList = Menu.findAllByParentId(menu.id)
                parentIdMenuListMap.put(menu.id, menuList)

        }

        [
                list               : menus,
                parentIdMenuListMap: parentIdMenuListMap,
                total              : menus?.totalCount,
        ]
    }

    def save() {

        Preconditions.checkNotNull(params.controllerName, 'controllerName 不能为空')

        String appId = params.appId
        if (StringUtils.isBlank(appId)) {
            appId = Constants.ADMIN_SYS_APP_ID
        }
        Menu pMenu = Menu.findById(params.long('parentId'))
        Menu menu = new Menu()
        menu.properties = params
        menu.appId = appId
        menu.parentId = params.long('parentId')
        menu.level = (pMenu?.level ?: 0) + 1
        menu.weight = params.int('weight', 0)
        menu.schoolId = params.long('schoolId')
        menu.type = params.byte('type', ConstantEnum.MenuType.INNER.type)
        menu.actionMask = params.long('actionMask', 0l)
        menu.dateCreated = new Date()
        menu.lastUpdated = new Date()
        menu.save(failOnError: true)

        [menu: menu]
    }

    def update() {

        Preconditions.checkNotNull(params.id,)

        Menu menu = Menu.findById(params.long('id'))

        Preconditions.checkNotNull(menu, "不存在" + params.id + "的菜单")

        menu.properties = params

        menu.lastUpdated = new Date()

        menu.save(failOnError: true)

        [menu: menu]


    }

    def delete() {
        Preconditions.checkNotNull(params.id,)

        Menu menu = Menu.findById(params.long('id'))

        Preconditions.checkNotNull(menu, "不存在" + params.id + "的菜单")

        menu.delete()

        [menu: menu]
    }
}

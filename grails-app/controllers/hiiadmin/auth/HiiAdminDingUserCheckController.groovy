package hiiadmin.auth

import com.alibaba.fastjson2.JSON
import com.bugu.BaseResult
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import hiiadmin.*
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.dingding.DingDingService
import hiiadmin.exceptions.Asserts
import hiiadmin.module.RemoteRequestParamVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import timetabling.Department
import timetabling.newMenu.BgbCampusRole
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.Staff
import timetabling.user.Teacher

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*

/**
 * 钉钉工作台登录*/
class HiiAdminDingUserCheckController implements BaseExceptionHandler {

    static Logger logger = LoggerFactory.getLogger(HiiAdminDingUserCheckController.class)

    @Resource
    BgbApiCore bgbApiCore

    StaffService staffService

    TeacherService teacherService

    DingDingService dingDingService

    CampusService campusService

    EnvService envService

    RemoteService remoteService

    DepartmentService departmentService

    BgbStaffRoleService bgbStaffRoleService

    BgbCampusRoleService bgbCampusRoleService

    UserEncodeInfoService userEncodeInfoService

    UserService userService

    def index() {
        String corpId = params.corpId
        String authCode = params.authCode
        Long dingAppId = params.long("appId")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Staff staff
        boolean pswd = false
        String jwt
        School school
        Campus campus
        List<BgbCampusRole> bgbCampusRoleList
        List<BgbStaffRole> bgbStaffRoleList
        Multimap<School, Campus> schoolCampusMultimap
        String dingUserId
        String dingAccessToken

        String mobile
        String dingUnionId
        Boolean hasMobile = false
        Boolean needDingAuthorize = campusService.fetchAllNeedDingAuthorize().contains(corpId)
        campus = campusService.findCampusByDingCorpId(corpId)
        // 如果dingAppId为空，则默认使用布谷智慧校园的appId
        if (ObjectUtils.isEmpty(dingAppId)) {
            if (envService.dev) {
                dingAppId = DingDing_APP_MAP.ZHIHUIXIAOYUAN_DAILY.appId
            } else {
                dingAppId = DingDing_APP_MAP.ZHIHUIXIAOYUAN_ONLINE.appId
            }
        }
        BaseResult baseResult = dingDingService.fetchDingAccessTokenV2(corpId, dingAppId)

        if (baseResult.success) {
            dingAccessToken = baseResult.result
        } else {
            resultVO.status = 1
            resultVO.code = baseResult.code
            resultVO.message = baseResult.message
            resultVO.msg = baseResult.message
            campus = campusService.findCampusByDingCorpId(corpId)
            resultVO.result.put('pcLoginPic', campus?.pcLoginPic)
            resultVO.result.put('pcDomain', campus?.pcDomain)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }
        BaseResult<Map<String, String>> dingUserBaseResult = dingDingService.fetchDingUserId(authCode, dingAccessToken)
        if (dingUserBaseResult.success) {
            Map<String, String> dingMap = dingUserBaseResult.result
            dingUserId = dingMap.get("dingUserId")
            dingUnionId = dingMap.get("dingUnionId")
        } else {
            resultVO.status = 1
            resultVO.code = dingUserBaseResult.code
            resultVO.message = dingUserBaseResult.message
            resultVO.msg = dingUserBaseResult.message
            resultVO.result.put('pcLoginPic', campus?.pcLoginPic)
            resultVO.result.put('pcDomain', campus?.pcDomain)
            resultVO.result.put('dingPcLoginPic', campus?.dingPcLoginPic)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }
        if (StringUtils.isNotBlank(dingUserId)) {

            List<Staff> campusStaffList = staffService.findAllStaffByDingCoprIdAndDingUserId(corpId, dingUserId)

            if (campusStaffList && campusStaffList.size() > 0) {
                staff = campusStaffList.find { it.campusId != null }

            }

            //查询上次登录的缓存用户
            Long id = staffService.getLastLoginStaffIdByDingUserId(dingUserId)
            if (id && campusStaffList.collect { it.id }.contains(id)) {
                staff = staffService.fetchStaffById(id)
                if (campus && staff.campusId != campus?.id) {
                    staff = campusStaffList.find { it.campusId == campus?.id }
                }
            }

            //用户未绑定过返回背景图即可
            if (!staff) {
                resultVO.status = 1
                resultVO.code = BizErrorCode.NO_USER_ERROR.code
                resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
                resultVO.result.put('dingPcLoginPic', campus?.dingPcLoginPic)
                render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
                return
            } else {
                Asserts.assertTrue(staff.activated, BizErrorCode.USER_NOT_ACTIVE_ERROR)
                schoolCampusMultimap = campusService.findSchoolCampusMultimapBySchoolIdInListAndTeacherId(campusStaffList.collect {
                    it.schoolId
                }, staff.teacherId)

                mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.STAFF.type, staff.id)
                staffService.recordLastLoginStaffIdByDingUserId(dingUserId, staff.id)
                if (mobile) {
                    String fixUserName = mobile + Constants.MULTI_APP_SPLIT + staff.appId + Constants.MULTI_APP_SPLIT + staff.campusId
                    Teacher teacher = userEncodeInfoService.fetchTeacherByTrueMobile(mobile)
                    if (teacher && !staff.teacherId) {
                        staff.teacherId = teacher.id
                        staff.save(failOnError: true)
                    }

                    bgbStaffRoleList = bgbStaffRoleService.fetchRoleByStaffId(staff.id)
                    if (bgbStaffRoleList?.size() < 1) {
                        resultVO.status = 0
                        resultVO.code = BizErrorCode.USER_NO_ROLE.code
                        resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
                    } else {
                        List<Long> roleIdList = bgbStaffRoleList*.roleId
                        bgbCampusRoleList = bgbCampusRoleService.fetchAllCampusRoleByIdList(roleIdList)
                    }
                    RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
                    ResultVO result = bgbApiCore.fetchToken(requestParamVO)
                    if (result.status == 1) {
                        jwt = result.result.get(Constants.JWT_TOKEN)
                    }
                    logger.info("调用glad获取token staff.id:{},fixUserName:{}, result:{}".toString(), staff.id, fixUserName, JSON.toJSONString(result))
                    response.addHeader(Constants.JWT_TOKEN, jwt)

                    if (staff.schoolId) {
                        school = School.get(staff.schoolId)
                    }
                    if (staff.campusId) {
                        campus = Campus.get(staff.campusId)
                    }
                }
                Asserts.assertNotNull(staff, BizErrorCode.USER_NO_ROLE)
                if (StringUtils.isNotBlank(staff.passwordHash)) {
                    pswd = true
                }
            }
        } else {
            resultVO.status = 0
        }

        String avatar = null
        List<Department> departmentList
        if (staff) {
            Teacher teacher = teacherService.fetchTeacherById(staff.teacherId)
            if (teacher) {
                userService.updateDingUserByDingLogin(teacher.id, campus?.id, corpId, dingUserId, dingUnionId)
            }
            departmentList = departmentService.findDepartmentByTeacherIdAndCampusId(teacher?.id, staff.campusId)
            avatar = teacher?.avatar
        } else {
            resultVO.status = 1
            resultVO.code = BizErrorCode.DING_PC_NO_BIND_ERROR.code
            resultVO.msg = BizErrorCode.DING_PC_NO_BIND_ERROR.msg
        }

        hasMobile = StringUtils.isNotEmpty(mobile)

        [jwt                 : jwt,
         username            : mobile,
         pswd                : pswd,
         id                  : staff?.id,
         school              : school,
         campus              : campus,
         schoolCampusMultimap: schoolCampusMultimap,
         nick                : staff?.nick,
         staff               : staff,
         resultVO            : resultVO,
         avatar              : avatar,
         departmentList      : departmentList,
         bgbCampusRoleList   : bgbCampusRoleList,
         needDingAuthorize   : needDingAuthorize,
         hasMobile           : hasMobile,
         dingUserId          : dingUserId,
         dingUnionId         : dingUnionId,
         corpId              : corpId]
    }
}

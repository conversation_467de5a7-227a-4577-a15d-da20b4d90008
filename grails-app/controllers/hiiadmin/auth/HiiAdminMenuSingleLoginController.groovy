package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.Auth3Service
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.login.UserSingleLoginInfoDTO
import hiiadmin.utils.ObjectUtils


class HiiAdminMenuSingleLoginController implements BaseExceptionHandler {

    Auth3Service auth3Service

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusMenuId = params.long("campusMenuId")
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Integer loginUrlType = params.int("loginUrlType", ConstantEnum.LoginUrlType.DEFAULT.type)
        ServiceResult<UserSingleLoginInfoDTO> serviceResult = auth3Service.campusSingleLoginByUserIdAndCampusMenuId(campusMenuId, campusId, userId, loginUrlType)
        if (serviceResult.success) {
            resultVO.result = ObjectUtils.resultVOObject2Map(serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

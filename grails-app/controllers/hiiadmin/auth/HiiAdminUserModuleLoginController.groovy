package hiiadmin.auth

import com.alibaba.fastjson2.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.userModule.TokenUserModuleProvider
import hiiadmin.userModule.UserTokenBO
import hiiadmin.utils.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Value

@Slf4j
class HiiAdminUserModuleLoginController implements BaseExceptionHandler {
    
    UserModuleLoginService userModuleLoginService
    
    @Value('${user-module.loginTest}')
    private Boolean openTestLogin
    
    @Value('${user-module.url}')
    private String usermoduleUrl

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String jwt_token = ''
        String json = request.JSON
        if (StringUtils.isNotBlank(json)) {
            jwt_token = JSON.parseObject(json).getString("authorization")
        }
        if (StringUtils.isBlank(jwt_token)) {
            jwt_token = params.authorization
        }
        
        if (StringUtils.isBlank(jwt_token)) {
            jwt_token = request.getHeader("authorization")
        }
        log.info("from remote token:${jwt_token}".toString())
        
        if (StringUtils.isBlank(jwt_token)) {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = '登录过期'
            render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
            return 
        }
        
        jwt_token = TokenUserModuleProvider.resolveToken(jwt_token)
        
        boolean validate = TokenUserModuleProvider.validateToken(jwt_token)
        
        if (validate) {
            resultVO.status = 0
            resultVO.code = 100
            resultVO.message = '登录过期'
            render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
            return
        }
        
//        if (openTestLogin) {
//            def object = new JSONObject()
//            object.put('token', jwt_token)
//            String result = HttpUtil.doGet(usermoduleUrl, object)
//            JSONObject resultObject = JSON.parseObject(result)
//            if (resultObject.getInteger('status') == 1) {
//                jwt_token = resultObject.getJSONObject('result').getString('token')
//            } else {
//                resultVO.status = 0
//                resultVO.code = resultObject.getInteger('code')
//                resultVO.message = resultObject.getString('message')
//                render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
//                return 
//            }
//        }

        UserTokenBO userTokenBO = TokenUserModuleProvider.checkAuthentication(jwt_token)
        ServiceResult serviceResult = userModuleLoginService.checkUserModuleUser(userTokenBO.userId, userTokenBO.organizationId, userTokenBO.userType)
        if (serviceResult.success) {
            resultVO.result = ObjectUtils.resultVOObject2Map(serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.code = serviceResult.code?.toInteger()
            resultVO.message = serviceResult.message
        }
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

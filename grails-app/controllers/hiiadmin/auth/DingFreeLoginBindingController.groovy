package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.BaseResult
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import hiiadmin.*
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.dingding.ApiDingUserMobileService
import hiiadmin.dingding.DingDingService
import hiiadmin.exceptions.Asserts
import hiiadmin.module.RemoteRequestParamVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.school.org.feature.UnitTeacherService
import hiiadmin.school.repository.RepositoryTeacherSchoolCampusService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import timetabling.Department
import timetabling.StaffDepartment
import timetabling.newMenu.BgbCampusRole
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.School
import timetabling.org.UnitTeacher
import timetabling.user.*

import javax.annotation.Resource

import static hiiadmin.ConstantEnum.*

class DingFreeLoginBindingController implements BaseExceptionHandler {

    static Logger logger = LoggerFactory.getLogger(DingFreeLoginBindingController.class)

    @Resource
    BgbApiCore bgbApiCore

    StaffService staffService

    TeacherService teacherService

    DingDingService dingDingService

    CampusService campusService

    EnvService envService

    RemoteService remoteService

    DepartmentService departmentService

    BgbStaffRoleService bgbStaffRoleService

    BgbCampusRoleService bgbCampusRoleService

    UserEncodeInfoService userEncodeInfoService

    ApiDingUserMobileService apiDingUserMobileService

    UserService userService

    RepositoryTeacherSchoolCampusService repositoryTeacherSchoolCampusService

    UnitTeacherService unitTeacherService

    UserWxDingService userWxDingService

    def save() {

        String dingUserId = params.dingUserId
        String dingUnionId = params.dingUnionid
        String corpId = params.corpId
        String authCode = params.authCode
        Long dingAppId = params.long('appId')
        ResultVO resultVO = ResultVO.success()
        Staff staff
        boolean pswd = false
        String jwt
        School school
        Campus campus
        List<BgbCampusRole> bgbCampusRoleList
        List<BgbStaffRole> bgbStaffRoleList
        Multimap<School, Campus> schoolCampusMultimap
        String dingAccessToken

        String mobile = apiDingUserMobileService.getUserMobile(authCode, dingAppId, corpId)
        campus = campusService.findCampusByDingCorpId(corpId)
        // 如果dingAppId为空，则默认使用布谷智慧校园的appId
        if (ObjectUtils.isEmpty(dingAppId)) {
            if (envService.dev) {
                dingAppId = DingDing_APP_MAP.ZHIHUIXIAOYUAN_DAILY.appId
            } else {
                dingAppId = DingDing_APP_MAP.ZHIHUIXIAOYUAN_ONLINE.appId
            }
        }
        BaseResult baseResult = dingDingService.fetchDingAccessTokenV2(corpId, dingAppId)

        if (baseResult.success) {
            dingAccessToken = baseResult.result
        } else {
            resultVO.status = 1
            resultVO.code = baseResult.code
            resultVO.message = baseResult.message
            resultVO.msg = baseResult.message
            campus = campusService.findCampusByDingCorpId(corpId)
            resultVO.result.put('pcLoginPic', campus?.pcLoginPic)
            resultVO.result.put('pcDomain', campus?.pcDomain)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        }

        List<Staff> campusStaffList = staffService.findAllStaffByDingCoprIdAndDingUserIdV2(corpId, dingUserId)
        if (campusStaffList && campusStaffList.size() > 0) {
            staff = campusStaffList.find { it.campusId == campus.id }
        }


        //查询上次登录的缓存用户
//        Long id = staffService.getLastLoginStaffIdByDingUserId(dingUserId)
//        if (id && campusStaffList.collect { it.id }.contains(id)) {
//            staff = staffService.fetchStaffById(id)
//            if (campus && staff.campusId != campus?.id) {
//                staff = campusStaffList.find { it.campusId == campus?.id }
//            }
//        }

        //用户未绑定过返回背景图即可
        if (!staff) {
            resultVO.status = 1
            resultVO.code = BizErrorCode.NO_USER_ERROR.code
            resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
            resultVO.result.put('dingPcLoginPic', campus?.dingPcLoginPic)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
            return
        } else {
            Asserts.assertTrue(staff.activated, BizErrorCode.USER_NOT_ACTIVE_ERROR)

            String userMobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(UserEncodeInfoType.MOBILE.type, UserTypeEnum.STAFF.type, staff.id)
            if (!userMobile) {
                userEncodeInfoService.saveUserEncodeInfo(mobile, UserTypeEnum.STAFF.type, staff.id, UserEncodeInfoType.MOBILE.type)
            }
            staffService.recordLastLoginStaffIdByDingUserId(dingUserId, staff.id)
            String fixUserName = mobile + Constants.MULTI_APP_SPLIT + staff.appId + Constants.MULTI_APP_SPLIT + staff.campusId
            Teacher finallyTeacher = userEncodeInfoService.fetchTeacherByTrueMobile(mobile)
            if (!finallyTeacher) {
                finallyTeacher = teacherService.fetchTeacherById(staff.teacherId, false)
                Asserts.assertNotNull(finallyTeacher, BizErrorCode.NO_USER_ERROR)
                userEncodeInfoService.saveUserEncodeInfo(mobile, UserTypeEnum.TEACHER.type, finallyTeacher.id, UserEncodeInfoType.MOBILE.type)
                logger.info("用户通过钉钉授权的userid和手机号进行绑定, dingUserId:${dingUserId},mobile:${mobile},staff.id:${staff?.id}")
            } else if (staff.teacherId != finallyTeacher.id) {
                User dingUser = userService.fetchTeacherUserByUId(staff.teacherId)
//                根据dingUserId查询出来的staff.teacherId
                Teacher repeatTeacher = teacherService.fetchTeacherById(staff.teacherId, false)
                finallyTeacher.unionCode = repeatTeacher.unionCode
                finallyTeacher.syncType = repeatTeacher.syncType
                teacherService.saveTeacher(finallyTeacher)
                repeatTeacher.status = 0
                teacherService.saveTeacher(repeatTeacher)

                //                     获取基准user信息
                User finallyUser = userService.fetchTeacherUserByUId(finallyTeacher.id)

//                    订正钉钉同步的TeacherSchoolCampus数据
                TeacherSchoolCampus modifyTSC = repositoryTeacherSchoolCampusService.fetchTeacherSchoolCampusByCampusIdAndTeacherId(campus.id, repeatTeacher.id)
                if (modifyTSC) {
                    modifyTSC.teacherId = finallyTeacher.id
                    repositoryTeacherSchoolCampusService.saveTeacherSchoolCampus(modifyTSC)
                }
//                    订正钉钉同步的StaffDepartment数据
                List<StaffDepartment> staffDepartments = departmentService.fetchAllStaffDepartmentByCampusIdAndTeacherId(campus.id, repeatTeacher.id)
                if (staffDepartments) {
                    staffDepartments*.teacherId = finallyTeacher.id
                    departmentService.saveAllStaffDepartment(staffDepartments)
                }
//                    订正钉钉同步的UnitTeacher数据
                List<UnitTeacher> unitTeacherList = unitTeacherService.fetchAllUnitTeacherByCampusIdAndTeacherId(campus.id, repeatTeacher.id)
                if (unitTeacherList) {
                    unitTeacherList*.teacherId = finallyTeacher.id
                    unitTeacherService.saveAllUnitTeacher(unitTeacherList)
                }
//                    订正钉钉同步的Staff数据
                List<Staff> staffList = staffService.fetchAllStaffByTeacherId(campus.id, repeatTeacher.id)
                if (staffList) {
                    staffList*.teacherId = finallyTeacher.id
                    staffService.saveAllStaff(staffList)
                    staff = staffList.find { it.appId = "rejmgk8s" }
                }
//                    订正钉钉同步的UserWxDing数据
                UserWxDing userWxDing = userWxDingService.fetchUserWxDingByUserIdAndUserType(dingUser.id, UserTypeEnum.TEACHER.type)
                userWxDing.userId = finallyUser.id
                userWxDingService.saveUserWxDing(userWxDing)

            }

//            TODO
            schoolCampusMultimap = campusService.findSchoolCampusMultimapBySchoolIdInListAndTeacherId(campusStaffList.collect {
                it.schoolId
            }, staff.teacherId)

            if (finallyTeacher && !staff.teacherId) {
                staff.teacherId = finallyTeacher.id
                staff.save(failOnError: true)
            }

            bgbStaffRoleList = bgbStaffRoleService.fetchRoleByStaffId(staff.id)
            if (bgbStaffRoleList?.size() < 1) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.USER_NO_ROLE.code
                resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
            } else {
                List<Long> roleIdList = bgbStaffRoleList*.roleId
                bgbCampusRoleList = bgbCampusRoleService.fetchAllCampusRoleByIdList(roleIdList)
            }
            RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
            ResultVO result = bgbApiCore.fetchToken(requestParamVO)
            if (result.status == 1) {
                jwt = result.result.get(Constants.JWT_TOKEN)
            }
            logger.info("调用glad获取token staff.id:{},fixUserName:{}, result:{}".toString(), staff.id, fixUserName, JSON.toJSONString(result))
            response.addHeader(Constants.JWT_TOKEN, jwt)

            if (staff.schoolId) {
                school = School.get(staff.schoolId)
            }
            if (staff.campusId) {
                campus = Campus.get(staff.campusId)
            }
        }
        Asserts.assertNotNull(staff, BizErrorCode.USER_NO_ROLE)
        if (StringUtils.isNotBlank(staff.passwordHash)) {
            pswd = true
        }

        String avatar = null
        List<Department> departmentList
        if (staff) {
            Teacher teacher = teacherService.fetchTeacherById(staff.teacherId)
            if (teacher) {
                userService.updateDingUserByDingLogin(teacher.id, campus?.id, corpId, dingUserId, dingUnionId)
            }
            departmentList = departmentService.findDepartmentByTeacherIdAndCampusId(teacher?.id, staff.campusId)
            avatar = teacher?.avatar
        } else {
            resultVO.status = 1
            resultVO.code = BizErrorCode.DING_PC_NO_BIND_ERROR.code
            resultVO.msg = BizErrorCode.DING_PC_NO_BIND_ERROR.msg
        }

        [
                jwt                 : jwt,
                username            : mobile,
                pswd                : pswd,
                id                  : staff?.id,
                school              : school,
                campus              : campus,
                schoolCampusMultimap: schoolCampusMultimap,
                nick                : staff?.nick,
                staff               : staff,
                resultVO            : resultVO,
                avatar              : avatar,
                departmentList      : departmentList,
                bgbCampusRoleList   : bgbCampusRoleList
        ]
    }
}

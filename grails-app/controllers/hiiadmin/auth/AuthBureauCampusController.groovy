package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.bureau.BureauCampusService
import hiiadmin.module.bugu.CampusVO
import timetabling.BureauCampus

import static com.bugu.PhoenixCoder.decodeId

/**
 * 教育局下属校区列表
 */
@Deprecated
class AuthBureauCampusController implements BaseExceptionHandler {

    BureauCampusService bureauCampusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String bureauIdStr = params.bureauId
        Long bureauId = decodeId(bureauIdStr)
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        List<BureauCampus> campusList = bureauCampusService.fetchAllBureauCampusViaBureauIdLimit(bureauId, p, s)
        Integer total = bureauCampusService.countBureauCampusByBureauId(bureauId)
        List<CampusVO> vos = []
        campusList.each {
            bureauCampus ->
                vos << new CampusVO().buildVO(bureauCampus)
        }
        resultVO.result.put('list', vos)
        resultVO.result.put('total', total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String bureauIdStr = params.bureauId
        Long bureauId = decodeId(bureauIdStr)
        String ids = params.ids
        bureauCampusService.createBureauCampus(bureauId, ids)
        resultVO.result.put('id', bureauIdStr)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

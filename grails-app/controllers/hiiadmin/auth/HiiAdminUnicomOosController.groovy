package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.UnicomOosService
import hiiadmin.module.UserRightsVO

class HiiAdminUnicomOosController {

    UnicomOosService unicomOosService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        boolean rememberMe = params.boolean("rememberMe")
        String token = params.unicomToken
        log.info("联通联合登入进入".toString())
        ServiceResult<UserRightsVO> serviceResult = unicomOosService.getJointUserRightsVOByUnicomToken(token, rememberMe)
        if (serviceResult.success) {
            resultVO.result = serviceResult.result
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

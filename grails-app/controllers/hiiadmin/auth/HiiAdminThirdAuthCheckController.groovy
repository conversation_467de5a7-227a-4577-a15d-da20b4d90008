package hiiadmin.auth

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import groovy.util.logging.Slf4j
import hiiadmin.Auth3Service
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.Constants
import hiiadmin.module.login.UserSingleLoginInfoDTO
import hiiadmin.utils.ObjectUtils
import org.springframework.util.StringUtils

import javax.servlet.http.HttpServletRequest

@Slf4j
class HiiAdminThirdAuthCheckController implements BaseExceptionHandler {

    Auth3Service auth3Service

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusMenuId = params.long("menuId")
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Integer loginUrlType = params.int("loginUrlType", ConstantEnum.LoginUrlType.DEFAULT.type)
        log.info("[ThirdAuthCheck]userId@${userId},campusId@${campusId}".toString())
        ServiceResult<UserSingleLoginInfoDTO> serviceResult = auth3Service.campusSingleLoginByUserIdAndCampusMenuId(campusMenuId, campusId, userId, loginUrlType)
        if (serviceResult.success) {
            resultVO.result = ObjectUtils.resultVOObject2Map(serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    /**
     *  Client client = Client.findByClientId(clientId)
     *         String jwt = resolveToken(request)
     *         boolean login = false
     *         logger.info("进入thirdAuthCheck${clientId}".toString())
     *         if (StringUtils.hasText(jwt)) {*             //如果已经登录的
     *             ResultVO result = apiAuth.fetchStaffIdByToken(jwt, clientId)
     *             if (result.status == 1) {*                 jwt = result.result.get(Constants.JWT_TOKEN)
     *                 if (org.apache.commons.lang3.StringUtils.isNotBlank(clientId)) {*                     Long staffId = result.result.get('id') as Long
     *                     Staff staffLogin = staffService.fetchStaffById(staffId)
     *                     String auth3Token = auth3Service.genThirdPartToken(client, staffLogin)
     *                     logger.info("auth3Token@${auth3Token}".toString())
     *                     if (menuId) {*                         ClientMenu clientMenu = clientMenuService.getClientMenu(client.id, menuId, menuType)
     *                         if (org.apache.commons.lang3.StringUtils.isNotBlank(clientMenu?.registeredRedirectUri)) {*                             client.registeredRedirectUri = clientMenu.registeredRedirectUri
     *}
     *                     }
     *                     client.registeredRedirectUri = client.registeredRedirectUri + "&token=" + auth3Token
     *                     resultVO.result.put("o3Token", auth3Token)
     *                     resultVO.result.put("client", client)
     *                     logger.info("url@${client.registeredRedirectUri}".toString())
     *          = true
     *                 }
     *             }
     *             logger.info("调用glad获取 clientId:{}, result:{}".toStri JSON.toJSONString(result))
     *         }
     *  未登录的
     *         if (!login) {
     *
     *         }
     *
     *         [client: client, login: login, resultVO: resultVO]
     * */

    private String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(Constants.JWT_TOKEN)
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            String jwt = bearerToken.substring(7, bearerToken.length())
            return jwt
        }

        String jwt = request.getParameter(Constants.JWT_TOKEN)
        if (StringUtils.hasText(jwt)) {
            return jwt
        }
        return null
    }
}

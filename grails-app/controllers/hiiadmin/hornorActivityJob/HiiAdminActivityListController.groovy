package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.ActivityService
import hiiadmin.module.honorActivityJob.ActivityVO
import timetabling.oa.Activity


class HiiAdminActivityListController implements BaseExceptionHandler{
	
    ActivityService activityService
	
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        List<Activity> activities = activityService.fetchAllActivityByCampus(campusId)
        List<ActivityVO> activityVOList = []
        activities.each {
            ActivityVO activityVO = new ActivityVO()
            activityVO.id = it.id
            activityVO.name = it.name
            activityVO.dateCreated = it.dateCreated?.getTime()
            activityVOList.add(activityVO)
        }
        resultVO.result.put("list", activityVOList)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

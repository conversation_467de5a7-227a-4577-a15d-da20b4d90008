package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.HonorDeclarationService
import hiiadmin.approval.honor.HonorDeclarationVOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.honorActivityJob.HonorDeclarationVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "学生/班级荣誉管理")
class HiiAdminHonorController implements BaseExceptionHandler {

    HonorDeclarationService honorDeclarationService

    HonorDeclarationVOService honorDeclarationVOService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Byte targetType = params.byte("targetType", 1 as byte)
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")

        String searchValue = params.searchValue

        String studentIdStr = params.studentId
        Long studentId = PhenixCoder.decodeId(studentIdStr)

        Long sectionId = PhenixCoder.decodeId(params.sectionId)
        Long gradeId = PhenixCoder.decodeId(params.gradeId)
        Long facultyId = PhenixCoder.decodeId(params.facultyId)
        Long majorId = PhenixCoder.decodeId(params.majorId)
        Long unitId = PhenixCoder.decodeId(params.unitId)

        String awardTypeIds = params.awardTypeIds
        String awardLevelIds = params.awardLevelIds
        String awardGradeIds = params.awardGradeIds
        String awardCategoryIds = params.awardCategoryIds

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def (List<HonorDeclarationVO> honorDeclarationVOList, Integer total) = honorDeclarationVOService.transformVO(campusId, targetType, startTime, endTime, searchValue, studentId, unitId, gradeId, sectionId, majorId, facultyId, awardTypeIds, awardLevelIds, awardGradeIds, awardCategoryIds, p, s)
        ResultVO resultVO = ResultVO.success(["list": honorDeclarationVOList, "total": total])
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def map = honorDeclarationService.fetchAllHonorScoreByCampusLimit(campusId, p, s)

        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = params.long("id")

        if (!id) {
            throw new HiiAdminException("评分项不存在！")
        }

        String score = params.score

        if (!score) {
            throw new HiiAdminException("请选择分数")
        }

        BigDecimal bigDecimal = new BigDecimal(score)
        BigDecimal zero = BigDecimal.ZERO

        int i = bigDecimal <=> zero

        if (i == -1) {
            throw new HiiAdminException("荣誉分数不能为负数！")
        }

        honorDeclarationService.updateHonorScore(id, bigDecimal)

        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = params.long("id")

        if (!id) {
            throw new HiiAdminException("请选择评分项!")
        }

        honorDeclarationService.deleteHonorDeclaration(id)

        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

}

package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.approval.HonorDeclarationService
import timetabling.org.Campus


class HiiAdminUpdateHonorController implements Serializable{
    
    HonorDeclarationService honorDeclarationService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        String item = params.item
        
        switch (item) {
            case 'honorScore':
                List<Campus> campusList = Campus.findAllByStatus(1 as byte)
                campusList.each {
                    honorDeclarationService.syncHonorScore(it.id)
                }
                break
            case 'honorCampus':
                honorDeclarationService.syncHonorDeclaraionCampus()
                break
        }
        resultVO.result.put("msg", "success")
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

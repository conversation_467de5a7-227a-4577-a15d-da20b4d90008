package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.StudentJobService
import hiiadmin.exceptions.HiiAdminException
import io.swagger.annotations.Api
import timetabling.oa.StudentJob
import timetabling.oa.StudentJobRecord

@Api(value = "学生任职履历")
class HiiAdminStudentJobController implements BaseExceptionHandler{
    
    StudentJobService studentJobService
    
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        Long startDate = params.long("startTime")
        Long endDate = params.long("endTime")
        
        String searchValue = params.searchValue
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        def map = studentJobService.fetchAllStudentJobRecordByLimit(campusId, startDate, endDate, searchValue, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        def map = studentJobService.fetchAllStudentJobByCampus(campusId, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")

        StudentJob studentJob = StudentJob.findByIdAndStatus(id, 1 as byte)
        if (!studentJob) {
            throw new HiiAdminException("未找到任职选项")
        }
        
        String score = params.score
        
        if (!score) {
            throw new HiiAdminException("分数不能为空！")
        }
        
        BigDecimal bigDecimal = new BigDecimal(score)
        studentJob.score = bigDecimal
        
        studentJobService.saveStudentJob(studentJob)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")

        StudentJobRecord studentJobRecord = StudentJobRecord.findByIdAndStatus(id, 1 as byte)
        
        if (!studentJobRecord){
            throw new HiiAdminException("未找到任职记录")
        }
        
        studentJobRecord.status = 0 as byte
        studentJobService.saveStudentJobRecord(studentJobRecord)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
}

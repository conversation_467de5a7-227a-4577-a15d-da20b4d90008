
package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.ActivityService
import hiiadmin.exceptions.HiiAdminException
import io.swagger.annotations.Api
import timetabling.oa.Activity
import timetabling.oa.ActivityRecord

@Api(value = "活动参与管理")
class HiiAdminActivityController implements BaseExceptionHandler{
    
    ActivityService activityService
	
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        
        String searchValue = params.searchValue
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        
        def map = activityService.fetchAllActivityByCampusByLimit(campusId, startTime, endTime, searchValue, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        int p = params.int("p", 1)
        int s = params.int("s", 30)        
        def map = activityService.fetchAllActivityByCampus(campusId, p, s)
        
        resultVO.result.put("list", map.list)
        resultVO.result.put("total", map.total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")
        
        String score = params.score

        if (!score) {
            throw new HiiAdminException("请选择分数")
        }

        BigDecimal bigDecimal = new BigDecimal(score)

        Activity activity = Activity.findByIdAndStatus(id, 1 as byte)
        
        if (!activity) {
            throw new HiiAdminException("活动评分不存在！")
        }
        
        activity.score = bigDecimal
        activityService.saveActivity(activity)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
    
    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")

        ActivityRecord activityRecord = ActivityRecord.findByIdAndStatus(id, 1 as byte)
        if (!activityRecord) {
            throw new HiiAdminException("未找到活动记录！")
        }
        activityRecord.status = 0 as byte
        
        activityService.saveActivityRecord(activityRecord)
        
        resultVO.result.put("id", id)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.HonorDeclarationService
import hiiadmin.module.honorActivityJob.HonorDeclarationVO
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import timetabling.oa.HonorDeclaration

@Api(value = "学生荣誉详情")
class HiiAdminHonorInfoController implements BaseExceptionHandler{

    HonorDeclarationService honorDeclarationService
	
    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long id = params.long("id")

        HonorDeclaration honorDeclaration = honorDeclarationService.fetchHonorDeclarationById(id)
        HonorDeclarationVO vo = honorDeclarationService.buildHonorDeclarationVO(honorDeclaration)
        
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

}

package hiiadmin.hornorActivityJob

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.approval.StudentJobService
import hiiadmin.module.honorActivityJob.StudentJobVO
import timetabling.oa.StudentJob


class HiiAdminStudentJobListController implements BaseExceptionHandler{
	
    StudentJobService studentJobService
	
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        List<StudentJob> jobList = studentJobService.fetchAllJobByCampus(campusId)

        List<StudentJobVO> jobVOList = []

        jobList.each {
            StudentJobVO jobVO = new StudentJobVO()
            jobVO.id = it.id
            jobVO.name = it.name
            jobVO.dateCreated = it.dateCreated?.getTime()
            jobVOList.add(jobVO)
        }

        resultVO.result.put("list", jobVOList)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

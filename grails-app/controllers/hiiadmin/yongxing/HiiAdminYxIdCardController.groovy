package hiiadmin.yongxing

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import timetabling.org.Campus
import timetabling.user.Student
import timetabling.user.Teacher

@Slf4j
class HiiAdminYxIdCardController implements BaseExceptionHandler {

    UserEncodeInfoService userEncodeInfoService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long schoolId = params.long("schoolId")
        Long campusId = params.long("campusId")
        List<Campus> campusList = []
        if (schoolId) {
            campusList = Campus.findAllBySchoolIdAndStatus(schoolId, 1 as byte)
        } else if (campusId) {
            campusList << Campus.findById(campusId)
        }

        String studentHQL = """ SELECT s FROM Student s, UnitStudent us
                                    WHERE s.id = us.studentId 
                                    AND us.campusId = :campusId
                                    AND s.status = 1 
                                    AND us.status = 1 
                                    AND us.unitType = 1 """

        String teacherHQL = """ SELECT t FROM Teacher t, TeacherSchoolCampus tsc 
                                    WHERE t.id = tsc.teacherId 
                                    AND tsc.campusId = :campusId 
                                    AND t.status = 1 
                                    AND tsc.status = 1"""
        campusList.each {
            Campus campus ->
                log.info("campusId@${campus.id}同步身份证号开始".toString())
                List<Student> studentList = Student.executeQuery(studentHQL, [campusId: campus.id]) as List<Student>
                log.info("campusId@${campus.id}, 学生数量@${studentList?.size()}".toString())
                studentList.each {
                    Student student ->
                        if (student.idCard) {
                            userEncodeInfoService.saveUserEncodeInfo(student.idCard, 1 as byte, student.id, 1 as byte)
                        }
                }
                log.info("校区id@${campus.id}，同步学生身份证完成,size@${studentList?.size()}".toString())
                List<Teacher> teacherList = Teacher.executeQuery(teacherHQL, [campusId: campus.id]) as List<Teacher>
                log.info("campusId@${campus.id}, 教职工数量@${teacherList?.size()}".toString())
                teacherList.each {
                    Teacher teacher ->
                        if (teacher.idCard) {
                            userEncodeInfoService.saveUserEncodeInfo(teacher.idCard, 6 as byte, teacher.id, 1 as byte)
                        }
                }

                log.info("校区id@${campus.id}, 同步教师身份证完成,size@${teacherList?.size()}".toString())
        }

        resultVO.result.put("msg", "success")

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

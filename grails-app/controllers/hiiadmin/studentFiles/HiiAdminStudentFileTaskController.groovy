package hiiadmin.studentFiles

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.studentFiles.StudentFileTaskVO
import hiiadmin.studentFiles.app.StudentFileTaskAppService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

import java.util.concurrent.TimeUnit

@Api(value = "学生档案生成", tags = "管理", consumes = "admin")
class HiiAdminStudentFileTaskController implements BaseExceptionHandler {

    StudentFileTaskAppService studentFileTaskAppService

    CacheService cacheService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int s = params.int("s", 30)
        int p = params.int("p", 1)
        Long campusId = request.getAttribute("campusId") as Long
        def resultMap = studentFileTaskAppService.fetchStudentFileTaskListPage(campusId, s, p)
        List<StudentFileTaskVO> studentFileTaskList = resultMap.fileTaskList
        Integer total = resultMap.total
        resultVO.result.put("list", studentFileTaskList)
        resultVO.result.put("total", total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        ServiceResult<StudentFileTaskVO> serviceResult = studentFileTaskAppService.findStudentFileTask(id)
        if (serviceResult.success) {
            resultVO.result.put("StudentFileTask", serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte timeType = params.byte("timeType")
        String semesterIdStr = params.semesterId
        Long semesterId = PhenixCoder.decodeId(semesterIdStr)
        String schoolYearIdStr =params.schoolYearId
        Long schoolYearId =PhenixCoder.decodeId(schoolYearIdStr)
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        String key
        if (semesterId) {
            key = "semester_${semesterId}_${timeType}_${schoolId}_${campusId}"
        } else {
            key = "schoolYear_${schoolYearId}_${timeType}_${schoolId}_${campusId}"
        }
        ServiceResult resultCache = cacheService.fileStudentCache.get(key)
        if (resultCache && resultCache.success) {
            resultVO.status = 0
            resultVO.message = "正在同步中，更新成功后会自动更新同步结果，请勿频繁更新!"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        cacheService.fileStudentCache.put(key, ServiceResult.success())
        ServiceResult<Long> serviceResult = studentFileTaskAppService.saveStudentFileTask(campusId, schoolId, timeType, semesterId, schoolYearId)
        if (!serviceResult.success) {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        } else {
            resultVO.result.put("id", serviceResult.result)
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        // 数据量大同时操作两个任务sql异常
        boolean flag = cacheService.tryLockAndRun4lockCache("studentFileTaskAppService_generate_file_task", 4, TimeUnit.HOURS, {
            studentFileTaskAppService.updateStudentFileTask(id)
        })
        
        if (!flag) {
            throw new HiiAdminException("存在正在更新的档案任务，请稍后重试！")
        }


        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        ServiceResult serviceResult = studentFileTaskAppService.deleteStudentFileTask(id)
        if (!serviceResult.success) {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        } else {
            resultVO.result.put("id", id)
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

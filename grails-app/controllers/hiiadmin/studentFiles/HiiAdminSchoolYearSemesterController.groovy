package hiiadmin.studentFiles

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.studentFiles.SchoolYearSemesterVO

@Deprecated
class HiiAdminSchoolYearSemesterController implements BaseExceptionHandler {

    SchoolYearSemesterService schoolYearSemesterService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<SchoolYearSemesterVO> schoolYearSemesterVOList = schoolYearSemesterService.serviceMethod(campusId)
        resultVO.result.put("list", schoolYearSemesterVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

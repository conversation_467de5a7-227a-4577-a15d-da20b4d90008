package hiiadmin.studentFiles

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.studentFiles.FileTaskRecordVO
import hiiadmin.studentFiles.app.StudentFileTaskAppService


class HiiAdminFileTaskRecordController implements BaseExceptionHandler {

    StudentFileTaskAppService studentFileTaskAppService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long taskId = params.long("taskId")
        int s = params.int("s", 30)
        int p = params.int("p", 1)
        def resultMap = studentFileTaskAppService.fetchFileTaskRecordPage(taskId, s, p)
        List<FileTaskRecordVO> fileTaskRecordList = resultMap.fileTaskList
        Integer total = resultMap.total
        resultVO.result.put("list", fileTaskRecordList)
        resultVO.result.put("total", total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        ServiceResult<Long> serviceResult = studentFileTaskAppService.retryFailureFileTaskRecord(id)
        if (serviceResult.success) {
            resultVO.result.put("id", id)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

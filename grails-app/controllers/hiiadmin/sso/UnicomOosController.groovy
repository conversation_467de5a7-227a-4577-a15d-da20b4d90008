package hiiadmin.sso

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class UnicomOosController implements BaseExceptionHandler {
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String token = params.unicomToken


        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }

    }
}

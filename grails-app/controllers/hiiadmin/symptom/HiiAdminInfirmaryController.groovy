package hiiadmin.symptom

import com.alibaba.fastjson.JSON
import com.bugu.BaseResult
import com.bugu.ResultVO
import com.google.common.collect.Lists
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.UserService
import hiiadmin.module.TeacherDTO
import hiiadmin.module.bugu.SymptomRecordVO
import hiiadmin.school.*
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import org.apache.commons.lang3.StringUtils
import timetabling.ParentStudent
import timetabling.WechatProfile
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.symptom.SymptomRecord
import timetabling.user.Student
import timetabling.user.User
import timetabling.user.UserWxDing

@Slf4j
@Api(value = "医务室管理", tags = "校园服务", consumes = "admin")
class HiiAdminInfirmaryController implements BaseExceptionHandler {

    UserService userService

    UnitService unitService

    StudentService studentService

    CampusService campusService

    TeacherService teacherService

    GradeService gradeService

    SymptomService symptomService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String classUnitIdStr = params.unitIds
        Long classUnitId = PhenixCoder.decodeId(classUnitIdStr)
        String searchValue = params.searchValue
        Long startTime = params.long('startDate')
        Long endTime = params.long("endDate")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = symptomService.pageSymptomRecordV2(campusId, classUnitId, searchValue, startTime, endTime, p, s)
        List<SymptomRecord> symptomRecordList = map.list
        Integer total = map.total
        List<SymptomRecordVO> symptomRecordVOList = Lists.newArrayList()
        symptomRecordList.each {
            SymptomRecordVO symptomRecordVO = symptomService.buildSymptomRecordVO(it)
            symptomRecordVOList.add(symptomRecordVO)
        }
        symptomRecordVOList.sort {
            -it.id
        }
        resultVO.result.put('total', total)
        resultVO.result.put('list', symptomRecordVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        SymptomRecord symptomRecord = SymptomRecord.findByIdAndStatus(id, 1 as Byte)
        SymptomRecordVO symptomRecordVO = symptomService. buildSymptomRecordVO(symptomRecord)
        resultVO.result.put('symptomRecord', symptomRecordVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute('userId') as Long
        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherDTOByTeacherId(userId, campusId, schoolId)
        TeacherDTO teacher = teacherBaseResult.result

        String studentIds = PhenixCoder.decodeIds(params.studentIds)
        String symptomIds = params.symptomIds
        String symptomSuggestIds = params.symptomSuggestIds
        Float temperature = params.float('temperature')
        Integer sphygmus = params.int('sphygmus')
        Integer diastolicBloodPressure = params.int('diastolicBloodPressure')
        Integer systolicBloodPressure = params.int('systolicBloodPressure')
        String illnessMemo = params.illnessMemo
        String handleMoreMemo = params.handleMoreMemo
        String symptomMemo = params.symptomMemo
        String symptomSuggestMemo = params.symptomSuggestMemo
        Long dateOnset = params.long('dateOnset')
        Byte notifyMaster = params.byte('notifyMaster')
        Byte notifyParent = params.byte('notifyParent')
        List<Long> studentIdList = Lists.newArrayList(StringUtils.split(studentIds, ",")).collect {
            it.toLong()
        }
        List<SymptomRecordVO> symptomRecordVOList = Lists.newArrayList()
        studentIdList.each {
            Long studentId ->
                Unit unit = unitService.fetchAdministrativeUnitByStudentId(studentId)
                SymptomRecordVO symptomRecordVO = new SymptomRecordVO()
                SymptomRecord symptomRecord = new SymptomRecord()
                symptomRecord.studentId = studentId
                Student student = studentService.fetchStudentById(studentId)

                symptomRecord.campusId = unit.campusId
                symptomRecord.schoolId = student.schoolId
                symptomRecord.studentName = student.name

                symptomRecord.classUnitId = unit.id

                symptomRecord.creatorId = teacher.id
                symptomRecord.symptomIds = symptomIds
                symptomRecord.symptomSuggestIds = symptomSuggestIds
                symptomRecord.notifyMaster = notifyMaster
                symptomRecord.notifyParent = notifyParent
                if (temperature) {
                    symptomRecord.temperature = temperature
                }
                if (sphygmus) {
                    symptomRecord.sphygmus = sphygmus
                }
                if (diastolicBloodPressure) {
                    symptomRecord.diastolicBloodPressure = diastolicBloodPressure
                }
                if (systolicBloodPressure) {
                    symptomRecord.systolicBloodPressure = systolicBloodPressure
                }
                if (dateOnset) {
                    symptomRecord.dateOnset = new Date(dateOnset)
                }
                symptomRecord.illnessMemo = illnessMemo
                symptomRecord.handleMoreMemo = handleMoreMemo
                symptomRecord.symptomMemo = symptomMemo
                symptomRecord.symptomSuggestMemo = symptomSuggestMemo
                symptomRecord.dateCreated = new Date()
                symptomRecord.lastUpdated = new Date()
                symptomRecord.status = 1 as Byte
                symptomRecord.save(failOnError: true)
                Campus campus = campusService.fetchCampusByCampusId(symptomRecord.campusId)
                if (symptomRecord.notifyMaster == 1 as Byte) {
                    Long teacherId = teacherService.fetchHeadmasterTeacherByStudentId(studentId)?.id
                    User user = userService.fetchTeacherUserByUId(teacherId)
                    if (user) {
                        Long wechatId = user?.wechatId
                        WechatProfile wechatProfile = userService.fetchWechatProfileById(wechatId)
                        String openId = wechatProfile?.openid
                        Grade grade = gradeService.fetchGradeById(unit?.gradeId)
                        Map symptomMap = symptomService.fetchAllSymptom()
                        String symptoms = ''
                        symptomRecord.symptomIds.split(',').each {
                            String idStr ->
                                Long id = Long.valueOf(idStr)
                                symptoms = symptoms + symptomMap.get(id)?.name + ' '
                        }
                        Map symptomSuggestMap = symptomService.fetchAllSymptomSuggest()
                        String suggest = ''
                        symptomRecord.symptomSuggestIds.split(',').each {
                            String idStr ->
                                Long id = Long.valueOf(idStr)
                                suggest = suggest + symptomSuggestMap.get(id)?.name + ' '
                        }
                        String dingSuggest = suggest
                        if (symptomRecord.temperature) {
                            suggest = "体温:" + symptomRecord.temperature + "," + suggest
                        }
                        String unitName = gradeService.createGradeUnitNameViaGradeAndUnit(grade, unit)
                        if (openId) {
                            Promise task = Promises.task {
                                symptomService.sendSymptomCheckingToTeacher(student.name, openId, symptomRecord.dateCreated?.getTime(), symptoms, suggest, unitName, symptomRecord.id, wechatProfile, campusId)
                            }
                            task.onError { Throwable throwable ->
                                log.error("就医登记微信通知班主任异常", throwable)
                            }
                        }
                        UserWxDing userWxDing = userService.fetchUserWxDingViaUserIdAndCampusIdAndCorpId(user.id, ConstantEnum.UserTypeEnum.PARENT.type, campus.id, campus.corpId)
                        String dingUserId = userWxDing?.dingUserIdStr
                        if (StringUtils.isNotBlank(dingUserId)) {
                            Promise task = Promises.task {
                                symptomService.sendDingSymptomCheckingToTeacher(campus?.agentId, dingUserId, student.name, symptoms, symptomRecord.temperature, dingSuggest, symptomRecord.id, campus?.corpId, campus?.id)
                            }
                            task.onError { Throwable throwable ->
                                log.error("就医登记Ding通知班主任异常", throwable)
                            }
                        }
                        resultVO.result.put('teacherResult', 1)
                    }
                }
                if (symptomRecord.notifyParent == 1 as Byte) {
                    List<ParentStudent> parentStudentList = ParentStudent.findAllByStudentIdAndStatus(studentId, 1 as Byte)
                    parentStudentList.each {
                        ParentStudent parentStudent ->
                            Long wechatId = userService.getWechatIdByParentId(parentStudent?.parentId)
                            User user = userService.fetchParentUserByUId(parentStudent?.parentId)
                            if (user) {
                                UserWxDing userWxDing = userService.fetchUserWxDingViaUserIdAndCorpIdForParent(user?.id, ConstantEnum.UserTypeEnum.PARENT.type, campus.corpId)
                                if (user?.wechatId || userWxDing?.dingUserIdStr) {
                                    Grade grade = gradeService.fetchGradeById(unit?.gradeId)
                                    Map symptomMap = symptomService.fetchAllSymptom()
                                    String symptoms = ''
                                    symptomRecord.symptomIds.split(',').each {
                                        String idStr ->
                                            Long id = Long.valueOf(idStr)
                                            symptoms = symptoms + symptomMap.get(id)?.name + ' '
                                    }
                                    Map symptomSuggestMap = symptomService.fetchAllSymptomSuggest()
                                    String suggest = ''
                                    symptomRecord.symptomSuggestIds.split(',').each {
                                        String idStr ->
                                            Long id = Long.valueOf(idStr)
                                            suggest = suggest + symptomSuggestMap.get(id)?.name + ' '
                                    }
                                    String dingSuggest = suggest
                                    if (symptomRecord.temperature) {
                                        suggest = "体温:" + symptomRecord.temperature + "," + suggest
                                    }
                                    String unitName = gradeService.createGradeUnitNameViaGradeAndUnit(grade, unit)
                                    String openId = userService.fetchWechatProfileById(wechatId)?.openid
                                    WechatProfile wechatProfile = userService.fetchWechatProfileById(wechatId)
                                    if (openId) {
                                        Promise task = Promises.task {
                                            symptomService.sendSymptomCheckingToParent(student.name, openId, symptomRecord.dateCreated?.getTime(), symptoms, suggest, unitName, wechatProfile?.appId, campusId)
                                        }
                                        task.onError { Throwable throwable ->
                                            log.error("就医登记微信通知家长异常", throwable)
                                        }
                                    }
                                    if (userWxDing?.dingUserIdStr) {
                                        Promise task = Promises.task {
                                            symptomService.sendDingSymptomChecking(campus?.agentId, userWxDing.dingUserIdStr, student.name, symptoms, symptomRecord.temperature, dingSuggest, campus?.corpId, campusId)
                                        }
                                        task.onError { Throwable throwable ->
                                            log.error("就医登记Ding通知家长异常", throwable)
                                        }
                                    }
                                    resultVO.result.put('parentResult', 1)
                                }
                            }
                    }
                }
                symptomRecordVO = symptomService.buildSymptomRecordVO(symptomRecord)
                symptomRecordVOList.add(symptomRecordVO)
        }

        resultVO.result.put('list', symptomRecordVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }
}

package hiiadmin.symptom

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.SymptomSuggestVO
import hiiadmin.module.bugu.SymptomVO
import hiiadmin.school.SymptomService
import io.swagger.annotations.Api
import timetabling.Illness
import timetabling.symptom.Symptom
import timetabling.symptom.SymptomSuggest

@Api(value = "医务信息", tags = "系统", consumes = "admin")
class HiiAdminSymptomController implements BaseExceptionHandler {

    SymptomService symptomService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        List<Symptom> symptomList = symptomService.fetchAllSymptomBySchoolId(schoolId)
        List<Illness> illnessList = symptomService.fetchAllIllNessBySchoolId(schoolId)
        List<SymptomSuggest> symptomSuggestList = symptomService.fetchAllSymptomSuggestBySchoolId(schoolId)
        List<SymptomVO> symptomVOList = symptomService.buildSymptomVOList(symptomList)
        List<Map> illList = symptomService.buildIllnessList(illnessList)
        List<SymptomSuggestVO> symptomSuggestVOList = symptomService.buildSymptomSuggestVOList(symptomSuggestList)
        resultVO.result.put('symptomList', symptomVOList)
        resultVO.result.put('symptomSuggestList', symptomSuggestVOList)
        resultVO.result.put('illnessList', illList)

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }
}

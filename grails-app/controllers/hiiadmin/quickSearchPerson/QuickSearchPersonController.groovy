package hiiadmin.quickSearchPerson

import com.alibaba.fastjson.JSON
import com.bugu.PhoenixCoder
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.gated.TrackVO
import hiiadmin.module.humanface.FaceGroupVO
import hiiadmin.module.quickSearchPerson.CountVO
import hiiadmin.module.quickSearchPerson.QuickSearchPersonVO
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.user.ParentService
import hiiadmin.track.TrackVODOService
import hiiadmin.utils.TimeUtils
import timetabling.track.Track


/**
 * @Author: sjl
 * @CreateTime: 2025-08-25
 * @Description: ${一键找人本地版}
 * @Version: 1.0
 */
@Slf4j
class QuickSearchPersonController implements BaseExceptionHandler {

    QuickSearchPersonService quickSearchPersonService

    TrackVODOService trackVODOService

    DockingPlatformCampusService dockingPlatformCampusService

    StudentService studentService

    TeacherService teacherService

    ParentService parentService

    def query() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte type = params.byte("type")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        String decodeDeviceIds = params.deviceIds
        Long unitId = PhoenixCoder.decodeId(params.unitId)
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Boolean isAsc = params.boolean("isAsc", false)

        List<Long> deviceIds = null
        String decodedIds = PhoenixCoder.decodeIds(decodeDeviceIds)
        if (decodedIds) {
            deviceIds = decodedIds.split(",").collect { it as Long }
        }

        List<QuickSearchPersonVO> quickSearchPersonVOList = []
        int total = 0
        def resultMap
        List<TrackVO> vos = []
        if (startTime || endTime || deviceIds) {
            Byte userType = null
            List<Track> trackList = quickSearchPersonService.findTrackByCampusIdAndSearchValuePage(campusId, deviceIds, searchValue, userType, startTime, endTime, unitId, p, s, isAsc)
            Integer count = quickSearchPersonService.countTrackByCampusId(campusId, deviceIds, searchValue, userType, startTime, endTime, unitId)
            trackList.each { track ->
                TrackVO vo = trackVODOService.transformTrackVO(track)
                if (track.firm == ConstantEnum.DeviceFirm.ALY_DEVICE.firm) {
                    vo.faceImg = dockingPlatformCampusService.getAliYunImgLinkByCampusId(track.faceImg, track.channelId, campusId)
                }

                vos << vo
            }
            QuickSearchPersonVO quickSearchPersonVO = new QuickSearchPersonVO(
                    trackList: vos,
            )
            quickSearchPersonVOList << quickSearchPersonVO
            total = count
        } else {
            switch (type) {
                case ConstantEnum.QueryType.STUDENT.type:
                    resultMap = quickSearchPersonService.findStudentByCampusIdAndSearchValuePage(campusId, searchValue, unitId, p, s)
                    quickSearchPersonVOList = resultMap.list
                    total = resultMap.total

                    break
                case ConstantEnum.QueryType.TEACHER.type:
                    resultMap = quickSearchPersonService.findTeacherFaceByCampusIdAndSearchValuePage(campusId, searchValue, p, s)
                    quickSearchPersonVOList = resultMap.list
                    total = resultMap.total
                    break
                case ConstantEnum.QueryType.PARENT.type:
                    resultMap = quickSearchPersonService.findParentFaceByCampusIdAndSearchValuePage(campusId, searchValue, unitId, s, p)
                    quickSearchPersonVOList = resultMap.list
                    total = resultMap.total
                    break
                case ConstantEnum.QueryType.VISITOR.type:
                    resultMap = quickSearchPersonService.findVisitorFaceByCampusIdAndSearchValuePage(campusId, searchValue, p, s)
                    quickSearchPersonVOList = resultMap.list
                    total = resultMap.total
                    break
            }
        }

        resultVO.result.put("list", quickSearchPersonVOList)
        resultVO.result.put("total", total)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def count() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte type = params.byte("type")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        String decodeDeviceIds = params.deviceIds
        Long unitId = PhoenixCoder.decodeId(params.unitId)
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Boolean isAsc = params.boolean("isAsc", false)

        List<Long> deviceIds = null
        String decodedIds = PhoenixCoder.decodeIds(decodeDeviceIds)
        if (decodedIds) {
            deviceIds = decodedIds.split(",").collect { it as Long }
        }

        def resultMap
        CountVO countVO = new CountVO()

        if (startTime || endTime || deviceIds) {
            Byte userType = null
            Integer count = quickSearchPersonService.countTrackByCampusId(campusId, deviceIds, searchValue, userType, startTime, endTime, unitId)
            countVO.trackCount = count
        } else {
            int total = studentService.countStudentByCampusIdAndSearchValueV2(campusId, searchValue, unitId)
            countVO.studentCount = total

            int teacherCount = teacherService.countTeacherBySchoolIdAndGender(campusId, searchValue)
            countVO.teacherCount =teacherCount

            Integer parentAndFerriesCount = parentService.countParentPageByCampusIdAndSearchValueV2(campusId, searchValue, unitId)
            countVO.parentAndFerriesCount = parentAndFerriesCount

            resultMap = quickSearchPersonService.findVisitorFaceByCampusIdAndSearchValuePage(campusId, searchValue, p, s)
            countVO.visitorCount = resultMap.total

        }


        resultVO.result.put("data", countVO)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

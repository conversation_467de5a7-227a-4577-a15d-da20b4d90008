package hiiadmin.html

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.AmisApi
import org.springframework.beans.factory.annotation.Autowired



class FindHtmlConfigController implements BaseExceptionHandler {

    @Autowired
    AmisApi amisApi

    def index() {

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String path = params.path
        Integer modulesType = params.int("modulesType", ConstantEnum.ClientModulesType.HII_ADMIN.type)
        Map map = [:]
        map.put("path", path)
        map.put("modulesType", modulesType)
        try {
            resultVO = amisApi.findHtmlConfig(map)
        } catch (Exception e) {
            log.info("[FindHtmlConfigController]result@${resultVO.message}".toString(), e)
        }
        resultVO.status = 1
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

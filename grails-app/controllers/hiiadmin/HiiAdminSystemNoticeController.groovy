package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import timetabling.SystemNotice

class HiiAdminSystemNoticeController implements BaseExceptionHandler {

    SystemNoticeService systemNoticeService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        
        SystemNotice systemNotice = systemNoticeService.fetchSystemNoticeByTerminalType(campusId)
        if (systemNotice) {
            String content = systemNotice.content
            // 1:维护公告 2：系统公告
            if (systemNotice.systemType == 1 as byte) {
                content = "【系统维护公告】" + content
            } else if (systemNotice.systemType == 2 as byte) {
                content = "【新功能发布公告】" + content
            }
            resultVO.result.put("id", systemNotice.id)
            resultVO.result.put("content", content)
            resultVO.result.put("systemType", systemNotice.systemType)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

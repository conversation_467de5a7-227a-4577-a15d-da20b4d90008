package hiiadmin.auth2

import com.alibaba.fastjson2.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.auth.SinglePointLoginService
import org.apache.commons.lang3.StringUtils

class AuthOutLoginController {
    SinglePointLoginService singlePointLoginService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = request.getAttribute("type") as Byte
        ServiceResult<String> serviceResult = singlePointLoginService.outLoginAuthentication(userId, campusId, userType)
        if (serviceResult.isSuccess()) {
            if (StringUtils.isNotBlank(serviceResult.result)){
                resultVO.result.put("redirectUri", serviceResult.getResult())
            }
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.getMessage()
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

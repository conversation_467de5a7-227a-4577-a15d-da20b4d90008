package hiiadmin.auth2

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.auth.LoginService

class AuthRemoteLoginController {

    LoginService loginService

    def save() {
        String bodyJson = request.JSON
        JSONObject jsonObject = JSON.parseObject(bodyJson)
        log.info("[login4UserInfo]parem@${jsonObject.toString()}".toString())
        String userName = jsonObject.getString("userName")
        String password = jsonObject.getString("password")
        Long campusId = jsonObject.getLong("campusId")
        log.info("[login4UserInfo] userName@${userName} password@${password}".toString())
        ResultVO resultVO =null
         try {
             resultVO = loginService.login4UserInfo(userName, password,campusId)
         } catch (Exception e) {
             resultVO = new ResultVO()
             resultVO.status = 0
             resultVO.code = 500
             resultVO.message = e.getMessage()
         }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.auth2

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import com.google.common.collect.Maps
import hiiadmin.*
import hiiadmin.auth.BgbApiCore
import hiiadmin.auth.SinglePointLoginService
import hiiadmin.auth.SinglePointLoginV2Service
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.module.RemoteRequestParamVO
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.module.newMenu.BgbCampusRoleVO
import hiiadmin.module.singleLogin.Auth2TokenBO
import hiiadmin.module.singleLogin.vo.Auth2UserInfoVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.newMenu.BgbStaffRoleService
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import timetabling.CampusCustomMade
import timetabling.Client
import timetabling.Department
import timetabling.newMenu.BgbStaffRole
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.Staff
import timetabling.user.Teacher
import timetabling.user.TeacherSchoolCampus
import timetabling.vocationalSchool.Faculty

/**
 * <AUTHOR>
 * @Date 2024-08-29 10:18
 */
class Auth2SingleLoginV2Controller implements BaseExceptionHandler {

    static Logger logger = LoggerFactory.getLogger(Auth2SingleLoginV2Controller.class)

    SinglePointLoginV2Service singlePointLoginV2Service

    StaffService staffService

    CampusService campusService

    BgbStaffRoleService bgbStaffRoleService

    RemoteService remoteService

    @Autowired
    BgbApiCore bgbApiCore

    TeacherService teacherService

    Auth3Service auth3Service

    DepartmentService departmentService

    FacultyService facultyService

    BgbCampusRoleService bgbCampusRoleService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String code = params.code
        String grantType = params.grantType
        String accessToken = params.accessToken
        resultVO.status = 1 as byte
        Auth2TokenBO auth2TokenBO = new Auth2TokenBO()
        auth2TokenBO.code = code
        auth2TokenBO.accessToken = accessToken
        auth2TokenBO.grantType = grantType == null ? ConstantEnum.GrantType.CODE.type : grantType
        Auth2UserInfoVO auth2UserInfoVO = null
        ServiceResult<Auth2UserInfoVO> serviceResult = singlePointLoginV2Service.oauth2SsoUser(auth2TokenBO)
        if (serviceResult.success) {
            auth2UserInfoVO = serviceResult.result
        } else {
            resultVO.status = 0
            resultVO.code = 500
            resultVO.message = serviceResult.message
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }

        Teacher teacher = auth2UserInfoVO.teacher
        Staff staff = staffService.fetchStaffByCampusIdAndTeacherId(auth2UserInfoVO.campusId, teacher.id)
        if (!staff) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.NO_USER_ERROR.code
            resultVO.msg = BizErrorCode.NO_USER_ERROR.msg
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }

        List<Staff> campusStaffList
        Map<School, List<Campus>> schoolCampusListMap = Maps.newHashMap()

        campusStaffList = staffService.fetchAllStaffByTeacherId(teacher.id)
        if (campusStaffList?.size() > 0) {
            schoolCampusListMap = campusService.findSchoolCampusListMapBySchoolIdInListAndAdminAndStaffList(campusStaffList*.schoolId, campusStaffList)
        } else {
            resultVO.status = 0
            resultVO.code = BizErrorCode.USER_NO_KAITONG.code
            resultVO.msg = BizErrorCode.USER_NO_KAITONG.msg
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }

        String jwt
        School school
        Campus campus
        String clientId
        Client client
        List<BgbCampusRoleVO> bgbCampusRoleVOList
        if (!staff.activated) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.USER_NOT_ACTIVE_ERROR.code
            resultVO.msg = BizErrorCode.USER_NOT_ACTIVE_ERROR.msg
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
        } else {
            List<BgbStaffRole> bgbStaffRoleList = bgbStaffRoleService.fetchBgbStaffRoleByStaffId(staff.id)
            if (bgbStaffRoleList?.size() < 1) {
                resultVO.status = 0
                resultVO.code = BizErrorCode.USER_NO_ROLE.code
                resultVO.msg = BizErrorCode.USER_NO_ROLE.msg
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            } else {
                RemoteRequestParamVO requestParamVO = remoteService.buildRemoteRequestVO(staff.id)
                ResultVO result = bgbApiCore.fetchToken(requestParamVO)
                if (result.status == 1) {
                    jwt = result.result.get(Constants.JWT_TOKEN)
                }
                logger.info("调用glad获取token staff.id:{}, result:{}".toString(), staff.id, JSON.toJSONString(result))
                response.addHeader(Constants.JWT_TOKEN, jwt)
                List<Long> roleIdList = bgbStaffRoleList*.roleId
                bgbCampusRoleVOList = bgbCampusRoleService.fetchBgbCampusRoleVOByIdList(roleIdList)

                if (staff.schoolId) {
                    school = School.get(staff.schoolId)
                }
                if (staff.campusId) {
                    campus = Campus.get(staff.campusId)
                }
            }
        }
        boolean pswd = false
        if (staff.passwordHash) {
            pswd = true
            //处理SSO相关
            clientId = params.clientId
            if (StringUtils.isNotBlank(clientId)) {
                client = Client.findByClientId(clientId)
                String auth3Token = auth3Service.genThirdPartToken(client, staff)
                resultVO.result.put("o3Token", auth3Token)
                resultVO.result.put("client", client)
            }
        }
        String avatar = null
        List<DepartmentVO> departmentList = []
        if (campus.type == ConstantEnum.CampusType.VOCATIONAL.type) {
            List<Faculty> facultyList = facultyService.fetchAllFacultyByUser(staff.campusId, teacher?.id)
            facultyList.each {
                DepartmentVO departmentVO = new DepartmentVO(
                        id: PhenixCoder.encodeId(it.id),
                        name: it.name
                )
                departmentList << departmentVO
            }
        } else {
            List<Department> departments = departmentService.findDepartmentByTeacherIdAndCampusId(teacher?.id, staff.campusId)
            departments.each {
                DepartmentVO departmentVO = new DepartmentVO(
                        id: PhenixCoder.encodeId(it.id),
                        name: it.name
                )
                departmentList << departmentVO
            }
        }
        avatar = teacher?.avatar
        TeacherSchoolCampus teacherSchoolCampus = teacherService.fetchTeacherSchoolByCampusIdAndTeacherId(campus?.id, staff?.teacherId, false)
        String jobNum = teacherSchoolCampus?.jobNum
        //TODO  迁移到service
        CampusCustomMade actionHide = CampusCustomMade.findByCampusIdAndTypeAndStatus(campus?.id, 1, 1)
        List<Long> supervisorIdList = departmentService.findDepartmentSupervisorByTeacherId(campus?.id, staff?.teacherId, campus?.type)

        String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.ID_CARD.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacher?.id)

        [
                jwt                : jwt,
                clientId           : clientId,
                client             : client,
                username           : mobile,
                pswd               : pswd,
                jobNum             : jobNum,
                school             : school,
                campus             : campus,
                schoolCampusListMap: schoolCampusListMap,
                staff              : staff,
                resultVO           : resultVO,
                avatar             : avatar,
                departmentList     : departmentList,
                bgbCampusRoleVOList: bgbCampusRoleVOList,
                customMade         : actionHide?.customMade,
                supervisorIdList   : supervisorIdList
        ]
    }
}

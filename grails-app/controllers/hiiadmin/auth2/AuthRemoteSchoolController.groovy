package hiiadmin.auth2

import com.bugu.ResultVO
import grails.rest.*
import grails.converters.*
import hiiadmin.UserService
import hiiadmin.auth.LoginService
import hiiadmin.module.bugu.dto.CampusDTO
import hiiadmin.module.bugu.dto.SchoolDTO
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import timetabling.org.Campus
import timetabling.org.School

class AuthRemoteSchoolController {

    CampusService campusService


    def save() {
        ResultVO resultVO = new ResultVO()
        List<School> schoolList = campusService.fetchAllSchool()
        List<Campus> campusList = campusService.fetchALlCampus()
        Map<Long, List<Campus>> schoolIdKeyMap = campusList.groupBy { it.schoolId }
        List<SchoolDTO> schoolDtoList = []
        schoolList.each {
            School school ->
                SchoolDTO dto = new SchoolDTO(id: school.id, name: school.name)
                dto.campusList = []
                List<Campus> campusSchoolList = schoolIdKeyMap.get(school.id)
                if (campusSchoolList != null && campusSchoolList.size() > 0) {
                    campusSchoolList.each {
                        Campus campus ->
                            dto.campusList << new CampusDTO(id: campus.id, name: campus.name)
                    }
                }
                schoolDtoList << dto
        }
        log.info("[findSchoolTree]size@${schoolDtoList.size()},campusId@${campusList.size()}".toString())
        resultVO.result.put("list", schoolDtoList)
        resultVO.message = "获取成功"
        withFormat {
            json { render text: com.alibaba.fastjson.JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

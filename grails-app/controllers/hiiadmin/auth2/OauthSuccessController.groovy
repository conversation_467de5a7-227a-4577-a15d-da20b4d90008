package hiiadmin.auth2

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.auth.OauthSuccessService

class OauthSuccessController implements BaseExceptionHandler {

    OauthSuccessService oauthSuccessService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String code = params.code
        ServiceResult<String> result = oauthSuccessService.getMobileBasicToken(code)
        if (result.success) {
            resultVO.result.put("phone", result.result)
        } else {
            resultVO.message = result.message
            resultVO.status = 0
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.auth2

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.UserService
import hiiadmin.auth.LoginService
import hiiadmin.module.bugu.dto.CodeUserDTO
import hiiadmin.module.bugu.dto.UserInfoDTO
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType




class AuthRemoteUserInfoController {




    UserService userService



	
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String bodyJson = request.JSON
        CodeUserDTO codeUserDTO = JSON.parseObject(bodyJson, CodeUserDTO.class)
        log.info("[findUserInfo]parem@${JSON.toJSONString(codeUserDTO)}".toString())
        UserInfoDTO userInfoDTO = userService.gerUserInfoDTO(codeUserDTO)
        log.info("[findUserInfo]parem@${JSON.toJSONString(userInfoDTO)}".toString())


        if (userInfoDTO) {
            resultVO.result.put("body", userInfoDTO)
            resultVO.message = "获取成功"
        } else {
            resultVO.status = 0
            resultVO.message = "人员不存在"
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

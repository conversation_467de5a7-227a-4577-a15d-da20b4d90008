package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.GradeVO
import hiiadmin.module.bugu.SectionVO
import hiiadmin.module.bugu.UnitVO
import hiiadmin.module.vocationalSchool.FacultyVO
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.UnitService
import hiiadmin.vocationalSchool.VocationalUnitService
import io.swagger.annotations.Api
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit

@Api(value = "拉取校区下所有行政班")
@Deprecated
class HiiAdminUnitTreeController implements BaseExceptionHandler {

    GradeService gradeService

    UnitService unitService

    CampusService campusService

    VocationalUnitService vocationalUnitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long
        Integer hideGrade = params.int("hideGrade", 0)

        Campus campus = campusService.fetchCampusById(campusId)
        
        if (campus.type == 1){
            Map<Long, List<Grade>> gradeMap = [:]
            Map<Long, List<Unit>> unitMap = [:]

            List<Grade> gradeList = gradeService.fetchAllNormalGradeByCampusId(campusId)
            List<Unit> unitList = unitService.fetchAllAdminUnitViaCampusId(campusId)
            gradeList.sort {
                it.code
            }
            unitList.sort {
                it.code
            }
            List<Long> sectionIdList = []
            if (gradeList) {
                gradeMap = gradeList.groupBy { it.sectionId }
                sectionIdList = gradeList*.sectionId.unique()
                sectionIdList.sort {
                    it
                }
            }

            if (unitList) {
                unitMap = unitList.groupBy { it.gradeId }
            }

            List<SectionVO> vos = []

            sectionIdList.each {
                Long sectionId ->
                    List<GradeVO> gradeVOList = []
                    SectionVO sectionVO = new SectionVO()
                    sectionVO.name = ConstantEnum.SectionType.getEnumByType(sectionId).name
                    sectionVO.id = sectionId
                    List<Grade> grades = gradeMap?.get(sectionId)
                    grades.each {
                        Grade grade ->
                            List<UnitVO> unitVOList = []
                            GradeVO gradeVO = new GradeVO()
                            gradeVO.id = grade.id
                            gradeVO.name = grade.name
                            gradeVO.code = grade.code
                            gradeVO.sectionId = sectionId
                            gradeVOList << gradeVO
                            List<Unit> units = unitMap?.get(grade.id)
                            units.each {
                                Unit unit ->
                                    UnitVO unitVO = new UnitVO()
                                    unitVO.id = unit.id
                                    unitVO.name = unit.name
                                    unitVO.alias = unit.alias
                                    unitVO.sectionId = sectionId
                                    unitVO.code = unit.code
                                    unitVOList << unitVO
                            }
                            unitVOList.sort { it.code }
                            gradeVO.units = unitVOList
                    }
                    gradeVOList.sort { it.code }
                    sectionVO.grades = gradeVOList

                    vos << sectionVO
            }

            resultVO.result.put("total", unitList.size())
            resultVO.result.put("list", vos)
        } else {
            List<FacultyVO> facultyVOList = []
            facultyVOList = vocationalUnitService.treeUnitV2(campusId, hideGrade)
            resultVO.result.put("total", facultyVOList.size())
            resultVO.result.put("list", facultyVOList)
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.DoorPlateVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.bugu.UnitFullVO
import hiiadmin.module.bugu.UnitVO
import hiiadmin.school.*
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.org.structure.UnitDataStructureService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.ObjectUtils
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import io.swagger.annotations.Api
import timetabling.Subjects
import timetabling.building.DoorPlate
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Teacher
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major

import static hiiadmin.ConstantEnum.UnitType.ADMINISTRATIVE_CLASS

@Api(value = "班级管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminUnitController implements BaseExceptionHandler {

    SchoolService schoolService

    UnitService unitService

    StudentService studentService

    DoorPlateService doorPlateService

    ParentService parentService

    TeacherService teacherService

    ViewDOService viewDOService

    GradeService gradeService

    CampusService campusService

    UnitDataStructureService unitDataStructureService

    FacultyService facultyService

    MajorService majorService
    
    BaseInfoDataBuildService baseInfoDataBuildService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long("campusId")
        Map<Long, Campus> longCampusMap = [:]
        if (campusId) {
            Campus campus = campusService.fetchCampusByCampusId(campusId)
            longCampusMap.put(campusId, campus)
        } else {
            longCampusMap = campusService.fetchCampusMapBySchoolId(schoolId)
        }
        Map<Long, Subjects> longSubjectsMap = schoolService.fetchSubjectIdMap()
        Long sectionId = params.long("sectionId")
        Long gradeId = params.long("gradeId")
        Long subjectId = params.long("subjectId")
        Byte type = params.byte("type")
        String include = params.include
        
        boolean graduate = params.boolean("graduate", false)
        Long facultyId = params.long("facultyId")
        Long majorId = params.long("majorId")
        
        def map = unitService.fetchAllUnitBySchoolIdCampusIdSectionIdGradeIdSubjectIdType(schoolId, campusId, sectionId, gradeId, subjectId, type, p, s, graduate, facultyId, majorId)
        List<Unit> unitList = map?.list
        Integer count = map?.total
        Long graduateTime = null
        
        
        Map<Long, Integer> unitIdStudentCountMap = [:]
        Map<Long, Integer> unitIdParentCountMap = [:]
        Map<Long, Grade> gradeMap = [:]
        Map<Long, Major> majorMap = [:]
        Map<Long, Faculty> facultyMap = [:]
        Map<Long, Teacher> headmasterTeacherMap = [:]
        if (unitList && include != "") {
            unitIdStudentCountMap = baseInfoDataBuildService.collectUnitStudentCountMap(unitList, graduate)
            unitIdParentCountMap = baseInfoDataBuildService.collectUnitStudentParentCountMap(unitList, graduate)
            gradeMap = gradeService.fetchAllGradeByGradeIdInList(unitList*.gradeId)?.collectEntries{[it.id, it]}
            majorMap = majorService.fetchAllMajorByIdList(unitList*.majorId)?.collectEntries{[it.id, it]}
            facultyMap = facultyService.fetchAllFacultyByIdList(unitList*.facultyId)?.collectEntries{[it.id, it]}
            headmasterTeacherMap = baseInfoDataBuildService.collectUnitHeadmasterMap(unitList, graduate)
        }
        
        List<UnitFullVO> unitVOList = unitList.collect {
            Unit unit ->
                UnitFullVO vo = new UnitFullVO().buildVO(unit)
                if (include != "") {
                    Integer studentTotal = unitIdStudentCountMap?.get(unit.id) ?: 0
                    Integer parentTotal = unitIdParentCountMap?.get(unit.id) ?: 0
                    Teacher teacher = headmasterTeacherMap?.get(unit.id)
                    if (teacher) {
                        TeacherVO teacherVO = new TeacherVO().buildVO(teacher)
                        vo.teacher = teacherVO
                    }
                    
                    if (!graduate) {
                        DoorPlate doorPlate = doorPlateService.fetchDoorPlateByUnitId(unit.id)
                        if (doorPlate) {
                            DoorPlateVO doorPlateVO = viewDOService.transformDoorPlateVO(doorPlate, false)
                            vo.doorPlate = doorPlateVO
                        }
                        if (vo.subjectId) {
                            Subjects subjects = longSubjectsMap.get(vo.subjectId)
                            vo.subjectName = subjects.name
                        }
                    }
                    Grade grade = gradeMap.get(unit.gradeId)
                    vo.gradeName = gradeService.appendGradeName(grade)
                    
                    vo.facultyName = facultyService.appendFacultyName(facultyMap?.get(unit.facultyId))
                    vo.majorName = majorService.appendMajorName(majorMap?.get(unit.majorId))
                    vo.studentTotal = studentTotal
                    vo.parentTotal = parentTotal
                    if (graduate) {
                        vo.graduateTime = unit.lastUpdated?.time
                    }
                }
                Campus campus = longCampusMap.get(vo.campusId)
                vo.campusName = campus?.name
                vo
        }
        
        if (graduate) {
            unitVOList?.sort{-it.graduateTime}
            if (gradeId) {
                graduateTime = gradeService.fetchGradeById(gradeId, false)?.lastUpdated?.time
            }
        } else {
            if (gradeId) {
                unitVOList.sort {
                    it.code
                }
            } else {
                unitVOList.sort {
                    [it.campusId, it.sectionId, it.gradeCode, it.code]
                }
            }
        }
        resultVO.result.put("graduateTime", graduateTime)
        resultVO.result.put('list', unitVOList)
        resultVO.result.put('total', count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long gradeId = params.long('gradeId')
        Preconditions.checkNotNull(gradeId, "未选择年级")
        Integer code = params.int('code')
        Byte type = params.byte("type", ADMINISTRATIVE_CLASS.type)
        String unitName = params.name
        String alias = params.alias
        Long subjectId = params.long("subjectId")
        Unit unit
        if (subjectId && type == ADMINISTRATIVE_CLASS.type) {
            Preconditions.checkArgument(false, "行政班不能设置学科")
        }
        Grade grade = gradeService.fetchGradeById(gradeId, false)
        if (type == ADMINISTRATIVE_CLASS.type) {
            if (code) {
                unit = unitService.createAndSaveAdminUnit(grade, code, type, unitName)
            } else {
                String maxCode = unitService.fetchMaxCodeAdminUnitByGradeId(gradeId)
                code = Integer.valueOf(maxCode) + 1
                unit = unitService.createAndSaveAdminUnit(grade, code, type, unitName)
            }
        } else {
            code = unitName.hashCode()
            unit = unitService.createAndSaveAdminUnit(grade, code, type, subjectId, unitName)
        }
        
        if (alias) {
            List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(unit?.gradeId, false)
            List<String> aliasList = unitList*.alias
            if (aliasList.contains(alias)) {
                resultVO.code = BizErrorCode.UNIT_ALIAS_ERROR.code
                resultVO.msg = BizErrorCode.UNIT_ALIAS_ERROR.msg
                resultVO.status = 0 as byte
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
            unit.alias = alias
        }
        unitService.saveUnit(unit)
        UnitFullVO vo = new UnitFullVO().buildVO(unit)
        Integer studentTotal = studentService.countStudentByUnitId(unit.id)
        Integer parentTotal = parentService.countParentByUnitId(unit.id)
        Teacher teacher = teacherService.fetchHeadmasterTeacherByUnitId(unit.id)
        if (teacher) {
            TeacherVO teacherVO = new TeacherVO().buildVO(teacher)
            vo.teacher = teacherVO
        }
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateByUnitId(unit.id)
        if (doorPlate) {
            DoorPlateVO doorPlateVO = viewDOService.transformDoorPlateVO(doorPlate, false)
            vo.doorPlate = doorPlateVO
        }
        if (vo.subjectId) {
            Subjects subjects = schoolService.fetchSubjectById(vo.subjectId)
            vo.subjectName = subjects.name
        }
        Campus campus = campusService.fetchCampusByCampusId(vo.campusId)
        vo.campusName = campus?.name
        vo.gradeName = grade.name
        vo.studentTotal = studentTotal
        vo.parentTotal = parentTotal
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        String alias = params.alias
        Unit unit = unitService.fetchUnitById(id)
        if (alias) {
            List<Unit> unitList = unitService.fetchAllAdminUnitByGradeId(unit?.gradeId, false)
            List<String> aliasList = unitList*.alias
            if (aliasList.contains(alias)) {
                resultVO.code = BizErrorCode.UNIT_ALIAS_ERROR.code
                resultVO.msg = BizErrorCode.UNIT_ALIAS_ERROR.msg
                resultVO.status = 0 as byte
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
            unit.alias = alias
        }
        if (unit.type != ADMINISTRATIVE_CLASS.type) {
            Byte type = params.byte("byte")
            Preconditions.checkState(type != ADMINISTRATIVE_CLASS.type, "不支持修改班级类型到行政班")
            String name = params.name
            Integer code = name.hashCode()
            Preconditions.checkState(!unitService.hasUnitByGradeIdAndCode(unit.gradeId, code, id), "当前年级班级名称已存在，请更改为其他名称")
            unit.name = name
            unit.code = code
            Long subjectId = params.long('subjectId')
            unit.subjectId = subjectId
        }
        unitService.saveUnit(unit)
        UnitVO vo = new UnitVO().buildVO(unit)
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        unitDataStructureService.deleteUnit(id, userId, userType)
        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

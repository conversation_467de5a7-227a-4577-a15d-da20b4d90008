package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.UnitTeacherVO
import hiiadmin.school.GradeService
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitTeacherService
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.org.UnitTeacher

import static hiiadmin.ConstantEnum.SectionType
import static hiiadmin.ConstantEnum.UnitType

@Deprecated
class HiiAdminUnitTeacherController implements BaseExceptionHandler {

    UnitService unitService

    GradeService gradeService

    UnitTeacherService unitTeacherService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        List<UnitTeacher> unitTeacherList = unitTeacherService.fetchAllUnitTeacherByCampusIdAndTeacherIdAndType(campusId, teacherId, UnitType.ADMINISTRATIVE_CLASS.type)
        Map<Long, Unit> unitMap = unitService.getAllUnitIdMapByCampusId(campusId)
        Map<Long, Grade> gradeMap = gradeService.fetchAllNormalGradeIdMapByCampusId(campusId)
        List<UnitTeacherVO> voList = []
        unitTeacherList.each {
            UnitTeacher unitTeacher ->
                Unit unit = unitMap?.get(unitTeacher.unitId)
                Grade grade = gradeMap?.get(unitTeacher.gradeId)
                UnitTeacherVO unitTeacherVO = new UnitTeacherVO(
                        id: unitTeacher.id,
                        unitId: unitTeacher.unitId,
                        unitName: unit?.name,
                        alias: unit?.alias,
                        gradeId: unit?.gradeId,
                        gradeName: grade?.name,
                        sectionId: grade?.sectionId,
                        sectionName: SectionType.getEnumByType(grade?.sectionId)?.name,
                        headMaster: unitTeacher.headmaster
                )
                voList.add(unitTeacherVO)
        }
        voList?.sort { -it.headMaster }
        resultVO.result.put("list", voList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.BaseResult
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.UserService
import hiiadmin.ViewDOService
import hiiadmin.module.TeacherDTO
import hiiadmin.module.bugu.UnitFullVO
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.utils.PhenixCoder
import timetabling.user.Teacher

/**
 * <AUTHOR>
 * @Date 2023-07-05 11:38
 */
@Slf4j
@Deprecated
class HiiAdminManageUnitTreeController implements BaseExceptionHandler {

    UnitService unitService

    UserService userService

    ViewDOService viewDOService

    TeacherService teacherService

    CampusService campusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String types = params.types ?: "1" //默认行政班
        Byte belong = params.byte('belong', 0 as Byte)
        List<Byte> typeList = types.split(",").collect {
            it as byte
        }
        Long gradeId = params.long("gradeId")

        Long majorId = params.long("majorId")
        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)
        TeacherDTO teacher = teacherBaseResult.result
        Long campusId = teacher.campusId
        Byte campusType = campusService.fetchCampusById(campusId)?.type
        List<UnitFullVO> unitFullVOList
        if (belong == 1) {
            unitFullVOList = viewDOService.transformAdminUnitFullVOByCampusIdAndTeacherId(campusId, teacher.id)
        } else {
            // 普校
            if (campusType == 1) {
                if (gradeId) {
                    unitFullVOList = unitService.getAllUnitFullVOByGradeIdAndTypeList(gradeId, typeList)
                } else {
                    Map<Long, Teacher> unitHeadMasterMap = teacherService.transformUnitHeadMasterNameMap(campusId)
                    unitFullVOList = unitService.getAllAdminUnitFullVOByCampusIdAndTypeList(campusId, typeList, unitHeadMasterMap)
                }
            } else {
                // 职校
                if (majorId) {
                    unitFullVOList = unitService.getAllUnitFullVOByMajorIdAndTypeList(campusId, majorId, typeList)
                } else {
                    Map<Long, Teacher> unitHeadMasterMap = teacherService.transformUnitHeadMasterNameMap(campusId)
                    unitFullVOList = unitService.getAllAdminUnitFullVOByCampusIdAndTypeList(campusId, typeList, unitHeadMasterMap)
                }
            }
        }
        if (campusType == 1) {
            unitFullVOList.sort {
                a, b ->
                    if (PhenixCoder.decodeId(a.sectionId) == PhenixCoder.decodeId(b.sectionId)) {
                        if (a.gradeCode == b.gradeCode) {
                            return (a.code.toInteger() ? a.code.toInteger() : a.code.hashCode()) <=> (b.code.toInteger() ? b.code.toInteger() : b.code.hashCode())
                        }
                        return a.gradeCode.toInteger() <=> b.gradeCode.toInteger()
                    }
                    return PhenixCoder.decodeId(a.sectionId).toInteger() <=> PhenixCoder.decodeId(b.sectionId).toInteger()
            }
        }
        unitFullVOList.each {unit->
            unit.id = PhenixCoder.decodeId(unit.id)
            unit.campusId = PhenixCoder.decodeId(unit.campusId)
            unit.unitId = PhenixCoder.decodeId(unit.unitId)
            unit.schoolId = PhenixCoder.decodeId(unit.schoolId)
            unit.gradeId = PhenixCoder.decodeId(unit.gradeId)
            unit.sectionId = PhenixCoder.decodeId(unit.sectionId)
            unit.facultyId = PhenixCoder.decodeId(unit.facultyId)
            unit.majorId = PhenixCoder.decodeId(unit.majorId)
            unit.masterId = PhenixCoder.decodeId(unit.masterId)
        }
        resultVO.result.put('list', unitFullVOList)
        resultVO.result.put('total', unitFullVOList?.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.UnitService
import io.swagger.annotations.Api

@Api(value = "学校未设置班主任/教室班级数量统计", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminUnitQuickRecordController implements BaseExceptionHandler {

    UnitService unitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long('campusId')
        Integer noDoorUnitCount = 0
        Integer noHeadmasterUnitCount = 0
        if (campusId) {
            noDoorUnitCount = unitService.noSetDoorUnitByCampusId(campusId)
            noHeadmasterUnitCount = unitService.noSetHeadmasterByCampusId(campusId)
        } else {
            noDoorUnitCount = unitService.noSetDoorUnitBySchoolId(schoolId)
            noHeadmasterUnitCount = unitService.noSetHeadmasterBySchoolId(schoolId)
        }
        resultVO.result.put('noDoorUnitCount', noDoorUnitCount)
        resultVO.result.put('noHeadmasterUnitCount', noHeadmasterUnitCount)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.CampusVO
import hiiadmin.module.bugu.GradeVO
import hiiadmin.module.bugu.SectionVO
import hiiadmin.module.bugu.UnitVO
import hiiadmin.module.vocationalSchool.FacultyVO
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.SchoolService
import hiiadmin.school.UnitService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.VocationalUnitService
import io.swagger.annotations.Api
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit

@Slf4j
@Api(value = "校区班级树列表", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminTreeSchoolController implements BaseExceptionHandler {

    SchoolService schoolService

    GradeService gradeService

    UnitService unitService

    CampusService campusService

    VocationalUnitService vocationalUnitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Long campusId = params.long("campusId")
        boolean full = params.boolean("full", false)
        boolean graduate = params.boolean("graduate", false)
        Integer hideGrade = params.int("hideGrade", 0)
        List<Campus> campusList = []
        if (campusId) {
            Campus campus = campusService.fetchCampusByCampusId(campusId)
            campusList << campus
        } else {
            campusList.addAll(schoolService.fetchAllCampusBySchoolIdLimit(schoolId, 1, 100))
        }
        List<CampusVO> campusVOList = campusList.collect {
            Campus campus ->
                CampusVO vo = new CampusVO().buildVO(campus)
                vo.id = PhenixCoder.decodeId(vo.id)
                vo.schoolId = PhenixCoder.decodeId(vo.schoolId)
                if (campus.type == 1) {
                    vo.sections = []
                    List<Grade> gradeList = gradeService.fetchAllNormalGradeByCampusId(campus.id, graduate)
                    Map<Long, List<GradeVO>> map = [:]
                    gradeList.each {
                        Grade grade ->
                            GradeVO gradeVO = new GradeVO().buildVO(grade)
                            gradeVO.id = grade.id
                            gradeVO.campusId = grade.campusId
                            gradeVO.sectionId = grade.sectionId

                            if (!map[grade.sectionId]) {
                                map.put(grade.sectionId, [])
                            }
                            List<Unit> unitList = []
                            if (full) {
                                if (graduate) {
                                    unitList.addAll(unitService.fetchAllFinishUnitByFinishGradeId(grade.id, ConstantEnum.UnitType.allUnitTypeList))
                                } else {
                                    unitList.addAll(unitService.fetchAllUnitByGradeId(grade.id, ConstantEnum.UnitType.allUnitTypeList))
                                }
                            } else {
                                unitList.addAll(unitService.fetchAllAdminUnitByGradeId(grade.id, false, graduate))
                            }
                            List<UnitVO> unitVOList = unitList.collect {
                                UnitVO unitVO = new UnitVO().buildVO(it)
                                unitVO.id = it.id
                                unitVO.gradeId = it.gradeId
                                unitVO.sectionId = it.sectionId
                                unitVO.facultyId = it.facultyId
                                unitVO.majorId = it.majorId
                                unitVO
                            }
                            try {
                                unitVOList.sort {
                                    it.code
                                }
                            } catch (RuntimeException e) {
                                log.warn("班级排序异常")
                            }
                            gradeVO.units = unitVOList
                            map[grade.sectionId].add(gradeVO)
                    }
                    map.each {
                        Long sectionId = it.key
                        try {
                            it.value.sort {
                                it.code
                            }
                        } catch (RuntimeException e) {
                            log.warn("年级排序异常")
                        }
                        List<GradeVO> gradeVOList = it.value
                        SectionVO sectionVO = new SectionVO(
                                id: sectionId,
                                name: ConstantEnum.SectionType.getEnumByType(sectionId)?.name,
                                grades: gradeVOList
                        )
                        vo.sections << sectionVO
                    }
                    try {
                        vo.sections.sort {
                            it.id
                        }
                    } catch (RuntimeException e) {
                        log.warn("学段排序异常")
                    }
                } else {
                    vo.facultyVOList = vocationalUnitService.unEncodeTreeUnitV2(campus.id, hideGrade)
                }
                vo
        }
        resultVO.result.put('list', campusVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

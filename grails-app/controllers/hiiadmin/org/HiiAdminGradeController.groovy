package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.GradeVO
import hiiadmin.school.*
import hiiadmin.school.org.structure.GradeDataStructureService
import hiiadmin.school.user.ParentService
import hiiadmin.utils.ObjectUtils
import hiiadmin.vocationalSchool.FacultyService
import io.swagger.annotations.Api
import org.joda.time.DateTime
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.SchoolYear
import timetabling.vocationalSchool.Faculty

@Slf4j
@Api(value = "年级管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminGradeController implements BaseExceptionHandler {

    GradeService gradeService

    SchoolService schoolService

    UnitService unitService

    StudentService studentService

    ParentService parentService

    CampusService campusService

    FacultyService facultyService

    GradeDataStructureService gradeDataStructureService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long('campusId')
        Long sectionId = params.long('sectionId')
        boolean graduate = params.boolean("graduate", false)
        if (!campusId) {
            campusId = request.getAttribute("campusId") as Long
        }

        Campus campus = campusService.fetchCampusById(campusId)

        if (campus.type == 1) {
            def list
            if (graduate) {
                list = gradeService.fetchAllGradeByCampusIdAndSectionIdLimit(schoolId, campusId, sectionId, p, s, ConstantEnum.RelationStatus.FINISH.status)
            } else {
                list = gradeService.fetchAllGradeByCampusIdAndSectionIdLimit(schoolId, campusId, sectionId, p, s)
            }
            Integer count = list?.totalCount
            Long maxGradeId = 0L
            if (p == 1 && list) {
                maxGradeId = list.get(0).id
            }
            List<GradeVO> gradeVOList = list.collect {
                Grade grade ->
                    GradeVO vo = new GradeVO().buildVO(grade)
                    Integer unitTotal = unitService.countNormalAdminUnitByGradeId(grade.id, false, graduate)
                    Integer studentTotal = studentService.countNormalAdminStudentByGradeId(grade.id, graduate)
                    Integer parentTotal = parentService.countParentByGradeId(grade.id, graduate)
                    vo.unitTotal = unitTotal
                    vo.studentTotal = studentTotal
                    vo.parentTotal = parentTotal
                    if (grade.id == maxGradeId) {
                        vo.maxGrade = true
                    }
                    if (graduate) {
                        vo.graduateTime = grade?.lastUpdated?.time
                    }
                    vo
            }
            Long year = new DateTime().year
            if (count > 0) {
                Long schoolYearId = list?.get(0)?.schoolYearId
                SchoolYear schoolYear = schoolService.fetchSchoolYearById(schoolYearId)
                year = schoolYear?.name
            }
            try {
                gradeVOList.sort {
                    [
                            it.campusId,
                            it.sectionId,
                            -it.code
                    ]
                }
            } catch (RuntimeException e) {
                log.warn("排序异常")
            }
            resultVO.result.put('list', gradeVOList)
            resultVO.result.put('year', year)
            resultVO.result.put('total', count)
        } else {
            // s为1000
            def list = gradeService.fetchAllGradeByCampusIdAndSectionIdLimit(schoolId, campusId, sectionId, p, 1000)
            List<Grade> gradeList = list as List<Grade>
            List<Faculty> facultyList = facultyService.fetchAllFacultyBySearch(campusId, null, 1 as byte)
            List<GradeVO> gradeVOList = []
            facultyList.each {
                Faculty faculty ->
                    gradeList.each {
                        Grade grade ->
                            GradeVO gradeVO = new GradeVO().buildVO(grade)
                            gradeVO.facultyId = faculty.id
                            gradeVO.facultyName = faculty.name
                            gradeVOList << gradeVO
                    }
            }

            resultVO.result.put("list", gradeVOList)
            resultVO.result.put("total", gradeVOList?.size())
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = params.long('campusId')
        Long schoolYearId = params.long('schoolYearId')
        SchoolYear schoolYear
        if (!schoolYearId) {
            schoolYear = schoolService.fetchNormalSchoolYearByCampusId(campusId)
        } else {
            schoolYear = schoolService.fetchSchoolYearById(schoolYearId)
        }
        Integer code = params.int('code')
        Integer sectionId = params.int('sectionId')
        String gradeName = params.gradeName
        List<Grade> gradeList = gradeService.fetchAllNormalGradeByCampusIdAndSectionId(campusId, sectionId)
        Byte sectionType = 1 as byte
        if (gradeList) {
            sectionType = gradeList.get(0).sectionType
        }
        Grade grade = gradeService.createAndSaveGrade(schoolYear, code, sectionId, sectionType, gradeName)
        GradeVO vo = new GradeVO().buildVO(grade)
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        gradeDataStructureService.deleteGrade(id)
        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
    /**
     * 毕业
     */
    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        gradeDataStructureService.updateGrade2Finish(id, userId, userType)
        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

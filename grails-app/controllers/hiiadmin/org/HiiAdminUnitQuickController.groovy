package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.DoorPlateVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.bugu.UnitFullVO
import hiiadmin.school.CampusService
import hiiadmin.school.GradeService
import hiiadmin.school.TeacherService
import hiiadmin.school.UnitService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.org.feature.DoorPlateUnitService
import hiiadmin.utils.ObjectUtils
import hiiadmin.vocationalSchool.FacultyService
import hiiadmin.vocationalSchool.MajorService
import io.swagger.annotations.Api
import timetabling.building.DoorPlate
import timetabling.org.Campus
import timetabling.org.Grade
import timetabling.org.Unit
import timetabling.user.Teacher
import timetabling.vocationalSchool.Faculty
import timetabling.vocationalSchool.Major

@Deprecated
@Api(value = "班级班主任/教室快速设置", tags = "基础管理", consumes = "admin")
class HiiAdminUnitQuickController implements BaseExceptionHandler {

    GradeService gradeService

    UnitService unitService

    TeacherService teacherService

    DoorPlateService doorPlateService

    ViewDOService viewDOService

    CampusService campusService

    FacultyService facultyService

    MajorService majorService

    DoorPlateUnitService doorPlateUnitService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Boolean light = params.boolean("light")
        Long campusId = params.long('campusId')
        Long schoolId = request.getAttribute("schoolId") as Long
        if (campusId == null) {
            campusId = request.getAttribute("campusId") as Long
        }
        Long sectionId = params.long('sectionId')
        Long gradeId = params.long("gradeId")

        Long facultyId = params.long("facultyId")
        Long majorId = params.long("majorId")

        List<UnitFullVO> unitFullVOList = []
        def list = unitService.fetchAllAdminUnitByCampusId(campusId, sectionId, gradeId, facultyId, majorId, p, s)

        Integer count = 0
        if (list) {
            Campus campus = campusService.fetchCampusByCampusId(campusId)
            Map<Long, Grade> map = [:]
            if (campus?.type == 1) {
                map = gradeService.fetchAllNormalGradeIdMapByCampusId(campusId)
            } else {
                map = gradeService.fetchAllGradeIdMapByCampusId(campusId)
            }
            Map<Long, Faculty> facultyMap = facultyService.buildClassFacultyMap(campusId, false)
            Map<Long, Major> majorMap = majorService.buildMajorMap(campusId, false)
            Multimap<Long, Teacher> unitIdTeacherMap = teacherService.fetchUnitIdTeacherIdListMapByCampusId(schoolId, campusId)
            count = list?.totalCount
            list.each {
                Unit unit ->
                    Grade grade = map.get(unit.gradeId)
                    UnitFullVO vo = null
                    if (grade) {
                        vo = new UnitFullVO().buildVO(unit, grade)
                    } else {
                        vo = new UnitFullVO().buildVO(unit)
                    }
                    Faculty faculty = facultyMap?.get(unit.facultyId)
                    if (faculty) {
                        vo.facultyId = unit.facultyId
                        vo.facultyName = faculty.name
                        vo.facultyStatus = faculty.status
                    }

                    Major major = majorMap?.get(unit.majorId)
                    if (major) {
                        vo.majorId = unit.majorId
                        vo.majorName = major.name
                        vo.majorStatus = major.status
                    }

                    if (!light) {
                        Teacher teacher = teacherService.fetchHeadmasterTeacherByUnitId(unit.id)
                        TeacherVO teacherVO = new TeacherVO().buildVO(teacher)
                        vo.teacher = teacherVO
                        DoorPlate doorPlate = doorPlateService.fetchDoorPlateByUnitId(unit.id)
                        DoorPlateVO doorPlateVO = viewDOService.transformDoorPlateVO(doorPlate, false)
                        vo.doorPlate = doorPlateVO
                        List<Teacher> teacherList = unitIdTeacherMap.get(unit.id) as List<Teacher>
                        vo.teacherList = teacherList.collect { new TeacherVO().buildVO(it) }
                    }
                    vo.campusName = campus.name
                    unitFullVOList << vo
            }
        }
        resultVO.result.put('list', unitFullVOList)
        resultVO.result.put('total', count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Unit unit = unitService.fetchUnitById(id)
        Grade grade = gradeService.fetchGradeById(unit.gradeId)
        UnitFullVO vo = null
        if (grade) {
            vo = new UnitFullVO().buildVO(unit, grade)
        } else {
            vo = new UnitFullVO().buildVO(unit)
        }
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateByUnitId(unit.id)
        DoorPlateVO doorPlateVO = viewDOService.transformDoorPlateVO(doorPlate, false)
        vo.doorPlate = doorPlateVO
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long unitId = params.long('id')
        Long doorPlateId = params.long('doorPlateId')
        Long teacherId = params.long('teacherId')
        String teacherIds = params.teacherIds
        Unit newUnit = unitService.fetchUnitById(unitId)
        ServiceResult serviceResult = null
        if (doorPlateId) {
            if (doorPlateId == -1) {
                serviceResult = doorPlateUnitService.deleteUnitDoorPlate(newUnit, null)
            } else {
                serviceResult = doorPlateUnitService.setUnitDoorPlate(newUnit, doorPlateId)
            }
        }
        if (teacherId) {
            if (teacherId == -1) {
                serviceResult = unitService.deleteUnitHeadmaster(newUnit, null)
            } else {
                serviceResult = unitService.setUnitHeadmaster(newUnit, teacherId)
            }
        }
        if (teacherIds) {
            serviceResult = unitService.setUnitTeacherList(unitId, teacherIds)
        }
        if (serviceResult.success) {
            resultVO.result.put('id', unitId)
        } else {
            resultVO.status = 0
            resultVO.code = serviceResult.code as Integer
            resultVO.msg = serviceResult.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long unitId = params.long('id')
        Long doorPlateId = params.long('doorPlateId')
        Unit newUnit = unitService.fetchUnitById(unitId)
        if (doorPlateId) {
            doorPlateUnitService.deleteUnitDoorPlate(newUnit, doorPlateId)
        }
        Long teacherId = params.long('teacherId')
        if (teacherId) {
            unitService.deleteUnitHeadmaster(newUnit, teacherId)
        }
        resultVO.result.put('id', unitId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.SemesterVO
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import io.swagger.annotations.Api
import org.joda.time.DateTime
import timetabling.org.Campus
import timetabling.org.Semester

@Api(value = "学期管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminSemesterController implements BaseExceptionHandler {

    SchoolService schoolService

    CampusService campusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long('campusId')
        List<Semester> semesterList
        if (campusId) {
            semesterList = schoolService.fetchAllSemesterByCampusId(campusId)
        } else {
            semesterList = schoolService.fetchAllSemesterBySchoolId(schoolId)
        }
        List<SemesterVO> semesterVOList = semesterList.collect {
            Campus campus = campusService.fetchCampusByCampusId(it.campusId)
            SemesterVO semesterVO = new SemesterVO().buildVO(it)
            semesterVO.campusName = campus.name
            semesterVO
        }
        semesterVOList.sort {
            SemesterVO a, SemesterVO b ->
                a.campusId <=> b.campusId | a.semesterType <=> b.semesterType
        }
        resultVO.result.put('list', semesterVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long

        String academicYear = params.academicYear
        String json = params.json
        Byte currentSemesterType = params.byte("currentSemesterType")
        
        schoolService.createSemester(campusId, schoolId,academicYear,json,currentSemesterType)
        resultVO.result.put("msg", "success")
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long startDate = params.long('startDate')
        Long endDate = params.long('endDate')
        Long originDate = params.long('originDate')
        Semester semester = schoolService.fetchSemesterBySemesterId(id)
        Semester s = schoolService.fetchAnotherSemester(semester.campusId, id)
        if (startDate) {
            semester.startDate = new Date(startDate)
        }
        if (endDate) {
            semester.endDate = new Date(endDate)
        }
        schoolService.examinationSemester(semester, s)
        if (originDate) {
            DateTime dateTime = new DateTime(originDate)
            int dayOfWeek = dateTime.dayOfWeek
            if (dayOfWeek != 1) {
                dateTime = dateTime.plusDays(1 - dayOfWeek)
            }
            semester.originDate = dateTime.toDate()
        }
        semester = schoolService.saveSemester(semester)
        SemesterVO vo = new SemesterVO().buildVO(semester)
        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        List<Semester> semesterList = schoolService.changeCurrentSemesterById(id)
        List<SemesterVO> semesterVOList = semesterList.collect {
            new SemesterVO().buildVO(it)
        }
        resultVO.result.put('list', semesterVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

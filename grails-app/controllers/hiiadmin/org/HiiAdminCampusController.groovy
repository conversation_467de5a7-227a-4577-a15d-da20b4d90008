package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.CampusVO
import hiiadmin.school.*
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import timetabling.org.Campus

@Api(value = "校区管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminCampusController implements BaseExceptionHandler {

    SchoolService schoolService

    GradeService gradeService

    CampusService campusService

    UnitService unitService

    StudentService studentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        List<Campus> campusList = schoolService.fetchAllCampusBySchoolIdLimit(schoolId, p, s)
        Integer total = schoolService.countAllBySchoolId(schoolId)
        List<CampusVO> campusVOList = campusList.collect {
            Campus campus ->
                CampusVO vo = new CampusVO().buildVO(campus)
                Integer studentTotal = studentService.countNormalAdminStudentByCampusId(campus.id)
                Integer unitTotal = unitService.countNormalAdminUnitByCampusId(campus.id)
                Integer gradeTotal = gradeService.countNormalGradeByCampusId(campus.id)
                Integer sectionTotal = schoolService.countNormalSectionByCampusId(campus.id)
                vo.studentTotal = studentTotal
                vo.unitTotal = unitTotal
                vo.gradeTotal = gradeTotal
                vo.sectionTotal = sectionTotal
                vo
        }
        campusVOList.sort {
            it.name
        }
        resultVO.result.put('list', campusVOList)
        resultVO.result.put('total', total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    // 开关荣誉评分，活动参与评分，任职履历评分
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = Campus.findById(campusId)

        String item = params.item
        Integer singleViolation = params.int("singleViolation")
        Integer totalViolation = params.int("totalViolation")
        Boolean flag = params.boolean("flag")

        switch (item) {
            case 'honor':
                campus.honorFlag = flag
                break
            case 'activity':
                campus.activityFlag = flag
                break
            case 'job':
                campus.jobFlag = flag
                break
            case 'violation':
                if (singleViolation != null && totalViolation != null) {
                    campus.singleViolation = singleViolation
                    campus.totalViolation = totalViolation
                }

                if (flag != null) {
                    campus.violationFlag = flag
                }
                break
        }

        campusService.campusSave(campus)

        resultVO.result.put(item, flag)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        Campus campus = Campus.findById(campusId)

        CampusVO campusVO = new CampusVO().buildVO(campus)
        campusVO.honorFlag = campus.honorFlag
        campusVO.activityFlag = campus.activityFlag
        campusVO.jobFlag = campus.jobFlag
        campusVO.violationFlag = campus.violationFlag
        campusVO.singleViolation = campus.singleViolation
        campusVO.totalViolation = campus.totalViolation

        resultVO.result = ObjectUtils.resultVOObject2Map(campusVO)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

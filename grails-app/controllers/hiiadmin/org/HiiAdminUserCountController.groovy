package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.TeacherService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.user.feature.ParentStudentService

@Deprecated
class HiiAdminUserCountController implements BaseExceptionHandler{
    
    ParentStudentService parentStudentService

    UnitStudentService unitStudentService

    TeacherService teacherService
	
    def index() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        Long campusId = params.long("campusId")
        if (!campusId) {
            campusId = request.getAttribute("campusId") as Long
        }
        
        
        Integer parentCount = parentStudentService.countParentByCampusId(campusId)
        Integer studentCount = unitStudentService.countStudentByCampusId(campusId)
        Integer teacherCount = teacherService.countAllTeacherByNameOrMobileOrJobNum(campusId, null)
        
        resultVO.result.put("parent", parentCount ?: 0)
        resultVO.result.put("student", studentCount ?: 0)
        resultVO.result.put("teacher", teacherCount ?: 0)
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

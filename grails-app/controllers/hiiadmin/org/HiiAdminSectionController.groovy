package hiiadmin.org

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.bugu.ResultVO
import com.google.common.base.Joiner
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.approval.ApprovalV2Service
import hiiadmin.exceptions.NoSettingsException
import hiiadmin.module.bugu.SectionVO
import hiiadmin.school.GradeService
import hiiadmin.school.SchoolService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.school.org.structure.GradeDataStructureService
import hiiadmin.school.org.structure.SchoolDataStructureService
import hiiadmin.school.user.ParentService
import io.swagger.annotations.Api
import timetabling.org.Campus
import timetabling.org.SchoolYear

import static grails.async.Promises.task

@Api(value = "学段管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminSectionController implements BaseExceptionHandler {

    SchoolService schoolService

    GradeService gradeService

    StudentService studentService

    UnitService unitService

    ApprovalV2Service approvalV2Service

    GradeDataStructureService gradeDataStructureService

    SchoolDataStructureService schoolDataStructureService

    ParentService parentService

    CacheService cacheService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolYearId = params.long('schoolYearId')
        Long campusId = params.long('campusId')
        boolean graduate = params.boolean("graduate", false)
        SchoolYear schoolYear
        if (!schoolYearId) {
            schoolYear = schoolService.fetchNormalSchoolYearByCampusId(campusId)
        } else {
            schoolYear = schoolService.fetchSchoolYearById(schoolYearId)
        }
        List<SectionVO> sectionVOList = []
        String types = ""
        List<String> typeStrList = []
        if (graduate) {
            // ConstantEnum.SectionType 
            List<SchoolYear> schoolYearList = schoolService.fetchAllSchoolYearByCampusId(campusId, 1 as byte)
            schoolYearList.each {
                if (it.sectionIds) {
                    List<String> sections = it.sectionIds.split(",")
                    typeStrList.addAll(sections)
                }
            }
            
            typeStrList.remove(null)
            if (typeStrList) {
                types = Joiner.on(",").join(typeStrList.unique())
            } else {
                types = "1,3,5,6,7,8,9,10"
            }
        } else {
            types = schoolYear.sectionIds
        }
        ConstantEnum.SectionType.getAllEnumByTypes(types).each {
            Integer studentTotal = studentService.countNormalAdminStudentByCampusIdAndSectionId(campusId, it.id, graduate)
            Integer unitTotal = unitService.countNormalAdminUnitByCampusIdAndSectionId(campusId, it.id, graduate)
            Integer gradeTotal = gradeService.countNormalGradeByCampusIdAndSectionId(campusId, it.id, graduate)
            Integer parentTotal = 0
            if (graduate) {
                parentTotal = parentService.countParentBySectionId(campusId, it.id, true)
            }
            sectionVOList << new SectionVO(
                    id: it.id,
                    name: it.name,
                    studentTotal: studentTotal,
                    unitTotal: unitTotal,
                    gradeTotal: gradeTotal,
                    parentTotal: parentTotal
            )
        }
        sectionVOList.sort {
            it.id
        }
        resultVO.result.put('list', sectionVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String section = params.section
        Long campusId = params.long('campusId')
        List<SectionVO> sectionVOList = JSONArray.parseArray(section, SectionVO.class)
        SchoolYear schoolYear = schoolService.fetchNormalSchoolYearByCampusId(campusId)
        String sectionIds = schoolDataStructureService.autoCreateAndSaveGradeAndUnit(schoolYear, sectionVOList)
        List<SectionVO> vos = []
        ConstantEnum.SectionType.getAllEnumByTypes(sectionIds).each {
            vos << new SectionVO(
                    id: it.id,
                    name: it.name
            )
        }
        resultVO.result.put('list', vos)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long campusId = params.long('campusId')
        gradeDataStructureService.deleteSection(campusId, id)
        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long sectionId = params.long('id')
        Long campusId = params.long('campusId')
        //预留字段 直接执行升级操作
//        Boolean force = params.boolean('force')
        if (!gradeService.canUpgrade(sectionId, campusId)) {
            throw new NoSettingsException("当前学段有未毕业班级")
        }
        String key = 'upGrade' + '_' + campusId + '_' + sectionId
        
        Long keyV = cacheService.updateGradeCache.get(key.toString())
        
        if (keyV && keyV == campusId) {
            resultVO.status = 0
            resultVO.message = "数据正在处理中，请稍后重试"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return 
        }
        cacheService.updateGradeCache.put(key.toString(), campusId)
        Campus campus = schoolService.fetchCampusByCampusId(campusId)
        gradeDataStructureService.update4UpgradeBySectionIdAndCampusId(sectionId, campus)
        def t = task {
            approvalV2Service.updateProcessJsonGradeIdByGradeUpdate(campusId)
        }
        cacheService.updateGradeCache.remove(key.toString())
        t.onComplete {
            log.info("升年级后更新审批中的年级成功，campusId:${campusId}")
        }
        resultVO.result.put('id', sectionId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

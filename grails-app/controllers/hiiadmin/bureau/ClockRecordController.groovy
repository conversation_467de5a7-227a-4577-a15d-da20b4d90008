package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ClockService
import hiiadmin.module.bugu.HealthClockRecordVO
import timetabling.HealthClockRecord

import static com.bugu.PhoenixCoder.decodeId

class ClockR<PERSON>ordController implements BaseExceptionHandler {

    ClockService clockService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String studentStr = params.studentId
        Long studentId = decodeId(studentStr)
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<HealthClockRecord> clockRecordList = clockService.fetchAllHealthClockRecordByClockIdAndStudentId(studentId, p, s)
        Integer total = clockService.countHealthClockRecordByClockIdAndStudentId(studentId)
        List<HealthClockRecordVO> voList = []
        clockRecordList.each {
            HealthClockRecordVO vo = new HealthClockRecordVO(
                    id: it.id,
                    clockTime: it.lastUpdated?.getTime(),
                    clock: it.clock
            )
            voList.add(vo)
        }
        resultVO.result.put('list', voList)
        resultVO.result.put('total', total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

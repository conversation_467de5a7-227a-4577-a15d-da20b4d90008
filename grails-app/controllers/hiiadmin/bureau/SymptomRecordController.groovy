package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Lists
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.SymptomRecordVO
import hiiadmin.school.SymptomService
import timetabling.symptom.SymptomRecord

import static com.bugu.PhoenixCoder.decodeId

class SymptomRecordController implements BaseExceptionHandler {

    SymptomService symptomService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        String studentStr = params.studentId
        Long studentId = decodeId(studentStr)
        List<SymptomRecord> symptomRecordList = symptomService.fetchAllSymptomRecordByStudentId(studentId, p, s)
        Integer total = symptomService.countSymptomRecordByStudentId(studentId)
        List<SymptomRecordVO> symptomRecordVOList = Lists.newArrayList()
        symptomRecordList.each {
            SymptomRecordVO symptomRecordVO = symptomService.buildSymptomRecordVO(it)
            symptomRecordVOList.add(symptomRecordVO)
        }
        symptomRecordVOList.sort {
            -it.id
        }
        resultVO.result.put('total', total)
        resultVO.result.put('list', symptomRecordVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

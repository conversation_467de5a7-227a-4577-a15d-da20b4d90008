package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.HealthRecordVO
import hiiadmin.school.HealthRecordService
import hiiadmin.utils.ObjectUtils
import timetabling.HealthRecord

import static com.bugu.PhoenixCoder.decodeId

class HealthRecordController implements BaseExceptionHandler {

    HealthRecordService healthRecordService

    ViewDOService viewDOService

    def show() {
        ResultVO resultVO = new ResultVO()
        String studentStr = params.id
        Long studentId = decodeId(studentStr)
        HealthRecord healthRecord = healthRecordService.fetchHealthRecordViaStudentId(studentId)
        if (healthRecord) {
            HealthRecordVO healthRecordVO = viewDOService.buildHealthRecordVO(healthRecord)
            resultVO.result.putAll(ObjectUtils.objectToMap(healthRecordVO))
        }
        resultVO.status = 1 as byte
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

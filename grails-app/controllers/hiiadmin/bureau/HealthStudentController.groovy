package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.BureauStudentVO
import hiiadmin.module.bugu.StudentVO
import hiiadmin.school.StudentService
import hiiadmin.utils.ObjectUtils
import timetabling.user.Student

import static com.bugu.PhoenixCoder.decodeId

class HealthStudentController implements BaseExceptionHandler {

    StudentService studentService

    ViewDOService viewDOService

    def index() {
        String campusStr = params.campusId
        Long campusId = decodeId(campusStr)
        String unitStr = params.unitId
        Long unitId = decodeId(unitStr)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        def map = studentService.fetchAllStudentByLimit(campusId, unitId, p, s)
        List<Student> studentList = map.get("list") as List<Student>
        Integer total = map.get("total") as Integer
        List<BureauStudentVO> vos = viewDOService.buildStudentVOList(campusId, studentList)
        resultVO.result.put("total", total)
        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String studentStr = params.id
        Long studentId = decodeId(studentStr)
        Student student = studentService.fetchStudentById(studentId)
        StudentVO vo = viewDOService.buildStudentVOAddParents(student)
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

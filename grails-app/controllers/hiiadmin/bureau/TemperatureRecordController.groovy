package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.TemperatureRecordVO
import hiiadmin.school.TemperatureRecordService
import timetabling.TemperatureRecord

import static com.bugu.PhoenixCoder.decodeId

class TemperatureRecordController implements BaseExceptionHandler {

    TemperatureRecordService temperatureRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String studentStr = params.studentId
        Long studentId = decodeId(studentStr)
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<TemperatureRecord> list = temperatureRecordService.fetchAllTemperatureRecordByStudentId(studentId, p, s)
        Integer total = temperatureRecordService.countTemperatureRecordByStudentId(studentId)
        List<TemperatureRecordVO> vos = []
        list.each {
            TemperatureRecordVO vo = new TemperatureRecordVO().buildVO(it)
            Byte status = -1
            if (it.normal || it.temperature <= ConstantEnum.Temperature.NORMAL.max) {
                status = 1
            }
            vo.status = status
            vos.add(vo)
        }
        resultVO.result.put('list', vos)
        resultVO.result.put('total', total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

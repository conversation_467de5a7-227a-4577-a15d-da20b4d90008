package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.dataV.TemplateService
import hiiadmin.module.dataV.DataVTemplateVO
import hiiadmin.utils.DataVTokenUtils
import timetabling.BureauDatavTemplate

import static com.bugu.PhoenixCoder.decodeId

class TemplateController implements BaseExceptionHandler {

    TemplateService templateService

    BureauService bureauService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Long bureauId = request.getAttribute("bureauId") as Long
        Long provinceId = bureauService.getBureau(bureauId)?.provinceId
        List<BureauDatavTemplate> list = templateService.fetchAllDatavTemplateByProvinceIdAndType(provinceId, ConstantEnum.DataTemplateEnum.BUREAU.type, p, s)
        Integer total = templateService.countDatavTemplateByProvinceIdAndType(provinceId, ConstantEnum.DataTemplateEnum.BUREAU.type)
        List<DataVTemplateVO> vos = []
        list.each {
            template ->
                vos << new DataVTemplateVO().buildVO(template)
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", total)
        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = decodeId(idStr)
        Long bureauId = request.getAttribute("bureauId") as Long
        BureauDatavTemplate template = templateService.getBureauDatavTemplate(id)
        switch (template.type) {
            case 1 as byte:
                String url = DataVTokenUtils.getSignedUrl(template.screenId, template.token)
                resultVO.result.put("url", url + "&bureauId=" + bureauId)
                break
            case 2 as byte:
                String url = template.url
                resultVO.result.put("url", url)
                break
        }
        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }
}

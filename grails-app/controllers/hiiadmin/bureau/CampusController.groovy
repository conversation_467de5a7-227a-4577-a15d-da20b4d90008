package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.bureau.BureauCampusVO
import timetabling.BureauCampus

class CampusController implements BaseExceptionHandler {

    BureauCampusService bureauCampusService


    def index() {
        Long bureauId = request.getAttribute("bureauId") as Long
        int p = params.int("p", 1)
        int s = params.int('s', 30)
        String searchValue = params.searchValue
        ResultVO resultVO = new ResultVO()
        List<BureauCampus> bureauCampusList = bureauCampusService.fetchAllBureauCampusViaBureauIdLimit(bureauId, searchValue, p, s)
        List<BureauCampusVO> vos = []
        bureauCampusList?.each {
            BureauCampus bureauCampus ->
//                List<Student> studentList = studentService.fetchAllStudentByCampusId(bureauCampus.campusId)
                BureauCampusVO vo = new BureauCampusVO().buildVO(bureauCampus)
                vos.add(vo)
        }
        Integer total = bureauCampusService.countBureauCampusByBureauId(bureauId) ?: 0
        resultVO.status = 1 as byte
        resultVO.result.put("list", vos)
        resultVO.result.put("total", total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

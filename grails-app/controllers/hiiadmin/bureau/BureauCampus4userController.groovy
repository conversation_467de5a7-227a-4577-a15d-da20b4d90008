package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.BureauVO

class BureauCampus4userController implements BaseExceptionHandler {

    BureauCampusService bureauCampusService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        List<BureauVO> bureauVOList = bureauCampusService.transformBureauSchoolCampusVO4Campus(campusId)
        render text: JSON.toJSONString(ResultVO.success([list: bureauVOList])), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.UnitVO
import hiiadmin.school.GradeService
import hiiadmin.school.UnitService
import timetabling.org.Grade
import timetabling.org.Unit

import static com.bugu.PhoenixCoder.decodeId

class UnitController implements BaseExceptionHandler {

    UnitService unitService

    GradeService gradeService

    ViewDOService viewDOService

    def index() {
        String campusStr = params.campusId
        ResultVO resultVO = new ResultVO()
        Long campusId = decodeId(campusStr)
        List<Grade> gradeList = gradeService.fetchAllGradeByCampusId(campusId)
        Map<Long, Grade> gradeMap = gradeService.fetchAllNormalGradeIdMapByCampusId(campusId)
        List<Unit> unitList = unitService.fetchAllAdminUnitViaCampusId(campusId)
        List<UnitVO> vos = []
        unitList?.each { Unit unit ->
            Grade grade = gradeMap.get(unit.gradeId)
            vos.add(viewDOService.buildUnitVOByUnit(unit, grade))
        }
        resultVO.status = 1 as byte
        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

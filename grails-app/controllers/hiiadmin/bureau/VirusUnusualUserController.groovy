package hiiadmin.bureau

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.BureauStudentVO
import hiiadmin.school.StudentService
import hiiadmin.school.VirusSymptomService
import hiiadmin.school.VirusUserService
import timetabling.VirusSymptom
import timetabling.VirusUnusualUserRecord
import timetabling.user.Student

import static com.bugu.PhoenixCoder.decodeId

class VirusUnusualUserController implements BaseExceptionHandler {

    VirusUserService virusUserService

    StudentService studentService

    VirusSymptomService virusSymptomService

    ViewDOService viewDOService

    def index() {
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        String campusStr = params.campusId
        Long adviceId = params.long('adviceId')
        Long startTimel = params.long('startTime')
        Long endTimel = params.long('endTime')

        Long campusId = decodeId(campusStr)

        //继续观察人数
        int watchCount = virusUserService.countVirusUnusualUserRecord(campusId, ConstantEnum.VirusAdviceEnum.WATCH.id)
        //疑似人数
        int seeminglyCount = virusUserService.countVirusUnusualUserRecord(campusId, ConstantEnum.VirusAdviceEnum.SEEMINGLY.id)
        //确诊人数
        int definiteCount = virusUserService.countVirusUnusualUserRecord(campusId, ConstantEnum.VirusAdviceEnum.DEFINITE.id)
        //解除观察人数
        int removedCount = virusUserService.countVirusUnusualUserRecord(campusId, ConstantEnum.VirusAdviceEnum.REMOVED.id)

        def virusUnusualUserRecordList = virusUserService.fetchVirusUnusualUserRecordPagedForAdmin(p, s, campusId, adviceId, startTimel, endTimel)

        Integer count = virusUnusualUserRecordList?.totalCount

        //这里只查询了学生的，如后续支持老师，则根据userType判断即可
        List<Long> studentIdList = Lists.newArrayList()
        Map<Long, String> symptomMap = Maps.newHashMap()
        List<VirusSymptom> virusSymptomList = virusSymptomService.findAllVirusSymptom()
        virusSymptomList.each {
            VirusSymptom symptom ->
                symptomMap.put(symptom.id, symptom.name)
        }
        virusUnusualUserRecordList.each {
            VirusUnusualUserRecord virusUnusualUserRecord ->
                studentIdList.add(virusUnusualUserRecord.uId)
        }
        List<Student> studentList
        Map<Long, BureauStudentVO> studentVOMap = Maps.newHashMap()
        if (studentIdList.size() > 0) {
            studentList = studentService.fetchStudentListByStudentIdList(studentIdList)
            studentList?.each {
                Student student ->
                    BureauStudentVO studentVO = viewDOService.buildBureauStudentVO(student)
                    studentVOMap.put(student.id, studentVO)
            }
        }

        [virusUnusualUserRecordList: virusUnusualUserRecordList,
         symptomMap                : symptomMap,
         totalCount                : count,
         watchCount                : watchCount,
         seeminglyCount            : seeminglyCount,
         definiteCount             : definiteCount,
         removedCount              : removedCount,
         studentVOMap              : studentVOMap
        ]
    }
}

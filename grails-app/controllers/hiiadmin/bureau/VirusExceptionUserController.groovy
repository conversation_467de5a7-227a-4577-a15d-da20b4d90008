package hiiadmin.bureau

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.VirusCampusVO
import hiiadmin.school.VirusUserService
import timetabling.BureauCampus
import timetabling.VirusUnusualUserRecord

import static com.bugu.PhoenixCoder.encodeId

class VirusExceptionUserController implements BaseExceptionHandler {

    BureauCampusService bureauCampusService

    VirusUserService virusUserService

    def index() {
        Long bureauId = request.getAttribute("bureauId") as Long
        int p = params.int("p", 1)
        int s = params.int('s', 30)
        ResultVO resultVO = new ResultVO()
        List<BureauCampus> bureauCampusList = bureauCampusService.fetchAllBureauCampusViaBureauIdLimit(bureauId, p, s)
        List<Long> campusIdList = bureauCampusList*.campusId
        List<VirusCampusVO> vos = []
        campusIdList?.each {
            Long campusId ->
                List<VirusUnusualUserRecord> virusUserList = virusUserService.fetchAllVirusUserByCampusId(campusId)
                Integer total = virusUserList?.size() ?: 0
                Integer watch = virusUserList?.findAll {
                    it.adviceId == ConstantEnum.VirusAdviceEnum.WATCH.id
                }?.size() ?: 0
                Integer seemingly = virusUserList?.findAll {
                    it.adviceId == ConstantEnum.VirusAdviceEnum.SEEMINGLY.id
                }?.size() ?: 0
                Integer definite = virusUserList?.findAll {
                    it.adviceId == ConstantEnum.VirusAdviceEnum.DEFINITE.id
                }?.size() ?: 0
                Integer removed = virusUserList?.findAll {
                    it.adviceId == ConstantEnum.VirusAdviceEnum.REMOVED.id
                }?.size() ?: 0
                BureauCampus bureauCampus = bureauCampusList.find { it.campusId == campusId }
                VirusCampusVO vo = new VirusCampusVO(
                        campusId: encodeId(campusId),
                        campusName: bureauCampus?.campusName,
                        schoolName: bureauCampus?.schoolName,
                        total: total,
                        watch: watch,
                        seemingly: seemingly,
                        definite: definite,
                        removed: removed
                )
                vos.add(vo)
        }
        Integer totalCount = bureauCampusService.countBureauCampusByBureauId(bureauId)
        resultVO.result.put("total", totalCount)
        resultVO.result.put("list", vos)
        resultVO.status = 1 as byte
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

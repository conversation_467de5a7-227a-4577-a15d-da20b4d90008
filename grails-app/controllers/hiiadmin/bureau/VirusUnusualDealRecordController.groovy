package hiiadmin.bureau

import com.google.common.collect.Maps
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.VirusSymptomService
import timetabling.VirusSymptom
import timetabling.VirusUnusualRecordHandleRecord

class VirusUnusualDealRecordController implements BaseExceptionHandler {

    VirusSymptomService virusSymptomService

    def index() {
        Long id = params.long("id")
        int p = params.int('p', 1)
        int s = params.int('s', 30)


        def c = VirusUnusualRecordHandleRecord.createCriteria()
        def virusUnusualRecordHandleRecordList = c.list(max: s, offset: (p - 1) * s) {
            eq('virusUnusualRecordId', id)
            eq('status', 1 as byte)
            order('id', 'desc')
        }

        Map<Long, String> symptomMap = Maps.newHashMap()
        List<VirusSymptom> virusSymptomList = virusSymptomService.findAllVirusSymptom()
        virusSymptomList.each {
            VirusSymptom symptom ->
                symptomMap.put(symptom.id, symptom.name)
        }

        int total = virusUnusualRecordHandleRecordList?.totalCount

        [virusUnusualRecordHandleRecordList: virusUnusualRecordHandleRecordList, symptomMap: symptomMap, total: total]
    }
}

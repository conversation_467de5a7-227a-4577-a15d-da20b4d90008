package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.HashMultimap
import com.google.common.collect.Multimap
import hiiadmin.module.JwtUser
import hiiadmin.module.SchoolVO
import hiiadmin.module.bugu.CampusVO
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import hiiadmin.school.TeacherService
import hiiadmin.utils.GladJwt
import timetabling.org.Campus
import timetabling.org.School
import timetabling.user.TeacherSchoolCampus

class AdminGladRefreshTokenController {

    TeacherService teacherService

    SchoolService schoolService

    CampusService campusService

    def save() {
        Long teacherId = request.getAttribute("userId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        if (teacherId && schoolId) {
            JwtUser jwtUser = new JwtUser()
            jwtUser.setSchoolId(schoolId)
            jwtUser.setCampusId(campusId)
            jwtUser.setUserId(teacherId)
            String token = GladJwt.createJWT(String.valueOf(teacherId), JSON.toJSONString(jwtUser))
            List<TeacherSchoolCampus> schoolCampusList = teacherService.fetchAllTeacherSchoolByTeacherId(teacherId)
            Multimap<Long, Campus> schoolCampusHashMultimap = HashMultimap.create()

            schoolCampusList.each { schoolCampus ->
                Campus campus = campusService.fetchCampusByCampusId(schoolCampus.campusId)
                schoolCampusHashMultimap.put(schoolCampus.schoolId, campus)
            }
            Set<Long> schoolIdList = schoolCampusHashMultimap.keySet()
            List<SchoolVO> schoolVOList = []
            schoolIdList.each { sId ->
                School school = schoolService.fetchSchoolById(sId)
                SchoolVO schoolVO = new SchoolVO().buildVO(school)
                schoolVO.campuses = []
                Set<Campus> campusList = schoolCampusHashMultimap.get(sId)
                campusList.each { campus ->
                    CampusVO campusVO = new CampusVO().buildVO(campus)
                    campusVO.current = (campus.id == campusId) ? 1 as byte : 0 as byte
                    schoolVO.campuses << campusVO
                }
                schoolVOList << schoolVO
            }
            resultVO.result.put('token', "Bearer " + token)
            resultVO.result.put("schoolCampusList", schoolVOList)
        } else {
            resultVO.status = 0
            resultVO.code = BizErrorCode.PERMISSION_ERROR.code
            resultVO.message = BizErrorCode.PERMISSION_ERROR.msg
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

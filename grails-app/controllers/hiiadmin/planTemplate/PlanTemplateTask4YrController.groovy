package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.MqMessageTag
import hiiadmin.docking.gate.DockingFirmAuthTaskService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.module.docking.dhYr.DockingFirmAuthTaskVO
import hiiadmin.module.planTemplate.PlanTemplatePersonGroupVO
import hiiadmin.module.planTemplate.PlanTemplateVO
import hiiadmin.mq.producer.YunRuiAuthOrderProducerService
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.ToStringUnits
import timetabling.docking.gate.DockingFirmAuthTask
import timetabling.planTemplate.PlanTemplate
import timetabling.planTemplate.PlanTemplatePersonGroup

import static hiiadmin.ConstantEnum.DeviceFirm
import static hiiadmin.ConstantEnum.OperateType

class PlanTemplateTask4YrController implements BaseExceptionHandler {

    DockingFirmAuthTaskService dockingFirmAuthTaskService

    PlanTemplateService planTemplateService

    YunRuiAuthOrderProducerService yunRuiAuthOrderProducerService

    DeviceService deviceService

    PlanTemplatePersonGroupService planTemplatePersonGroupService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        String code = params.code
        Long planId = params.long("planId")
        Integer type = params.int("type")
        Integer status = params.int("status")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = dockingFirmAuthTaskService.fetchDockingFirmAthTakLimit(campusId, searchValue, type, status, code, planId, p, s)
        List<DockingFirmAuthTaskVO> vos = map.list?.collect {
            DockingFirmAuthTaskVO taskVO = new DockingFirmAuthTaskVO().buildVO(it)

            PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(it.planId)
            taskVO.planTemplate = vo
            taskVO
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long("id")
        DockingFirmAuthTask task = dockingFirmAuthTaskService.fetchTaskById(taskId)
        DockingFirmAuthTaskVO taskVO = new DockingFirmAuthTaskVO().buildVO(task)
        List<Long> deviceIds = ToStringUnits.idsString2LongList(task.getDeviceIds())
        List<DeviceVO> deviceVOList = deviceService.fetchAllDeviceByIdInList(deviceIds)?.collect {
            new DeviceVO().buildVO(it)
        }

        taskVO.devices = deviceVOList

        PlanTemplatePersonGroup planTemplatePersonGroup = planTemplatePersonGroupService.fetchPlanTemplatePersonGroupById(task.groupId)
        PlanTemplatePersonGroupVO groupVO = new PlanTemplatePersonGroupVO().buildVO(planTemplatePersonGroup)
        taskVO.planTemplatePersonGroup = groupVO

        PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(task.planId)
        taskVO.planTemplate = vo

        resultVO.result = taskVO
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }


    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Long groupId = params.long("groupId")
        Long planId = params.long("planId")
        String deviceIds = params.deviceIds
        if (!deviceIds) {
            throw new HiiAdminException("请选择设备！")
        }
        String name = params.name
        PlanTemplate planTemplate = planTemplateService.fetchPlanTemplateByPlanId(planId)
        if (planTemplate.firm != DeviceFirm.DH_YR.firm) {
            throw new HiiAdminException("时间模板不支持该平台")
        }
        DockingFirmAuthTask authTask = dockingFirmAuthTaskService.createAuthTask(name, planId, OperateType.UPDATE.type, groupId, deviceIds)

        yunRuiAuthOrderProducerService.sendMessage(MqMessageTag.authGateMessage.name(), campusId.toString(), campusId.toString(), JSON.toJSONString([taskId: authTask.id]).bytes)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success(id: authTask.id)), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        Long campusId = request.getAttribute("campusId") as Long
        long taskId = params.long("id")
        DockingFirmAuthTask authTask = dockingFirmAuthTaskService.deleteTask(taskId)
        yunRuiAuthOrderProducerService.sendMessage(MqMessageTag.authGateMessage.name(), campusId.toString(), campusId.toString(), JSON.toJSONString([taskId: authTask.id]).bytes)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success(id: authTask.id)), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

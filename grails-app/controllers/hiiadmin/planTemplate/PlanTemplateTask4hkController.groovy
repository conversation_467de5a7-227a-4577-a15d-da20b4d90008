package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.planTemplate.PlanTemplateDeviceVO
import hiiadmin.module.planTemplate.PlanTemplateTaskVO
import hiiadmin.module.planTemplate.PlanTemplateVO
import timetabling.planTemplate.PlanTemplate
import timetabling.planTemplate.PlanTemplateDevice
import timetabling.planTemplate.PlanTemplateTask

/**
 * 安防管控中心
 * -下发任务管理
 */
@Slf4j
class PlanTemplateTask4hkController implements BaseExceptionHandler {

    PlanTemplateService planTemplateService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        String code = params.code
        Long planId = params.long("planId")
        Byte status = params.byte("status")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = planTemplateService.fetchAllPlanTemplateTaskByCampusId(campusId, searchValue, status, code, planId, p, s)
        List<PlanTemplateTaskVO> vos = map.list?.collect {
            PlanTemplate planTemplate = planTemplateService.fetchPlanTemplateByPlanId(it.planId)
            PlanTemplateVO vo = new PlanTemplateVO().buildVO(planTemplate)
            PlanTemplateTaskVO taskVO = new PlanTemplateTaskVO().buildVO(it)
            taskVO.planTemplate = vo
            taskVO.deviceCount = planTemplateService.countPlanTemplateDeviceByTaskId(it.id)
            taskVO
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte

        Long taskId = params.long("id")
        PlanTemplateTask task = planTemplateService.fetchPlanTemplateTaskByTaskId(taskId)
        PlanTemplateTaskVO planTemplateTaskVO = new PlanTemplateTaskVO().buildVO(task)

        PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(task)
        planTemplateTaskVO.planTemplate = vo

        List<PlanTemplateDevice> planTemplateDeviceList = planTemplateService.fetchAllPlanTemplateDeviceByTaskId(taskId)
        planTemplateTaskVO.devices = planTemplateDeviceList.collect {
            new PlanTemplateDeviceVO().buildVO(it)
        }
        resultVO.result = planTemplateTaskVO
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = ResultVO.success()

        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long planId = params.long("planId")
        String deviceIds = params.deviceIds
        Integer firm = params.int("firm", ConstantEnum.DeviceFirm.HK_EDU.firm)
        if (firm != ConstantEnum.DeviceFirm.HK_EDU.firm) {
            throw new HiiAdminException("仅支持海康")
        }
        Integer taskType = params.int("taskType")
        Long groupId = params.long("groupId")
        String name = params.name

        PlanTemplateTask task = planTemplateService.authDownloadV2(schoolId, campusId, planId, name, firm, taskType, deviceIds, groupId)
        resultVO.result.put("id", task.id)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long("id")
        planTemplateService.deletePlanTemplateTaskV2(taskId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplatePersonGroupVO
import timetabling.planTemplate.PlanTemplatePersonGroup

/**
 * 安防管控中心
 * -人员通行组
 */
@Slf4j
class PlanTemplatePersonGroupController implements BaseExceptionHandler {

    PlanTemplatePersonService planTemplatePersonService

    PlanTemplatePersonGroupService planTemplatePersonGroupService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        String groupName = params.searchValue
        Byte groupType = params.byte("userType")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = planTemplatePersonGroupService.fetchAllPlanTemplatePersonGroupByCampusId(campusId, groupName, groupType, p, s)
        List<PlanTemplatePersonGroup> groupList = map.list
        Integer count = map.total as Integer
        List<PlanTemplatePersonGroupVO> vos = []
        groupList?.each {
            PlanTemplatePersonGroupVO groupVO = new PlanTemplatePersonGroupVO().buildVO(it)
            Integer personCount = planTemplatePersonService.countPlanTemplatePersonByGroupId(it.id)
            groupVO.personCount = personCount
            vos << groupVO
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        Byte userType = params.byte("userType")
        Long groupId = planTemplatePersonGroupService.createAndAsyncPersonGroup(campusId, name, userType)
        resultVO.result.put("id", groupId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long groupId = params.long("id")
        planTemplatePersonGroupService.deleteAndAsyncPlanTemplatePersonGroup(groupId)
        resultVO.result.put("id", groupId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

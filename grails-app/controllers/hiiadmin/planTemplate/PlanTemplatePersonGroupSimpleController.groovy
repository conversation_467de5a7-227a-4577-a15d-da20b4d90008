package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplatePersonGroupVO
import timetabling.planTemplate.PlanTemplatePersonGroup

class PlanTemplatePersonGroupSimpleController implements BaseExceptionHandler {

    PlanTemplatePersonGroupService planTemplatePersonGroupService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")

        List<PlanTemplatePersonGroup> groupList = planTemplatePersonGroupService.fetchAllPlanTemplatePersonGroup(campusId, firm)

        List<PlanTemplatePersonGroupVO> vos = groupList.collect { group ->
            new PlanTemplatePersonGroupVO().buildVO(group)
        }
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([list: vos])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplatePersonVO
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import timetabling.planTemplate.PlanTemplatePersonGroup

import static hiiadmin.ConstantEnum.UserTypeEnum

/**
 * 安防管控中心
 * -人员管理
 */
@Slf4j
class PlanTemplatePersonController implements BaseExceptionHandler {

    PlanTemplateService planTemplateService

    PlanTemplatePersonService planTemplatePersonService

    PlanTemplatePersonGroupService planTemplatePersonGroupService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long groupId = params.long("groupId")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        boolean exist = params.boolean("exist", true)
        PlanTemplatePersonGroup planTemplatePersonGroup = planTemplatePersonGroupService.fetchPlanTemplatePersonGroupById(groupId)

        Long gradeId = PhenixCoder.decodeId(params.gradeId)
        Long unitId = PhenixCoder.decodeId(params.unitId)

        Boolean gender = params.boolean("gender")
        Long layerId = PhenixCoder.decodeId(params.layerId)
        String searchValue = params.searchValue

        Long departmentId = PhenixCoder.decodeId(params.departmentId)

        String studentCodes = params.studentCodes
        if (exist) {
            switch (planTemplatePersonGroup.userType) {
                case UserTypeEnum.STUDENT.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4existStudent(groupId, gradeId, unitId, gender, layerId, searchValue, p, s)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
                case UserTypeEnum.TEACHER.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4existTeacher(groupId, departmentId, gender, searchValue, p, s)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
                case UserTypeEnum.FERRIES.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4Ferries(groupId, gradeId, unitId, searchValue, exist, p, s, true)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
            }
        } else {
            switch (planTemplatePersonGroup.userType) {
                case UserTypeEnum.STUDENT.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4unexistStudent(groupId, gradeId, unitId, gender, layerId, searchValue, studentCodes, p, s)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
                case UserTypeEnum.TEACHER.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4unexistTeacher(groupId, departmentId, gender, searchValue, p, s)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
                case UserTypeEnum.FERRIES.type:
                    def map = planTemplateService.transformPlanTemplatePersonVO4Ferries(groupId, gradeId, unitId, searchValue, exist, p, s)
                    List<PlanTemplatePersonVO> vos = map.list
                    resultVO.result.put("list", vos)
                    Integer total = map.total
                    resultVO.result.put("total", total)
                    break
            }
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
    /**
     * TODO 云睿权限处理
     * 产品逻辑没理顺，暂不加
     * @return
     */
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long groupId = params.long("groupId")
        String userIds = PhenixCoder.decodeIds(params.userIds)
        PlanTemplatePersonGroup group = planTemplatePersonGroupService.fetchPlanTemplatePersonGroupById(groupId)

        String msg = planTemplatePersonService.personAdd(group, userIds)
        planTemplatePersonGroupService.updateGroupLastUpdate(groupId)
        resultVO.result.put("message", msg)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long groupId = params.long("id")
        String userIds = PhenixCoder.decodeIds(params.userIds)
        List<Long> userIdList = ToStringUnits.idsString2LongList(userIds)
        planTemplatePersonService.personDel(groupId, userIdList)
        planTemplatePersonGroupService.updateGroupLastUpdate(groupId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.planTemplate.PlanTemplateVO
import timetabling.planTemplate.PlanTemplate

/**
 * 安防管控中心
 * -计划模板管理
 */
class PlanTemplateV2Controller implements BaseExceptionHandler {

    PlanTemplateService planTemplateService
    
    PlanTemplateV2Service planTemplateV2Service

    public static final int MAX_TEMPLATE = 128

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm")
        if (!firm) {
            throw new HiiAdminException("请选择平台！")
        }
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = planTemplateService.fetchAllPlanTemplateByCampusIdAndFirm(campusId, firm, searchValue, p, s)
        List<PlanTemplate> planTemplateList = map.list
        List<PlanTemplateVO> vos = planTemplateList.collect { planTemplate ->
            PlanTemplateVO vo = new PlanTemplateVO().buildVO(planTemplate)
            vo
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def edit() {
        Integer firm = params.int("id")
        Long campusId = request.getAttribute("campusId") as Long
        Integer total = planTemplateService.countPlanTemplateByCampusIdAndFirm(campusId, firm)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([continue: (total < MAX_TEMPLATE)])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        String memo = params.memo

        String days = params.days
        Integer firm = params.int("firm")
        Integer total = planTemplateService.countPlanTemplateByCampusIdAndFirm(campusId, firm)
        if (total >= MAX_TEMPLATE) {
            throw new HiiAdminException("可创建时间计划模板已达上限")
        }
        Long id = planTemplateV2Service.createAndAsyncPlanTemplateV2(campusId, name, memo, firm, days)
        resultVO.result.put("id", id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long planId = params.long("id")
        PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(planId)
        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = ResultVO.success()
        Long planId = params.long("id")
        String name = params.name
        String memo = params.memo

        String days = params.days
        planTemplateV2Service.updateAndAsyncPlanTemplateV2(planId, name, memo, days)
        resultVO.result.put("id", planId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long planId = params.long("id")

        planTemplateV2Service.deletePlanTemplate(campusId, planId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

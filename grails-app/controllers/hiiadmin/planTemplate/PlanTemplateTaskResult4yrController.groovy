package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.docking.gate.DockingFirmAuthPersonDeviceService
import hiiadmin.module.docking.dhYr.DockingFirmAuthPersonDeviceVO


class PlanTemplateTaskResult4yrController implements BaseExceptionHandler {


    DockingFirmAuthPersonDeviceService dockingFirmAuthPersonDeviceService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long("id")
        String name = params.name
        String code = params.code
        String mobile = params.mobile
        Integer status = params.int("status")
        int p = params.int("p", 1)
        int s = params.int("s", 30)


        List<DockingFirmAuthPersonDeviceVO> voList = dockingFirmAuthPersonDeviceService.transformVoByTaskId(taskId, name, code, mobile, status, p, s)
        Integer total = dockingFirmAuthPersonDeviceService.countDockingFirmAuthPersonDevice(taskId, name, code, mobile, status)
        Integer successCount = dockingFirmAuthPersonDeviceService.countStatusNum(taskId, ConstantEnum.IssueStatus.SUCCESS.status)
        Integer errorCount = dockingFirmAuthPersonDeviceService.countStatusNum(taskId, ConstantEnum.IssueStatus.FAILURE.status)
        resultVO.result.put("list", voList)
        resultVO.result.put("total", total)
        resultVO.result.put("successCount", successCount)
        resultVO.result.put("errorCount", errorCount)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }

    }
}

package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplatePersonV2VO


class PlanTemplatePersonV2Controller implements BaseExceptionHandler {

    PlanTemplatePersonService planTemplatePersonService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long

        Byte userType = params.byte("userType") as Byte
        String searchValue = params.searchValue

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        def (List<PlanTemplatePersonV2VO> list, Integer total) = planTemplatePersonService.fetchTemplatePerson4StudentAndTeacher(campusId, searchValue, userType, p, s)
        resultVO.result.put("total", total)
        resultVO.result.put("list", list)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "utf-8"
    }
}

package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplateDeviceVO
import hiiadmin.module.planTemplate.PlanTemplateTaskVO
import hiiadmin.module.planTemplate.PlanTemplateVO
import timetabling.planTemplate.PlanTemplateDevice
import timetabling.planTemplate.PlanTemplateTask

/**
 * 安防管控中心
 * -下发任务管理
 */
@Deprecated
@Slf4j
class PlanTemplateTaskController implements BaseExceptionHandler {

    PlanTemplateService planTemplateService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        Byte type = params.byte("type")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = planTemplateService.fetchAllPlanTemplateTaskByCampusId(campusId, searchValue, type, p, s)
        List<PlanTemplateTaskVO> vos = map.list?.collect {
            PlanTemplateTaskVO taskVO = new PlanTemplateTaskVO().buildVO(it)
            taskVO.deviceCount = planTemplateService.countPlanTemplateDeviceByTaskId(it.id)
            taskVO
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long taskId = params.long("id")
        PlanTemplateTask task = planTemplateService.fetchPlanTemplateTaskByTaskId(taskId)
        PlanTemplateTaskVO planTemplateTaskVO = new PlanTemplateTaskVO().buildVO(task)

        PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(task)
        planTemplateTaskVO.planTemplate = vo

        List<PlanTemplateDevice> planTemplateDeviceList = planTemplateService.fetchAllPlanTemplateDeviceByTaskId(taskId)
        planTemplateTaskVO.devices = planTemplateDeviceList.collect {
            new PlanTemplateDeviceVO().buildVO(it)
        }
        resultVO.result = planTemplateTaskVO
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = ResultVO.success()

        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long

        Long planId = params.long("planId")
        String deviceIds = params.deviceIds
        Integer firm = params.int("firm")

        Integer taskType = params.int("taskType")

//        String name = params.name

        PlanTemplateTask task = planTemplateService.authDownload(schoolId, campusId, planId, firm, taskType, deviceIds)
        resultVO.result.put("id", task.id)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long taskId = params.long("id")
        planTemplateService.deletePlanTemplateTask(taskId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

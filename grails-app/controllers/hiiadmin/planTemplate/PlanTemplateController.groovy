package hiiadmin.planTemplate

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.planTemplate.PlanTemplatePersonGroupVO
import hiiadmin.module.planTemplate.PlanTemplateVO
import io.swagger.annotations.Api
import timetabling.planTemplate.PlanTemplate
import timetabling.planTemplate.PlanTemplateGroupRelated
import timetabling.planTemplate.PlanTemplatePersonGroup

@Deprecated
@Api(value = "计划模板管理", tags = "安防管控中心", consumes = "admin")
class PlanTemplateController implements BaseExceptionHandler {

    PlanTemplateService planTemplateService
    
    PlanTemplateV2Service planTemplateV2Service

    PlanTemplatePersonGroupService planTemplatePersonGroupService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<PlanTemplate> planTemplateList = planTemplateService.fetchAllPlanTemplateByCampusId(campusId, p, s)
        Integer count = planTemplateService.countPlanTemplateByCampusId(campusId)
        List<PlanTemplateVO> vos = planTemplateList.collect {
            planTemplate ->
                PlanTemplateVO vo = new PlanTemplateVO().buildVO(planTemplate)

                List<PlanTemplateGroupRelated> groupRelatedList = planTemplateService.fetchAllPlanTemplateGroupRelatedByPlanId(planTemplate.id)
                List<PlanTemplatePersonGroupVO> personGroupVOS = []
                groupRelatedList.each { groupRelated ->
                    PlanTemplatePersonGroup group = planTemplatePersonGroupService.fetchPlanTemplatePersonGroupById(groupRelated.groupId)
                    personGroupVOS << new PlanTemplatePersonGroupVO().buildVO(group)
                }
                vo.personGroups = personGroupVOS
                vo
        }
        resultVO.result.put("list", vos)
        resultVO.result.put("total", count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        String memo = params.memo

        String days = params.days
        String groupIds = params.groupIds

        Long id = planTemplateService.createAndAsyncPlanTemplate(campusId, name, memo, groupIds, days)
        resultVO.result.put("id", id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long planId = params.long("id")
        PlanTemplateVO vo = planTemplateService.transformPlanTemplateVO(planId)
        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long planId = params.long("id")
        String name = params.name
        String memo = params.memo

        String days = params.days
        String groupIds = params.groupIds
        planTemplateService.updateAndAsyncPlanTemplate(planId, name, memo, groupIds, days)
        resultVO.result.put("id", planId)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long planId = params.long("id")
        
        Long campusId = request.getAttribute("campusId") as Long

        planTemplateV2Service.deletePlanTemplate(campusId, planId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

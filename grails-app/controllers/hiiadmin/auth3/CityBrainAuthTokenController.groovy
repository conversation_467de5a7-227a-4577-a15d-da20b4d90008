package hiiadmin.auth3

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.github.kevinsawicki.http.HttpRequest
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.TestApi
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import javax.annotation.Resource

class CityBrainAuthTokenController implements BaseExceptionHandler {

    final static Logger logger = LoggerFactory.getLogger(CityBrainAuthTokenController.class)

    @Resource
    TestApi testApi

    def index() {
        String token = params.token
        String v = params.v

        logger.info("城市大脑token:{}", token)

        [token: "null"]
    }

    //根据token解析城市大脑用户
    def save() {
        String token = params.token

        int status = 0
        HttpRequest httpRequest = HttpRequest.get("http://************:8287/hzrxyj-web/common/getUserForSso?token=" + token + "&enterId=5")
                .header("Content-Type", "application/json")
                .trustAllCerts().trustAllHosts()
        int code = httpRequest.code()
        String body = httpRequest.body()
        logger.info("城市大脑返回:{}", body)
        if (code == 200) {
            JSONObject jsonObject = JSON.parseObject(body)
            Integer rCode = jsonObject.code as Integer
            if (rCode == 2000) {
                status = 1
            }
        }

        [status: 0]
    }

//    public static void main(String[] args) {
//        HttpRequest httpRequest = HttpRequest.get("http://************:8287/hzrxyj-web/common/getUserForSso?token=5000000546923328&enterId=5")
//                .header("Content-Type", "application/json")
//                .trustAllCerts().trustAllHosts()
//        int code = httpRequest.code()
//        String body = httpRequest.body()
//        logger.info("城市大脑返回:{}", body)
//
//    }
}

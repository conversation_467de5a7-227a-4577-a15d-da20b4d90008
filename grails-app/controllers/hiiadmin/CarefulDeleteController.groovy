package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.apiCloud.AntennaApi
import hiiadmin.attendance.AttendanceGroupService
import hiiadmin.biz.StaffService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.planTemplate.PlanTemplatePersonGroupService
import hiiadmin.planTemplate.PlanTemplatePersonService
import hiiadmin.planTemplate.PlanTemplateRelatedService
import hiiadmin.school.CampusService
import hiiadmin.school.StudentService
import hiiadmin.school.TeacherService
import hiiadmin.school.TemperatureRecordService
import hiiadmin.school.user.ParentService
import hiiadmin.syn.SynUserV2Service
import org.joda.time.DateTime
import timetabling.docking.DockingFirmSyncTask
import timetabling.org.Campus
import timetabling.planTemplate.PlanTemplatePersonGroup
import timetabling.planTemplate.PlanTemplateRelated
import timetabling.user.Parent
import timetabling.user.Staff
import timetabling.user.Student
import timetabling.user.Teacher

import javax.annotation.Resource

import static com.bugu.MobileUtil.encodeMobile
import static com.bugu.PhoenixCoder.decodeId
import static hiiadmin.utils.TimeUtils.getDateTimeOfDay

@Slf4j
class CarefulDeleteController {

    TemperatureRecordService temperatureRecordService

    CampusService campusService

    AttendanceGroupService attendanceGroupService

    TeacherService teacherService

    StaffService staffService

    ParentService parentService

    @Resource
    AntennaApi antennaApi

    StudentService studentService

    SynUserV2Service synUserV2Service

    PlanTemplatePersonGroupService planTemplatePersonGroupService

    PlanTemplatePersonService planTemplatePersonService

    PlanTemplateRelatedService planTemplateRelatedService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int hour = params.int("hour")
        int nowHour = new DateTime().hourOfDay
        if (hour == nowHour) {
            String option = params.option
            int year = params.int("year")
            int month = params.int("month")
            int day = params.int("day")
            switch (option) {
                case "temperature":
                    DateTime dateTime = new DateTime().withYear(year).withMonthOfYear(month).withDayOfMonth(day)
                    Long campusId = decodeId(params.campus)
                    Campus campus = campusService.fetchCampusByCampusId(campusId)
                    if (!campus) {
                        resultVO.status = 0
                        resultVO.message = "校区不存在"
                    }
                    Long dayDatestamp = getDateTimeOfDay(dateTime).millis
                    int deleteCount = temperatureRecordService.deleteTemperatureRecordByCampusIdAndDayStamp(campusId, dayDatestamp)
                    resultVO.message = "success"
                    resultVO.result.put("操作校区", campus.name)
                    resultVO.result.put("删除的数据日期", "${year}-${month}-${day}".toString())
                    resultVO.result.put("删除目标", "温度大于等于37.3℃、小于35℃、状态为异常的测温数据")
                    resultVO.result.put("删除数据量", deleteCount)
                    break
                case "attendanceGroup":
                    Long time = params.long('time')
                    DateTime dateTime = new DateTime()
                    if (year != dateTime.year || month != dateTime.monthOfYear || day != dateTime.dayOfMonth) {
                        resultVO.status = 0
                        resultVO.message = "参数错误，操作未进行"
                    } else {
                        attendanceGroupService.updateAttendanceGroup()
                        resultVO.message = "success"
                        resultVO.result.put("操作事项", "住校生-通校生考勤人员列表更新完成")
                    }
                    break
                case "mobileEncode":
                    int s = 100
                    Integer teacherCount = Teacher.count()
                    int p = 1
                    int count = 0
                    while (count <= teacherCount) {
                        List<Teacher> teacherList = Teacher.findAll(max: s, offset: (p - 1) * s)
                        teacherList.each {
                            teacher ->
                                String mobile = teacher.deMobile
                                mobile = mobile.replaceAll("""\\*""", "0")
                                teacher.encodeMobile = encodeMobile(mobile)
                                teacher.mobile = mobile
                                teacherService.saveTeacher(teacher)
                        }
                        count = s * p
                        p++
                    }
                    int hiiCount = Staff.count()
                    p = 1
                    count = 0
                    while (count < hiiCount) {
                        List<Staff> staffList = Staff.findAll(max: s, offset: (p - 1) * s)
                        staffList.each {
                            staff ->
                                String mobile = staff.deMobile
                                mobile = mobile.replaceAll("""\\*""", "0")
                                staff.encodeMobile = encodeMobile(mobile)
                                staff.mobile = mobile
                                staffService.save(staff)
                        }
                        count = s * p
                        p++
                    }
                    int parentCount = Parent.count()
                    p = 1
                    count = 0
                    while (count < parentCount) {
                        List<Parent> parents = Parent.findAll(max: s, offset: (p - 1) * s)
                        parents.each {
                            parent ->
                                String mobile = parent.deMobile
                                mobile = mobile.replaceAll("""\\*""", "0")
                                parent.encodeMobile = encodeMobile(mobile)
                                parent.mobile = mobile
                                parentService.saveParent(parent)
                        }
                        count = s * p
                        p++
                    }
                    break
                case "asyncDaHuaTeacher":
                    Long schoolId = params.long("schoolId")
                    Long campusId = params.long("campusId")
                    List<Teacher> teacherList = teacherService.fetchAllNormalTeacher(schoolId, campusId, 1, 1000)
                    Integer size = teacherList.size()
                    teacherList.eachWithIndex { teacher, i ->
                        if (teacher.avatar) {
                            AntennaApi.UpPersonVO upPersonVO = new AntennaApi.UpPersonVO(
                                    campusId: campusId,
                                    userId: teacher.id,
                                    userType: ConstantEnum.UserTypeEnum.TEACHER.type,
                                    code: teacher.mobile,
                                    name: teacher.name,
                                    avatarUrl: teacher.avatar,
                                    roleId: "teacher001",
                                    sex: teacher.gender ? 1 : 2,
                                    mobile: teacher.mobile
                            )

                            ResultVO re = antennaApi.updateOrCreatePerson(upPersonVO)
                            log.info("""${JSON.toJSONString(re)}""")
                            log.info("------------ 同步进度 teacherId@${teacher.id} name@${teacher.name} ---${i}/${size}")
                        } else {
                            log.info("""老师@${teacher.id} name@${teacher.name} 没有照片不进行同步""")
                        }
                    }
                    break
                case "asyncDaHuaStudent":
                    Long campusId = params.long("campusId")
                    List<Student> studentList = studentService.fetchAllStudentByCampusId(campusId, false)
                    int size = studentList.size()
                    studentList.eachWithIndex { student, i ->
                        if (student.pic) {
                            AntennaApi.UpPersonVO upPersonVO = new AntennaApi.UpPersonVO(
                                    campusId: campusId,
                                    userId: student.id,
                                    userType: ConstantEnum.UserTypeEnum.STUDENT.type,
                                    code: student.code,
                                    name: student.name,
                                    avatarUrl: student.pic,
                                    roleId: "student001",
                                    sex: student.gender ? 1 : 2,
                            )

                            ResultVO re = antennaApi.updateOrCreatePerson(upPersonVO)
                            log.info("""${JSON.toJSONString(re)}""")
                            log.info("------------ 同步进度 studnetId@${student.id} name@${student.name} ---${i}/${size}")
                        } else {
                            log.info("""学生@${student.id} name@${student.name} 没有照片不进行同步""")
                        }
                    }
                    break
                case "同步删除人员数据订正":
                    long campusId = params.long("campusId")
                    String userIds = params.userIds
                    List<Long> userIdList = userIds.split(",").collect { it as Long }
                    byte userType = params.byte("userType")
                    synUserV2Service.syncDeleteUser2Firm(campusId, userIdList, userType, null, null, DockingFirmSyncTask.OPERATION_TYPE_ONE, [content: "单个人员删除"])
                    break
                case "初始化配置人员组":
                    long campusId = params.long("campusId")
                    long groupId = params.long("groupId")
                    PlanTemplatePersonGroup personGroup = planTemplatePersonGroupService.fetchPlanTemplatePersonGroupById(groupId)
                    if (personGroup.campusId != campusId) {
                        throw new HiiAdminException("c g 关系异常")
                    }
                    PlanTemplateRelated templateRelated = planTemplateRelatedService.fetchPlanTemplateRelated4hkGroup(groupId)
                    planTemplatePersonService.cleanAndInitPersonGroup(campusId, groupId, templateRelated.relatedId, personGroup.userType)
                    break
                default:
                    break
            }
        } else {
            resultVO.status = 0
            resultVO.message = "时间错误"
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

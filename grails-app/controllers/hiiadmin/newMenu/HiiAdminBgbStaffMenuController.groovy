package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.Constants
import hiiadmin.auth.BgbApiAuth

import javax.annotation.Resource

/**
 * 新版菜单
 */
class HiiAdminBgbStaffMenuController implements BaseExceptionHandler {

    @Resource
    private BgbApiAuth bgbApiAuth

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long staffId = request.getAttribute('staffId') as Long
        String token = request.getHeader(Constants.JWT_TOKEN)
        Long campusId = request.getAttribute("campusId") as Long
        ResultVO result = bgbApiAuth.fetchBgbCampusMenu(campusId, staffId, ConstantEnum.BgbMenuTerminal.PC.type, token)
        render text: JSON.toJSONString(result), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class HiiAdminBgbCampusMenuSortController implements BaseExceptionHandler {

    BgbCampusMenuService bgbCampusMenuService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String sortJson = params.sort
        bgbCampusMenuService.sortBgbCampusMenu(sortJson)
        resultVO.result.put("id", 1)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

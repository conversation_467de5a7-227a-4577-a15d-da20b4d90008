package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import org.apache.commons.lang3.StringUtils


class HiiAdminBgbMenuCollectionSortController implements BaseExceptionHandler{

    BgbMenuCollectionService bgbMenuCollectionService
	
    def save() { 
        String ids = params.ids
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        if (StringUtils.isNotBlank(ids)) {
            bgbMenuCollectionService.sortAndUpdate(campusId, userId, ids)
        } else {
            bgbMenuCollectionService.deleteAllCollectionByUserId(userId, campusId)
        }
        
        render text: JSON.toJSONString(ResultVO.success()), contentType: "application/json", encoding: "UTF-8"
    }
}

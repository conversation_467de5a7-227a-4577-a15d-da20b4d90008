package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import timetabling.newMenu.BgbCampusRole

class AuthBgbCampusRoleController implements BaseExceptionHandler {

    BgbCampusRoleService bgbCampusRoleService

    BgbCampusRoleMenuService bgbCampusRoleMenuService

    BgbStaffRoleService bgbStaffRoleService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 20)
        String searchValue = params.searchValue
        Boolean all = params.boolean("all", false)
        def map = bgbCampusRoleService.pageBgbCampusRole(campusId, searchValue, all, p, s)
        resultVO.result.put("list", map.list)
        resultVO.result.put('total', map.total)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String name = params.name
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        String memo = params.memo
        if (bgbCampusRoleService.hasRoleName(campusId, name)) {
            resultVO = resultVO.failure(BizErrorCode.CAMPUS_ROLE_NAME_ERROR.code, BizErrorCode.CAMPUS_ROLE_NAME_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        BgbCampusRole bgbCampusRole = bgbCampusRoleService.createBgbCampusRole(schoolId, campusId, name, memo)
        resultVO.result.put("id", bgbCampusRole.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        String memo = params.memo
        Byte status = params.byte("status")
        BgbCampusRole bgbCampusRole = bgbCampusRoleService.hasRoleName(campusId, name)
        if (bgbCampusRole && bgbCampusRole.id != id) {
            resultVO = resultVO.failure(BizErrorCode.CAMPUS_ROLE_NAME_ERROR.code, BizErrorCode.CAMPUS_ROLE_NAME_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        bgbCampusRoleService.updateBgbCampusRole(id, name, memo, status)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String menuIds = params.menuIds
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        bgbCampusRoleMenuService.updateCampusRoleMenu(schoolId, campusId, id, menuIds)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        BgbCampusRole bgbCampusRole = bgbCampusRoleService.getBgbCampusRole(id)
        if (bgbCampusRole.admin) {
            resultVO.status = 0
            resultVO.code = BizErrorCode.ADMIN_ROLE_DELETE_ERROR.code
            resultVO.msg = BizErrorCode.ADMIN_ROLE_DELETE_ERROR.msg
        } else {
            bgbCampusRoleService.deleteBgbCampusRole(bgbCampusRole)
            bgbStaffRoleService.deleteStaffRoleByRoleId(id)
            resultVO.result.put("id", id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }


}

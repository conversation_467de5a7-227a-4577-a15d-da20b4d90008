package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.module.newMenu.BgbCampusMenuCategoryVO

class HiiAdminBgbCampusMenuCategoryController implements BaseExceptionHandler {

    BgbCampusMenuCategoryService bgbCampusMenuCategoryService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        Byte terminal = params.byte("terminal")
        List<BgbCampusMenuCategoryVO> bgbCampusMenuCategoryVOList = bgbCampusMenuCategoryService.fetchAllBgbCampusMenuCategoryVOListByCampusIdAndTerminal(campusId, terminal, searchValue)
        resultVO.result.put("list", bgbCampusMenuCategoryVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        String name = params.name
        String icon = params.icon
        String color = params.color
        Byte terminal = params.byte("terminal")
        Byte yunXueType = params.byte("yunXueType")
        if (!name) {
            resultVO = resultVO.failure(BizErrorCode.CAMPUS_MENU_CATEGORY_CREATE_ERROR.code, BizErrorCode.CAMPUS_MENU_CATEGORY_CREATE_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        //判断名称是否存在
        if (name && bgbCampusMenuCategoryService.hasName(campusId, terminal, name)) {
            resultVO = resultVO.failure(BizErrorCode.MENU_CATEGORY_NAME_ERROR.code, BizErrorCode.MENU_CATEGORY_NAME_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        bgbCampusMenuCategoryService.createBgbCampusMenuCategory(schoolId, campusId, name, icon, terminal, color, yunXueType)
        resultVO.result.put("id", 1)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        String icon = params.icon
        String color = params.color
        Byte terminal = params.byte("terminal")
        Byte yunXueType = params.byte("yunXueType")
        Long oldId = bgbCampusMenuCategoryService.hasName(campusId, terminal, name)
        //判断名称是否存在
        if (name && oldId && oldId != id) {
            resultVO = resultVO.failure(BizErrorCode.MENU_CATEGORY_NAME_ERROR.code, BizErrorCode.MENU_CATEGORY_NAME_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        bgbCampusMenuCategoryService.updateBgbCampusMenuCategory(id, name, icon, color, yunXueType)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String id = params.id
        String sortJson = params.sort
        Byte terminal = params.byte("terminal")
        Long campusId = request.getAttribute("campusId") as Long
        bgbCampusMenuCategoryService.sortBgbBaseMenuCategory(campusId, terminal, sortJson)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        resultVO = bgbCampusMenuCategoryService.deleteBgbCampusMenuCategory(id, resultVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

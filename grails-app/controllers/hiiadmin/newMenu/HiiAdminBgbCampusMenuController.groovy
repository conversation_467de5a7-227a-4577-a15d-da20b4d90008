package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.BizErrorCode
import hiiadmin.module.newMenu.BgbCampusMenuVO

class HiiAdminBgbCampusMenuController implements BaseExceptionHandler {

    BgbCampusMenuService bgbCampusMenuService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long superMenuId = params.long("superMenuId")
        Byte terminal = params.byte("terminal")
        Long campusId = request.getAttribute("campusId") as Long
        List<BgbCampusMenuVO> bgbCampusMenuVOS = bgbCampusMenuService.fetchBgbBaseMenuVOList(campusId, superMenuId, terminal)
        resultVO.result.put("list", bgbCampusMenuVOS)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long categoryId = params.long("categoryId")
        Long schoolId = request.getAttribute("schoolId")as Long
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        String icon = params.icon
        String color = params.color
        Byte terminal = params.byte("terminal")
        bgbCampusMenuService.createBgbCampusMenu(schoolId, campusId, categoryId, name, icon, terminal, color)
        resultVO.result.put("id", categoryId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String name = params.name
        String icon = params.icon
        String color = params.color
        String reportFormIds = params.reportFormIds
        String specialPathJson = params.specialPathJson
        bgbCampusMenuService.updateBgbCampusMenu(id, name, icon, color,reportFormIds,specialPathJson)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long categoryId = params.long("categoryId")
        Long menuId = params.long("menuId")
        bgbCampusMenuService.changeCampusMenuCategory(id, categoryId, menuId)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        if (bgbCampusMenuService.canDeleteBgbCampusMenu(id)) {
            resultVO = resultVO.failure(BizErrorCode.DELETE_MENU_CATEGORY_ERROR.code, BizErrorCode.DELETE_MENU_CATEGORY_ERROR.msg)
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        bgbCampusMenuService.deleteBgbCampusMenu(id)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.newMenu.BgbCampusRoleVO

class HiiAdminBgbCampusRoleController implements BaseExceptionHandler {

    BgbCampusRoleService bgbCampusRoleService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<BgbCampusRoleVO> bgbCampusRoleVOList = bgbCampusRoleService.fetchBgbCampusRoleVOByCampusId(campusId)
        resultVO.result.put("list", bgbCampusRoleVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

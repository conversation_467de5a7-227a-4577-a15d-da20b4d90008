package hiiadmin.newMenu

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.MenuCollectionVO
import hiiadmin.utils.PhenixCoder
import org.apache.commons.lang3.StringUtils
import timetabling.newMenu.BgbMenuCollection

class HiiAdminBgbMenuCollectionController implements BaseExceptionHandler {

    BgbMenuCollectionService bgbMenuCollectionService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        List<BgbMenuCollection> bgbMenuCollectionList = bgbMenuCollectionService.fetchBgbMenuCollectionByCampusIdAndUserId(campusId, userId)
        List<MenuCollectionVO> menuCollectionVOList = bgbMenuCollectionList.collect {
            new MenuCollectionVO(
                    id: it.id,
                    campusId: PhenixCoder.encodeId(it.campusId),
                    menuId: it.menuId,
                    idx: it.idx
            )
        }
        resultVO.result.put("list", menuCollectionVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Long menuId = params.long("menuId")
        BgbMenuCollection menuCollection = bgbMenuCollectionService.collectionOrCancelMenu(schoolId, campusId, userId, menuId)
        resultVO.result.put("id", menuCollection.id)
        
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

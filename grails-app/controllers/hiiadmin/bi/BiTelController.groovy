package hiiadmin.bi

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class BiTelController implements BaseExceptionHandler {

    BiTelService biTelService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List userIdList = request.JSON as List
        List<String> telList = biTelService.fetchUserEncodeInfoByUserIdList(userIdList)
        resultVO.result.put("list", telList)
        header('Access-Control-Allow-Origin', '*')
        header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        header('Access-Control-Expose-Headers', 'Content-Length,Content-Range')
        header('Access-Control-Allow-Headers', 'authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range')
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

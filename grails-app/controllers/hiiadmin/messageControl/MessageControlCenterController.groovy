package hiiadmin.messageControl

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.mapstruct.MessageControlCenterMapper
import hiiadmin.module.messageControl.MessageControlCenterVO
import org.springframework.beans.factory.annotation.Autowired
import timetabling.messageControl.MessageControlCenter

@Deprecated
class Message<PERSON><PERSON>rolCenterController implements BaseExceptionHandler {

    MessageControlCenterService messageControlCenterService

    @Autowired
    MessageControlCenterMapper messageControlCenterMapper

    def patch() {
        Integer type = params.int("id")

        Long campusId = request.getAttribute("campusId") as Long

        MessageControlCenter center = messageControlCenterService.checkMessageControlCenter(campusId, type)
        MessageControlCenterVO centerVO = messageControlCenterMapper.convert2MessageControlCenterVO(center)
        ResultVO resultVO = ResultVO.success(centerVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.messageControl

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.mapstruct.MessageControlCenterMapper
import hiiadmin.module.messageControl.MessageControlCenterVO
import hiiadmin.module.messageControl.MessageControlPersonVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.messageControl.MessageControlCenter

/**
 * 消息管控中心推送人员对象配置
 */
@Deprecated
class MessageControlPersonController implements BaseExceptionHandler {

    MessageControlCenterService messageControlCenterService

    MessageControlPersonService messageControlPersonService

    @Autowired
    MessageControlCenterMapper messageControlCenterMapper

    def index() {
        Integer type = params.int("id")
        Long campusId = request.getAttribute("campusId") as Long
        MessageControlCenter controlCenter = messageControlCenterService.findOrCreateMessageControlCenter(campusId, type)
//        if (!controlCenter) {
//            throw new HiiAdminException("配置不存在")
//        }
        MessageControlCenterVO centerVO = messageControlCenterMapper.convert2MessageControlCenterVO(controlCenter)
        def (List<MessageControlPersonVO> teacherVOList, List<MessageControlPersonVO> roleVOList, List<MessageControlPersonVO> departmentVOList) = messageControlPersonService.transformMessageControlPersonVO(controlCenter)

        ResultVO resultVO = ResultVO.success([center     : centerVO,
                                              teachers   : teacherVOList,
                                              roles      : roleVOList,
                                              departments: departmentVOList])

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }

    }

    def save() {
        Integer type = params.int("id")
        Long campusId = request.getAttribute("campusId") as Long
        String pushOpt = params.pushOpt

        String teacherIds = params.teacherIds
        String roleIds = params.roleIds
        String departmentIds = params.departmentIds

        MessageControlCenter messageControlCenter = messageControlCenterService.findOrCreateMessageControlCenter(campusId, type)

        messageControlCenterService.updateMessageControlCenter(messageControlCenter, pushOpt, teacherIds, roleIds, departmentIds)
        ResultVO resultVO = ResultVO.success()
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

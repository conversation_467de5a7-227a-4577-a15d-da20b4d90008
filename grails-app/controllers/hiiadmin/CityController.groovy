package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.bugu.CityVO
import hiiadmin.module.bugu.ProvinceVO
import timetabling.City
import timetabling.Province

class CityController implements BaseExceptionHandler {

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        List<Province> provinceList = Province.findAll()
        List<ProvinceVO> provinceVOList = []
        provinceList?.each {
            Province province ->
                List<City> cityList = City.findAllByProvinceId(province.id)
                List<CityVO> cityVOList = []
                cityList?.each {
                    City city ->
                        cityVOList.add(viewDOService.buildCityVO(city))
                }
                provinceVOList.add(viewDOService.buildProvinceVO(province, cityVOList))
        }
        resultVO.status = 1 as byte
        resultVO.result.put("list", provinceVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"

    }
}

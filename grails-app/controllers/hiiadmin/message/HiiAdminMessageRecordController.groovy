package hiiadmin.message

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.msg.MessageService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import io.swagger.annotations.Api
import timetabling.Message
import timetabling.user.Teacher

@Api(value = "校内通知名单", tags = "校园服务", consumes = "admin")
class HiiAdminMessageRecordController {

    MessageService messageService

    TeacherService teacherService

    ViewDOService viewDOService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        Long msgId = params.long('msgId')
        //TODO 本处消息状态和家校通不一致，亟待处理统一
        Byte confirmedStatus = params.byte("confirmedStatus", ConstantEnum.MessageStatus.SENT.status)
        Message message = messageService.fetchMessageByIdAndStatus(msgId, 1 as byte)
        Multimap<String, Long> messageConfirmTeachersInfo = messageService.messageConfirmTeachersInfo(msgId, confirmedStatus)
        Set<Long> confirmTeacherSet = messageConfirmTeachersInfo.get(ConstantEnum.MessageConfirmEnum.CONFIRMED.key)
        Set<Long> unConfirmTeacherSet = messageConfirmTeachersInfo.get(ConstantEnum.MessageConfirmEnum.UN_CONFIRMED.key)
        List<Teacher> confirmTeacherList = teacherService.fetchAllTeacherByIdInList(confirmTeacherSet.toList())
        List<Teacher> unConfirmTeacherList = teacherService.fetchAllTeacherByIdInList(unConfirmTeacherSet.toList())
        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, messageConfirmTeachersInfo.values() as List<Long>)
        List<TeacherVO> confirmTeacherVOList = confirmTeacherList?.collect {
            TeacherVO teacherVO = viewDOService.buildTeacherVOById4Notice(it)
            teacherVO.mobile = teacherIdMobileMap.get(it.id)
            teacherVO
        }
        confirmTeacherVOList?.sort { it.name }
        List<TeacherVO> unConfirmTeacherVOList = unConfirmTeacherList?.collect {
            TeacherVO teacherVO = viewDOService.buildTeacherVOById4Notice(it)
            teacherVO.mobile = teacherIdMobileMap.get(it.id)
            teacherVO
        }
        unConfirmTeacherVOList?.sort { it.name }
        resultVO.result.put("lastNotifyTime", message?.lastNotifyTime?.getTime())
        resultVO.result.put("confirmedCount", confirmTeacherVOList?.size() ?: 0)
        resultVO.result.put("confirmTeacherList", confirmTeacherVOList)
        resultVO.result.put("unconfirmedCount", unConfirmTeacherVOList?.size() ?: 0)
        resultVO.result.put("unconfirmedTeacherLit", unConfirmTeacherVOList)
        resultVO.status = 1 as byte
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

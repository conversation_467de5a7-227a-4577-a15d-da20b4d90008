package hiiadmin.message

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.msg.MessageService
import timetabling.Message

/**
 * <AUTHOR>
 * @Date 2022-10-17 15:19
 */
@Slf4j
class HiiAdminMessageV2Controller {

    MessageService messageService

    def update() {
        ResultVO resultVO = new ResultVO()
        Long id = params.long('id')
        Boolean top = params.boolean('top', false)
        Message message = messageService.fetchMessageById(id)
        message.top = top
        messageService.saveMessage(message)
        resultVO.result.put('id', message?.id)
        resultVO.status = 1

        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: 'utf-8'
    }
}

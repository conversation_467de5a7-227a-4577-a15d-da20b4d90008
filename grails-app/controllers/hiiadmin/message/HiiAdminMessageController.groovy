package hiiadmin.message

import com.alibaba.fastjson.JSON
import com.bugu.BaseResult
import com.bugu.ResultVO
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.UserService
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.TeacherDTO
import hiiadmin.module.msg.MessageVO
import hiiadmin.msg.MessageService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import io.swagger.annotations.Api
import timetabling.Message
import timetabling.MessageRecord


@Api(value = "校内通知", tags = "校园服务", consumes = "admin")
@Slf4j
class HiiAdminMessageController {

    UserService userService

    MessageService messageService

    ViewDOService viewDOService

    def index() {
        ResultVO resultVO = new ResultVO()
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Byte byMe = params.byte("byMe", 1 as byte)
        String searchValue = params.searchValue
        Byte publishType = params.byte("type") as Byte

        Long startDate = params.long("startDate") as Long
        Long endDate = params.long("endDate") as Long

        Long teacherId = PhenixCoder.decodeId(params.teacherId)
        Boolean isRead = params.boolean("isRead")

        Byte noticeType = params.byte('noticeType')

        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)
        TeacherDTO teacher = teacherBaseResult.result
        List<MessageVO> messageVOList = []
        int total = 0
        if (byMe == 1 as byte) {
            def map = messageService.fetchAllMessage4senderCate(teacher.campusId,
                    teacher.id, ConstantEnum.UserTypeEnum.TEACHER.type, ConstantEnum.MessageCates.EDUCATIONAL_ADMINISTRATION_NOTICE_ONLINE.cate,
                    searchValue, publishType, startDate, endDate, noticeType, p, s)
            List<Message> messageList = map.list
            total = (int) (map.total ? map.total : 0)
            messageList?.each {
                Message message ->
                    messageVOList << viewDOService.buildTeacherMessageVO(message)
            }
        } else {
            List<MessageRecord> messageRecordList = messageService.fetchAllMessageRecord4receiverCate(teacher.campusId, teacher.id,
                    ConstantEnum.UserTypeEnum.TEACHER.type, ConstantEnum.MessageCates.EDUCATIONAL_ADMINISTRATION_NOTICE_ONLINE.cate,
                    teacherId,isRead, searchValue, startDate, endDate, noticeType, p, s)
            total = messageService.countMessageRecord4receiverCate(teacher.campusId, teacher.id, ConstantEnum.UserTypeEnum.TEACHER.type, ConstantEnum.MessageCates.EDUCATIONAL_ADMINISTRATION_NOTICE_ONLINE.cate, teacherId,isRead, searchValue, startDate, endDate,noticeType)
            messageRecordList?.each { MessageRecord messageRecord ->
                Message message = messageService.fetchMessageById(messageRecord.messageId, false)
                MessageVO messageVO = new MessageVO().buildVO(message)
                messageVO.recordStatus = messageRecord?.status
                messageVO.isNew = messageRecord?.isNew
                messageVOList << messageVO
            }
        }
        
        resultVO.result.put('list', messageVOList)
        resultVO.result.put('total', total)
        resultVO.status = 1 as byte
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)
        TeacherDTO teacher = teacherBaseResult.result
        Long id = params.long('id')
        Message message = messageService.fetchMessageById(id, false)
        if (message) {
            MessageVO messageVO
            MessageRecord messageRecord = messageService.fetchMessageRecordByMessageIdAndReceiverId(message?.id, teacher?.id, ConstantEnum.UserTypeEnum.TEACHER.type)
            if (messageRecord) {
                if(messageRecord.status < ConstantEnum.MessageStatus.READ_NOT_CONFIRMED.status){
                    messageRecord.status = ConstantEnum.MessageStatus.READ_NOT_CONFIRMED.status
                    messageService.saveMessageRecord(messageRecord)
                }
                messageVO = viewDOService.buildTeacherMessageVO(message, messageRecord)

            } else {
                messageVO = viewDOService.buildTeacherMessageVO(message)
            }
            resultVO.result = messageVO
        } else {
            resultVO.result.put("status", 0)
        }
        resultVO.status = 1 as byte
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        String title = params.title
        String content = params.content
        String url = params.url
        Integer reSend = params.int('reSend', 0)
        Long msgId = params.long('msgId')
        String teacherIds = PhenixCoder.decodeIds(params.teacherIds)
        String pic = params.pic
        String enclosure = params.enclosure

        Boolean top = params.boolean("top", false)

        Byte noticeType = params.byte('noticeType')
        Long noticeId = PhenixCoder.decodeId(params.noticeId)

        Byte requireConfirm = params.byte('requireConfirm', 0 as Byte)
        String json = params.json
        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)

        TeacherDTO teacher = teacherBaseResult.result
        Message message
        //再次发送
        if (reSend == 1) {
            message = messageService.updateLastNotifyTime(teacher.schoolId, teacher.campusId, msgId)
            messageService.asyncReSendWxMessage2Teacher(msgId)
        } else {
            //获取最终接收的教师Id列表
            Set<Long> toSendMessageTeacherIdSet = ToStringUnits.idsString2LongList(teacherIds)
            int teacherCount = toSendMessageTeacherIdSet?.size() ?: 0

            def messageInfo = messageService.saveMainMessage(requireConfirm, teacher, title, content, url,
                    ConstantEnum.MessageCates.EDUCATIONAL_ADMINISTRATION_NOTICE_ONLINE.cate,
                    teacherCount, pic, enclosure, json, teacherIds, top,noticeType,noticeId)

            message = messageInfo.message

            Promise task = Promises.task {
                messageService.genMessageRecordsAndSendWxNotify2Teacher(teacher?.campusId, message, toSendMessageTeacherIdSet)
            }
            task.onError { Throwable throwable ->
                log.error("hiiAdmin teacher notice message send", throwable)
            }
        }
        resultVO.result.put('id', message?.id)
        resultVO.status = 1

        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: 'utf-8'
    }

    def update() {
        ResultVO resultVO = ResultVO.success()
        Long msgId = params.long('id')
        Byte status = params.byte("status")
        Message message = messageService.fetchMessageByIdAndStatus(msgId, 1 as byte)
        //确认+已读
        if (message?.status > ConstantEnum.MessageStatus.DELETE.status) {
            BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)
            TeacherDTO teacher = teacherBaseResult.result

            MessageRecord messageRecord = messageService.fetchMessageRecordByMessageIdAndReceiverId(msgId, teacher?.id, ConstantEnum.UserTypeEnum.TEACHER.type)
            if (messageRecord) {
                if (status == ConstantEnum.MessageStatus.READ_NOT_CONFIRMED.status && messageRecord.status < ConstantEnum.MessageStatus.CONFIRMED.status) {
                    messageRecord.status = status
                    messageService.saveMessageRecord(messageRecord)
                    resultVO.result.put("id", message.id)
                } else if (status == ConstantEnum.MessageStatus.CONFIRMED.status) {
                    messageRecord.status = status
                    messageService.saveMessageRecord(messageRecord)
                    message.confirmedCount = message.confirmedCount + 1
                    message.lastUpdated = message.lastUpdated
                    messageService.saveMessage(message)
                    resultVO.result.put("id", message.id)
                }
            }
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        Long msgId = params.long("id")
        Long userId = request.getAttribute("userId") as Long
        Message message = messageService.fetchMessageById(msgId, false)
        if (message.senderId == userId && message.senderType == ConstantEnum.UserTypeEnum.TEACHER.type) {
            messageService.withdrawSchoolMessage(msgId)
        } else {
            throw new HiiAdminException("仅能撤回自己发布的通知信息")
        }

        render text: JSON.toJSONString(ResultVO.success([id: msgId])), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        String title = params.title
        String content = params.content
        String url = params.url
        Long msgId = params.long('id')
        String teacherIds = PhenixCoder.decodeIds(params.teacherIds)
        String pic = params.pic
        String enclosure = params.enclosure
        Byte requireConfirm = params.byte('requireConfirm', 0 as Byte)
        String json = params.json
        Boolean top = params.boolean("top", false)

        Byte noticeType = params.byte('noticeType')
        Long noticeId = PhenixCoder.decodeId(params.noticeId)

        BaseResult<TeacherDTO> teacherBaseResult = userService.findTeacherViaRequest(request)

        TeacherDTO teacher = teacherBaseResult.result

        Message message = messageService.fetchMessageById(msgId, false)

        if (message) {
            //获取最终接收的教师Id列表
            Set<Long> toSendMessageTeacherIdSet = ToStringUnits.idsString2LongList(teacherIds)
            int teacherCount = toSendMessageTeacherIdSet?.size() ?: 0
            messageService.updateMessageStatus(msgId,teacher, title, content, url, teacherIds, pic, enclosure, requireConfirm, json, teacherCount, top,noticeType,noticeId)
            Message updateMessage = messageService.fetchMessageById(msgId, false)
            Promise task = Promises.task {
                messageService.genMessageRecordsAndSendWxNotify2Teacher(teacher?.campusId, updateMessage, toSendMessageTeacherIdSet)
            }
            task.onError { Throwable throwable ->
                log.error("hiiAdmin teacher notice message send", throwable)
            }
        }
        resultVO.result.put('id', message?.id)
        resultVO.status = 1

        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: 'utf-8'
    }
}

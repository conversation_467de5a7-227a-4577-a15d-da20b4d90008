package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.auth.LoginRecordService
import hiiadmin.biz.StaffService
import hiiadmin.module.JwtUser
import hiiadmin.school.CampusService
import hiiadmin.utils.JWTUtils
import hiiadmin.utils.Password
import hiiadmin.utils.PatternUtils
import org.apache.commons.lang3.StringUtils
import timetabling.user.Staff

import static com.google.common.base.Preconditions.checkNotNull
import static com.google.common.base.Preconditions.checkState

class LoginController implements BaseExceptionHandler {

    LoginRecordService loginRecordService

    EnvService envService

    StaffService staffService

    CampusService campusService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 0
        String mobile = params.mobile
        String password = params.password
        JwtUser jwtUser = new JwtUser()
        String token
        checkNotNull(mobile, "请输入手机号")
        checkState(PatternUtils.isMobile(mobile, envService), "请输入正确的手机号")
        checkNotNull(password, "请输入密码")
        Staff staff = staffService.fetchStaffByTrueMobileAndAppId(mobile, "BUREAU")
        checkNotNull(staff, "用户不存在,请联系管理员")
        if (!staff.passwordHash) {
            resultVO.code = BizErrorCode.NULL_PASSWORD_ERROR.code
            resultVO.msg = BizErrorCode.NULL_PASSWORD_ERROR.msg
        } else if (!Password.authenticatePassword(staff.passwordHash, password)) {
            resultVO.code = BizErrorCode.LOGIN_ERROR.code
            resultVO.msg = BizErrorCode.LOGIN_ERROR.msg
        } else {
            jwtUser.staffId = staff.id
            jwtUser.bureauId = staff.bureauId
            jwtUser.mobile = mobile
            resultVO.status = 1
            token = JWTUtils.createOneDayJWT(jwtUser)
        }
        if (token) {
            resultVO.result.put("mobile", mobile)
            resultVO.result.put(Constants.JWT_TOKEN, token)
            if (StringUtils.isBlank(staff.passwordHash)) {
                resultVO.result.put('pswd', false)
            } else {
                resultVO.result.put('pswd', true)
            }
            String corpId = campusService.fetchCampusById(staff?.campusId)?.corpId
            resultVO.result.put("corpId", corpId)
        }

        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String password = params.password
        String checkNo = params.code
        String mobile = params.mobile
        checkNotNull(mobile, "请输入手机号")
        checkState(PatternUtils.isMobile(mobile, envService), "请输入正确的手机号")
        checkNotNull(password, "请输入密码")
        checkState(loginRecordService.checkResetPassword(mobile, checkNo), "验证码错误")
        Staff staff = staffService.fetchStaffByTrueMobileAndAppId(mobile, "BUREAU")
        if (staff) {
            staff.passwordHash = Password.createPassword(password)
            staffService.save(staff)
        } else {
            resultVO.status = 0
            resultVO.code = BizErrorCode.AUTHORITY_ERROR.code
            resultVO.msg = BizErrorCode.AUTHORITY_ERROR.msg
        }
        if (resultVO.status == 1) {
            resultVO.result.put("success", true)
        } else {
            resultVO.result.put("success", false)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.department

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.Department4SchoolVO
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.school.CampusService
import hiiadmin.school.repository.RepositoryCampusService
import hiiadmin.utils.PhenixCoder
import timetabling.Department
import timetabling.org.Campus

class HiiAdminDepartment4SchoolController implements BaseExceptionHandler {

    DepartmentService departmentService

    CampusService campusService

    RepositoryCampusService repositoryCampusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long schoolId = request.getAttribute("schoolId") as Long
        boolean t = params.boolean("t")
        String searchValue = params.searchValue

        String encodeCampusId = params.campusId

        List<Campus> campusList = []
        
        if (encodeCampusId) {
            campusList.add(campusService.fetchCampusById(PhenixCoder.decodeId(encodeCampusId)))
        } else {
            campusList = repositoryCampusService.findCampusListBySchoolId(schoolId)
        }
        
        Multimap<Long, Department> campusIdDepartmentMap = departmentService.transformCampusIdDepartmentListBySchoolId(schoolId)
        List<Department4SchoolVO> list = []
        campusList.each {
            Campus campus ->
                List<DepartmentVO> departmentList = departmentService.transformDepartmentVOList(campus, t, searchValue, campusIdDepartmentMap.get(campus.id).toList())
                departmentList.each{dept->
                    dept.id = PhenixCoder.decodeId(dept.id)
                    dept.teachers.each {tea->
                        tea.id = PhenixCoder.decodeId(tea.id)
                        tea.staffDepartmentId = PhenixCoder.decodeId(tea.staffDepartmentId)
                    }
                    dept.persons.each {per->
                        per.userId = PhenixCoder.decodeId(per.userId)
                    }
                }
                Department4SchoolVO department4SchoolVO = new Department4SchoolVO(
                        campusId: campus.id,
                        campusName: campus.name,
                        departmentList: departmentList
                )
                list.add(department4SchoolVO)
        }
        resultVO.result.put("list", list)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}
package hiiadmin.department

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.school.CampusService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import io.swagger.annotations.Api
import timetabling.Department
import timetabling.org.Campus
import timetabling.vocationalSchool.Faculty

import javax.servlet.http.HttpServletResponse

@Api(value = "部门管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminDepartmentController implements BaseExceptionHandler {

    DepartmentService departmentService

    CampusService campusService

    FacultyService facultyService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        boolean t = params.boolean("t")
        boolean normal = params.boolean("normal", true)
        String searchValue = params.searchValue
        Byte type = params.byte("type")
        List<DepartmentVO> departmentList = departmentService.getDepartmentVOList(campusId, t, searchValue, type, normal)
        departmentList.each{dept->
            dept.id = PhenixCoder.decodeId(dept.id)
            dept.teachers.each {tea->
                tea.id = PhenixCoder.decodeId(tea.id)
                tea.staffDepartmentId = PhenixCoder.decodeId(tea.staffDepartmentId)
            }
            dept.persons.each {per->
                per.userId = PhenixCoder.decodeId(per.userId)
            }
        }
        resultVO.result.put("list", departmentList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        String name = params.name
        
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = Campus.findByIdAndStatus(campusId, 1 as byte)
        DepartmentVO vo = null
        if (campus.type == 1){
            Department department = departmentService.fetchDepartmentById(id)
            if (department) {
                department.name = name
                departmentService.saveDepartment(department)
            }
            vo = new DepartmentVO(
                    id: department.id,
                    name: department.name,
            )
        } else {
            Faculty faculty = facultyService.update(campusId, id, name)?.faculty
            vo = new DepartmentVO(
                    id: faculty?.id,
                    name: faculty?.name
            )
        }
        resultVO.result = vo
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        String names = params.names
        Byte type = params.byte("type")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        ServiceResult serviceResult = departmentService.createAndSaveDepartments(campus, names, type)
        if (!serviceResult.success) {
            resultVO.status = 0
            resultVO.code = serviceResult.code.toInteger()
            resultVO.msg = serviceResult.message
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long id = params.long('id')
        String choice = params.choice
        
        Long campusId = request.getAttribute("campusId") as Long
        
        switch (choice) {
        //添加老师
            case "plus":
                String teacherIds = params.teacherIds
                departmentService.plusTeacher2department(id, teacherIds, campusId)
                break
                //移除老师
            case "minus":
                String teacherIds = params.teacherIds
                departmentService.minusTeacher4department(id, teacherIds, campusId)
                break
                //设置、取消主管
            case "supervisor":
                Long teacherId = params.long("teacherId")
                Boolean supervisor = params.boolean("supervisor")
                departmentService.setDepartmentSupervisor(id, teacherId, supervisor, campusId)
                break
        }
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long campusId = request.getAttribute("campusId") as Long
        
        Campus campus = campusService.fetchCampusByCampusId(campusId)
        
        if (campus.type == 1){
            Department department = departmentService.fetchDepartmentById(id)
            if (campusId != department?.campusId) {
                response.status = HttpServletResponse.SC_UNAUTHORIZED
                resultVO.status = 0
//            TODO 报错
                resultVO.code = 100
                resultVO.msg = "操作异常"
            } else {
                departmentService.deleteDepartmentById(id)
                resultVO.result.put("id", id)
            }
        } else {
            facultyService.delete(campusId, id)
            resultVO.result.put("id", id)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.department

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.biz.DepartmentService
import hiiadmin.biz.StaffService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import io.swagger.annotations.Api
import timetabling.Department
import timetabling.StaffDepartment
import timetabling.user.Staff
import timetabling.user.Teacher

/**
 * 该接口返回数据存在异常
 * 建议使用 /api/hiiadmin/1.0/school/teacher/department
 */
@Deprecated
@Api(value = "部门老师", tags = "总务办公", consumes = "admin")
class HiiAdminDepartmentTeacherController implements BaseExceptionHandler {

    DepartmentService departmentService

    StaffService staffService

    TeacherService teacherService

    UserEncodeInfoService userEncodeInfoService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long staffId = request.getAttribute("staffId") as Long

        List<StaffDepartment> staffDepartmentList = departmentService.fetchAllStaffDepartmentByCampusId(campusId)
        Map<Department, List<Teacher>> departmentIdTeacherListMap = Maps.newHashMap()
        //查找校区下所有老师
        List<Staff> staffList = staffService.fetchAllStaffByCampusId(campusId)
        Set<Long> teacherAllIdSet = staffList.collect { it.teacherId }.toSet()
        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacherAllIdSet as List<Long>, false)
        List<Department> departmentList = departmentService.fetchAllDepartmentByCampusId(campusId)
        departmentList?.each {
            Department department ->
                List<Long> teacherIdList = Lists.newArrayList()
                staffDepartmentList?.each {
                    StaffDepartment staffDepartment ->
                        if (staffDepartment.departmentId == department.id) {
                            teacherIdList.add(staffDepartment.teacherId)
                            teacherAllIdSet.remove(staffDepartment.teacherId)
                        }
                }
                List<Teacher> teacherList = teacherService.findTeacherListByTeacherIdInList(teacherIdList)
                departmentIdTeacherListMap.put(department, teacherList)
        }
        Department department = new Department()
        department.id = -1l
        department.name = '未分组'
        //未分组的老师
        List<Teacher> noDepartmentTeacherList = teacherService.findTeacherListByTeacherIdInList(teacherAllIdSet.asList())
        departmentIdTeacherListMap.put(department, noDepartmentTeacherList)

        [
                departmentIdTeacherListMap: departmentIdTeacherListMap,
                teacherIdMobileMap:teacherIdMobileMap
        ]
    }
}

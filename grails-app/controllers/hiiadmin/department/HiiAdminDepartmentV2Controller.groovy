package hiiadmin.department

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.DepartmentService
import hiiadmin.module.bugu.DepartmentVO
import timetabling.StaffDepartment

class HiiAdminDepartmentV2Controller implements BaseExceptionHandler {

    DepartmentService departmentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        Integer countType = params.int('countType', 1)
        List<Long> teacherIdList = []
        List<StaffDepartment> staffDepartmentList = departmentService.fetchAllStaffDepartmentByCampusId(campusId)
        if (staffDepartmentList && staffDepartmentList.size() > 0) {
            teacherIdList = staffDepartmentList*.teacherId.unique()
        }
        //未分组
        if (countType == 1) {
            List<Long> longList = departmentService.fetchAllStaffDepartmentTeacherIdByCampusIdAndTeacherIdList(campusId, teacherIdList)
            if (longList && longList.size() > 0) {
                teacherIdList = longList
            }
        }
        List<DepartmentVO> departmentList = departmentService.getDepartmentVOListV2(campusId, teacherIdList)
        resultVO.result.put("list", departmentList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

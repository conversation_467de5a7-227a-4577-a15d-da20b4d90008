package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO

class HiiadminEvaluationUnitQueryController {

    TeachingEvaluationService teachingEvaluationService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Byte teachingType = params.byte("teachingType")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        def resultList = teachingEvaluationService.findUnitByUserId(userId, campusId, teachingType)
        resultVO.result.put("list", resultList)
        resultVO.result.put("total", resultList.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def show() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Map<String, Object> map = teachingEvaluationService.checkUserIsHeadMaster(userId, campusId)
        resultVO.result = map
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "评语统计", tags = "行为德育", consumes = "admin")
class HiiadminTeachingEvaluationAccountingController {

    TeachingEvaluationAccountingService teachingEvaluationAccountingService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String studentIdStr = params.studentId
        Long studentId = PhenixCoder.decodeId(studentIdStr)
        Byte teachingType = params.byte("teachingType")
        Integer p = params.int("p", 1)
        Integer s = params.int("s", 30)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Map map = teachingEvaluationAccountingService.findAllTeachingCommentByStudentId(studentId, teachingType, userId, campusId, p, s)
        resultVO.result.put("list", map.get("list"))
        resultVO.result.put("total", map.get("total"))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        Long id = params.long("id")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        teachingEvaluationAccountingService.delete(id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

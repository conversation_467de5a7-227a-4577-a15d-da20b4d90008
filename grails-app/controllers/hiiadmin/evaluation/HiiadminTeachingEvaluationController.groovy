package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.evaluation.TeachingEvaluationStudentVO
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "评语填写", tags = "行为德育", consumes = "admin")
class HiiadminTeachingEvaluationController implements BaseExceptionHandler {

    TeachingEvaluationService teachingEvaluationService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String unitId = params.unitId
        Byte teachingType = params.byte("teachingType")
        String studentMsg = params.studentMsg
        studentMsg = PatternUtils.filter(studentMsg)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List<TeachingEvaluationStudentVO> studentVOList = teachingEvaluationService.fetchAllStudentByTeacherAndUnit(userId, campusId, unitId, teachingType, studentMsg)
        resultVO.result.put("list", studentVOList)
        resultVO.result.put("total", studentVOList.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String unitId = params.unitId
        String encodeStudentId = params.studentId
        Long studentId = PhenixCoder.decodeId(encodeStudentId, true)
        Byte teachingType = params.byte("teachingType")
        String content = params.content
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        teachingEvaluationService.saveTeachEvaluation(campusId, userId, unitId, studentId, teachingType, content)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def update() {
        Long id = params.long("id")
        String content = params.content
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        teachingEvaluationService.updateTeachEvaluation(id, content)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


}

package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "评语导出", tags = "行为德育", consumes = "admin")
class HiiadminTeachingEvaluationExportController {

    TeachingEvaluationExportService teachingEvaluationExportService

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String unitIds = params.unitIds
        unitIds = PhenixCoder.decodeIds(unitIds)
        String teachingTypes = params.teachingTypes
        String unitNames = params.unitNames
        Long semesterId = params.long("semesterId")

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = teachingEvaluationExportService.saveExportRecord(unitIds, teachingTypes, semesterId, campusId, unitNames, userId)
        resultVO.result.put("id", id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Integer p = params.int("p")
        Integer s = params.int("s")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Map resultMap = teachingEvaluationExportService.findAllExportRecord(userId, campusId, p, s)
        resultVO.result.put("list", resultMap.get("list"))
        resultVO.result.put("total", resultMap.get("total"))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

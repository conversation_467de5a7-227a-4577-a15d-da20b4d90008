package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.school.CampusService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.org.Campus

@Api(value = "创建评教任务时查询课程", tags = "智慧教务", consumes = "admin")
class HiiadminStudentEvaluationCourseController {

    StudentEvaluationTaskService studentEvaluationTaskService

    CampusService campusService

    def index() {
        ResultVO resultVO = new ResultVO()
        Long campusId = request.getAttribute("campusId") as Long
        Campus campus = campusService.fetchCampusById(campusId)
        String gradeIds = params.gradeIds
        gradeIds = PhenixCoder.decodeIds(gradeIds) //职校为unitId
        if (!gradeIds) {
            if (campus?.type == ConstantEnum.CampusType.K12.type) {
                throw new HiiAdminException("请选择年级")
            } else {
                throw new HiiAdminException("请选择班级")
            }
        }
        def list = studentEvaluationTaskService.findAllCourseByCurrentSemester(campusId, gradeIds, campus?.type)
        resultVO.status = 1
        resultVO.result.put("list", list)
        resultVO.result.put("total", list.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.teaching.clientapi.domain.basic.Unit
import com.bugu.teaching.clientapi.domain.lecture.LectureOriginTeachingRelationCell
import com.bugu.teaching.clientapi.service.TeachingRelationService
import com.bugu.teaching.clientapi.service.TeachingStudentService
import com.bugu.teaching.clientapi.service.TeachingUnitService
import groovy.util.logging.Slf4j
import hiiadmin.CacheService
import hiiadmin.ConstantEnum
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import org.apache.dubbo.config.annotation.DubboReference
import timetabling.evaluation.StudentEvaluationCommitRecord
import timetabling.evaluation.StudentEvaluationTask
import timetabling.org.Semester
import timetabling.org.UnitStudent
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher

import java.util.concurrent.TimeUnit

@Api(value = "学生评教任务", tags = "智慧教务", consumes = "admin")
@Slf4j
class HiiadminStudentEvaluationTaskController {

    StudentEvaluationTaskService studentEvaluationTaskService

    @DubboReference
    private TeachingUnitService teachingUnitService

    @DubboReference
    private TeachingRelationService relationService

    @DubboReference
    private TeachingStudentService teachingStudentService

    SchoolService schoolService

    CacheService cacheService

    CampusService campusService


    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer p = params.int("p", 1)
        Integer s = params.int("s", 30)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Map resultMap = studentEvaluationTaskService.findAllEvaluationTask(campusId, p, s)
        resultVO.result.put("list", resultMap.list)
        resultVO.result.put("total", resultMap.total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        Long id = params.long("id")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        def result = studentEvaluationTaskService.findEvaluationTaskById(id)
        resultVO.result = result
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        String title = params.title
        String questionJson = params.questionJson
        String formJson = params.formJson
        String gradeIds = params.gradeIds //职校为unitId
        gradeIds = PhenixCoder.decodeIds(gradeIds)
        String courseIds = params.courseIds
        try {
            Byte campusType = campusService.fetchCampusById(campusId)?.type ?: ConstantEnum.CampusType.K12.type

            Date date = new Date()
            StudentEvaluationTask studentEvaluationTask = new StudentEvaluationTask()
            switch (userType) {
                case ConstantEnum.UserTypeEnum.STUDENT.type:
                    Student student = Student.findById(userId)
                    studentEvaluationTask.createUserName = student.name ? student.name : null
                    break
                case ConstantEnum.UserTypeEnum.PARENT.type:
                    Parent parent = Parent.findById(userId)
                    studentEvaluationTask.createUserName = parent.name ? parent.name : null
                    break
                case ConstantEnum.UserTypeEnum.TEACHER.type:
                    Teacher teacher = Teacher.findById(userId)
                    studentEvaluationTask.createUserName = teacher.name ? teacher.name : null
                    break
                default:
                    break
            }
            List<String> courseIdList = courseIds.trim().split(",").toList()
            List<Long> gradeIdList = gradeIds.trim().split(",").toList().collect { it as Long }
            //查询应提交人数
            List<LectureOriginTeachingRelationCell> lectureOriginTeachingRelationCells = relationService.findAllTeachingRelation(campusId.toString(), 1 as byte)

            studentEvaluationTask.campusId = campusId
            studentEvaluationTask.createUserId = userId
            studentEvaluationTask.title = title
            studentEvaluationTask.status = ConstantEnum.StudentEvaluationTaskStatus.NOTPUBLISH.type
            if (campusType == ConstantEnum.CampusType.K12.type) {
                studentEvaluationTask.gradeIds = gradeIds
            } else {
                studentEvaluationTask.vocationalUnitIds = gradeIds
            }
            studentEvaluationTask.courseIds = courseIds
            studentEvaluationTask.dateCreated = date
            studentEvaluationTask.lastUpdated = date
            studentEvaluationTask.version = 1L
            studentEvaluationTask.questionJson = questionJson
            studentEvaluationTask.formJson = formJson
            //关联当前学期
            Semester semester = schoolService.fetchCurrentSemesterByCampusId(campusId)
            studentEvaluationTask.semesterId = semester?.id
            studentEvaluationTask.semesterName = semester.academicYear.substring(0, 9) + ConstantEnum.SemesterType.getEnumByType(semester.semesterType).name

            List<StudentEvaluationCommitRecord> list = []
            Set<LectureOriginTeachingRelationCell> lectureOriginTeachingRelationCellSet = lectureOriginTeachingRelationCells.findAll { l -> courseIdList.contains(l.courseId) }.toSet()
            List<String> unitIdList = lectureOriginTeachingRelationCellSet*.unitId
            Map<String, List<LectureOriginTeachingRelationCell>> map = lectureOriginTeachingRelationCellSet.groupBy { l -> l.unitId }
            List<com.bugu.teaching.clientapi.domain.basic.Student> teachingStudentList = []
            if (campusType == ConstantEnum.CampusType.K12.type) {
                teachingStudentList = teachingStudentService.findByUnitIdInList(unitIdList, 1 as byte).findAll { st -> gradeIdList.contains(st.grade?.id as Long) }
            } else {
                List<com.bugu.teaching.clientapi.domain.basic.Student> teachingStudents = teachingStudentService.findByUnitIdInList(unitIdList, 1 as byte)
                teachingStudents?.each { student ->
                    List<String> teachingUnitList = student.units*.id?.toList() ?: []
                    gradeIdList?.each { gradeId ->
                        if (gradeId?.toString() in teachingUnitList) {
                            teachingStudentList.add(student)
                        }
                    }
                }
            }
//        log.info("教学班学生集合:${JSONObject.toJSONString(teachingStudentList)}".toString())
            if (!teachingStudentList) {
                throw new HiiAdminException("该课程或该年级对应的部分教学班没有分配学生，请分配学生后再去创建评教任务!")
            }
            List<Long> studentIdList = teachingStudentList*.id.collect { it as Long }
            log.info("studentIdList参数:{}", studentIdList)
            Map<Long, List<UnitStudent>> studentMap = UnitStudent.findAllByStudentIdInListAndUnitTypeAndStatus(studentIdList, ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type, 1 as byte).groupBy { u -> u.studentId }
            Long id
            if (teachingStudentList) {
                studentEvaluationTask.mustCommitCount = teachingStudentList.size()
                id = studentEvaluationTaskService.saveTask(studentEvaluationTask)
                String flag = "studentEvaluationTask_${id}".toString()
                cacheService.studentEvaluationTask.put(flag, Boolean.TRUE, 1, TimeUnit.HOURS)
                //生成记录
                teachingStudentList.each { st ->
                    List<Unit> unitList = st.units
                    unitList.each { u ->
                        if (map.containsKey(u.id)) {
                            map.get(u.id).each { l ->
                                StudentEvaluationCommitRecord studentEvaluationCommitRecord = new StudentEvaluationCommitRecord()
                                studentEvaluationCommitRecord.studentId = Long.parseLong(st.id)
                                studentEvaluationCommitRecord.lastUpdated = new Date()
                                studentEvaluationCommitRecord.dateCreated = new Date()
                                studentEvaluationCommitRecord.courseId = l.courseId
                                studentEvaluationCommitRecord.campusId = campusId
                                studentEvaluationCommitRecord.classUnitId = studentMap.get(st.id as Long)?.get(0)?.unitId
                                studentEvaluationCommitRecord.teacherId = Long.parseLong(l.teacherId)
                                studentEvaluationCommitRecord.teacherName = l.teacherName
                                studentEvaluationCommitRecord.studentEvaluationTaskId = id
                                studentEvaluationCommitRecord.courseName = l.courseName
                                studentEvaluationCommitRecord.gradeId = Long.parseLong(st.grade.id)
                                studentEvaluationCommitRecord.status = 0 as byte
                                studentEvaluationCommitRecord.studentCode = st.code
                                studentEvaluationCommitRecord.studentName = st.name
                                studentEvaluationCommitRecord.version = 1L
                                list.add(studentEvaluationCommitRecord)
                            }
                        }
                    }
                }
            } else {
                id = studentEvaluationTaskService.saveTask(studentEvaluationTask)
            }
            studentEvaluationTaskService.save(list)
            if (id) {
                String flag = "studentEvaluationTask_${id}".toString()
                if (list.isEmpty()) {
                    cacheService.studentEvaluationTask.remove(flag)
                } else {
                    cacheService.studentEvaluationTask.put(flag, Boolean.TRUE, 1, TimeUnit.MINUTES)
                }
            }
            resultVO.result.put("id", id)
        } catch (Exception e) {
            log.error("创建评教任务失败:".toString(), e)
            resultVO.message = e.message
            resultVO.status = 0
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Byte userType = request.getAttribute("type") as Byte
        String title = params.title
        String formJson = params.formJson
        String questionJson = params.questionJson
        String gradeIds = params.gradeIds
        gradeIds = PhenixCoder.decodeIds(gradeIds)
        String courseIds = params.courseIds
        Long id = params.long("id")
        String flag = "studentEvaluationTask_${id}".toString()
        Boolean get = cacheService.studentEvaluationTask.get(flag)

        if (get) {
            resultVO.status = 0
            resultVO.message = "上一次更改正在同步中，请稍后重试"
            resultVO.code = 100
            render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
            return
        }
        try {
            Byte campusType = campusService.fetchCampusById(campusId)?.type ?: ConstantEnum.CampusType.K12.type

            String publishUsername = ""
            switch (userType) {
                case ConstantEnum.UserTypeEnum.STUDENT.type:
                    Student student = Student.findById(userId)
                    publishUsername = student.name ? student.name : null
                    break
                case ConstantEnum.UserTypeEnum.PARENT.type:
                    Parent parent = Parent.findById(userId)
                    publishUsername = parent.name ? parent.name : null
                    break
                case ConstantEnum.UserTypeEnum.TEACHER.type:
                    Teacher teacher = Teacher.findById(userId)
                    publishUsername = teacher.name ? teacher.name : null
                    break
                default:
                    break
            }
            List<String> courseIdList = courseIds.trim().split(",").toList()
            StudentEvaluationTask studentEvaluationTask = StudentEvaluationTask.findById(id)
            if (ConstantEnum.StudentEvaluationTaskStatus.GATHER.type.equals(studentEvaluationTask.status)) {
                throw new HiiAdminException("请取消发布后再修改!")
            }
            Integer mustCommitCount = studentEvaluationTask.mustCommitCount
            //查询该任务下是否生成过学生，如果有则删除并重新生成，没有则不管
            Integer count = StudentEvaluationCommitRecord.countByStudentEvaluationTaskIdAndCampusId(id, campusId)
            List<StudentEvaluationCommitRecord> list = []
            List<Long> gradeIdList = gradeIds.trim().split(",").toList().collect { it as Long }
            List<LectureOriginTeachingRelationCell> lectureOriginTeachingRelationCells = relationService.findAllTeachingRelation(campusId.toString(), 1 as byte)
            Set<LectureOriginTeachingRelationCell> lectureOriginTeachingRelationCellSet = lectureOriginTeachingRelationCells.findAll { l -> courseIdList.contains(l.courseId) }.toSet()
            List<String> unitIdList = lectureOriginTeachingRelationCellSet*.unitId
            Map<String, List<LectureOriginTeachingRelationCell>> map = lectureOriginTeachingRelationCellSet.groupBy { l -> l.unitId }
            List<com.bugu.teaching.clientapi.domain.basic.Student> teachingStudentList = []

            if (campusType == ConstantEnum.CampusType.K12.type) {
                teachingStudentList = teachingStudentService.findByUnitIdInList(unitIdList, 1 as byte).findAll { st -> gradeIdList.contains(st.grade?.id as Long) }
            } else {
                List<com.bugu.teaching.clientapi.domain.basic.Student> teachingStudents = teachingStudentService.findByUnitIdInList(unitIdList, 1 as byte)
                teachingStudents?.each { student ->
                    List<String> teachingUnitList = student.units*.id?.toList() ?: []
                    gradeIdList?.each { gradeId ->
                        if (gradeId?.toString() in teachingUnitList) {
                            teachingStudentList.add(student)
                        }
                    }
                }
            }
            //        log.info("教学班学生集合:${JSONObject.toJSONString(teachingStudentList)}".toString())
            if (!teachingStudentList) {
                throw new HiiAdminException("该课程或该年级对应的部分教学班没有分配学生，请分配学生后再重新编辑评教任务!")
            }
            List<Long> studentIdList = teachingStudentList*.id.collect { it as Long }
            log.info("studentIdList参数:${studentIdList}".toString())
            Map<Long, List<UnitStudent>> studentMap = UnitStudent.findAllByStudentIdInListAndUnitTypeAndStatus(studentIdList, ConstantEnum.UnitType.ADMINISTRATIVE_CLASS.type, 1 as byte).groupBy { u -> u.studentId }
            if (teachingStudentList) {
                mustCommitCount = teachingStudentList.size()
                log.info("mustCommitCount参数日志打印:${mustCommitCount}".toString())
                //生成记录
                teachingStudentList.each { st ->
                    List<Unit> unitList = st.units
                    unitList.each { u ->
                        if (map.containsKey(u.id)) {
                            map.get(u.id).each { l ->
                                StudentEvaluationCommitRecord studentEvaluationCommitRecord = new StudentEvaluationCommitRecord()
                                studentEvaluationCommitRecord.studentId = Long.parseLong(st.id)
                                studentEvaluationCommitRecord.lastUpdated = new Date()
                                studentEvaluationCommitRecord.dateCreated = new Date()
                                studentEvaluationCommitRecord.courseId = l.courseId
                                studentEvaluationCommitRecord.campusId = campusId
                                studentEvaluationCommitRecord.studentEvaluationTaskId = id
                                studentEvaluationCommitRecord.classUnitId = studentMap.get(st.id as Long)?.get(0)?.unitId
                                studentEvaluationCommitRecord.teacherId = Long.parseLong(l.teacherId)
                                studentEvaluationCommitRecord.teacherName = l.teacherName
                                studentEvaluationCommitRecord.courseId = l.courseId
                                studentEvaluationCommitRecord.gradeId = Long.parseLong(st.grade.id)
                                studentEvaluationCommitRecord.courseName = l.courseName
                                studentEvaluationCommitRecord.status = 0 as byte
                                studentEvaluationCommitRecord.studentName = st.name
                                studentEvaluationCommitRecord.version = 1L
                                studentEvaluationCommitRecord.studentCode = st.code
                                list.add(studentEvaluationCommitRecord)
                            }
                        }
                    }
                }
            }
            studentEvaluationTaskService.update(title, questionJson, gradeIds, courseIds, id, campusId, formJson, list, count, mustCommitCount)
        } catch (Exception e) {
            log.error("编辑评教任务失败:".toString(), e)
            resultVO.message = e.message
            resultVO.status = 0
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        studentEvaluationTaskService.deleteById(id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Byte status = params.byte("status")
        Long campusId = request.getAttribute("campusId") as Long
        studentEvaluationTaskService.updateStatus(id, status, campusId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.evaluation.TeachingEvaluationManageVO
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "评语管理", tags = "行为德育", consumes = "admin")
class HiiadminTeachingEvaluationManageController {

    TeachingEvaluationManageService teachingEvaluationManageService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String unitId = params.unitId
        String studentMsg = params.studentMsg
        studentMsg = PatternUtils.filter(studentMsg)
        String semesterIdStr = params.semesterId
        Long semesterId = PhenixCoder.decodeId(semesterIdStr)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List<TeachingEvaluationManageVO> studentVOList = teachingEvaluationManageService.fetchAllStudentByTeacherAndUnit(userId, campusId, PhenixCoder.decodeId(unitId), studentMsg, semesterId)
        resultVO.result.put("list", studentVOList)
        resultVO.result.put("total", studentVOList.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        Long campusId = request.getAttribute("campusId") as Long
        String studentIdStr = params.id
        Long studentId = PhenixCoder.decodeId(studentIdStr)

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String semesterIdStr = params.semesterId
        Long semesterId = PhenixCoder.decodeId(semesterIdStr)
        
        Integer p = params.int("p", 1)
        Integer s = params.int("s", 30)
        Map map = teachingEvaluationManageService.fetchAllTeachingEvaluationByStudentId(semesterId, studentId, campusId, p, s)
        resultVO.result.put("list", map.get("list"))
        resultVO.result.put("total", map.get("total"))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

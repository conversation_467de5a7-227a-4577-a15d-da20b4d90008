package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "评语模板下载", tags = "行为德育", consumes = "admin")
class HiiadminTeachingEvaluationTemplateController {

    TeachingEvaluationExportService teachingEvaluationExportService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String unitIds = params.unitIds
        unitIds = PhenixCoder.decodeIds(unitIds)
        Byte teachingType = params.byte("teachingType")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String url = ""
        try {
            url = teachingEvaluationExportService.downloadTemplate(unitIds, teachingType, campusId, userId)
        } catch (Exception e) {
            resultVO.status = 0
            resultVO.message = e.message
        }
        resultVO.result.put("url", url)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

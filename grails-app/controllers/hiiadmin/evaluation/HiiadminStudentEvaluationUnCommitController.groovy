package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.module.bugu.StudentVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

@Api(value = "未提交名单", tags = "智慧教务", consumes = "admin")
class HiiadminStudentEvaluationUnCommitController {

    StudentEvaluationCommitRecordService studentEvaluationCommitRecordService

    def show() {
        Long campusId = request.getAttribute("campusId") as Long
        String unitIdStr = params.unitId
        Long unitId = PhenixCoder.decodeId(unitIdStr)
        Long id = params.long("id")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List<StudentVO> list = studentEvaluationCommitRecordService.findUncommitStudent(unitId, id, campusId)
        resultVO.result.put("list", list)
        resultVO.result.put("total", list.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

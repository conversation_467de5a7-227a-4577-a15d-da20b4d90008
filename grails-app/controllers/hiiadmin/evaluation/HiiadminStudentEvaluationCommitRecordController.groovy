package hiiadmin.evaluation

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api

/***
 * 学生评教提交情况
 */
@Api(value = "提交情况", tags = "智慧教务", consumes = "admin")
class HiiadminStudentEvaluationCommitRecordController {

    StudentEvaluationCommitRecordService studentEvaluationCommitRecordService

    def show() {
        Long campusId = request.getAttribute("campusId") as Long
        String gradeIdStr = params.gradeId
        Long gradeId = PhenixCoder.decodeId(gradeIdStr)
        Long id = params.long("id")
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List result = studentEvaluationCommitRecordService.getCommitCondition(campusId, gradeId, id)
        resultVO.result.put("list", result)
        resultVO.result.put("total", result.size())
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

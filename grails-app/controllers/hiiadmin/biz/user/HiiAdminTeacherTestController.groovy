package hiiadmin.biz.user

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.ToStringUnits

/**
 * <AUTHOR>
 * @Date 2022-10-25 18:18
 */
@Slf4j
class HiiAdminTeacherTestController implements BaseExceptionHandler {

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String ids = params.ids
        List<Long> teacherIds = ToStringUnits.idsString2LongList(ids)
        Long teacherId = params.long("teacherId", 22713)
        Map<Long, String> teacherIdIdCardMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.ID_CARD.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacherIds, false)
        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(ConstantEnum.UserEncodeInfoType.MOBILE.type, ConstantEnum.UserTypeEnum.TEACHER.type, teacherIds, false)
        resultVO.result.put("idCard", teacherIdIdCardMap.get(teacherId))
        resultVO.result.put("mobile", teacherIdMobileMap.get(teacherId))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

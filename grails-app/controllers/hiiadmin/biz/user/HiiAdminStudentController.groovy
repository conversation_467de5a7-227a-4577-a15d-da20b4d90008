package hiiadmin.biz.user

import com.alibaba.fastjson.JSON
import com.bugu.IdCardUtil
import com.bugu.PhoenixCoder
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.EnvService
import hiiadmin.StudentSearchService
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.module.ListVM
import hiiadmin.module.bugu.StudentVO
import hiiadmin.school.CampusService
import hiiadmin.school.CardUserService
import hiiadmin.school.StudentService
import hiiadmin.school.UnitService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.school.user.AppStudentService
import hiiadmin.school.user.structure.StudentDataBuildService
import hiiadmin.school.user.structure.StudentDataStructureService
import hiiadmin.user.PersonDataChangeService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.UserNameCorrectUtil
import io.swagger.annotations.Api
import timetabling.org.Campus
import timetabling.org.Unit
import timetabling.org.UnitStudent
import timetabling.user.Student

import static hiiadmin.ConstantEnum.*

@Slf4j
@Api(value = "学生管理", tags = "基础管理", consumes = "admin")
@Deprecated
class HiiAdminStudentController implements BaseExceptionHandler {

    StudentService studentService

    UnitService unitService

    EnvService envService

    UnitStudentService unitStudentService

    StudentSearchService studentSearchService

    CardUserService cardUserService

    UserEncodeInfoService userEncodeInfoService

    PersonDataChangeService personDataChangeService

    StudentDataStructureService studentDataStructureService

    StudentDataBuildService studentDataBuildService
    
    ViewDOService viewDOService

    AppStudentService appStudentService

    CampusService campusService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
//        Long campusId = params.long("campusId")
        Long sectionId = params.long("sectionId")
        Long gradeId = params.long("gradeId")
        Long unitId = params.long("unitId")
        String unitIds = params.unitIds
        Byte type = params.byte("type")
        
        String searchValue = params.searchValue
        // 放学班使用
        Long afterUnitId = params.long("afterUnitId")
        boolean graduate = params.boolean("graduate", false)
        Byte belong = params.byte('belong', 0 as Byte)
        Long teacherId = request.getAttribute("userId") as Long
        List<Long> manageUnitIds = []
        if (belong == 1 as byte) {
            manageUnitIds = unitService.fetchAllUnitByCampusIdAndTeacherId(campusId, teacherId, null)*.id
        }
        List<Student> studentList = []
        Integer count = 0

        Campus campus = campusService.fetchCampusByCampusId(campusId)
        
        if (searchValue) {
            studentList.addAll(studentSearchService.searchStudentLimit(schoolId, campusId, sectionId, gradeId, unitId, null, searchValue, p, s, graduate, manageUnitIds))
            count = studentSearchService.countSearchStudent(schoolId, campusId, sectionId, gradeId, unitId,null,null, null, searchValue, graduate, manageUnitIds)
        } else if (unitIds) {
            List<Long> unitIdList = unitIds.split(",").collect { it.toLong() }
            List<UnitStudent> unitStudentList = unitStudentService.fetchAllUnitStudentViaUnitIdList(unitIdList)
            List<Long> studentIdList = unitStudentList*.studentId
            if (studentIdList && studentIdList.size() > 0) {
                studentList = studentService.fetchAllStudentByStudentIdInList(studentIdList)
                count = studentIdList.size()
            }
        } else {
            ListVM<Student> studentListVMList = studentService.fetchAllStudentByLimit(schoolId, campusId, sectionId, gradeId, unitId, type, p, s, graduate, manageUnitIds)
            studentList.addAll(studentListVMList.list)
            count = studentListVMList.total
        }
        List<StudentVO> studentVOList = studentDataBuildService.buildStudentVOListAddParent(studentList, campus, afterUnitId, graduate)
        if (graduate && unitId) {
            resultVO.result.put('graduateTime', unitService.fetchUnitById(unitId,false)?.lastUpdated?.time)
        }
        studentVOList.each{ stu ->
            stu.id = PhenixCoder.decodeId(stu.id)
            stu.classUnitId = PhenixCoder.decodeId(stu.classUnitId)
            stu.campusId = PhenixCoder.decodeId(stu.campusId)
            stu.schoolId = PhenixCoder.decodeId(stu.schoolId)
            stu.unitId = PhenixCoder.decodeId(stu.unitId)
            stu.gradeId = PhenixCoder.decodeId(stu.gradeId)
            stu.sectionId = PhenixCoder.decodeId(stu.sectionId)
            stu.facultyId = PhenixCoder.decodeId(stu.facultyId)
            stu.majorId = PhenixCoder.decodeId(stu.majorId)
        }
        resultVO.result.put('list', studentVOList)
        resultVO.result.put('total', count)
        resultVO.result.put('campusStrId', PhoenixCoder.encodeId(campusId))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long studentId = params.long('id')
        Student student = studentService.fetchStudentById(studentId)
        StudentVO vo = appStudentService.buildStudentVOAddParents(student)
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long

        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        
        String livePlace = params.livePlace

        String name = params.name
        if (!name) {
            throw new HiiAdminException("姓名不能为空")
        } else if (!PatternUtils.isNameLe50(name)) {
            throw new HiiAdminException("姓名不得超过50字符，请重新设置")
        }
        String eName = params.eName
        if (eName) {
            if (!PatternUtils.isEName(eName)) {
                throw new HiiAdminException("""英文名仅能包含英文、数字、空格、'.'、'·'""")
            } else if (!PatternUtils.isENameLe100(eName)) {
                throw new HiiAdminException("英文名不得超过100字符，请重新设置")
            }
        }
        String code = params.code
        if (!code) {
            throw new HiiAdminException("学号不能空")
        } else if (code.length() > 25) {
            throw new HiiAdminException("学号长度不超过25！")
        }
        Long unitId = params.long("unitId")

        if (!unitId) {
            throw new HiiAdminException("班级不能为空")
        }
        String avatar = params.avatar
        Boolean gender = params.boolean('gender')
        String idCard = params.idCard
        String cardNum = params.cardNum
        Unit unit = unitService.fetchUnitById(unitId)
        Student student = studentService.createAndSaveNewStudentV2(unit, name, eName, code, gender, avatar, idCard, cardNum, livePlace)
        StudentVO studentVO = viewDOService.buildStudentVO(student, false)
        int changeBit = PersonDataChangeEnum.userBaseChangeHandlerBit()
        personDataChangeService.studentDataChange(campusId, student, userId, userType, changeBit, OperateType.ADD.type)
        resultVO.result = ObjectUtils.resultVOObject2Map(studentVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long id = params.long('id')
        Long unitId = params.long('unitId')
        Long schoolId = request.getAttribute("schoolId") as Long
        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte
        Byte studentStatus = params.byte("status")
        resultVO.result.put('id', id)
        if (studentStatus != null) {
            // 增加休学功能
            if (!id) {
                throw new HiiAdminException("未选择学生")
            }
            studentDataStructureService.suspendStudent(id, campusId, userId, userType)
        } else {
            //目前不存在换班同时更改学号的情况。
            if (unitId) {
                Unit unit = unitService.fetchUnitById(unitId)
                studentService.changeStudent2NewUnit(id, unit)
            }
            Student student = studentService.fetchStudentById(id)
            String name = params.name
            if (name) {
                if (!PatternUtils.isNameLe50(name)) {
                    throw new HiiAdminException("姓名不得超过50字符，请重新设置")
                }
                student.name = name
                student.simpleName= UserNameCorrectUtil.extractInitialsName(name)
            } else {
                throw new HiiAdminException("姓名不能为空")
            }
            String eName = params.eName
            if (eName) {
                if (!PatternUtils.isEName(eName)) {
                    throw new HiiAdminException("""英文名仅能包含英文、数字、空格、'.'、'·'""")
                }

                if (!PatternUtils.isENameLe100(eName)) {
                    throw new HiiAdminException("英文名不得超过100字符，请重新设置")
                }

                student.eName = eName
            } else {
                student.eName = null
            }
            String avatar = params.avatar
            String livePlace = params.livePlace
            
            if (livePlace) {
                if (livePlace.length() > 200) {
                    throw new HiiAdminException("家庭住址字数超出限制200")
                }
                student.livePlace = livePlace
            } else {
                student.livePlace = null
            }
            
            student.pic = avatar ? PatternUtils.getFileName(avatar) : null
            String idCard = params.idCard
            if (idCard) {
                //仅线上进行完全校检
                def re = IdCardUtil.identityCardVerification(idCard, envService.prod)
                if (!re.success) {
                    throw new HiiAdminException(re.message)
                }
                Student s = userEncodeInfoService.fetchStudentByTrueIdCard(idCard)
                if (s && s.id != id) {
                    throw new HiiAdminException("身份证号码已存在")
                }
            }
            userEncodeInfoService.saveUserEncodeInfo(idCard, UserTypeEnum.STUDENT.type, id, UserEncodeInfoType.ID_CARD.type)
            Boolean gender = params.boolean('gender')
            if (gender != null) {
                student.gender = gender
            }
            String code = params.code
            if (code) {
                if (code.length() > 25) {
                    throw new HiiAdminException("学号长度不能超过25！")
                }
                Student s = studentService.fetchStudentByCampusIdIdAndCodeAndStudentIdNotEq(campusId, code, id)
                if (s) {
                    throw new HiiAdminException("学号已存在")
                }
                student.code = code
            }
            String cardNum = params.cardNum
            if (cardNum) {
                cardUserService.createOrUpdateCardUser4student(schoolId, id, cardNum)
            } else {
                cardUserService.deleteUserCardNum(student.id, UserTypeEnum.STUDENT.type)
            }
            studentService.saveStudent(student)
            int changeBit = PersonDataChangeEnum.userBaseChangeHandlerBit()
            personDataChangeService.studentDataChange(campusId, student, userId, userType, changeBit, OperateType.UPDATE.type)

        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long unitId = params.long('unitId')
        Long campusId = request.getAttribute("campusId") as Long

        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte

        if (unitId) {
            Unit unit = unitService.fetchUnitById(unitId)
            if (unit.type == UnitType.ADMINISTRATIVE_CLASS.type) {
                studentDataStructureService.deleteStudent(campusId, id, userId, userType)
            } else {
                studentService.removeStudent4Unit(unitId, id)
            }
        } else {
            studentDataStructureService.deleteStudent(campusId, id, userId, userType)
        }

        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Long unitId = params.long('unitId')
        if (unitId) {
            Unit unit = unitService.fetchUnitById(unitId)
            studentService.changeStudent2NewUnit(id, unit)
        }
        resultVO.result.put('id', id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

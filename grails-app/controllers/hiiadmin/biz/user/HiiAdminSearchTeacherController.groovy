package hiiadmin.biz.user

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Lists
import hiiadmin.biz.DepartmentService
import hiiadmin.mapstruct.DepartmentMapper
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.school.CampusService
import hiiadmin.school.TeacherService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import hiiadmin.vocationalSchool.FacultyService
import io.swagger.annotations.Api
import org.springframework.beans.factory.annotation.Autowired
import timetabling.Department
import timetabling.org.Campus
import timetabling.vocationalSchool.Faculty

import static hiiadmin.ConstantEnum.UserEncodeInfoType
import static hiiadmin.ConstantEnum.UserTypeEnum


@Api(value = "查询教师")
class HiiAdminSearchTeacherController {

    TeacherService teacherService

    CampusService campusService

    DepartmentService departmentService

    FacultyService facultyService

    @Autowired
    DepartmentMapper departmentMapper

    UserEncodeInfoService userEncodeInfoService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long campusId = request.getAttribute("campusId") as Long

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        String searchValue = params.searchValue

        Campus campus = campusService.fetchCampusById(campusId)

        List<TeacherVO> vos = teacherService.fetchAllTeacherByNameOrMobileOrJobNum(campusId, searchValue, p, s)
        List<Long> teacherIdList = vos.collect { PhenixCoder.decodeId(it.id)}
        Map<Long, String> teacherIdMobileMap = userEncodeInfoService.fetchDecodeInfoMapByTypeAndUserTypeAndUserIdList(UserEncodeInfoType.MOBILE.type, UserTypeEnum.TEACHER.type, teacherIdList)
        vos.each {
            List<DepartmentVO> departmentVOList = Lists.newArrayList()
            if (campus.type == 1) {
                List<Department> departmentList = departmentService.findDepartmentListByTeacherIdAndCampusId(PhenixCoder.decodeId(it.id), campusId)
                departmentList?.each {
                    Department department ->
                        DepartmentVO departmentVO = departmentMapper.convert2DepartmentVO(department)
                        if (departmentVO) {
                            departmentVOList.add(departmentVO)
                        }
                }
            }

            if (campus.type == 2) {
                List<Faculty> faculties = facultyService.fetchAllFacultyByUser(campusId,PhenixCoder.decodeId(it.id))
                departmentVOList = faculties?.collect {
                    Faculty faculty ->
                        if (faculty.name != '未分组') {
                            DepartmentVO departmentVO = new DepartmentVO()
                            departmentVO.id = PhenixCoder.encodeId(faculty.id)
                            departmentVO.name = faculty.name
                            departmentVO
                        }
                }
            }

            it.departments = departmentVOList
            it.departments?.removeAll([null])

            it.mobile = teacherIdMobileMap.get(it.id)
        }

        Integer count = teacherService.countAllTeacherByNameOrMobileOrJobNum(campusId, searchValue)

        resultVO.result.put("total", count)
        resultVO.result.put("list", vos)

        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

package hiiadmin.biz.user

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.StudentSearchService
import hiiadmin.module.bugu.StudentVO
import hiiadmin.school.CampusService
import hiiadmin.school.StudentService
import hiiadmin.school.user.structure.StudentDataBuildService
import hiiadmin.utils.PhenixCoder
import timetabling.org.Campus
import timetabling.user.Student


class HiiAdminLowCodeStudentController {
    
    CampusService campusService

    StudentSearchService studentSearchService
    StudentDataBuildService studentDataBuildService
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        int p = params.int('pageNum', 1)
        int s = params.int('pageSize', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long sectionId = PhenixCoder.decodeId(params.sectionId)
        Long gradeId = PhenixCoder.decodeId(params.gradeId)
        Long unitId = PhenixCoder.decodeId(params.unitId)
        String searchValue = params.searchValue
        boolean graduate = params.boolean("graduate", false)
        List<Student> studentList = studentSearchService.searchStudentLimit(schoolId, campusId, sectionId, gradeId, unitId, null, searchValue, p, s, graduate)
        Integer count = studentSearchService.countSearchStudent(schoolId, campusId, sectionId, gradeId, unitId, null, null, null, searchValue, graduate)
        Campus campus = campusService.fetchCampusById(campusId)
        
        List<StudentVO> studentVOList = studentDataBuildService.buildStudentVOListAddParent(studentList, campus, null, graduate)
        resultVO.result.put("list", studentVOList)
        resultVO.result.put("total", count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

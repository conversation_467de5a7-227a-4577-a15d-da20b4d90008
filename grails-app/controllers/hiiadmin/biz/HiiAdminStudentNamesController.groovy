package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.StudentVO
import hiiadmin.school.CampusService
import hiiadmin.school.StudentService
import hiiadmin.school.user.structure.StudentDataBuildService
import io.swagger.annotations.Api
import org.apache.commons.lang3.StringUtils
import timetabling.org.Campus
import timetabling.user.Student

@Api(value = "通过学生姓名返回")
class HiiAdminStudentNamesController {

    StudentService studentService

    StudentDataBuildService studentDataBuildService

    CampusService campusService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String names = params.names

        
        
        Preconditions.checkNotNull(names, "必要参数为空")

        Long campusId = request.getAttribute("campusId") as Long
        List<String> nameList = names.split(",")
        List<StudentVO> vos = []
        if (StringUtils.isNotBlank(names)) {

            List<Student> studentList = studentService.fetchAllStudentByNamesAndCampusId(nameList, campusId)

            Campus campus = campusService.fetchCampusById(campusId)

            if (studentList) {
                vos = studentDataBuildService.buildStudentVOListAddParent(studentList, campus)
            }
        }

        resultVO.result.put("list", vos)
        resultVO.result.put("total", vos?.size())

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

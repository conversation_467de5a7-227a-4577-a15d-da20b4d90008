package hiiadmin.biz.moral

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.messageControl.MessageControlCenterService
import hiiadmin.module.ResultVM
import hiiadmin.module.moral.MoralRecordVO
import hiiadmin.moral.MoralRegisterRecordService
import hiiadmin.moral.MoralService
import timetabling.moral.MoralResult

/**
 * <AUTHOR>
 * @Date 2023-02-15 11:17
 */
@Slf4j
@Transactional
class HiiAdminMoralRegisterRecordController implements BaseExceptionHandler {

    MoralRegisterRecordService moralRegisterRecordService

    ViewDOService viewDOService

    MoralService moralService

    MessageControlCenterService messageControlCenterService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Long type = params.long("type")
        Long itemId = params.long("itemId")
        Long moralMenuId = params.long("moralMenuId")
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Byte status = params.byte("status")
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = moralRegisterRecordService.fetchAllMoralResultLimit(campusId, type, itemId, moralMenuId, startTime, endTime, status, searchValue, p, s)
        List<MoralRecordVO> vos = viewDOService.buildMoralRegisterRecordVOS(schoolId, map?.list)
        int openStatus = messageControlCenterService.fetchMessageControlCenterOpenStatusByCampusIdAndType(campusId, 1) ?: 0

        resultVO.result.put("openStatus", openStatus)
        resultVO.result.put("list", vos)
        resultVO.result.put("total", map?.total ?: 0)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long resultId = params.long("id")
        MoralResult moralResult = moralService.fetchMoralResultById(resultId)
        MoralRecordVO vo = viewDOService.buildMoralRegisterRecordVO(moralResult)

        resultVO.result.put("vo", vo)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }

    def delete() {
        Long cancellerId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long resultId = params.long("id")
        boolean dateLimit = params.boolean("dateLimit", true)
        ResultVM resultVM = moralRegisterRecordService.deleteMoralResult2Record(campusId, resultId, dateLimit, cancellerId)

        render text: resultVM.toString(), contentType: 'application/json', encoding: "UTF-8"
    }
}

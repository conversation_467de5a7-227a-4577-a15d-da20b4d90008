package hiiadmin.biz.moral

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.moral.MoralMenuVO
import hiiadmin.module.moral.MoralVO
import hiiadmin.moral.MoralService
import io.swagger.annotations.Api
import timetabling.moral.Moral
import timetabling.moral.MoralMenu

@Api(value = "德育管理", tags = "行为德育", consumes = "admin")
class HiiAdminMoralController implements BaseExceptionHandler {

    MoralService moralService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long

        Byte type = params.byte('type')
        Long itemId = params.long("itemId")
        List<MoralMenu> menuList = moralService.fetchAllMoralMenuByCampusId(campusId, itemId, type)
        Multimap<Long, Moral> moralMap = moralService.fetchAllMoralListMapByCampusId(campusId)
        List<MoralMenuVO> vos = menuList.collect { menu ->
            MoralMenuVO vo = new MoralMenuVO().buildVO(menu)
            vo.morals = moralMap.get(menu.id).collect { moral ->
                new MoralVO().buildVO(moral)
            }
            vo
        }
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([list: vos])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

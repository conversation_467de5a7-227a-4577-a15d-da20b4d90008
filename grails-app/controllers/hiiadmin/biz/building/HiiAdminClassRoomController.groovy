package hiiadmin.biz.building

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.bugu.DoorPlateVO
import hiiadmin.school.building.BuildingService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.building.DoorPlateVOService
import hiiadmin.school.building.PewService
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import timetabling.building.Building
import timetabling.building.BuildingLayer
import timetabling.building.DoorPlate

@Deprecated
@Api(value = "教用空间管理", tags = "管理", consumes = "admin")
class HiiAdminClassRoomController implements BaseExceptionHandler {

    DoorPlateService doorPlateService

    DoorPlateVOService doorPlateVOService

    ViewDOService viewDOService

    PewService pewService

    BuildingService buildingService

    final static TEACHING_TYPE = ConstantEnum.DoorPlateType.TEACHING.type

    def index() {
        //分页从1开始
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long("campusId")
        if (!campusId) {
            campusId = request.getAttribute("campusId") as Long
        }
        Long buildingId = params.long('buildingId')
        Long layerId = params.long('layerId')

        String searchValue = params.searchValue
        ResultVO resultVO = doorPlateVOService.getAllDoorPlateByResultSchoolIdAndType(schoolId, campusId, buildingId, layerId, TEACHING_TYPE, searchValue, p, s)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(id)
        DoorPlateVO doorPlateVO = viewDOService.transformAdminDoorPlateVO(doorPlate, true)
        resultVO.result = ObjectUtils.resultVOObject2Map(doorPlateVO)
        def unitCounts = pewService.getStudentCountWithUnitByDoorPlateId(id)
        if (unitCounts) {
            resultVO.result.put('unitCounts', unitCounts)
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        long schoolId = request.getAttribute("schoolId") as Long
        String name = params.name
        String codeStr = params.code
        Integer code = null
        if (codeStr.contains("-")) {
            code = codeStr.split("-")[1] as Integer
        } else {
            code = codeStr as Integer
        }
        if (!code) {
            throw new HiiAdminException("请输入正确的房间编号")
        } else if (code > 999) {
            throw new HiiAdminException("房间号不支持添加超过999")
        }
        Long layerId = params.long("layerId")
        Integer column = params.int("columnTotal")
        Integer row = params.int("rowTotal")

        Building building = buildingService.fetchBuildingByLayerId(layerId)
        BuildingLayer layer = buildingService.fetchLayerById(layerId, false)
        def x = doorPlateService.code2padCode(layer.code, code)
        Integer indexCode = x.indexCode
        String padCode = x.padCode
        if (doorPlateService.isExistDoorPlate4LayerByCode(layerId, indexCode)) {
            throw new HiiAdminException("楼层内房间号已存在，请修改后重试")
        }
        if (doorPlateService.isExistDoorPlate4LayerByName(layerId, name)) {
            throw new HiiAdminException("楼层内房间名已存在，请修改后重试")
        }

        DoorPlate doorPlate = new DoorPlate(
                schoolId: schoolId,
                campusId: building.campusId,
                name: name,
                buildingId: layer.buildingId,
                layerId: layerId,
                code: indexCode,
                padCode: padCode,
                status: 1 as byte,
                type: TEACHING_TYPE,
                columnTotal: column,
                rowTotal: row,
                memo: building.name + name
        )
        doorPlateService.saveDoorPlate(doorPlate)

        DoorPlateVO doorPlateVO = viewDOService.transformAdminDoorPlateVO(doorPlate)
        resultVO.result = ObjectUtils.resultVOObject2Map(doorPlateVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long doorPlateId = params.long("id")
        String name = params.name
        String codeStr = params.code

        Integer column = params.int("columnTotal")
        Integer row = params.int("rowTotal")
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(doorPlateId, false)
        if (name) {
            if (doorPlateService.isExistDoorPlate4LayerByName(doorPlate.layerId, name, doorPlateId)) {
                throw new HiiAdminException("楼层内房间名已存在，请修改后重试")
            }
            doorPlate.name = name
        }
        if (codeStr) {
            Integer code = null
            if (codeStr.contains("-")) {
                code = codeStr.split("-")[1] as Integer
            } else {
                code = codeStr as Integer
            }
            if (code > 999) {
                throw new HiiAdminException("房间号不支持添加超过999")
            }
            BuildingLayer layer = buildingService.fetchLayerById(doorPlate.layerId, false)
            def x = doorPlateService.code2padCode(layer.code, code)
            Integer indexCode = x.indexCode
            String padCode = x.padCode
            if (doorPlateService.isExistDoorPlate4LayerByCode(doorPlate.layerId, indexCode, doorPlateId)) {
                throw new HiiAdminException("楼层内房间号已存在，请修改后重试")
            }
            doorPlate.code = indexCode
            doorPlate.padCode = padCode
        }
        if (column) {
            doorPlate.columnTotal = column
        }
        if (row) {
            doorPlate.rowTotal = row
        }
        doorPlateService.saveDoorPlate(doorPlate)
        DoorPlateVO doorPlateVO = viewDOService.transformAdminDoorPlateVO(doorPlate)
        resultVO.result = ObjectUtils.resultVOObject2Map(doorPlateVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long

        Long id = params.long('id')
        if (schoolId) {
            doorPlateService.deleteDoorPlateByDoorPlateId(campusId, id)
        }
        resultVO.result.put("id", id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long doorPlateId = params.long("id")
        Long pewTypeAppId = params.long("pewTypeApplicationId")
        pewService.deleteAllPews4doorPlateIdAndPewTypeAppId(doorPlateId, pewTypeAppId)
        resultVO.result.put("id", doorPlateId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.biz.building

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.base.Preconditions
import hiiadmin.BaseExceptionHandler
import hiiadmin.ViewDOService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.module.building.BedVO
import hiiadmin.school.building.BedService
import hiiadmin.school.building.BuildingService
import hiiadmin.school.building.BuildingTransformService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import timetabling.building.Bed
import timetabling.building.Building
import timetabling.building.BuildingLayer
import timetabling.building.DoorPlate

@Deprecated
@Api(value = "宿舍床位管理", tags = "管理", consumes = "admin")
class HiiAdminBedController implements BaseExceptionHandler {

    BedService bedService

    ViewDOService viewDOService

    DoorPlateService doorPlateService

    BuildingTransformService buildingTransformService

    BuildingService buildingService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long doorPlateId = params.long('doorPlateId')
        Long layerId = params.long("layerId")
        List<BedVO> bedVOS = buildingTransformService.transformBedVO(campusId, layerId, doorPlateId)
        resultVO.result.put('list', bedVOS)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long doorPlateId = params.long('doorPlateId')
        Preconditions.checkNotNull(doorPlateId, "房间参数不能为空")
//        Integer code = params.int("code")
        String codes = params.codes
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(doorPlateId)
        BuildingLayer layer = buildingService.fetchLayerById(doorPlate.layerId)
        Building building = buildingService.fetchBuildingById(doorPlate.buildingId)
        bedService.createBedBatch(building, layer, doorPlate, codes)
        doorPlateService.updateDoorPlateBedTotal(doorPlateId)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long bedId = params.long("id")
        Long studentId = params.long("studentId")
        if (studentId) {
            buildingService.invalidateStudentDormitory2BedNameByStudentId(studentId)
            String name = buildingService.getStudentDormitory2BedNameByStudentId(studentId)
            if (name) {
                throw new HiiAdminException("该学生已经安排其他床位 " + name)
            }
        }
        Bed bed = bedService.addStudentId2Bed(campusId, studentId, bedId)
        DoorPlate doorPlate = doorPlateService.fetchDoorPlateById(bed?.doorPlateId)
        BedVO vo = viewDOService.buildBedVO(bed, doorPlate?.campusId)
        resultVO.result = ObjectUtils.resultVOObject2Map(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        Long campusId = request.getAttribute("campusId") as Long

        Long newBedId = params.long("id")
        Long oldBedId = params.long("bedId")
        bedService.exchangeBedStudent(campusId, oldBedId, newBedId)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long

        Long bedId = params.long("id")
        Bed bed = bedService.deleteBed(campusId, bedId)
        doorPlateService.updateDoorPlateBedTotal(bed.doorPlateId)
        resultVO.result.put("id", bedId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

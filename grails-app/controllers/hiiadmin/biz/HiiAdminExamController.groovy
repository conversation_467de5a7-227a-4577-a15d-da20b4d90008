package hiiadmin.biz

import grails.gorm.transactions.Transactional
import hiiadmin.BaseExceptionHandler
import hiiadmin.school.SchoolService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.Exam
import timetabling.org.Semester

@Api(value = "考试管理", tags = "教务管理", consumes = "admin")
@Transactional
class HiiAdminExamController implements BaseExceptionHandler {

    SchoolService schoolService

    def index() {
        int p = params.int('p', 1)//分页从1开始
        int s = params.int('s', 30)
        Long schoolId = request.getAttribute("schoolId") as Long
        def c = Exam.createCriteria()
        def examList = c.list(max: s, offset: (p - 1) * s) {
            eq('status', 1 as Byte)
            eq("schoolId", schoolId)
            order('startTime', "desc")
        }
        [examList: examList, total: examList?.totalCount]
    }

    def save() {
        Exam exam = new Exam()
        Long schoolId = request.getAttribute("schoolId") as Long
        exam.schoolId = schoolId
        Long a = params.long("startTime")
        exam.startTime = new Date(a)
        Long b = params.long("endTime")
        exam.endTime = new Date(b)
        String semesterIdStr = params.semesterId
        Long semesterId = PhenixCoder.decodeId(semesterIdStr)
        exam.semesterId = semesterId
        if (null != semesterId) {
            Semester semester = schoolService.fetchSemesterBySemesterId(semesterId)
            exam.academicYear = semester.academicYear
            exam.semesterType = semester.semesterType
        }
        exam.name = params.name
        exam.status = 1 as Byte
        exam.classUnitIds = params.classUnitIds
        exam.subjectIds = params.subjectIds
        exam.save(failOnError: true)
        [exam: exam]
    }

    def update() {
        Long id = params.long('id')
        Exam exam = Exam.findById(id)
        String semesterIdStr = params.semesterId
        Long semesterId = PhenixCoder.decodeId(semesterIdStr)
        if (null != semesterId) {
            Semester semester = schoolService.fetchSemesterBySemesterId(semesterId)
            exam.academicYear = semester.academicYear
            exam.semesterType = semester.semesterType
        }
        String name = params.name
        if (null != name) {
            exam.name = name
        }
        Long a = params.long("startTime")
        if (null != a) {
            exam.startTime = new Date(a)
        }
        Long b = params.long("endTime")
        if (null != b) {
            exam.endTime = new Date(b)
        }
        [exam: exam]

    }

    def delete() {
        Long id = params.long("id")
        Exam exam = Exam.findById(id)
        exam.status = 0 as Byte
        exam.save(failOnError: true)
        [examId: exam.id]
    }
}

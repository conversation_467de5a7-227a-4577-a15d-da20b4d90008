package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.StudentSearchService
import hiiadmin.ViewDOService
import hiiadmin.module.bugu.StudentRoomPositionVO
import hiiadmin.module.bugu.StudentSearchVO
import hiiadmin.school.building.BuildingService
import hiiadmin.school.building.DoorPlateService
import hiiadmin.school.building.PewService
import io.swagger.annotations.Api
import timetabling.building.Building
import timetabling.building.BuildingLayer
import timetabling.building.DoorPlate
import timetabling.building.Pew
import timetabling.user.Student

import static hiiadmin.ConstantEnum.DoorPlateType

@Deprecated
@Api(value = "学生班级位置", tags = "系统", consumes = "admin")
class HiiAdminStudentRoomPositionController implements BaseExceptionHandler {

    ViewDOService viewDOService

    DoorPlateService doorPlateService

    PewService pewService

    BuildingService buildingService

    StudentSearchService studentSearchService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long unitId = params.long("unitId")
        Long gradeId = params.long("gradeId")
        Long sectionId = params.long("sectionId")

        String searchValue = params.searchValue

        List<Building> buildingList = buildingService.fetchAllBuildingBySchoolId(campusId)
        List<Long> buildingIdList = []
        Map<Long, Building> buildingIdMap = [:]
        buildingList.each {
            buildingIdMap.put(it.id, it)
            buildingIdList << it.id
        }
        List<BuildingLayer> buildingLayerList = buildingService.fetchAllBuildingLayerByDoorPlateIdList(buildingIdList)
        Map<Long, BuildingLayer> layerIdMap = [:]
        buildingLayerList.each {
            BuildingLayer buildingLayer ->
                layerIdMap.put(buildingLayer.id, buildingLayer)
        }
        List<DoorPlate> doorPlateList = doorPlateService.fetchAllDoorPlateByCampusIdAndType(campusId, DoorPlateType.TEACHING.type)
        Map<Long, DoorPlate> doorIdMap = [:]
        doorPlateList.each {
            DoorPlate door ->
                doorIdMap.put(door.id, door)
        }

//        List<Student> studentList = studentSearchService.searchStudent4Pew(schoolId, campusId, sectionId, gradeId, unitId, null, searchValue)
        List<StudentSearchVO> voList = studentSearchService.searchStudent4Pew(schoolId, campusId, sectionId, gradeId, unitId, null, null, null, null, searchValue)

        List<Long> studentIdList = studentList*.id
        List<Pew> pewList = []
        if (studentIdList && studentIdList.size() > 0) {
            pewList = pewService.fetchAllPewViaStudentIdList(studentIdList)
        }
        List<StudentRoomPositionVO> studentRoomPositionVOList = []
        studentList.each {
            Student student ->
                StudentRoomPositionVO studentRoomPositionVO = new StudentRoomPositionVO().buildVO(viewDOService.buildStudentVO(student, false))
                Pew pew = pewList?.find { it.studentId == student.id }
                if (pew) {
                    DoorPlate plate = doorIdMap.get(pew.doorPlateId)
                    Building building = buildingIdMap.get(plate.buildingId)
                    BuildingLayer buildingLayer = layerIdMap.get(plate.layerId)
                    studentRoomPositionVO.buildingName = building?.name
                    studentRoomPositionVO.layerName = buildingLayer?.name
                    studentRoomPositionVO.classroomName = plate?.code ? plate?.code + "室" : null
                }
                studentRoomPositionVO.pewId = pew?.id
                studentRoomPositionVO.rowIdx = pew?.rowIdx
                studentRoomPositionVO.columnIdx = pew?.columnIdx
                studentRoomPositionVOList.add(studentRoomPositionVO)
        }
        resultVO.result.put("total", studentRoomPositionVOList.size())
        resultVO.result.put('list', studentRoomPositionVOList)
        withFormat {
            json { render text: JSON.toJSONString(resultVO, SerializerFeature.DisableCircularReferenceDetect), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

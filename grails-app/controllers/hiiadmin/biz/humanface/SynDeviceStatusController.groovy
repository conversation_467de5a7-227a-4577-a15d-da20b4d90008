package hiiadmin.biz.humanface

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import grails.async.Promise
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.humanface.SynDeviceToFirmService
import timetabling.org.Campus

@Slf4j
class SynDeviceStatusController implements BaseExceptionHandler {

    CacheService cacheService


    SynDeviceToFirmService synDeviceToFirmService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Integer firm = params.int("firm", 410)
        
        Long deviceId = params.long("deviceId")
        
        String key = "${firm}_${campusId}_${deviceId}".toString()
        ServiceResult result = cacheService.synAliYunDevice.get(key)
        if (result && result.success) {
            resultVO.status = 0
            resultVO.message = "正在同步中，更新成功后会自动更新同步结果，请勿频繁更新!"
        } else {

            cacheService.synAliYunDevice.put(key, ServiceResult.success("正在同步中。。。。"))
            Promise task = Promises.task {
                synDeviceToFirmService.synDeviceToFirm(campusId, firm, deviceId)
            }
            task.onComplete {
                log.info('application ready ...... end to config resources......')
            }
            task.onError { Throwable throwable ->
                log.warn("同步设备状态异常${throwable.message}".toString(), throwable)
            }
            resultVO.message = "正在同步中。。。。。"
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }


    }

    //同步所有校区的设备在线状态
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        List<Campus> campusList = Campus.findAllByStatus(1 as byte)
        Promise task = Promises.task {
            campusList.each {
                synDeviceToFirmService.syncDeviceLineStatus(it.id)
            }
        }
        task.onComplete {
            log.info('application ready ...... end to config resources......')
        }
        task.onError { Throwable throwable ->
            log.warn("同步设备状态异常${throwable.message}".toString(), throwable)
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

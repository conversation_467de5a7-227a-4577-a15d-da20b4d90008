package hiiadmin.biz.humanface

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.module.device.YrDeviceChannelVO
import hiiadmin.module.device.YrDeviceVO
import hiiadmin.school.device.DeviceService
import timetabling.device.Device


class TrackAlarmDeviceController implements BaseExceptionHandler {

    DeviceService deviceService

    def index() {

        Long campusId = request.getAttribute("campusId") as Long

        List<Device> deviceList = deviceService.fetchAllTrackAlarmDeviceByCampusId(campusId)
        List<YrDeviceVO> vos = deviceList.collect { device ->
            YrDeviceVO deviceVO = new YrDeviceVO().buildVO(device)
            if (device.type == ConstantEnum.DeviceType.EDGE_BOX.type) {
                List<Device> channelList = deviceService.fetchAllChannelByDeviceId(device.id)
                deviceVO.channels = channelList.collect {
                    new YrDeviceChannelVO().buildVO(it)
                }
            }
            deviceVO
        }
        ResultVO resultVO = ResultVO.success([list: vos])


        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")

    }

}

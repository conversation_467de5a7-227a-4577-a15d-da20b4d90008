package hiiadmin.biz.humanface

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Lists
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.dataCenter.TrackAlarmHandleService
import hiiadmin.dataCenter.TrackAlarmService
import hiiadmin.mapConfig.service.CampusMapConfigService
import hiiadmin.module.dataCenter.AlarmHandleVO
import hiiadmin.module.dataCenter.TrackAlarmVO
import hiiadmin.school.building.BuildingService
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.DeviceSpaceService
import hiiadmin.school.repository.RepositoryStudentService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import io.swagger.annotations.Api
import timetabling.device.Device
import timetabling.iot.DeviceSpace
import timetabling.mapConfig.CampusMapConfig
import timetabling.track.TrackAlarm
import timetabling.track.TrackAlarmHandle
import timetabling.user.Student

@Api(value = "告警事件列表", tags = "校园安全", consumes = "admin")
class TrackAlarmEventController implements BaseExceptionHandler {

    TrackAlarmService trackAlarmService

    TrackAlarmHandleService trackAlarmHandleService

    CampusMapConfigService campusMapConfigService

    DeviceService deviceService

    DeviceSpaceService deviceSpaceService

    BuildingService buildingService

    RepositoryStudentService repositoryStudentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String eventName = params.eventName
        Integer eventType = params.int("eventType")
        Integer alarmType = params.int("alarmType")
        Long deviceId = params.long("deviceId")
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Integer operationStatus = params.int("operationStatus")
        String buildingIdStr = params.buildingId
        String layerIdStr = params.layerId
        String doorplateIdStr = params.doorplateId

        if (!startTime || !endTime) {
            resultVO.status = 0
            resultVO.message = "请选择日期范围"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        if (startTime && endTime && startTime > endTime) {
            resultVO.status = 0
            resultVO.message = "开始时间不能大于结束时间"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        if (startTime && ToStringUnits.belongsToHistoricalData(startTime, -30)) {
            resultVO.status = 0
            resultVO.message = "暂提供30天内的记录"
            render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
            return
        }
        Integer firm = params.int("firm")
        int p = params.int("p", 1)
        int s = params.int("s", 10)
        String areaPathIds = buildingService.transformAreaIdPathV2(buildingIdStr, layerIdStr, doorplateIdStr)

        def map = trackAlarmService.fetchTrackAlarmPage(campusId, eventName, deviceId, alarmType, operationStatus, startTime, endTime, firm, areaPathIds, s, p)
        List<TrackAlarm> trackAlarmList = map.list as List<TrackAlarm>
        Integer total = map.total
        List<TrackAlarmVO> voList = []
        Map<Long, AlarmHandleVO> trackAlarmVOMap = trackAlarmHandleService.transformTrackAlarmHandleVO(trackAlarmList*.id)
        if (trackAlarmList != null && trackAlarmList.size() > 0) {


            List<Long> deviceIdList = []
            trackAlarmList.each { TrackAlarm trackAlarm ->
                TrackAlarmVO vo = new TrackAlarmVO().buildVO(trackAlarm)
                AlarmHandleVO alarmHandleVO = trackAlarmVOMap.get(trackAlarm.id)
                if (alarmHandleVO) {
                    vo.operationStatus = alarmHandleVO.operationStatus
                    vo.handle = alarmHandleVO
                } else {
                    vo.operationStatus = ConstantEnum.TrackAlarmOperationStatus.UNTREATED.status
                }
                if (trackAlarm.deviceId) {
                    deviceIdList << trackAlarm.deviceId
                }
                voList << vo
            }
            if (deviceIdList) {
                List<DeviceSpace> deviceSpaceList = deviceSpaceService.fetchAllDeviceSpace4device(campusId, deviceIdList)
                Map<Long, DeviceSpace> deviceSpaceMap = deviceSpaceList.collectEntries { [(it.deviceId): it] }
                voList.each {
                    if (it.deviceId) {
                        DeviceSpace space = deviceSpaceMap.get(it.deviceId)
                        if (space) {
                            it.buildingId = PhenixCoder.encodeId(space.buildingId)
                            it.layerId = PhenixCoder.encodeId(space.layerId)
                            it.doorplateId = PhenixCoder.encodeId(space.doorplateId)
                            it.rowIndex = space.rowIndex
                            it.colIndex = space.colIndex
                        }
                    }
                }
            }
        }
        resultVO.result.put("list", voList)
        resultVO.result.put("total", total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long

        TrackAlarm trackAlarm = trackAlarmService.fetchTrackAlarmById(id)
        TrackAlarmVO vo = new TrackAlarmVO().buildVO(trackAlarm)

        CampusMapConfig config = campusMapConfigService.getCampusMapConfigByCampusId(campusId, schoolId)
        if (trackAlarm.deviceId) {
            Device device = deviceService.fetchDeviceById(trackAlarm.deviceId)
            vo.coordinate = device?.coordinate
            DeviceSpace space = deviceSpaceService.fetchDeviceSpaceByDeviceId(campusId, trackAlarm.deviceId)
            if (space && space.areaIdPath) {
                Map<String, String> areaPathIdNameMap = buildingService.getAreaPathIdNameMap(campusId, false)
                List<String> names = Lists.newArrayList()
                if (areaPathIdNameMap.containsKey(space.areaIdPath)) {
                    names.add(areaPathIdNameMap.get(space.areaIdPath))
                }
                if (vo.deviceName) {
                    names.add(vo.deviceName)
                }
                vo.objName = names.join("/")
            }
        } else if (trackAlarm.areaPathIds) {
            Map<String, String> areaPathIdNameMap = buildingService.getAreaPathIdNameMap(campusId, false)
            vo.objName = areaPathIdNameMap.get(trackAlarm.areaPathIds)
            vo.deviceName = vo.objName
        }
        if (trackAlarm.relatedPersonIds) {
            List<Long> personIds = trackAlarm.relatedPersonIds.split(",").collect { it as Long }
            List<Student> studentList = repositoryStudentService.fetchAllStudentByStudentIdInList(personIds)
            vo.relatedPersons = studentList.collect { it.name }.join(",")
        }
        vo.campusMap = config?.mapPic

        TrackAlarmHandle alarmHandle = trackAlarmHandleService.fetchTrackAlarmHandleByAlarmId(id)
        AlarmHandleVO alarmHandleVO = new AlarmHandleVO().buildVO(alarmHandle)
        if (alarmHandle) {
            vo.operationStatus = alarmHandle.operationStatus
            vo.handle = alarmHandleVO
        } else {
            vo.operationStatus = ConstantEnum.TrackAlarmOperationStatus.UNTREATED.status
        }
        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        Long trackAlarmId = params.long("trackAlarmId")
        Integer operationStatus = params.int("operationStatus", ConstantEnum.TrackAlarmOperationStatus.IGNORE.status)
        String content = params.content

        TrackAlarmHandle alarmHandle = trackAlarmHandleService.cratedAndSaveTrackAlarmHandle(campusId, trackAlarmId, teacherId, operationStatus, content)
        AlarmHandleVO alarmHandleVO = new AlarmHandleVO().buildVO(alarmHandle)
        TrackAlarm trackAlarm = trackAlarmService.fetchTrackAlarmById(trackAlarmId)
        TrackAlarmVO vo = new TrackAlarmVO().buildVO(trackAlarm)
        vo.handle = alarmHandleVO
        vo.operationStatus = operationStatus

        resultVO.result = vo
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }

    }
}

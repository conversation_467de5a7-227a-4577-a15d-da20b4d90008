package hiiadmin.biz.humanface

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.attendance.AttendanceService
import hiiadmin.module.humanface.AttendanceBigDataVO
import hiiadmin.school.device.DeviceService

class AttendanceBigScreenController implements BaseExceptionHandler {

    AttendanceService attendanceService

    DeviceService deviceService

    def index() {

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String item = params.item
        Long campusId = request.getAttribute("campusId") as Long
        switch (item) {
            case "device":
                //获取设备直播地址
                Long deviceId = params.long("deviceId")
                String imgUrl = deviceService.getStreamingUrl(deviceId, campusId)
                resultVO.result.put("imgUrl", imgUrl)
                break
            case "attendanceImg":
                List<String> imgList = attendanceService.getAttendanceUserAvatar(campusId, ConstantEnum.UserTypeEnum.STUDENT.type, System.currentTimeMillis())
                resultVO.result.put("imgList", imgList)
                break
            case "attendanceInfo":
                List<AttendanceBigDataVO> bigDataList = attendanceService.getAttendanceUserInfo(campusId, ConstantEnum.UserTypeEnum.STUDENT.type, System.currentTimeMillis())
                resultVO.result.put("bigDataList", bigDataList)
                break
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

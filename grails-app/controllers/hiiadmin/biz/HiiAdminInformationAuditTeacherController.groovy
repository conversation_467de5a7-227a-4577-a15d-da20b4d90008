package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.school.CampusService
import timetabling.org.Campus


class HiiAdminInformationAuditTeacherController implements BaseExceptionHandler {


    CampusService campusService

    InformationAuditService informationAuditService

    //获取有信息审核菜单的老师
    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Campus campus = campusService.fetchCampusById(campusId)
        resultVO.result.put("audit", campus.informationAudit)
        List<TeacherVO> teacherVOList = informationAuditService.getAuditTeacher(schoolId, campusId)
        resultVO.result.put("list", teacherVOList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
        return
    }
}

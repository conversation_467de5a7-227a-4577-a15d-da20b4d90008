package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.TemperatureRecordVO
import hiiadmin.school.TemperatureRecordService
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import timetabling.TemperatureRecord

@Api(value = "测温记录", tags = "校园安全", consumes = "admin")
class HiiAdminTemperatureRecordController implements BaseExceptionHandler {

    TemperatureRecordService temperatureRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String studentIdStr = params.studentId
        Long studentId = PhenixCoder.decodeId(studentIdStr)
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<TemperatureRecord> list = temperatureRecordService.fetchAllTemperatureRecordByStudentId(studentId, p, s)
        Integer total = temperatureRecordService.countTemperatureRecordByStudentId(studentId)
        List<TemperatureRecordVO> vos = list.collect {
            new TemperatureRecordVO().buildVO(it)
        }
        resultVO.result.put('list', vos)
        resultVO.result.put('total', total)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

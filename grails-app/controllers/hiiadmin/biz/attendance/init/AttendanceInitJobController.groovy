package hiiadmin.biz.attendance.init

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.async.Promises
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.attendance.v2.Attendance2DetailService

/**
 * <AUTHOR>
 * @Date 2023-09-22 10:45
 */
@Slf4j
class AttendanceInitJobController implements BaseExceptionHandler {

    Attendance2DetailService attendance2DetailService

    CacheService cacheService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long allocationId = params.long("allocationId")
        Long currentTime = System.currentTimeMillis()
        Long lastInitTime = cacheService.attendanceInitJob.get(allocationId)
        if (lastInitTime) {
            resultVO.status = 0
            resultVO.message = "时间间隔为30分钟，请勿频繁点击"
        } else {
            Promises.task {
                attendance2DetailService.cleanAttendanceDetail4Allocation(allocationId)
                attendance2DetailService.createAttendanceDetail4Allocation(allocationId)
                cacheService.attendanceInitJob.put(allocationId, currentTime)
                cacheService.attendanceInitJobTime.put(allocationId, currentTime)
            }.onError {Throwable throwable -> 
                log.error("AttendanceInitJobController error: ", throwable)
            }.onComplete {
                log.info("AttendanceInitJobController init success, allocationId@${allocationId}".toString())
            }
            resultVO.message = "正在初始化，请稍后刷新页面查看"
        }
        resultVO.result.put("allocationId", allocationId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

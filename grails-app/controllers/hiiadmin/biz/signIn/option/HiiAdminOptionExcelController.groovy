package hiiadmin.biz.signIn.option

import hiiadmin.BaseExceptionHandler
import hiiadmin.file.XssfExcelService
import hiiadmin.option.OptionService
import hiiadmin.utils.TimeUtils
import org.apache.poi.ss.usermodel.Workbook
import timetabling.option.Option

class HiiAdminOptionExcelController implements BaseExceptionHandler {

    OptionService optionService

    XssfExcelService xssfExcelService
    /**
     * 导出excel
     * */
    def index() {
        Long electiveId = params.long('optionId')
        Option elective = optionService.fetchOptionByOptionId(electiveId)
        response.setHeader("Cache-Control", "must-revalidate, post-check=0, pre-check=0")
        response.setHeader("Pragma", "public")
        response.setDateHeader("Expires", (System.currentTimeMillis() + 1000))
        Workbook workbook = xssfExcelService.getOptionExcel(elective)
        String dateFormat = TimeUtils.dateToStr(new Date(), TimeUtils.DAY_FORMAT_MMdd)
        String fileName = "${elective.name}统计结果_${dateFormat}.xlsx"
        File file = xssfExcelService.getExcelFile(workbook, fileName)
        fileName = URLEncoder.encode(fileName, "UTF-8")
        response.setContentType("multipart/form-data")
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName)
        OutputStream out = null
        try {
            out = response.getOutputStream()
            out << file?.bytes
            try {
                file.delete()
            } catch (Exception e) {
                log.error("文件删除错误：{}", e)
            }
        } catch (IOException e) {
            log.error("文件下载错误:{}", e)
        } finally {
            out.flush()
            out.close()
        }
    }
}

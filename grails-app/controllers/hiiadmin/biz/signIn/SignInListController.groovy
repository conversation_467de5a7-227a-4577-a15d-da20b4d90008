package hiiadmin.biz.signIn

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.signIn.SignInRecord4TeacherVO
import hiiadmin.signIn.SignInVOService
import hiiadmin.utils.PhenixCoder

class SignInListController implements BaseExceptionHandler {

    SignInVOService signInVOService

    def teacherRecord() {
        Long campusId = request.getAttribute("campusId") as Long
        String planId = params.planId

        Long subjectId = params.long("subjectId")
        String teacherIdStr = params.teacherId
        Long teacherId = PhenixCoder.decodeId(teacherIdStr)
        Byte signInType = params.byte("signInType")
        Long startDate = params.long("startDate"),
             endDate = params.long("endDate")
        int p = params.int("p", 1),
            s = params.int("s", 20)
        def (List<SignInRecord4TeacherVO> list, int total) = signInVOService.transformSignInRecord4TeacherVOList(campusId, planId, subjectId, teacherId, signInType, startDate, endDate, p, s)
        ResultVO resultVO = ResultVO.success([list: list, total: total])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def recordInfo() {
        Long recordId = params.long("id")
        SignInRecord4TeacherVO vo = signInVOService.transformSignInRecord4TeacherVO(recordId)
        ResultVO resultVO = ResultVO.success(vo)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

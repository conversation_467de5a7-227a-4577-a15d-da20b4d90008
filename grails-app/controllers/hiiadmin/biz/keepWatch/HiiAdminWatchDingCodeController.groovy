package hiiadmin.biz.keepWatch

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.keepWatch.KeepWatchService


class HiiAdminWatchDingCodeController implements BaseExceptionHandler{

    KeepWatchService keepWatchService
	
    def save() { 
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        
        keepWatchService.updateAllCampusDingCode()
        
        resultVO.result.put("msg", "success")
        
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

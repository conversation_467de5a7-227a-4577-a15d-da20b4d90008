package hiiadmin.biz.keepWatch

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.ViewDOService
import hiiadmin.keepWatch.KeepWatchService
import hiiadmin.module.bugu.DoorPlateVO
import hiiadmin.module.keepWatch.WatchDeviceVO
import hiiadmin.module.keepWatch.WatchPlaceVO
import hiiadmin.module.keepWatch.WatchTimeVO
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PhenixCoder
import io.swagger.annotations.Api
import org.apache.commons.lang3.StringUtils
import timetabling.keepWatch.WatchPlace
import timetabling.keepWatch.WatchTime

@Api(value = "教师巡更", tags = "校园安全", consumes = "admin")
class HiiAdminWatchPlaceController implements BaseExceptionHandler {

    KeepWatchService keepWatchService

    ViewDOService viewDOService

    DeviceService deviceService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<WatchPlace> watchPlaceList = keepWatchService.fetchAllWatchPlaceViaCampusId(campusId)
        List<WatchTime> watchTimeList = keepWatchService.fetchAllWatchTimeViaCampusId(campusId)
        List<WatchPlaceVO> paperWatchPlaceVOList = []
        List<WatchPlaceVO> watchPlaceVOList = []
        List<WatchPlaceVO> vos = []
        List<WatchPlaceVO> padWatchPlaceVOList = []
        watchPlaceList.each {
            WatchPlace watchPlace ->
                WatchPlaceVO watchPlaceVO = viewDOService.buildWatchPlaceVO(watchPlace)
                List<WatchTime> watchTimes = watchTimeList.findAll { it.placeId == watchPlace.id }
                List<WatchTimeVO> watchTimeVOList = watchTimes.collect { viewDOService.buildWatchTimeVO(it) }
                watchPlaceVO.timeVOList = watchTimeVOList
                watchPlaceVOList << watchPlaceVO
        }
        
        paperWatchPlaceVOList = watchPlaceVOList.findAll{it.watchType == ConstantEnum.KeepWatchType.PAPER.type}
        paperWatchPlaceVOList?.sort{it.id}
        vos.addAll(paperWatchPlaceVOList)
        
        
        padWatchPlaceVOList = watchPlaceVOList.findAll{it.watchType == ConstantEnum.KeepWatchType.PAD.type}
        List<Long> deviceIdList = padWatchPlaceVOList*.deviceId?.unique()
        Map<Long, DoorPlateVO> doorPlateVOMap =  deviceService.getDoorPlateVOByDeviceIdInList(deviceIdList)
        padWatchPlaceVOList.each { watchPlaceVO ->
            DoorPlateVO doorPlateVO = doorPlateVOMap.get(watchPlaceVO.deviceId)
            if (doorPlateVO) {
                watchPlaceVO.buildingId = doorPlateVO.buildingId
                watchPlaceVO.layerCode = doorPlateVO.layerCode 
                watchPlaceVO.doorPlateCode = doorPlateVO.code 
            } else {
                watchPlaceVO.buildingId = PhenixCoder.encodeId(999999l)
                watchPlaceVO.layerCode = 999999
                watchPlaceVO.doorPlateCode = String.valueOf(0)
            }
        }
        padWatchPlaceVOList?.sort{a, b ->
            PhenixCoder.decodeId(a.buildingId) <=> PhenixCoder.decodeId(b.buildingId) ?: a.layerCode <=> b.layerCode ?: a.doorPlateCode <=> b.doorPlateCode ?: a.id <=> b.id
        }
        vos.addAll(padWatchPlaceVOList)
        
        vos.removeAll([null])
        
        resultVO.result.put("list", vos)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        WatchPlace watchPlace = keepWatchService.getWatchPlace(id)
        WatchPlaceVO watchPlaceVO = viewDOService.buildWatchPlaceVO(watchPlace)
        List<WatchTime> watchTimeList = keepWatchService.fetchAllWatchTimeViaPlaceId(watchPlace.id)

        List<WatchTimeVO> watchTimeVOList = watchTimeList.collect { viewDOService.buildWatchTimeVO(it) }
        watchPlaceVO.timeVOList = watchTimeVOList
        resultVO.result = ObjectUtils.objectToMap(watchPlaceVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String name = params.name
        String json = params.watchTimes
        String deviceJson = params.deviceJson
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        Byte watchType = params.byte("watchType")
        String watchSignInType = params.watchSignInType

        String memo = params.memo
        if (watchType == ConstantEnum.KeepWatchType.PAPER.type) {
            List<String> nameList = keepWatchService.fetchAllWatchPlaceViaCampusId(campusId)*.name
            if (nameList?.contains(name)) {
                resultVO.code = 300001
                resultVO.status = 0
                resultVO.msg = "点位名称已存在"
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
        }
        if (watchType == ConstantEnum.KeepWatchType.PAD.type) {
            List<WatchDeviceVO> watchDeviceList = JSON.parseArray(deviceJson, WatchDeviceVO)
            watchDeviceList.each {
                WatchDeviceVO watchDevice ->
                    WatchPlace watchPlace = keepWatchService.fetchWatchPlaceByCampusIdAndPlaceId(campusId, watchDevice.deviceId)
                    if (watchPlace == null) {
                        watchPlace = new WatchPlace(
                                schoolId: schoolId,
                                deviceId: watchDevice?.deviceId,
                                campusId: campusId,
                                watchType: watchType,
                                status: 1 as byte
                        )
                    }
                    watchPlace.memo = memo
                    watchPlace.watchSignInType = watchSignInType
                    watchPlace.name = watchDevice.deviceName
                    keepWatchService.createWatchPlace(watchPlace, json)
            }
        } else {
            WatchPlace watchPlace = new WatchPlace(
                    schoolId: schoolId,
                    campusId: campusId,
                    watchType: watchType,
                    memo: memo,
                    watchSignInType: watchSignInType,
                    name: name,
                    status: 1 as byte
            )
            keepWatchService.createWatchPlace(watchPlace, json)
        }
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String name = params.name
        String memo = params.memo
        String json = params.watchTimes
        Long campusId = request.getAttribute("campusId") as Long
        WatchPlace watchPlace = keepWatchService.getWatchPlace(id)
        if (watchPlace.watchType == ConstantEnum.KeepWatchType.PAPER.type) {
            List<String> nameList = keepWatchService.fetchAllWatchPlaceViaCampusId(campusId)*.name
            if (watchPlace?.name != name && nameList?.contains(name)) {
                resultVO.code = 300001
                resultVO.status = 0
                resultVO.msg = "点位名称已存在"
                render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
                return
            }
        }
        String watchSignInType = params.watchSignInType
        if (StringUtils.isNotEmpty(watchSignInType)) {
            watchPlace.watchSignInType = watchSignInType
        }
        if (StringUtils.isNotEmpty(name)) {
            watchPlace.name = name
        }
        if (StringUtils.isNotEmpty(memo)) {
            watchPlace.memo = memo
        }
        def map = keepWatchService.createWatchPlace(watchPlace, json)
        watchPlace = map.watchPlace
        WatchPlaceVO watchPlaceVO = viewDOService.buildWatchPlaceVO(watchPlace)
        List<WatchTime> watchTimeList = map.watchTimeList
        List<WatchTimeVO> watchTimeVOList = watchTimeList.collect { viewDOService.buildWatchTimeVO(it) }
        watchPlaceVO.timeVOList = watchTimeVOList
        resultVO.result = ObjectUtils.objectToMap(watchPlaceVO)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        String ids = params.ids
        
        Long campusId = request.getAttribute("campusId") as Long
        List<Long> placeIdList = ids.split(",").collect { it.toLong() }
        keepWatchService.updateQrCode4Place(placeIdList, campusId)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"

    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        WatchPlace watchPlace = keepWatchService.getWatchPlace(id)
        watchPlace = keepWatchService.deleteWatchPlace(watchPlace)
        resultVO.result.put("id", watchPlace.id)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

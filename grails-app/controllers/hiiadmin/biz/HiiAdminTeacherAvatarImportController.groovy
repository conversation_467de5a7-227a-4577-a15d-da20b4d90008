package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.UserInfoChangeEnum
import hiiadmin.listen.PersonDataChangeEnum
import hiiadmin.syn.SynUserV2Service
import hiiadmin.user.PersonDataChangeService
import timetabling.docking.DockingFirmSyncTask

import static hiiadmin.ConstantEnum.UserTypeEnum

class HiiAdminTeacherAvatarImportController implements BaseExceptionHandler {

    TeacherAvatarImportService teacherAvatarImportService

    PersonDataChangeService personDataChangeService

    def save() {
        ResultVO resultVO = ResultVO.success()

        Long campusId = request.getAttribute("campusId") as Long
        String jobNum = params.jobNum
        String avatar = params.avatar
        String name = params.name

        Long userId = request.getAttribute("userId") as Long
        Byte userType = request.getAttribute("type") as Byte

        ServiceResult<Map<String, String>> result = teacherAvatarImportService.updateTeacherAvatar(campusId, jobNum, name, avatar)
        if (result.success) {
            if (result.result?.id) {
                int changeBit = PersonDataChangeEnum.onSyncUserHandlerBit()+PersonDataChangeEnum.onAuthGateFaceLibHandlerBit()
                personDataChangeService.teachersDataChange(campusId, [result.result.id as Long], userId, userType, changeBit, ConstantEnum.OperateType.UPDATE.type,"批量导入照片-单个教职工同步", UserInfoChangeEnum.onAvatar())
//                synUserV2Service.syncUpdateOrCreateUser2Firm(campusId, [result.result.id as Long], UserTypeEnum.TEACHER.type, userId, userType, DockingFirmSyncTask.OPERATION_TYPE_MANY, "批量导入照片-单个教职工同步")
            }
            resultVO.result = result.result
        } else {
            resultVO = ResultVO.failure(result.code as Integer, result.message)
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

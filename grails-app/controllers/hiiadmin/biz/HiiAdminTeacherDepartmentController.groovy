package hiiadmin.biz

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.bugu.DepartmentVO

@Deprecated
class HiiAdminTeacherDepartmentController implements BaseExceptionHandler {

    DepartmentService departmentService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        Long campusId = request.getAttribute("campusId") as Long
        boolean t = params.boolean("t")
        String searchValue = params.searchValue
        List<DepartmentVO> departmentList = departmentService.getDepartmentVOList(campusId, t, searchValue)
        resultVO.result.put("list", departmentList)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8"
    }
}

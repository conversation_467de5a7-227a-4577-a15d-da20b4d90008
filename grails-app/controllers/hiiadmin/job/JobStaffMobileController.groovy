package hiiadmin.job

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler

class JobStaffMobileController implements BaseExceptionHandler {
    
    JobStaffMobileService jobStaffMobileService
    
    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = params.long("campusId")
        jobStaffMobileService.fixMobileByCampus(campusId)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

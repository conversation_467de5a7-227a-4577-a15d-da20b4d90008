package hiiadmin.track

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.TrackMessageMapper
import hiiadmin.module.track.TrackMessageDeviceVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.device.Device
import timetabling.track.TrackMessageDevice
import timetabling.track.TrackMessageFeature

import java.util.concurrent.TimeUnit

class TrackMessageDeviceController implements BaseExceptionHandler {

    TrackMessageDeviceService trackMessageDeviceService

    TrackMessageFeatureService trackMessageFeatureService

    @Autowired
    TrackMessageMapper trackMessageMapper

    CacheService cacheService

    def index() {
        String idStr = params.featureId
        long featureId = PhenixCoder.decodeId(idStr)
        Long campusId = request.getAttribute("campusId") as Long
        int p = params.int("p", 1)
        int s = params.int("s", 30)

        int exist = params.int("exist", 1)
        Integer firm = params.int("firm")
        String searchValue = params.searchValue
        ResultVO resultVO = null
        if (exist) {
            Integer count = trackMessageDeviceService.countTrackMessageDevice(featureId, searchValue)
            List<TrackMessageDevice> deviceList = trackMessageDeviceService.fetchAllTrackMessageDevice(featureId, searchValue, p, s)
            List<TrackMessageDeviceVO> deviceVOList = trackMessageDeviceService.transformTrackMessageDeviceVO(campusId, deviceList)
            resultVO = ResultVO.success([list: deviceVOList, total: count])
        } else {
            Byte type = params.byte("type")
            String positionIdStr = params.positionId
            Long positionId = PhenixCoder.decodeId(positionIdStr)
            Integer count = trackMessageDeviceService.countUnExistTrackMessageDevice(campusId, firm, type, positionId, searchValue)
            List<Device> deviceList = trackMessageDeviceService.fetchAllUnExistTrackMessageDevice(campusId, firm, type, positionId, searchValue, p, s)
            List<TrackMessageDeviceVO> deviceVOList = trackMessageDeviceService.transformUnExistTrackMessageDeviceVO(campusId, deviceList)
            resultVO = ResultVO.success([list: deviceVOList, total: count])
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        String idStr = params.featureId
        long featureId = PhenixCoder.decodeId(idStr)
        String deviceIds = params.deviceIds
        TrackMessageFeature feature = trackMessageFeatureService.getTrackMessageFeatureById(featureId)
        if (!feature) {
            throw new HiiAdminException("配置不存在")
        }

        String key = "trackMessageDevice${featureId}"
        boolean flag = cacheService.tryLockAndRun4lockCache(key, 30, TimeUnit.SECONDS, {
            trackMessageDeviceService.addTrackMessageDevice(feature.campusId, featureId, deviceIds)
        })
        if (!flag) {
            throw new HiiAdminException("操作频繁，请稍后重试！")
        }

        ResultVO resultVO = ResultVO.success()
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        String idStr = params.id
        long featureDeviceId = PhenixCoder.decodeId(idStr)
        trackMessageDeviceService.removeTrackMessageDevice(featureDeviceId)
        ResultVO resultVO = ResultVO.success([id: idStr])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

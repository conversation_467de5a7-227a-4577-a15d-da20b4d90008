package hiiadmin.track

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CampusCustomMadeService
import hiiadmin.exceptions.Asserts
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.TrackMessageMapper
import hiiadmin.module.track.TrackMessageFeatureVO
import hiiadmin.module.track.TrackMessagePersonVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.track.TrackMessageFeature
import timetabling.track.TrackMessagePerson

import static hiiadmin.CampusConstantEnum.CampusCustomMadeType
import static hiiadmin.ConstantEnum.TrackMessageFeatureOpt

class TrackMessagePersonController implements BaseExceptionHandler {

    TrackMessageFeatureService trackMessageFeatureService

    TrackMessagePersonService trackMessagePersonService

    CampusCustomMadeService campusCustomMadeService

    @Autowired
    TrackMessageMapper trackMessageMapper

    def index() {
        Long campusId = request.getAttribute("campusId") as Long

        String idStr = params.featureId
        long featureId = PhenixCoder.decodeId(idStr)
        TrackMessageFeature messageFeature = trackMessageFeatureService.getTrackMessageFeatureById(featureId)
        if (!messageFeature) {
            throw new HiiAdminException("配置不存在")
        }
        TrackMessageFeatureVO featureVO = trackMessageMapper.convert2TrackMessageFeatureVO(messageFeature)
        Boolean allowMessagePic = campusCustomMadeService.checkTypeLockStatus(campusId, CampusCustomMadeType.TRACK_MESSAGE_PIC.type)
        featureVO.allowMessagePic = allowMessagePic
        def (List<TrackMessagePersonVO> teacherVOList, List<TrackMessagePersonVO> roleVOList, List<TrackMessagePersonVO> departmentVOList) = trackMessagePersonService.transformTrackMessagePersonVO(messageFeature)

        ResultVO resultVO = ResultVO.success([feature    : featureVO,
                                              teachers   : teacherVOList,
                                              roles      : roleVOList,
                                              departments: departmentVOList])

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }

    }

    def edit() {
        String idStr = params.featureId
        long featureId = PhenixCoder.decodeId(idStr)
        Integer personType = params.int("personType", TrackMessageFeatureOpt.TEACHER.opt)
        List<TrackMessagePerson> personList = trackMessagePersonService.fetchAllTrackMessagePersonByFeatureId(featureId, personType)

        List<TrackMessagePersonVO> personBaseVOList = trackMessageMapper.listConvert2TrackMessagePersonVO(personList)
        ResultVO resultVO = ResultVO.success([list: personBaseVOList])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        String idStr = params.featureId
        long featureId = PhenixCoder.decodeId(idStr)
        String pushOpt = params.pushOpt

        String teacherIds = params.teacherIds
        teacherIds = PhenixCoder.decodeIds(teacherIds)
        String roleIds = params.roleIds
        String departmentIds = params.departmentIds
        departmentIds = PhenixCoder.decodeIds(departmentIds)

        Integer openStatus = params.int("openStatus")
        //开启格言消息推送
        Integer maximOpenStatus = params.int("maximOpenStatus")
        if (openStatus != null ) {
            trackMessageFeatureService.checkTrackMessageFeature(featureId, openStatus)
        }
        Boolean messagePicEnable = params.boolean("messagePicEnable")
        if (messagePicEnable) {
            Asserts.assertTrue(campusCustomMadeService.checkTypeLockStatus(campusId, CampusCustomMadeType.TRACK_MESSAGE_PIC.type), "该功能未开放，请联系客服开放！")
        }

        Integer silentPeriod = params.int("silentPeriod", 0)
        trackMessageFeatureService.updateTrackMessageFeature(maximOpenStatus,featureId, messagePicEnable, pushOpt, teacherIds, roleIds, departmentIds, silentPeriod)
        ResultVO resultVO = ResultVO.success()
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        String idStr = params.id
        long messagePersonId = PhenixCoder.decodeId(idStr)
        trackMessagePersonService.removeTrackMessagePerson(messagePersonId)
        ResultVO resultVO = ResultVO.success()
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.track

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.TrackMessageMapper
import hiiadmin.module.track.TrackMessageFeatureVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.track.TrackMessageFeature


class TrackMessageFeatureController implements BaseExceptionHandler {

    TrackMessageFeatureService trackMessageFeatureService

    @Autowired
    TrackMessageMapper trackMessageMapper

    def index() {
        long campusId = request.getAttribute("campusId") as long
        List<TrackMessageFeatureVO> featureVOList = trackMessageFeatureService.fetchCampusAllTrackMessageFeature(campusId)

        ResultVO resultVO = ResultVO.success([list: featureVOList])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        String idStr = params.id
        long featureId = PhenixCoder.decodeId(idStr)
        TrackMessageFeature messageFeature = trackMessageFeatureService.getTrackMessageFeatureById(featureId)
        if (!messageFeature) {
            throw new HiiAdminException("配置不存在")
        }

        TrackMessageFeatureVO featureVO = trackMessageMapper.convert2TrackMessageFeatureVO(messageFeature)
        ResultVO resultVO = ResultVO.success(featureVO)

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        String idStr = params.id
        long featureId = PhenixCoder.decodeId(idStr)

        TrackMessageFeature feature = trackMessageFeatureService.checkTrackMessageFeature(featureId)
        TrackMessageFeatureVO featureVO = trackMessageMapper.convert2TrackMessageFeatureVO(feature)
        ResultVO resultVO = ResultVO.success(featureVO)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

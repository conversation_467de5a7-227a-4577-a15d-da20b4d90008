package hiiadmin

class UrlMappings {

    static mappings = {
        group "/api/hiiadmin/1.0", {
            //获取低代码页面
            '/findHtmlConfig'(resources: 'findHtmlConfig', includes: ['index', 'show'])
            '/auth/remoteSchool'(resources: 'authRemoteSchool', includes: ['index', 'save'])
            '/auth/remoteUserInfo'(resources: 'authRemoteUserInfo', includes: ['index', 'save'])
            '/auth/remoteLogin'(resources: 'authRemoteLogin', includes: ['index', 'save'])
            "/inside/singlePointLogin"(resources: "hiiAdminSinglePointLogin", includes: ['index'])
            "/outLogin"(resources: "authOutLogin", includes: ['index', 'save', 'update', 'delete', 'patch'])
            "/revision/ferriesTOParent"(resources: "hiiAdminRevisionFerriesTOParent", includes: ['index'])
            //之前调用auth的接口迁移至此start
            "/auth/menu"(resources: "hiiAdminStaffMenu", includes: ['show'])
            "/menu"(resources: "hiiAdminMenu", includes: ['show', 'index', 'save', 'update'])
            "/pswd"(resources: "hiiAdminUpdatePassword", includes: ['save'])
            "/thirdAuthenticate"(resources: "hiiAdminThirdTokenChange", includes: ['save'])
            "/thirdAuthCheck"(resources: "hiiAdminThirdAuthCheck", includes: ['index'])
            "/oos/unicom"(resources: "hiiAdminUnicomOos", includes: ['save'])
            "/auth/mobile/login"(resources: "hiiAdminMobileLogin", includes: ['save'])
            "/auth/authority"(resources: "hiiAdminAuthority", includes: ['index', 'show', 'update', 'save', 'delete'])
            "/biz/checkNoo"(resources: "CheckNo", includes: ['index', 'show', 'update', 'save', 'delete'])
            "/auth/authenticate"(resources: "hiiAdminAuthenticate", includes: ['save', 'update', 'patch'])
            "/auth/userModuleLogin"(resources: "hiiAdminUserModuleLogin", includes: ['save'])
            //单点登入
            "/auth/menuSingleLogin"(resources: "hiiAdminMenuSingleLogin", includes: ['save', 'update'])
            "/auth/authenticate/mine"(resources: "hiiAdminAuthenticateMine", includes: ['index'])
            "/changeCampus"(resources: "hiiAdminChangeCampus", includes: ['update'])
            "/ding/user/check"(resources: "hiiAdminDingUserCheck", includes: ['index'])
            "/ding/user/dflb"(resources: "dingFreeLoginBinding", includes: ['save'])
            //之前调用auth的接口迁移至此end
            "/qiniu/token"(controller: "qiniu", action: 'index')//七牛
            "/glad/ref/token"(resources: "adminGladRefreshToken", includes: ['save'])

            "/third/login"(resources: "cityBrainAuthToken", includes: ['index', 'save'])//城市大脑token
            "/test"(resources: "test", includes: ['index'])
            "/health"(resources: "healthCheck", includes: ['index'])
            "/qiniu/token"(controller: "qiniu", action: 'index')//七牛
            "/oauth/unicom/oos"(resources: "unicomOos", includes: ['show', 'index'])
            "/oauth/success/callback"(resources: "oauthSuccess", includes: ['show', 'index'])
            //-------------以下是hii迁移而来-------------
            "/elegance"(resources: "hiiAdminElegance", includes: ['index', 'show', 'update', 'save', 'delete', 'patch'])
            "/elegance/unit"(resources: "hiiAdminEleganceUnit", includes: ['index'])
            //班级公告
            "/examinationNotify"(resources: "adminExamPageUploadNotify", includes: ['save'])
            "/school/detection"(resources: "hiiAdminDetection", includes: ['index'])
            //校区管理
            "/examinationNotify"(resources: "adminExamPageUploadNotify", includes: ['save'])
            "/school/campus"(resources: "hiiAdminCampus", includes: ['index', 'update', 'show'])
            "/school/campus/tree"(resources: "hiiAdminTreeSchool", includes: ['index'])//🚩
            "/school/teachScope"(resources: "hiiAdminTeachScope", includes: ['index'])
            "/school/section"(resources: "hiiAdminSection", includes: ['index', 'save', 'delete', 'patch'])
            "/school/grade"(resources: "hiiAdminGrade", includes: ['index', 'save', 'delete', 'patch'])

            "/school/semester"(resources: "hiiAdminSemester", includes: ['index', 'update', 'patch', 'save'])//🚩
            "/school/campus/teachingWeek"(resources: "hiiAdminTeachingWeek", includes: ['index', 'show', 'save'])

            //家长-学生
            group "/school/parentStudent"(resources: "hiiAdminParentStudent", includes: ['index', 'save', 'update', 'delete', 'patch'])
            //接送人-学生
            "/school/ferriesStudent"(resources: "hiiAdminFerriesStudent", includes: ['index', 'save', 'update', 'delete', 'patch'])
            //访客
            "/visitor"(resources: "hiiAdminVisitor", includes: ['index', 'save', 'update', 'delete', 'patch'])
            "/visitorRecord"(resources: "hiiAdminVisitorRecord", includes: ['index'])
            "/visit/today"(controller: "hiiAdminVisitorRecord", action: 'todayVisitor', method: 'GET')
            "/school/subjects"(resources: "hiiAdminSubjects", includes: ['index'])

            "/school/password/reset"(resources: "hiiAdminPasswordReset", includes: ['update'])

            "/menu/menuCollection"(resources: "hiiAdminMenuCollection", includes: ['index', 'show', 'update', 'save', 'delete'])
            "/school/teacher/avatar/import"(resources: "hiiAdminTeacherAvatarImport", includes: ['save'])
            "/mobile/jobFix"(resources: "jobStaffMobile", includes: ['save'])

            group "/school/studentFiles", {
                "/task"(resources: "hiiAdminStudentFileTask", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/schoolYearSemester"(resources: "hiiAdminSchoolYearSemester", includes: ['index'])
                "/fileTaskRecord"(resources: "hiiAdminFileTaskRecord", includes: ['index', 'update'])
            }

            group "/school/student", {
                "/lowCode"(resources: "hiiAdminLowCodeStudent", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/"(resources: "hiiAdminStudent", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/img/import"(resources: "hiiAdminStudentImgImport", includes: ['index', 'save'])
                "/batch"(resources: "hiiAdminStudentBatch", includes: ['show', 'save'])
                "/special"(resources: "hiiAdminStudentSpecial", includes: ['index', 'update'])
            }

            group "/school/map", {
                "/mapConfig"(resources: "hiiAdminCampusMapConfig", includes: ['show', 'index', 'save', 'update'])
                "/device"(resources: "hiiAdminMapDevice", includes: ['show', 'index', 'save', 'update'])
            }

            group "/school/teacher", {
                //查询教职工
                "/search"(resources: "hiiAdminSearchTeacher", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/"(resources: "hiiAdminTeacher", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/batch"(resources: "hiiAdminTeacherBatch", includes: ['save'])
                //校区老师部门列表
                "/department"(resources: "hiiAdminDepartment", includes: ['index', 'update', 'save', 'patch', 'delete'])
                "/departmentV2"(resources: "hiiAdminDepartmentV2", includes: ['index'])
                "/department/teacher/list"(resources: "hiiAdminTeacher4Department", includes: ['index'])
                "/department/sort"(resources: "hiiAdminDepartmentSort", includes: ['save'])
                "/department4School"(resources: "hiiAdminDepartment4School", includes: ['index'])
                "/test"(resources: "hiiAdminTeacherTest", includes: ['index'])
            }
            group "/school/unit", {
                "/"(resources: "hiiAdminUnit", includes: ['index', 'save', 'update', 'delete'])
                "/tree"(resources: "hiiAdminTreeNameUnit", includes: ['index'])
                "/quick"(resources: "hiiAdminUnitQuick", includes: ['index', 'show', 'update', 'delete'])
                "/quickRecord"(resources: "hiiAdminUnitQuickRecord", includes: ['index'])
                "/teacher"(resources: "hiiAdminUnitTeacher", includes: ['index'])
                "/unitTree"(resources: "hiiAdminUnitTree", includes: ['index'])
                "/manageUnit"(resources: "hiiAdminManageUnitTree", includes: ['index'])
            }
            group "/school/device", {
                "/"(resources: "hiiAdminDevice", includes: ['index', 'save', 'delete', 'update', 'show'])
//                云睿设备列表
                "/yunrui"(resources: "yrDevice", includes: ['index', 'save', 'delete', 'update', 'show'])
//                云睿通道列表
                "/yunruiChannel"(resources: "yrDeviceChannel", includes: ['index', 'save', 'delete', 'update', 'patch', 'show'])
//                云睿通道算法
                "/yunruiChannelCalculation"(resources: "yrDeviceChannelCalculation", includes: ['update'])
                //告警中心消息配置
                "/yunruiAlarmMsgConfig"(resources: 'yrDeviceAlarmMsgConfig', includes: ['index', 'save'])
                "/tree"(resources: "hiiAdminTreeDevice", includes: ['index'])
                "/push"(resources: "hiiAdminDevicePush", includes: ['save'])
                "/classPad"(resources: "hiiAdminClassPadDevice", includes: ['index']) //班牌模板选择应用设备

                //班牌模板
                "/padTemplate"(resources: 'hiiAdminPadTemplate', includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])

                //班牌组件列表
                "/deviceComponentLibrary"(resources: 'hiiAdminPadDeviceComponentLibrary', includes: ['index'])
                "/deviceComponentType"(resources: 'hiiAdminPadDeviceComponentType', includes: ['index'])

                //获取设备截屏
                "/iotDeviceScreenshot"(resources: 'iotDeviceScreenshot', includes: ['index', 'show'])

                //获取设备视频流
                "/video"(resources: 'deviceVideoSteam', includes: ['show'])

                //班牌数据发布
                group "/boxGroup", {
                    "/"(resources: 'hiiAdminBoxGroup', includes: ['index', 'save', 'update', 'delete'])
                    "/boxGroupRecord"(resources: 'hiiAdminBoxGroupRecord', includes: ['index', 'save', 'update', 'delete', 'patch'])
                    "/boxGroupRecordData"(resources: 'hiiAdminBoxGroupRecordData', includes: ['index', 'show', 'save', 'update', 'patch'])
                }

                group "/eventAttendance", {
                    "/configure"(resources: "hiiAdminEventAttendanceConfigure", includes: ['index', 'show', 'update', 'delete', 'save', 'patch'])
                    "/configureTimeRules"(resources: "hiiAdminConfigureTimeRules", includes: ['index', 'show', 'update', 'delete', 'save', 'patch'])
                    "/eventRecord"(resources: "hiiAdminEventAttendanceRecord", includes: ['index', 'show', 'update', 'delete', 'save'])
                    "/eventDoorPlate"(resources: "hiiAdminEventDoorPlate", includes: ['index', 'show', 'update', 'delete', 'save'])
                    "/createEventRecord"(resources: "hiiAdminCreateEventRecord", includes: ['index', 'show', 'update', 'patch', 'delete'])
                    "/revisionEventRecord"(resources: "hiiAdminRevisionEventRecord", includes: ['index', 'show', 'update', 'patch'])
                    "/rocket"(resources: "rocketTest", includes: ['index'])
                }
                "/face"(resources: "hiiAdminFaceDevice", includes: ['index', 'update', 'delete', 'save', 'show', 'patch'])
                "/alarmEvent"(resources: "alarmEvent", includes: ['index', 'save', 'update', 'show', 'delete'])
                "/faceGroup"(resources: "faceGroup", includes: ['index', 'save'])
                "/get/algorithmNode"(resources: "algorithmNode", includes: ['index'])
                "/eventType"(resources: "trackEventType", includes: ['index'])
                "/${item}/synAliDeviceTest"(resources: " synDeviceToAliDeviceTest", includes: ['index'])
                "/synDeviceStatus"(resources: "synDeviceStatus", includes: ['index', 'save', 'update'])
                "/deviceStatusHistory"(resources: "hiiAdminDeviceStatusHistory", includes: ['index', 'save'])
                "/deviceStatusStatistics"(resources: "hiiAdminDeviceStatusStatistics", includes: ['index'])
                "/deviceStatusTimeline/${action}"(controller: "hiiAdminDeviceStatusStatistics", method: 'GET')
                "/findFaceGroup"(resources: "findFaceGroup", includes: ['index', 'show', 'update'])
                "/strangerTrack"(resources: "strangerTrack", includes: ['index', 'show', 'update'])
                "/synUserFaces"(resources: "synUserFaces", includes: ['save', 'show', 'update'])
                "/edgeDeviceGroup"(resources: "deviceGroup", includes: ['save', 'show', 'update'])
                "/eventPushTerminal"(resources: "hiiAdminEventPushTerminal", includes: ['index', 'update'])
                "/eventPushRecord"(resources: "hiiAdminEventPushRecord", includes: ['index'])
                // 接送相关设备接口
                "/pickUpScreen"(resources: "hiiAdminPickUpDevice", includes: ['index', 'show', 'update', 'save', 'delete', 'patch'])
                "/authDevice"(resources: "enterPlanAuthDevice", includes: ['index'])
                "/parentAuthDevice"(resources: 'leavePlanLevelParentAuthDevice', includes: ['index'])
                "/screenDevice"(resources: 'leavePlanLevelScreenDevice', includes: ['index'])
                "/deviceSelect"(resources: 'leavePlanLevelDeviceSelect', includes: ['index'])


            }
            //告警中心
            group "/school/eventTrack", {
                "/"(resources: "trackAlarmEvent", includes: ['index', 'show', 'save'])
                "/pic"(resources: "alarmMsgPic", includes: ['show'])
                "/device"(resources: "trackAlarmDevice", includes: ['index']) //获取安防设备

            }
            "/aLiDevice/event/${item}/dataV"(resources: "aLiDataBigScree", includes: ['index'])

            "/attendance/${item}/bigScreen"(resources: "attendanceBigScreen", includes: ['index'])

            group "/attendance/init", {
                "/"(resources: "attendanceInitJob", includes: ['save'])
                "/event"(resources: "eventAttendanceInitJob", includes: ['save'])
            }

            "/easyv/sereenlink"(resources: "easyvScreenConver", includes: ['index'])
            group "/school/signIn", {
                "/signUpdate/${action}"(controller: "signInUpdate", method: "POST")
                "/list/$action"(controller: "signInList", method: "GET")
                "/"(resources: "hiiAdminSignIn", includes: ['index', 'save', 'update'])
                "/setting"(resources: "hiiAdminSignInSetting", includes: ['index'])
                "/openSign"(resources: "hiiAdminSignInOpen", includes: ['index', 'update'])
                "/statistics"(resources: "hiiAdminSignInStatistics", includes: ['index'])
                "/record"(resources: "hiiAdminSignInRecord", includes: ['index'])
                "/seat"(resources: "hiiAdminSignInSeat", includes: ['index', 'save', 'update', 'delete'])
                "/student"(resources: "hiiAdminSignInStudent", includes: ['index'])
                "/remind"(resources: "hiiAdminSignInRemind", includes: ['index', 'save'])
                "/export"(resources: "hiiAdminSignInExportRecord", includes: ['index', 'save'])
            }
            
            "/operation/event/record"(resources: "operateEventRecord", includes: ['index'])

            "/school/avatar/batch"(resources: "hiiAdminTeacherAvatarBatch", includes: ['save'])
            //新闻后台
            "/school/journalism"(resources: 'hiiAdminJournalismPro', includes: ['index', 'save', 'show', 'delete', 'update'])
            "/askForLeave"(resources: 'hiiAdminAskForLeave', includes: ['index', 'show'])//后台请假列表
            "/staff"(resources: 'hiiAdminStaff', includes: ['index', 'save', 'update', 'delete'])//后台-操作员管理
            group "/school/building", {
                "/"(resources: "hiiAdminBuilding", includes: ['index', 'save', 'delete', 'update'])
                "/campus"(resources: "hiiAdminCampusBuilding", includes: ['index'])
                "/layer"(resources: "hiiAdminLayer", includes: ['index', 'save', 'delete'])
                "/door"(resources: "hiiAdminDoorPlate", includes: ['index', 'show', 'save', 'update', 'delete'])
                "/tree"(resources: "hiiAdminBuildingTree", includes: ['index'])
            }
            "/school/exam"(resources: "hiiAdminExam", includes: ['index', 'save', 'delete', 'update'])//考试管理
            "/school/classroom"(resources: "hiiAdminClassRoom", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
            "/school/dormitory"(resources: "hiiAdminDormitory", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
            "/school/pew"(resources: "hiiAdminPew", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
            //座位表管理
            group "/school/pew", {
                //类型管理
                "/type"(resources: "hiiAdminPewType", includes: ['index', 'save', 'update', 'delete', 'patch'])
                "/typeDoorPlate"(resources: "hiiAdminPewTypeDoorPlate", includes: ['index', 'save', 'update', 'delete', 'patch'])
                //类型初始化
                "/initPewType"(resources: "hiiAdminInitPewType", includes: ['index'])
                //类型应用管理
                "/typeApplication"(resources: "hiiAdminPewTypeApplication", includes: ['index', 'save', 'update', 'delete', 'patch'])
                //复制座位表
                "/pewCopy"(resources: "hiiAdminPewCopy", includes: ['save'])
            }

            "/school/bed"(resources: "hiiAdminBed", includes: ['index', 'save', 'update', 'delete', 'patch'])
            "/school/attendanceStandard"(resources: "hiiAdminAttendanceDayStandard", includes: ['index', 'save', 'update'])
            "/school/wenYin"(resources: "hiiAdminWenYin", includes: ['index', 'update'])//文印室
            "/school/wenYinTeacher"(resources: "hiiAdminWenYinTeacher", includes: ['index', 'save', 'update'])//教师文印申请
            "/school/dormitoryCard"(resources: "hiiAdminDormitoryCard", includes: ['index', 'update'])

            group "/school/option", {
                "/"(resources: "hiiAdminOption", includes: ['index', 'show', 'edit', 'save', 'update', 'patch', 'delete'])
                "/employ"(resources: "hiiAdminOptionEmploy", includes: ['index', 'update', 'delete'])
                "/unit"(resources: "hiiAdminOptionUnitRecord", includes: ['index', 'show'])
                "/subject"(resources: "hiiAdminOptionSubjectRecord", includes: ['index', 'show'])
                "/student"(resources: "hiiAdminOptionStudentRecord", includes: ['index', 'show', 'save', 'update'])
                "/excel"(resources: "hiiAdminOptionExcel", includes: ['index'])
            }
            //场地管理
            group "/position", {
                "/"(resources: "hiiAdminPosition", includes: ['index', 'show', 'edit', 'save', 'update', 'patch', 'delete'])
            }

            "/search/student"(resources: "hiiAdminStudentSearch", includes: ['index'])
            "/search/studentNames"(resources: "hiiAdminStudentNames", includes: ['index'])
            "/monitor/dingTalk"(resources: "hiiAdminDingTalk", includes: ['save'])

            "/nonresident"(resources: "hiiAdminNonresident", includes: ['index', 'save', 'update', 'delete', 'patch'])
            "/dataTemplate"(resources: "hiiAdminTemplate", includes: ['index', 'show'])
            "/dataTemplateUrl"(resources: "hiiAdminTemplateUrl", includes: ['show'])
            "/dataScreen"(resources: "hiiAdminDataScreen", includes: ['index', 'show'])

            group "/health", {
                "/clockRecord"(resources: "hiiAdminClockRecord", includes: ['index'])
                "/temperatureRecord"(resources: "hiiAdminTemperatureRecord", includes: ['index'])
                "/temperature"(resources: "hiiAdminTemperature", includes: ['index', 'delete'])
                "/symptomRecord"(resources: "hiiAdminSymptomRecord", includes: ['index'])
                "/adminHealthRecord"(resources: "hiiAdminHealthRecord", includes: ['show'])
                "/student"(resources: "hiiAdminHealthStudent", includes: ['index', 'show', 'save', 'update', 'delete'])
            }
            "/adminVirusUnusualUserRecord"(resources: "hiiAdminVirusUnusualUser", includes: ['index'])//后台异常人员记录
            "/adminVirusUnusualUserRecordHandleRecord"(resources: "hiiAdminVirusUnusualDealRecord", includes: ['index'])

            group "/moral", {
                "/"(resources: "hiiAdminMoral", includes: ['index'])//德育评分
                "/tree"(resources: "hiiAdminMoralTree", includes: ['index'])
                "/excel"(resources: "hiiAdminMoralResultExcel", includes: ['save'])
                "/item"(resources: "hiiAdminMoralItem", includes: ['index'])//德育评分
                "/menu"(resources: "hiiAdminMoralMenu", includes: ['index', 'save', 'update', 'patch', 'delete'])//德育评分
                "/option"(resources: "HiiAdminMoralOption", includes: ['index', 'save', 'update', 'patch', 'delete'])
                "/result"(resources: "hiiAdminMoralResult", includes: ['index'])//德育评分
                "/baseScore"(resources: "hiiAdminMoralBaseScore", includes: ['index', 'save'])//德育基础分
                "/warning"(resources: "hiiAdminMoralWarning", includes: ['index', 'show', 'save', 'update', 'patch', 'delete'])
                "/registerRecord"(resources: "hiiAdminMoralRegisterRecord", includes: ['index', 'show', 'delete'])//登记记录
//德育预警配置
                "/warningRecord"(resources: "hiiAdminMoralWarningRecord", includes: ['index', 'show'])//预警记录
                "/push"(resources: "hiiAdminMoralPush", includes: ['index', 'show', 'save', 'update', 'patch', 'delete'])
            }

            "/excelTemp/export"(resources: "hiiAdminExcelTemp", includes: ['index'])//一卡通模板下载

            group "/bureau", {
                "/campus4user"(resources: "bureauCampus4user", includes: ['index'])
            }
            //-------------以上是hii迁移而来-------------
            //发送验证码
            group "/pc", {
                "/checkNo/send"(resources: "checkNo", includes: ['index', 'save'])
                group "/bureau", {
                    "/login"(resources: "login", includes: ['save'])
                    "/login/update"(resources: "login", includes: ['update'])
                    "/campus"(resources: "campus", includes: ['index'])
                    "/unit"(resources: "unit", includes: ['index'])
                    "/student"(resources: "healthStudent", includes: ['index', 'show'])
                    "/healthRecord"(resources: "healthRecord", includes: ['show'])
                    "/clockRecord"(resources: "clockRecord", includes: ['index'])
                    "/temperatureRecord"(resources: "temperatureRecord", includes: ['index'])
                    "/symptomRecord"(resources: "symptomRecord", includes: ['index'])
                    "/virusUser"(resources: "virusExceptionUser", includes: ['index'])
                    "/virusUnusualUserRecord"(resources: "virusUnusualUser", includes: ['index'])
                    "/virusUnusualUserRecordHandleRecord"(resources: "virusUnusualDealRecord", includes: ['index'])
                    "/dataTemplate"(resources: "template", includes: ['index', 'show'])
                }

                group "/approval", {
                    "/template"(resources: "hiiAdminApprovalTemplate", includes: ['index', 'show', 'save'])
                    "/departTeacher"(resources: "hiiAdminDepartmentTeacher", includes: ['index'])
                }
            }
            //auth放外层以匹配auth-admin域名
            group "/auth", {
                // 教育局列表
                "/bureau"(resources: "authBureau", includes: ['index', 'save', 'update'])
                "/city"(resources: "city", includes: ['index'])
                "/approval/theme"(resources: "hiiAdminApprovalTemplateTheme", includes: ['index', 'save', 'show', 'update'])
                // 教育局下校区列表
                "/bureau/campus"(resources: "authBureauCampus", includes: ['index', 'update'])
                // 校区列表
                "/campus"(resources: "authCampus", includes: ['index'])
            }

            group "/dataCenter", {
                "/login"(resources: "dataCenterLogin", includes: ['save'])
                "/dataAssets"(resources: "hiiAdminDataAssets", includes: ['index', 'save', 'delete'])
                "/dataSub"(resources: "hiiAdminDataSub", includes: ['index'])
                "/dataField"(resources: "hiiAdminDataField", includes: ['index'])
                "/dataRecord"(resources: "hiiAdminDataRecord", includes: ['index'])
                "/bookAssets/$item/count"(resources: "bookAssets", includes: ['index'])
                "/warning/$item/count"(resources: "academicWarning", includes: ['index'])
                "/schoolPic/$item/count"(resources: "schoolPic", includes: ['index'])
                "/courseAnalysis/$item/count"(resources: "courseAnalysis", includes: ['index'])
                "/teacherPic/$item/count"(resources: "teacherPic", includes: ['index'])
                "/teacherTitle/ability/count"(resources: "teacherTitle", includes: ['index'])
                "/easyv/$item/count"(resources: "easyv", includes: ['index'])
                "/easyvScreen"(resources: "easyvScreen", includes: ['index'])
                "/research/$item/data"(resources: "research", includes: ['index'])
                "/schoolWord/$item/data"(resources: "dataStudentWork", includes: ['index'])
                "/dataMenu"(resources: "dataMenu", includes: ['index'])
                "/trackAlarm"(resources: "trackAlarm", includes: ['index'])
                "/trackAlarmCount"(resources: "trackAlarmCount", includes: ['index'])
                "/searchValue"(resources: "dataSearchValue", includes: ['index', 'save'])
                "/student/archives"(resources: "dataStudentArchives", includes: ['index', 'show'])
                "/userTrack"(resources: "hiiAdminUserTrack", includes: ['index', 'show'])
                "/department"(resources: "dataDepartment", includes: ['index', 'show'])
                "/humanMessage"(resources: "dataHumanMessage", includes: ['index', 'show'])
                "/HumanMsgSendUser"(resources: "HumanMsgSendUser", includes: ['index', 'save', 'show'])
                "/schoolAssets/$item/count"(resources: "schoolAssets", includes: ['index'])
                "/dataSub/search"(resources: "dataSubSearch", includes: ['index'])
            }

            group "/courseOption", {
                "/task"(resources: "hiiAdminElectiveTask", includes: ['index', 'save', 'update', 'delete', 'show', 'patch', 'edit'])
                "/allCheck"(resources: "hiiAdminElectiveTaskAllCheck", includes: ['index'])
                "/unit"(resources: "hiiAdminElectiveUnit", includes: ['index', 'save', 'show'])
                "/course"(resources: "hiiAdminElectiveCourse", includes: ['index', 'show', 'save', 'update'])
                "/student"(resources: "hiiAdminElectiveStudent", includes: ['index', 'save', 'update', 'show', 'delete'])
                "/import"(resources: "hiiAdminElectiveImportExcel", includes: ['save', 'update', 'show', 'delete'])

            }

            group "/gated", {
                "/"(resources: "hiiAdminGated", includes: ["index", "save", "show", "update", "delete", "patch"])
                "/daDuaStat"(resources: "hiiAdminGatedDaDuaStat", includes: ["index", "patch"])
                "/daDuaStat/imp"(resources: "hiiAdminGatedDaDuaExcel", includes: ["index"])
                "/void"(resources: "hiiAdminGatedVoid", includes: ["index", "save", "show", "update", "delete"])
            }
            group "/planTemplate", {
                "/template"(resources: "planTemplateV2", includes: ["index", "edit", "show", "save", "update", "delete"])
                "/task4hk"(resources: "planTemplateTask4hk", includes: ["index", "save", "show", "update", "delete"])
                "/task4yr"(resources: "planTemplateTask4Yr", includes: ["index", "save", "show", "delete"])
                //云睿下发结果查询
                "/task4yr/result"(resources: "planTemplateTaskResult4yr", includes: ["index", "save", "show", "delete"])
                "/person"(resources: "planTemplatePerson", includes: ["index", "save", "delete"])
                "/personGroup"(resources: "planTemplatePersonGroup", includes: ["index", "save", "delete"])
                "/personGroupSimple"(resources: "planTemplatePersonGroupSimple", includes: ["index"])
                "/personV2"(resources: "planTemplatePersonV2", includes: ['index'])
            }

            group "/docking", {
                "/dockingCampusPlatform"(resources: "dockingCampusPlatform", includes: ["index"])

                "/syncPerson"(resources: "dockingFirmPerson", includes: ["index", "show", "patch"])
                "/syncPersonOrg"(resources: "dockingFirmPersonOrg", includes: ["index", "patch"])
                "/dockingFirmSyncTask"(resources: "dockingFirmSyncTask", includes: ["index", "show", "patch"])
                "/dockingFirmPersonConfig"(resources: "dockingFirmPersonConfig", includes: ["index"])
                "/checkDockingFirmPersonAvatar"(resources: "checkDockingFirmPersonAvatar", includes: ["index"])

                "/hik/authConfigSearch"(resources: "authConfigSearch4Hik", includes: ["index"])
                "/hik/downloadRecordChannel"(resources: "authDownloadRecordChannel4Hik", includes: ["index"])
                "/hik/downloadRecordPerson"(resources: "authDownloadRecordPerson4Hik", includes: ["index"])
                "/hik/planTemplate"(resources: "dockingPlanTemplate4Hik", includes: ["index"])

                "/dhyr/person2Device"(resources: "dockingFirmPersonDevice", includes: ["save"])

                "/yunRui/alarmConfig"(resources: 'dockingDeviceAlarmConfig', includes: ['index', 'save', 'update', 'delete'])
                "/yunRui/alarmControl"(resources: 'dockingDeviceAlarmControl', includes: ['save'])
                "/yunRui/alarmEnum"(resources: 'dockingAlarmEnum', includes: ['index'])

                "/platform"(resources: "dockingPlatform", includes: ['show', 'patch'])
                "/platformStatusHistory"(resources: "dockingPlatformStatusHistory", includes: ['index'])
            }

            //导入 导出
            "/impRecord"(resources: "hiiAdminImpRecord", includes: ['index', 'show', 'save'])
            "/deviceImpRecord"(resources: "hiiAdminDeviceImpRecord", includes: ['index', 'show', 'save'])

            group "/track", {
                "/"(resources: "hiiAdminTrack", includes: ["index", 'show'])
                "/messageFeature"(resources: "trackMessageFeature", includes: ["index", "show", 'patch'])
                "/messageFeature/device"(resources: "trackMessageDevice", includes: ["index", "save", 'delete'])
                "/messageFeature/person"(resources: "trackMessagePerson", includes: ["index", "edit", 'save', 'delete'])
            }

            "/trackPic"(resources: "trackPic", includes: ["index", 'show'])
            "/careful/$option/$year/$month/$day/$hour/$campus/delete"(resources: "carefulDelete", includes: ['index'])
            //巡更
            group "/watch", {
                "/place"(resources: "hiiAdminWatchPlace", includes: ["index", "show", "save", "update", "patch", "delete"])
                "/record"(resources: "hiiAdminWatchRecord", includes: ["index", "save", "update"])
                "/dingCode"(resources: "hiiAdminWatchDingCode", includes: ['save'])
            }
            group "/checkFace", {
                "/"(resources: "checkFace", includes: ['index'])
                "/record"(resources: "checkFaceRecord", includes: ['index'])
                "/db"(resources: "checkFaceDB", includes: ['index', 'save'])
            }

            group "/approvalV2", {
                "/templateTheme"(resources: "hiiAdminApprovalTemplateThemeV2", includes: ['index', 'show', 'update', 'patch'])
                "/record"(resources: "hiiAdminApprovalRecordV2", includes: ['index', 'show', 'save'])
                "/recordList"(resources: "hiiAdminApprovalRecordListV2", includes: ['index', 'show', 'save'])
                "/record4me"(resources: "hiiAdminApprovalRecord4Me", includes: ['index'])
                "/exportRecord"(resources: "approvalExportRecord", includes: ['index'])
                "/autographType"(resources: "approvalAutographType", includes: ['index', 'save'])
                "/token"(resources: "approvalToken", includes: ['index'])
                "/leaveRecord"(resources: "hiiAdminLeaveRecord", includes: ['index'])
                "/leaveExport"(resources: "leaveExport", includes: ['save'])
                "/leaveCategory"(resources: "hiiAdminLeaveCategory", includes: ['index'])
                "/teacherLeaveCategory"(resources: "hiiAdminTeacherLeaveRecord", includes: ['index', 'save'])
                "/wenYin"(resources: "hiiAdminWenYinApprovalRecord", includes: ['index', 'show', 'update'])
                "/cancelLeave"(resources: "hiiAdminLeaveRevoke", includes: ['save'])
                "/violation"(resources: "hiiAdminViolationRecord", includes: ['index', 'show', 'update', 'delete'])
                "/violationExport"(resources: "violationExport", includes: ['save'])
                "/themeGroup"(resources: "approvalTemplateThemeGroup", includes: ['index', 'save', 'update', 'delete'])
                "/themeGroupSort"(resources: "approvalTemplateThemeGroupSort", includes: ['save'])
                "/themeSort"(resources: "approvalTemplateThemeSort", includes: ['save'])
                "/"(resources: "hiiAdminApprovalV2", includes: ['index', 'save', 'show', 'update'])
                "/auth/leaveCategory"(resources: "authLeaveCategory", includes: ['index', 'save', 'update', 'delete'])
                "/auth/leaveDirection"(resources: "authLeaveDirection", includes: ['index'])
                "/honorDeclaration/selection"(resources: "honorDeclarationSelection", includes: ['index'])
                "/checkInvoice"(resources: "hiiAdminCheckInvoice", includes: ['index'])
                "/honor"(resources: "hiiAdminHonor", includes: ['index', 'show', 'update', 'delete'])
                "/honor/info"(resources: "hiiAdminHonorInfo", includes: ['show'])
                "/activity"(resources: "hiiAdminActivity", includes: ['index', 'show', 'update', 'delete', 'patch'])
                "/activity/list"(resources: "hiiAdminActivityList", includes: ['index'])
                "/studentJob"(resources: "hiiAdminStudentJob", includes: ['index', 'show', 'update', 'delete', 'patch'])
                "/studentJob/list"(resources: "hiiAdminStudentJobList", includes: ['index'])
                "/honorSync"(resources: "hiiAdminUpdateHonor", includes: ['index'])
                "/violation/manage"(resources: "hiiAdminViolation", includes: ['index', 'update', 'patch'])
                "/teacherLeaveStatistics"(resources: "teacherLeaveStatistics", includes: ['index', 'save'])
                "/violation/list"(resources: "violationList", includes: ['index'])
                "/leaveRecordGet/$action"(controller: "leaveRecordList", method: "GET")
            }

            group "/studentEvaluation", {
                "/allTask"(resources: "hiiadminStudentEvaluationTask", includes: ["index", "show", "save", "update", "delete", "patch"])
                "/courseSelect"(resources: "hiiadminStudentEvaluationCourse", includes: ["index"])
                "/commitStat"(resources: "hiiadminStudentEvaluationCommitRecord", includes: ["show"])
                "/uncommit"(resources: "hiiadminStudentEvaluationUnCommit", includes: ["show"])
            }

            group "/teachingEvaluation", {
                "/comment"(resources: "hiiadminTeachingEvaluation", includes: ["index", "show", "save", "update", "delete"])
                "/unitQuery"(resources: "hiiadminEvaluationUnitQuery", includes: ["index", "show"])
                "/accounting"(resources: "hiiadminTeachingEvaluationAccounting", includes: ["index", "show", "save", "update", "delete"])
            }


            group "/teachingEvaluationManage", {
                "/item"(resources: "hiiadminTeachingEvaluationManage", includes: ["index", "show", "save", "update", "delete"])
                "/export"(resources: "hiiadminTeachingEvaluationExport", includes: ["index", "show", "save"])
            }
            group "/teachingEvaluationTemplate", {
                "/download"(resources: "hiiadminTeachingEvaluationTemplate", includes: ["index", "show", "save", "update", "delete"])
            }

            group "/symptom", {
                "/"(resources: "hiiAdminSymptom", includes: ['index'])
                "/infirmary"(resources: "hiiAdminInfirmary", includes: ['index', 'show', 'save'])
            }

            group "/document", {
                "/template"(resources: "hiiAdminDocumentTemplate", includes: ['index', 'show', 'save', 'update', 'patch'])
                "/template4Register"(resources: "hiiAdminDocumentTemplate4Register", includes: ['index'])
                "/node"(resources: "hiiAdminDocumentNode", includes: ['index', 'save'])
                "/category"(resources: "hiiAdminDocumentCategory", includes: ['index', 'save', 'update', 'delete'])
                "/"(resources: "hiiAdminDocument", includes: ['index', 'show', 'save', 'update', 'patch'])
                "/documentLibrary"(resources: "hiiAdminDocumentLibrary", includes: ['index', 'save'])
                "/export"(resources: "hiiAdminDocumentExport", includes: ['index', 'save'])
                "/comment"(resources: "hiiAdminDocumentComment", includes: ['save', 'delete', 'patch'])
            }

            "/userInfo"(resources: "hiiAdminUserInfo", includes: ['index'])

            //校内通知
            group "/teacher/notice", {
                "/"(resources: "hiiAdminMessage", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])
                "/messageV2"(resources: "hiiAdminMessageV2", includes: ['update'])
            }
            "/teacher/notice/record"(resources: "hiiAdminMessageRecord", includes: ['index'])
            "/campus/authority"(resources: "hiiAdminCampusAuthority", includes: ['index'])
            //新权限菜单
            group "/bgb/menu", {
                "/collection"(resources: "hiiAdminBgbMenuCollection", includes: ['index', 'save'])
                "/collectionMenu/sort"(resources: "hiiAdminBgbMenuCollectionSort", includes: ['save'])
                "/role"(resources: "hiiAdminBgbCampusRole", includes: ['index'])
                "/staff"(resources: "hiiAdminBgbStaffMenu", includes: ['index'])
                "/"(resources: "hiiAdminBgbCampusMenu", includes: ['index', 'save', 'update', 'patch', 'delete'])
                "/category"(resources: "hiiAdminBgbCampusMenuCategory", includes: ['index', 'save', 'update', 'patch', 'delete'])
                "/sort"(resources: "hiiAdminBgbCampusMenuSort", includes: ['save'])
                "/auth/role"(resources: "authBgbCampusRole", includes: ['index', 'save', 'update', 'patch', 'delete'])
            }

            // 放学班
            group "/school/pickUp", {
                // 放学班
                "/afterUnit"(resources: "hiiAdminAfterUnit", includes: ['index', 'show', 'save', 'delete', 'update'])
                "/afterUnitName"(resources: "hiiAdminAfterUnitName", includes: ['index'])
                "/afterUnitStudent"(resources: "hiiAdminAfterUnitStudent", includes: ['index', 'save', 'update', 'delete'])
                // 同步行政班
                "/unitSync"(resources: "hiiAdminAfterUnitSync", includes: ['save'])
                "/afterUnitTeacher"(resources: "hiiAdminAfterUnitTeacher", includes: ['index', 'save'])
                // 放学计划
                "/leavePlanTask"(resources: "hiiAdminLeavePlanTask", includes: ['index', 'save'])
                "/afterPlanInit"(resources: "hiiAdminAfterPlanInit", includes: ['index', 'save'])
                "/afterUnitAnalysis"(resources: "hiiAdminAfterUnitAnalysis", includes: ['save'])
            }

            // 设备开关机计划
            "/startUpPlan"(resources: "hiiAdminStartUpPlan", includes: ['index', 'show', 'save', 'update', 'delete', 'patch'])

            // 职校
            group "/school/vocational", {
                "/faculty"(resources: "hiiAdminFaculty", includes: ['index', 'update', 'save', 'delete', 'patch'])
                "/facultySort"(resources: 'hiiAdminFacultySort', includes: ['save'])
                "/major"(resources: "hiiAdminMajor", includes: ['index', 'save', 'delete'])
                "/grade"(resources: "hiiAdminVocationalGrade", includes: ['index', 'save', 'update', 'delete'])
                "/culture"(resources: "hiiAdminCulture", includes: ['index', 'save', 'delete'])
                "/schoolSystem"(resources: "hiiAdminSchoolSystem", includes: ['index', 'save', 'delete'])
                "/unit"(resources: "hiiAdminVocationalUnit", includes: ['index', 'show', 'update', 'save', 'delete', 'patch'])
                "/unitTree"(resources: "hiiAdminVocationalUnitTree", includes: ['index'])
                "/facultyUser"(resources: "hiiAdminFacultyUser", includes: ['index', 'save', 'delete'])
            }


            //阿里云oss上传
            group "/oss", {
                "/token"(resources: "ossToken", includes: ['index'])
                "/temporaryUrl"(resources: "ossTemporaryUrl", includes: ['index'])
                "/temporaryUrls"(resources: "ossTemporaryUrlList", includes: ['save'])
                "/update"(resources: "ossUpdate", includes: ['save'])
                "/smartCampusUrls"(resources: "smartCampusOssUrl", includes: ['save'])
            }
            // 信息发布审核
            group "/informationAudit", {
                "/"(resources: "hiiAdminInformationAudit", includes: ['index', 'update', 'save'])
                "/teacher"(resources: 'hiiAdminInformationAuditTeacher', includes: ['index'])

            }

            group "/school/suspend", {
                "/student"(resources: "hiiAdminSuspendStudent", includes: ['index', 'update'])
            }

            "/idCard/fix"(resources: "hiiAdminYxIdCard", includes: ['save'])

            group "/reportForm", {
                "/"(resources: "reportForm", includes: ['index', 'update', 'patch'])
                "/sort"(resources: "reportFormSort", includes: ['save'])
                "/group"(resources: "reportFormGroup", includes: ['index', 'save', 'update', 'delete'])
                "/group/sort"(resources: "reportFormGroupSort", includes: ['save'])
            }

            //杭高跑操
            group "/jogging", {
                "/plan"(resources: "joggingPlan", includes: ['index', 'show', 'save', 'update', 'delete'])
                "/point"(resources: "point", includes: ['index', 'save', 'delete'])
                "/pointDevice"(resources: "pointDevice", includes: ['index', 'show', 'patch', 'delete'])
                "/time"(resources: "joggingTime", includes: ['show'])
                "/cylinder"(resources: "joggingStudentCylinder", includes: ['index', 'save'])
                "/record"(resources: "joggingRecord", includes: ['index', 'show'])
                "/job"(resources: "joggingJob", includes: ['index', 'delete', 'show', 'patch'])
                "/rate"(resources: "joggingCompletionRate", includes: ['index'])
            }

            // 解析学号或姓名 批量导入学生教师使用
            "/analysis/user"(resources: "hiiAdminAnalysisUser", includes: ['save'])


            //资产
            group "/store", {

                "/fix"(resources: "hiiAdminFix", includes: ['index', 'show', 'update'])
            }

            //bi
            group "/bi", {
                "/tel"(resources: "biTel", includes: ['save'])
                "/tokenCheck"(resources: "biTokenCheck", includes: ['save'])
            }

            group "/messageControl", {
                "/center"(resources: "messageControlCenter", includes: ['patch'])
                "/person"(resources: "messageControlPerson", includes: ['index', 'save'])
            }

            group "/quickSearchPerson", {
                "/$action"(controller: "quickSearchPerson", method: "GET")
            }


            "/userCount"(resources: "hiiAdminUserCount", includes: ['index'])
        }
        "/api/hiiadmin/1.0/system/notice"(resources: "hiiAdminSystemNotice", includes: ['index'])
        "/api/hiiadmin/1.0/user/encodeId"(resources: "userEncodeId", includes: ['index'])

        "/api/hiiadmin/1.0/sta"(resources: "hiiAdminSta", includes: ['index'])
        "/api/hiiadmin/1.0/campusSum"(resources: "hiiAdminCampusSummary", includes: ['index'])
        "/api/hiiadmin/1.0/shareScreen"(resources: "shareScreen", includes: ['index', 'save','update', 'delete'])
        "/api/hiiadmin/1.0/shareScreen"(resources: "openShareScreen", includes: ['show'])

        //auth2.0单点登录
        "/api/hiiadmin/1.0/single/login/code"(resources: "auth2SingleLogin", includes: ['index'])
        "/api/hiiadmin/1.0/single/login/codev2"(resources: "auth2SingleLogin", includes: ['index'])

        "/"(controller: 'application', action: 'index')
        "500"(view: '/error')
        "404"(view: '/notFound')
    }
}

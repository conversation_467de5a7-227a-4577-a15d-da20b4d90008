package hiiadmin

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO

class UserEncodeIdController implements BaseExceptionHandler {

    UserEncodeIdService userEncodeIdService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long schoolId = request.getAttribute("schoolId") as Long
        resultVO.result = userEncodeIdService.fetchUserEncodeIdMap(userId, campusId, schoolId)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

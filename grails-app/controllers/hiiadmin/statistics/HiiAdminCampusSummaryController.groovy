package hiiadmin.statistics

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CountVO
import hiiadmin.school.CampusService
import hiiadmin.school.org.feature.UnitStudentService
import hiiadmin.utils.PhenixCoder
import timetabling.ParentStudent
import timetabling.device.Device
import timetabling.org.Campus
import timetabling.user.TeacherSchoolCampus

class HiiAdminCampusSummaryController implements BaseExceptionHandler {

    CampusService campusService

    UnitStudentService unitStudentService

    def index() {
        String campusIdStr = params.id
        Long campusId = PhenixCoder.decodeId(campusIdStr,true)

        Campus campus = campusService.findCampusById(campusId)
        Integer teacherCount = TeacherSchoolCampus.countByCampusIdAndStatus(campus.id, 1 as Byte)
        CountVO countVO = new CountVO()
        countVO.teacherCount = teacherCount
        def studentIdList = unitStudentService.studentByCampusIdList(campus.id)
        if (studentIdList && studentIdList.size() > 0) {
            Integer parentCount = ParentStudent.countByStudentIdInListAndStatus(studentIdList, 1 as Byte)
            countVO.parentCount = parentCount
            Integer studentCount = studentIdList.size()
            countVO.studentCount = studentCount
        } else {
            countVO.parentCount = 0
            countVO.studentCount = 0
        }
        Integer count = Device.countByCampusIdAndStatus(campus.id, 1 as Byte)
        countVO.deviceCount = count

        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        resultVO.result.put("deviceCount", countVO.deviceCount)
        resultVO.result.put("studentCount", countVO.studentCount)
        resultVO.result.put("parentCount", countVO.parentCount)
        resultVO.result.put("teacherCount", countVO.teacherCount)
        resultVO.result.put("id", campus?.id)
        resultVO.result.put("name", campus?.name)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

package hiiadmin.statistics

import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.QueenApi
import hiiadmin.school.CampusService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.TimeUtils
import timetabling.*
import timetabling.attendance.AttendanceDetail
import timetabling.attendance.AttendanceRules
import timetabling.keepWatch.WatchRecord
import timetabling.moral.MoralDetail
import timetabling.oa.ApprovalNew
import timetabling.org.Campus
import timetabling.sign.SignUp
import timetabling.sign.SignUpRecord
import timetabling.studentFile.Clock
import timetabling.studentFile.ClockRecord
import timetabling.symptom.SymptomRecord
import timetabling.track.Track

import javax.annotation.Resource

class HiiAdminStaController implements BaseExceptionHandler {

    CampusService campusService

    @Resource
    QueenApi queenApi

    def index() {
        String campusIdStr = params.id
        Long campusId = PhenixCoder.decodeId(campusIdStr,true)
        Campus campus = campusService.findCampusById(campusId)
        int week = params.int('week', 0)
        int before = params.int('before', 0)
        //获取当天初始时间
        Date dayStartTime = TimeUtils.getDateStartTime(new Date())
        if (week == 1) {
            dayStartTime = TimeUtils.getDateBeforeOrAfter(dayStartTime, -7)
        } else {
            if (before != 0) {
                dayStartTime = TimeUtils.getDateBeforeOrAfter(dayStartTime, before)
            }
        }
        Long dateTime0 = dayStartTime.getTime()
        int susheCount = 0
        Integer daoxiaoCount = 0
        Integer lixiaoCount = 0
        Integer shenPiCount = 0
        Integer qingjiaCount = 0
        Integer xiaoNeiTongZhiCount = 0
        Integer xueShengJieSongCount = 0
        Integer jiuYiCount = 0
        Integer xunHuCount = 0
        Integer jiaXiaoLiuYanCount = 0
        Integer padXinWenCount = 0
        Integer fengCaiCount = 0
        Integer tiaoDaiKeCount = 0

        AttendanceRules susheAttendanceRules = AttendanceRules.findByCampusIdAndNameAndStatus(campus.id, '宿舍考勤', 1 as Byte)
        AttendanceRules daoxiaoAttendanceRules = AttendanceRules.findByCampusIdAndNameAndStatus(campus.id, '到校考勤', 1 as Byte)
        AttendanceRules liXiaoAttendanceRules = AttendanceRules.findByCampusIdAndNameAndStatus(campus.id, '离校考勤', 1 as Byte)
        if (susheAttendanceRules) {
            susheCount = AttendanceDetail.countByCampusIdAndRulesIdAndDayDatestampGreaterThanAndStatus(campus.id, susheAttendanceRules.id, dateTime0, 1 as Byte)
        }
        if (daoxiaoAttendanceRules) {
            daoxiaoCount = AttendanceDetail.countByCampusIdAndRulesIdAndDayDatestampGreaterThanAndStatus(campus.id, daoxiaoAttendanceRules.id, dateTime0, 1 as Byte)
        }
        if (liXiaoAttendanceRules) {
            lixiaoCount = AttendanceDetail.countByCampusIdAndRulesIdAndDayDatestampGreaterThanAndStatus(campus.id, liXiaoAttendanceRules.id, dateTime0, 1 as Byte)
        }
        //人脸次数
        int faceCount = Track.countByCampusIdAndDateCreatedGreaterThan(campus.id, dayStartTime)
        //请假
        qingjiaCount = ApprovalNew.countByCampusIdAndLeaveCategoryIdIsNotNullAndStudentIdIsNotNullAndDateCreatedGreaterThan(campus.id, dayStartTime)
        //审批
        shenPiCount = ApprovalNew.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //校内通知
        xiaoNeiTongZhiCount = Message.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //学生接送
        xueShengJieSongCount = Track.countByCampusIdAndDateCreatedGreaterThanAndUserType(campusId, dayStartTime, ConstantEnum.UserTypeEnum.FERRIES.type)
        //就医
        jiuYiCount = SymptomRecord.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //寻呼
        xunHuCount = Paging.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //家校留言
        jiaXiaoLiuYanCount = LeanCloudMessage.countByCampusIdAndTypeAndDateCreatedGreaterThan(campusId, ConstantEnum.LeanCloudType.MESSAGE.type, dayStartTime)
        //新闻
        fengCaiCount = LeanCloudMessage.countByCampusIdAndTypeAndDateCreatedGreaterThan(campusId, ConstantEnum.LeanCloudType.ELEGANCE.type, dayStartTime)
        //教师巡更
        int xungengCount = WatchRecord.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //测温记录
        int temperatureCount = TemperatureRecord.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        //德育
        int moralCount = MoralDetail.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        padXinWenCount = Journalism.countByCampusIdAndDateCreatedGreaterThan(campusId, dayStartTime)
        try {
            def re = queenApi.statistic(campusId, dayStartTime.getTime(), new Date().getTime())
            if (re?.result?.substitute) {
                tiaoDaiKeCount = tiaoDaiKeCount + re?.result?.substitute as Integer
            }

            if (re?.result?.swap) {
                tiaoDaiKeCount = tiaoDaiKeCount + re?.result?.swap as Integer
            }

            if (re?.result?.cancel) {
                tiaoDaiKeCount = tiaoDaiKeCount + re?.result?.cancel as Integer
            }
        } catch (Exception e) {
            log.error("调用queeen9 获取调代课信息失败 exp:{}", e.message)
        }

        //活动报名(人次)sign_up 不限制时间
        List<SignUp> signUpList = SignUp.findAllBySchoolId(campus.schoolId)
        List<Long> ids = signUpList.collect { it.id }
        int huodongbaomingCount = 0
        if (ids?.size() > 0) {
            huodongbaomingCount = SignUpRecord.countBySignUpIdInList(ids)
        }

        //活动打卡(人次)clock 不限制时间
        List<Clock> clockList = Clock.findAllByCampusId(campusId)
        List<Long> cids = clockList.collect { it.id }
        int huodongdakaCount = 0
        if (cids?.size() > 0) {
            huodongdakaCount = ClockRecord.countByClockIdInList(cids)
        }
        //家校通知(条数) 不限制时间
        List<Message> messageList = Message.findAllByCampusId(campusId)
        List<Long> mids = messageList.collect { it.id }
        int jiaxiaotongzhiCount = 0
        if (mids?.size() > 0) {
            jiaxiaotongzhiCount = MessageRecord.countByMessageIdInList(mids)
        }
        [
                campus              : campus,
                faceCount           : faceCount,
                huodongdakaCount    : huodongdakaCount,
                jiaxiaotongzhiCount : jiaxiaotongzhiCount,
                susheCount          : susheCount,
                daoxiaoCount        : daoxiaoCount,
                tiaoDaiKeCount      : tiaoDaiKeCount,
                lixiaoCount         : lixiaoCount,
                shenPiCount         : shenPiCount,
                xiaoNeiTongZhiCount : xiaoNeiTongZhiCount,
                xueShengJieSongCount: xueShengJieSongCount,
                jiuYiCount          : jiuYiCount,
                xunHuCount          : xunHuCount,
                moralCount          : moralCount,
                xungengCount        : xungengCount,
                temperatureCount    : temperatureCount,
                qingjiaCount        : qingjiaCount,
                jiaXiaoLiuYanCount  : jiaXiaoLiuYanCount,
                padXinWenCount      : padXinWenCount,
                huodongbaomingCount : huodongbaomingCount,
                fengCaiCount        : fengCaiCount
        ]
    }
}

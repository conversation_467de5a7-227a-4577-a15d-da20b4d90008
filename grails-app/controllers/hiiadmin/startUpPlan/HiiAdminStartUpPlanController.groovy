package hiiadmin.startUpPlan

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.module.startUpPlan.StartUpPlanVO
import io.swagger.annotations.Api

@Api(value = "开关机计划", tags = "总务办公", consumes = "admin")
class HiiAdminStartUpPlanController {

    StartUpPlanService startUpPlanService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        int p = params.int("p", 1)
        int s = params.int("s", 30)

        Long campusId = request.getAttribute("campusId") as Long

        String name = params.searchValue

        def map = startUpPlanService.startUpPlanPage(name, campusId, p, s)
        resultVO.result.put("total", map.total)
        resultVO.result.put("list", map.list)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = params.long("id")

        StartUpPlanVO startUpPlanVO = startUpPlanService.getStartUpPlanById(id)

        resultVO.result = startUpPlanVO

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        String name = params.name

        Integer planType = params.int("planType")

        String timeDate = params.timeDate

        Long campusId = request.getAttribute("campusId") as Long

        ServiceResult<Long> serviceResult = startUpPlanService.saveStartUpPlan(name, planType, timeDate, campusId)

        if (serviceResult.success) {
            resultVO.result.put("id", serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
            resultVO.code = serviceResult.code as int
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = params.long("id")
        String name = params.name

        String timeDate = params.timeDate

        ServiceResult<Long> serviceResult = startUpPlanService.updateStartUpPlan(id, name, timeDate)

        if (serviceResult.success) {
            resultVO.result.put("id", serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
            resultVO.code = serviceResult.code as int
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1

        Long id = params.long("id")

        ServiceResult<Long> serviceResult = startUpPlanService.deleteStartUpPlan(id)

        if (serviceResult.success) {
            resultVO.result.put("id", serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
            resultVO.code = serviceResult.code as int
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Boolean useStatus = params.boolean("useStatus", true)
        String deviceIds = params.deviceIds
        String item = params.item
        Long deviceId = params.long("deviceId")
        Integer type = params.int("type")
        ServiceResult<Long> serviceResult = startUpPlanService.useDeviceStartupPlan(id, useStatus, deviceIds, item, deviceId, type)

        if (serviceResult.success) {
            resultVO.result.put("id", serviceResult.result)
        } else {
            resultVO.status = 0
            resultVO.message = serviceResult.message
            resultVO.code = serviceResult.code as int
        }
        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }
}

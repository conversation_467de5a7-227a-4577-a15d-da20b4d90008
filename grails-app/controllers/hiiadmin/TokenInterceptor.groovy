package hiiadmin

import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import com.buguk12.logging.LogUtils
import com.buguk12.logging.TL
import grails.converters.JSON
import groovy.util.logging.Slf4j
import hiiadmin.dengBao.DengBaoService
import hiiadmin.module.JwtUser
import hiiadmin.school.CampusService
import hiiadmin.utils.CtJWT
import hiiadmin.utils.JWTUtils
import hiiadmin.utils.JwtUserUtil
import hiiadmin.utils.PatternUtils
import org.apache.commons.lang3.StringUtils
import org.jose4j.jwt.JwtClaims
import timetabling.org.Campus

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Slf4j
class TokenInterceptor {

    int order = HIGHEST_PRECEDENCE + 50

    DengBaoService dengBaoService

    CampusService campusService

    TokenInterceptor() {
        StringBuffer sb = new StringBuffer()
        sb.append("|login").append("|checkNo").append("|healthCheck").append("|carefulDelete").append("|hiiAdminOptionExcel").
                append("|easyv").append("|schoolAssets").append("|dataCenterLogin").append("|openShareScreen")
                .append("|research").append("|dataStudentWork").append("|cityBrainAuthToken").append("|hiiAdminAuthenticate")
                .append("|dingFreeLoginBinding").append("|userExchangeToken").append("|hiiAdminUserModuleLogin")
        matchAll().except(controller: ~/(${sb.toString()})/).except(uri: '/actuator/health')
                .except(uri: "/error")
                .except(uri: "/notFound")
                .except(uri: "/api/hiiadmin/1.0/biz/checkNo")
                .except(uri: "/api/hiiadmin/1.0/thirdAuthenticate")
        //.except(uri: "/api/hiiadmin/1.0/thirdAuthCheck")
                .except(uri: "/api/hiiadmin/1.0/auth/mobile/login")
                .except(uri: "/api/hiiadmin/1.0/health")
                .except(uri: "/api/hiiadmin/1.0/pc/bureau/login")
                .except(uri: "/api/hiiadmin/1.0/pc/bureau/login/update")
                .except(uri: "/api/hiiadmin/1.0/excelTemp/export")
                .except(uri: "/api/hiiadmin/1.0/school/option/excel")
                .except(uri: "/api/hiiadmin/1.0/third/login")
                .except(uri: "/api/hiiadmin/1.0/screen/auth")
                .except(uri: "/api/hiiadmin/1.0/sta")
                .except(uri: "/api/hiiadmin/1.0/oauth/success/callback")
                .except(uri: "/hiiAdmin/gated/api/**")
                .except(uri: "/api/hiiadmin/lecture/**")
                .except(uri: "/api/hiiadmin/school/**")
                .except(uri: "/api/hiiadmin/job/**")
                .except(uri: "/api/hiiadmin/schoolBasicsData/**")
                .except(uri: "/api/hiiadmin/1.0/oos/unicom")
                .except(uri: "/api/hiiadmin/1.0/ding/user/check")
                .except(uri: "/api/hiiadmin/trackalarm/**")
                .except(uri: "/api/hiiadmin/userInfo/**")
                .except(uri: "/api/hiiadmin/1.0/auth/remoteSchool")
                .except(uri: "/api/hiiadmin/1.0/auth/remoteUserInfo")
                .except(uri: "/api/hiiadmin/1.0/auth/remoteLogin")
                .except(uri: "/api/hiiadmin/1.0/single/login/code")
                .except(uri: "/api/hiiadmin/1.0/feign/user/**")
                .except(uri: "/api/hiiadmin/1.0/data/sync/pd")
                .except(uri: "/api/hiiadmin/1.0/auth/userModuleLogin")
                .except(uri: "/api/hiiadmin/1.0/quartzJob/**")
    }

    boolean before() {
        String visit = webRequest.request.requestURI
        String actionName = actionName
        String controllerName = controllerName
        String jwt_token = request.getHeader(Constants.JWT_TOKEN)
        ResultVO resultVO = new ResultVO()

        if (StringUtils.isBlank(jwt_token)) {
            log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token)
            resultVO.status = 0
            resultVO.code = 100
            resultVO.msg = '登录过期'
            render resultVO as JSON
            response.status = HttpServletResponse.SC_UNAUTHORIZED
            return false
        }
        log.info("jwt_token:{}", jwt_token)

        String paramsStr = com.alibaba.fastjson.JSON.toJSONString(params)
        String referer = request.getHeader("Referer")
        def requestMap = [
                visit         : visit,
                actionName    : actionName,
                controllerName: controllerName,
                params        : params,
                referer       : referer
        ]
        log.info(com.alibaba.fastjson.JSON.toJSONString(requestMap))
        if (params.searchValue) {
            params.put("searchValue", PatternUtils.filter(params.searchValue))
        }
        if (params.searchName) {
            params.put("searchName", PatternUtils.filter(params.searchName))
        }

        String ip = getIpAddr(request)


        TL.set(LogUtils.Namespace.appName, "hiiadmin")
        TL.set(LogUtils.Namespace.source, LogUtils.Namespace.source_web)
        TL.set(LogUtils.Namespace.platform, "1")
        TL.set(LogUtils.Namespace.category, controllerName)
        TL.set(LogUtils.Namespace.action, actionName)


        log.info("agent:{}", request.getHeader("User-Agent"))
        log.info("custom-agent:{}", request.getHeader("custom-agent"))
        if (jwt_token.contains("Bearer")) {
            def cpResult = CtJWT.validateGladToken(jwt_token)
            if (!cpResult.isSuccess() || null == cpResult.getResult()) {
                //判断是否是移动端token
                def cpResultMobile = CtJWT.validateHiiToken(jwt_token)
                if (!cpResultMobile.isSuccess() || null == cpResultMobile.getResult()) {
                    log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token, resultVO?.code)
                    resultVO.status = 0
                    resultVO.code = 100
                    resultVO.msg = '无权限'
                    render resultVO as JSON
                    response.status = HttpServletResponse.SC_UNAUTHORIZED
                    return false
                } else {
                    return true
                }
            }
            String issJson = cpResult.getResult().getIssuer()
            JwtUser jwtUser = JSONObject.parseObject(issJson, JwtUser.class)
            JwtUserUtil.setJwtUser(jwtUser)
            Long id = jwtUser?.id
            Long userId = jwtUser?.userId
            Long schoolId = jwtUser?.schoolId
            String openId = jwtUser.openId
            Byte type = jwtUser?.type
            Long staffId = jwtUser?.staffId
            Long campusId = jwtUser?.campusId
            Byte campusType = jwtUser?.campusType
            TL.set(LogUtils.Namespace.token, issJson)
            if (campusId) {
                TL.set(LogUtils.Namespace.campusId, String.valueOf(campusId))
            }
            request.setAttribute('id', id)
            request.setAttribute('userId', userId)
            request.setAttribute('type', type)
            request.setAttribute('openId', openId)
            request.setAttribute('schoolId', schoolId)
            request.setAttribute('campusId', campusId)
            request.setAttribute('staffId', staffId)
            request.setAttribute('campusType', campusType)
            if (campusId && userId != 481L) {
                Campus campus = campusService.fetchCampusById(campusId)
                if (campus?.serviceStatus != 1) {
                    log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token, resultVO?.code)
                    resultVO.status = 0
                    resultVO.code = BizErrorCode.SERVICE_STOP.code
                    resultVO.message = BizErrorCode.SERVICE_STOP.msg
                    render resultVO as JSON
                    response.status = HttpServletResponse.SC_UNAUTHORIZED
                    return false
                }
            }

            dengBaoService.saveRecord(ip, staffId, visit, controllerName, actionName, params)

            //type 9 代表PC端
/*            try {
//                monitorService.recordUserReq(userId, 99 as Byte, 9 as Byte, InfluxDBService.USER_UV_TABLE)
//                monitorService.recordUserControllerReq(String.valueOf(campusId), controllerName, 99 as Byte, 9 as Byte, InfluxDBService.CONTROLLER_PV_TABLE)
            } catch (Exception) {

            }*/

            log.info("√ token parse success...... request url:" + visit + ", params: " + paramsStr + " , staffId:" + jwtUser?.staffId + ", ---token: " + jwt_token + ', userToken:' + com.alibaba.fastjson.JSON.toJSONString(jwtUser))
        } else if (jwt_token.contains("hiiAdmin")) {
            resultVO = JWTUtils.checkJwt(jwt_token)
            if (resultVO.status != 1) {
                log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token, resultVO?.code)
                resultVO.status = 0
                resultVO.code = 100
                resultVO.msg = '登录过期'
                render resultVO as JSON
                response.status = HttpServletResponse.SC_UNAUTHORIZED
                return false
            }
            JwtClaims jwtClaims = resultVO.result['jwt']
            JSONObject jwtUser = JSONObject.parseObject(jwtClaims.claimsMap.user.toString())
            request.setAttribute('jwtUser', jwtUser)
            request.setAttribute('staffId', jwtUser?.staffId)
            request.setAttribute('companyId', jwtUser?.companyId)
            request.setAttribute('bureauId', jwtUser?.bureauId)
            request.setAttribute('mobile', jwtUser?.mobile)

            dengBaoService.saveRecord(ip, (Long) (jwtUser?.staffId), visit, controllerName, actionName, params)
            log.info("√ token parse success...... request url:" + visit + ", params: " + paramsStr + " , staffId:" + jwtUser?.staffId + ", ---token: " + jwt_token + ', userToken:' + com.alibaba.fastjson.JSON.toJSONString(jwtUser))
        } else if (jwt_token.contains("DataCenter")) {
            request.setAttribute('campusId', 7L)
            log.info("√ token parse success...... request url:" + visit + ", params: " + paramsStr + ", ---token: " + jwt_token)
        } else {
            def cpResult = CtJWT.validateHiiToken(jwt_token)
            if (!cpResult.isSuccess() || null == cpResult.getResult()) {
                log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token, resultVO?.code)
                resultVO.status = 0
                resultVO.code = 100
                resultVO.msg = '无权限'
                render resultVO as JSON
                response.status = HttpServletResponse.SC_UNAUTHORIZED
                return false
            }
//            log.error("!token parse failed error... request url:{},action:{},controller:{},agent:{},customAgent:{},token:{},code:{}", visit, actionName, controllerName, request.getHeader("User-Agent"), request.getHeader("custom-agent"), jwt_token, resultVO?.code)
//            resultVO.status = 0
//            resultVO.code = 100
//            resultVO.msg = '无权限'
//            render resultVO as JSON
//            response.status = HttpServletResponse.SC_UNAUTHORIZED
//            return false
        }
        true
    }

    boolean after() { true }

    void afterView() {
        // no-op
    }

    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for")
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP")
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP")
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr()
            if (ip.equals("127.0.0.1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null
                try {
                    inet = InetAddress.getLocalHost()
                } catch (Exception e) {
                    e.printStackTrace()
                }
                ip = inet.getHostAddress()
            }
        }
        // 多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","))
            }
        }
        log.info("ip:" + ip)
        return ip
    }
}

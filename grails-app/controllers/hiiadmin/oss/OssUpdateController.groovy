package hiiadmin.oss

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler

import static grails.async.Promises.task

@Slf4j
class OssUpdateController implements BaseExceptionHandler {

    OssUpdateService ossUpdateService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Integer type = params.int("type")
        switch (type) {
            case 1:
                def p1 = task {
                    ossUpdateService.updateStudentPicToOss()
                }
                p1.onError {
                    Throwable throwable ->
                        log.info("处理学生照片至oss出错！".toString(), throwable)
                }
                p1.onComplete {
                    log.info("处理学生照片至oss成功！")
                }
                break
            case 2:
                def p2 = task {
                    ossUpdateService.updateTeacherPicToOss()
                }
                p2.onError {
                    Throwable throwable ->
                        log.info("处理老师照片至oss出错！".toString(), throwable)
                }
                p2.onComplete {
                    log.info("处理老师照片至oss成功！")
                }
                break
            case 3:
                def p3 = task {
                    ossUpdateService.updateStudentIdCardToUserEncodeInfo()
                }
                p3.onError {
                    Throwable throwable ->
                        log.info("处理学生身份证加密出错！".toString(), throwable)
                }
                p3.onComplete {
                    log.info("处理学生身份证加密成功！")
                }
                break
            case 4:
                def p4 = task {
                    ossUpdateService.updateTeacherIdCardToUserEncodeInfo()
                }
                p4.onError {
                    Throwable throwable ->
                        log.info("处理老师身份证加密出错！".toString(), throwable)
                }
                p4.onComplete {
                    log.info("处理老师身份证加密成功！")
                }
                break
            case 5:
                def p5 = task {
                    ossUpdateService.updateTeacherMobileToUserEncodeInfo()
                }
                p5.onError {
                    Throwable throwable ->
                        log.info("处理老师手机号加密出错！".toString(), throwable)
                }
                p5.onComplete {
                    log.info("处理老师手机号加密成功！")
                }
                break
            case 6:
                def p6 = task {
                    ossUpdateService.updateParentMobileToUserEncodeInfo()
                }
                p6.onError {
                    Throwable throwable ->
                        log.info("处理家长手机号加密出错！".toString(), throwable)
                }
                p6.onComplete {
                    log.info("处理家长手机号加密成功！")
                }
                break
            case 7:
                def p7 = task {
                    ossUpdateService.updateParentPicToOss()
                }
                p7.onError {
                    Throwable throwable ->
                        log.info("处理家长照片至oss出错！".toString(), throwable)
                }
                p7.onComplete {
                    log.info("处理家长照片至oss成功！")
                }
                break
            case 8:
                def p8 = task {
                    ossUpdateService.updateFerriesMobileToUserEncodeInfo()
                }
                p8.onError {
                    Throwable throwable ->
                        log.info("处理接送人手机号加密出错！".toString(), throwable)
                }
                p8.onComplete {
                    log.info("处理接送人手机号加密成功！")
                }
                break
            case 9:
                def p9 = task {
                    ossUpdateService.updateFerriesPicToOss()
                }
                p9.onError {
                    Throwable throwable ->
                        log.info("处理接送人照片至oss出错！".toString(), throwable)
                }
                p9.onComplete {
                    log.info("处理接送人照片至oss成功！")
                }
                break
            case 10:
                def p10 = task {
                    ossUpdateService.updateVisitorPicToOss()
                }
                p10.onError {
                    Throwable throwable ->
                        log.info("处理访客照片至oss出错！".toString(), throwable)
                }
                p10.onComplete {
                    log.info("处理访客照片至oss成功！")
                }
                break
            case 11:
                def p11 = task {
                    ossUpdateService.updateLeaveRecordAvatarToOss()
                }
                p11.onError {
                    Throwable throwable ->
                        log.info("处理请假照片至oss出错！".toString(), throwable)
                }
                p11.onComplete {
                    log.info("处理请假照片至oss成功！")
                }
                break
            case 12:
                def p12 = task {
                    ossUpdateService.updateVisitorMobileToUserEncodeInfo()
                }
                p12.onError {
                    Throwable throwable ->
                        log.info("处理访客手机号加密出错！".toString(), throwable)
                }
                p12.onComplete {
                    log.info("处理访客手机号加密成功！")
                }
                break
            case 13:
                def p13 = task {
                    ossUpdateService.updateVisitorIdCardToUserEncodeInfo()
                }
                p13.onError {
                    Throwable throwable ->
                        log.info("处理访客身份证号加密出错！".toString(), throwable)
                }
                p13.onComplete {
                    log.info("处理访客身份证号加密成功！")
                }
                break
            case 14:
                def p14 = task {
                    ossUpdateService.updateImageStudentAvatarToOss()
                }
                p14.onError {
                    Throwable throwable ->
                        log.info("处理学生档案照片至oss出错！".toString(), throwable)
                }
                p14.onComplete {
                    log.info("处理学生档案照片至oss成功！")
                }
                break
            case 15:
                def p15 = task {
                    ossUpdateService.updateStaffMobileToUserEncodeInfo()
                }
                p15.onError {
                    Throwable throwable ->
                        log.info("处理用户手机号加密出错！".toString(), throwable)
                }
                p15.onComplete {
                    log.info("处理用户手机号加密成功！")
                }
                break
        }
        resultVO.result.put("success", true)
        render text: JSON.toJSONString(resultVO), contentType: 'application/json;', encoding: "UTF-8"
    }
}

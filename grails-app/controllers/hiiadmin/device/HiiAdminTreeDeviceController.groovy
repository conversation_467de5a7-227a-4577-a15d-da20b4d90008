package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.ConstantEnum
import hiiadmin.docking.DockingPlatformCampusService
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.school.device.DeviceService
import timetabling.device.Device
import timetabling.docking.DockingPlatformCampus

import static hiiadmin.ConstantEnum.DeviceType

class HiiAdminTreeDeviceController {

    DeviceService deviceService

    DockingPlatformCampusService dockingPlatformCampusService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String option = params.option
        Long campusId = request.getAttribute("campusId") as Long
        switch (option) {
            case "gated":
                List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndType(campusId, DeviceType.GATE.type)
                List<DeviceVO> vos = []
                deviceList.each {
                    Device device ->
                        vos << new DeviceVO().buildVO(device)
                }
                resultVO.result.put("list", vos)
                break
            case "track":
                Byte type = params.byte("type") // 1:已对接, 否则全部
                List<DeviceVO> vos = []
                List<Device> deviceList = []
                if (type && type == 1 as byte) {
                    List<DockingPlatformCampus> dockingPlatformCampuses = dockingPlatformCampusService.fetchAllCampusPlatformByCampusId(campusId)
                    if (dockingPlatformCampuses) {
                        deviceList = deviceService.fetchAllDaHuaDeviceByFirmInListCampusId(campusId, dockingPlatformCampuses*.firm)
                    }
                    //需要另外查出班级
                    deviceList.addAll(deviceService.fetchAllDeviceByCampusIdAndType(campusId, DeviceType.CLASS_PAD.type))
                } else {
                    deviceList = deviceService.fetchAllDeviceByCampusIdAndTypeList(campusId, [DeviceType.CLASS_PAD.type, DeviceType.FACE_GATE.type, DeviceType.FACE_CAMERA.type, DeviceType.GATE.type])
                }

                deviceList.each {
                    Device device ->
                        vos << new DeviceVO().buildVO(device)
                }
                resultVO.result.put("list", vos)
                break
                // 人脸闸机
            case 'face':
                Integer firm = params.int("firm", ConstantEnum.DeviceFirm.HK_EDU.firm)
                List<Device> deviceList = deviceService.fetchAllDeviceByCampusIdAndFirmAndType(campusId, firm, DeviceType.GATE.type)
                List<DeviceVO> vos = []
                deviceList.each { Device device ->
                    vos << new DeviceVO().buildVO(device)
                }
                resultVO.result.put("list", vos)
                break
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

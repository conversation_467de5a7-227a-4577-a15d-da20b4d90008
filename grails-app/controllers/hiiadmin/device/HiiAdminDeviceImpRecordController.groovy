package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.imp.device.ImpDeviceService
import hiiadmin.module.imp.DeviceImportRecordVO
import timetabling.imp.ImpRecord

class HiiAdminDeviceImpRecordController implements BaseExceptionHandler {

    ImpDeviceService impDeviceService


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Byte type = params.byte("type")
        String fileUrl = params.fileUrl
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        Long teacherId = request.getAttribute("userId") as Long
        ImpRecord impRecord = new ImpRecord(
                schoolId: schoolId,
                campusId: campusId,
                fileUrl: fileUrl,
                teacherId: teacherId,
                type: type,
                status: ConstantEnum.ImpStatus.IMPORTING.status,
        )
        impRecord = impRecord.save(failOnError: true)
        //协议类型
        Byte protocolType = params.byte("protocolType")
        //设备种类
        Byte deviceKind = params.byte("deviceKind")
        String algs = params.algs
        //设备类型
        Byte deviceType = params.byte("deviceType")
        ServiceResult<List<DeviceImportRecordVO>> serviceResult = impDeviceService.updateSchoolAliDeviceImport(fileUrl, impRecord, protocolType, deviceKind, algs, deviceType)
        if (!serviceResult.success) {
            resultVO.status = 0
            resultVO.message = serviceResult.message
        } else {
            List<DeviceImportRecordVO> deviceImportRecordVOList = serviceResult.result
            resultVO.result.put("list", deviceImportRecordVOList)
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

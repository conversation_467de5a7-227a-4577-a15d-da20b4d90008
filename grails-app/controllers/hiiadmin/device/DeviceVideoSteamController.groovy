package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.apiCloud.AntennaDhYrApi
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @Date 2023-10-08 10:17
 */
@Slf4j
class DeviceVideoSteamController implements BaseExceptionHandler {

    @Autowired
    AntennaDhYrApi antennaDhYrApi

    def show() {
        ResultVO resultVO = new ResultVO()
        Long deviceId = params.long("id")
        resultVO = antennaDhYrApi.fetchDeviceVideoStream(deviceId)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

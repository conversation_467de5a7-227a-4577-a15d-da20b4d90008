package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.device.DeviceStatusStatisticsVO
import hiiadmin.school.device.DeviceStatusHistoryService
import org.joda.time.DateTime

/**
 * 设备状态统计控制器
 *
 * <AUTHOR>
 * @date 2025/08/15
 */
class HiiAdminDeviceStatusStatisticsController implements BaseExceptionHandler {

    DeviceStatusHistoryService deviceStatusHistoryService

    /**
     * 获取设备状态统计数据
     */
    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("deviceId")
        // 获取统计数据
        Map<String, Object> statistics = deviceStatusHistoryService.getDeviceStatusStatistics(deviceId)
        
        // 构建VO
        DeviceStatusStatisticsVO vo = DeviceStatusStatisticsVO.buildVO(statistics, null)
        
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success(vo)), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    /**
     * 获取设备时间轴数据（单独接口）
     */
    def timeline() {
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("deviceId")
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        Date startDate = new Date(startTime)
        Date endDate = new Date(endTime)

        // 获取时间轴数据
        List<Map<String, Object>> timelineData = deviceStatusHistoryService.getDeviceTimelineData(deviceId, startDate,endDate)

        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([timeline: timelineData])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import hiiadmin.BaseExceptionHandler
import hiiadmin.biz.PositionService
import hiiadmin.humanface.HumanAddDeviceService
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.module.bugu.PositionVO
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.ObjectUtils
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import io.swagger.annotations.Api
import timetabling.Position
import timetabling.device.Device

@Api(value = "相机类设备管理", tags = "设备管理", consumes = "admin")
class HiiAdminFaceDeviceController implements BaseExceptionHandler {

    DeviceService deviceService

    PositionService positionService


    HumanAddDeviceService humanAddDeviceService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Byte type = params.byte("type")
        Integer firm = params.int("firm")
        String name = params.searchName
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        String positionIdStr = params.positionId
        Long positionId = PhenixCoder.decodeId(positionIdStr)
        Byte onlineStatus = params.byte("onlineStatus")

        Boolean allCheck = params.boolean("allCheck", false)
        if (allCheck) {
            List<Device> list = deviceService.fetchAllFaceDeviceByCampusId(campusId, firm, type, onlineStatus, name, positionId)
            resultVO.result.put("list", list*.id)
            render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
            return
        }
        List<Device> list = deviceService.fetchAllFaceDeviceByCampusId(campusId, firm, type, onlineStatus, name, positionId, p, s)
        Integer count = deviceService.countFaceDeviceByCampusId(campusId, firm, type, onlineStatus, name, positionId)
        List<DeviceVO> vos = []
        list.each {
            if (it) {
                DeviceVO vo = new DeviceVO().buildVO(it)

                boolean bindThisPosition = false
                List<Position> positionList = positionService.fetchAllPositionByDeviceId(campusId, it.id)
                if (positionList) {
                    vo.positions = positionList.collect {
                        if (!bindThisPosition && it.id == positionId) {
                            bindThisPosition = true
                        }
                        new PositionVO().buildVO(it)
                    }
                    vo.positionIds = PhenixCoder.encodeIds(ToStringUnits.list2String(positionList*.id))
                    vo.positionName = ToStringUnits.list2String(positionList*.name)
                }

                vo.bindThisPosition = bindThisPosition
                vos << vo
            }
        }
        resultVO.result.put('list', vos)
        resultVO.result.put('total', count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        //平台
        Integer firm = params.int("firm")
        //设备类型
        Byte type = params.byte("type")
        //协议类型
        Byte protocolType = params.byte("protocolType")
        //设备种类
        Byte deviceKind = params.byte("deviceKind")
        String name = params.name
        String url = params.url
        Integer port = params.int("port")
        Integer fps = params.int("fps")
        Integer gpuId = params.int("gpuId", 0)
        String deviceIp = params.deviceIp
        String userName = params.userName
        String password = params.password
        //经纬度
        String address = params.address

        String algs = params.algs

        String time = params.runTimes

        String nodeMac = params.nodeMac

        String deviceNo = params.deviceNo

        Long standardId = params.long("standardId")

        Long campusId = request.getAttribute("campusId") as Long

        Long schoolId = request.getAttribute("schoolId") as Long

        boolean judgmentDevice = deviceService.judgmentDeviceNameAndIpIsExist(campusId, name, deviceIp, firm)

        if (judgmentDevice) {
            Device device = new Device(
                    firm: firm,
                    deviceLevel:1,
                    deviceIp: deviceIp,
                    deviceType: Device.DEVICE_TYPE_FACE,
                    type: type,
                    protocolType: protocolType,
                    name: name,
                    deviceName: name,
                    url: url,
                    port: port,
                    userName: userName,
                    password: password,
                    address: address,
                    campusId: campusId,
                    schoolId: schoolId,
                    deviceKind: deviceKind,
                    algs: algs,
                    runTimes: time,
                    deviceNo: deviceNo,
                    standardId: standardId,
                    status: 1 as Byte,
                    onlineStatus: 0 as Byte,
                    connectStatus: 0 as Byte,
                    nodeMac: nodeMac,
                    fps: fps,
                    gpuId: gpuId
            )
            try {
                ServiceResult<Long> result = humanAddDeviceService.saveDeviceSyn(device, nodeMac)
                if (result.success) {
                    resultVO.result.put("id", result.result)
                } else {
                    resultVO.message = result.message
                    resultVO.status = 0
                }
            } catch (Exception e) {
                resultVO.status = 0
                resultVO.message = e.message
            }
        } else {
            resultVO.message = "该名称或者ip已存在，名称和ip不能重复！"
            resultVO.status = 0
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        ServiceResult<DeviceVO> result = humanAddDeviceService.findDeviceImgByDevice(id)
        if (result.success) {
            resultVO.result.put("deviceVo", result.result)
        } else {
            resultVO.status = 0
            resultVO.message = result.result
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Byte type = params.byte('type')
        String name = params.name
        //设备场所
        String positionIdsStr =params.positionIds
        String positionIds =PhenixCoder.decodeIds(positionIdsStr)
        String model = params.model
        String algs = params.algs

        String time = params.runTimes
        //注意：阿里算法节点mac，不是设备mac
        String nodeMac = params.nodeMac

        String deviceNo = params.deviceNo

        ServiceResult<Device> result = humanAddDeviceService.updateDeviceSyn(id, type, name, positionIds, model, algs, time, nodeMac, deviceNo)
        if (result.success) {
            resultVO.result = ObjectUtils.resultVOObject2Map(new DeviceVO().buildVO(result.result))
        } else {
            resultVO.status = 0
            resultVO.message = result.message
        }

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        try {
            humanAddDeviceService.deleteDeviceByDeviceId(id)
            resultVO.result.put("id", id)
        } catch (Exception e) {
            resultVO.status = 0
            resultVO.message = e.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String deviceIds = params.deviceIds
        String positionIdsStr =params.positionIds
        String positionIds =PhenixCoder.decodeIds(positionIdsStr)
        ServiceResult result = humanAddDeviceService.batchUpdateDevicePosition(deviceIds, positionIds)
        if (!result.success) {
            resultVO.status = 0
            resultVO.message = result.message
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

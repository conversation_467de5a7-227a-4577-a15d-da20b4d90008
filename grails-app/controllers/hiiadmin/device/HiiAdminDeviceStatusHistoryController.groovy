package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.device.DeviceStatusHistoryVO
import hiiadmin.school.device.DeviceStatusHistoryService
import timetabling.device.DeviceStatusHistory


class HiiAdminDeviceStatusHistoryController implements BaseExceptionHandler {

    DeviceStatusHistoryService deviceStatusHistoryService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("deviceId")
        Long time = params.long("time")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = deviceStatusHistoryService.fetchAllDeviceStatusHistory(deviceId, time, p, s)
        List<DeviceStatusHistory> statusHistoryList = map.list
        List<DeviceStatusHistoryVO> vos = statusHistoryList.collect {
            new DeviceStatusHistoryVO().buildVO(it)
        }
        Integer total = map.total
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success([list: vos, total: total])), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.docking.yunRui.DockingAlarmEnumService
import hiiadmin.docking.yunRui.DockingDeviceAlarmControlService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.school.device.DeviceService
import timetabling.device.Device

import java.util.concurrent.TimeUnit

@Slf4j
class YrDeviceChannelCalculationController implements BaseExceptionHandler {

    DeviceService deviceService

    DockingAlarmEnumService dockingAlarmEnumService

    DockingDeviceAlarmControlService dockingDeviceAlarmControlService

    CacheService cacheService

    def update() {
        Long deviceChannelId = params.long("id")

        String calculationTypes = params.calculationTypes ?: null
        String key = "deviceChannelId${deviceChannelId}"
        boolean flag = cacheService.tryLockAndRun4lockCache(key, 30, TimeUnit.SECONDS, {
            List<Integer> calculationTypeList = calculationTypes?.split(",")?.collect { it as Integer }

            Device deviceChannel = deviceService.fetchDeviceById(deviceChannelId)

            if (calculationTypeList?.size() < 1) {
                deviceChannel.calculationBit = null
                deviceChannel.calculation = 0
                deviceService.saveDevice(deviceChannel)
                deviceService.updateDeviceDeviceUserCalculation(deviceChannel.parentId)
            } else {
                Device device = deviceService.fetchDeviceById(deviceChannel.parentId)
                Map<Integer, Integer> alarmTypeYrCalculationMap = dockingAlarmEnumService.getAllYrCalculationMap()
                Integer nowDeviceCalculation = deviceService.nowCalculation4device(deviceChannel.parentId) ?: 0
                Integer oldChannelCalculation = deviceChannel.calculation?:0,
                        newChannelCalculation = 0,
                        calculationBit = 0
                calculationTypeList.each {
                    newChannelCalculation += alarmTypeYrCalculationMap.get(it)
                    calculationBit += Math.pow(2, it - 1).intValue()
                }
                log.info("${newChannelCalculation}  ${oldChannelCalculation}  ${newChannelCalculation} ".toString())
                if ((nowDeviceCalculation - oldChannelCalculation + newChannelCalculation) > device.maxCalculation) {
                    throw new HiiAdminException("算力已超过阈值")
                } else {
                    dockingDeviceAlarmControlService.createOrUpdateDockingDeviceAlarmControl4Device(deviceChannelId, deviceChannel.serialNumber, deviceChannel.channelNo, calculationTypeList)
                    deviceChannel.calculationBit = calculationBit
                    deviceChannel.calculation = newChannelCalculation
                    deviceService.saveDevice(deviceChannel)
                    deviceService.updateDeviceDeviceUserCalculation(deviceChannel.parentId)
                }
            }
        })
        if (!flag) {
            throw new HiiAdminException("操作频繁，请稍后重试！")
        }
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

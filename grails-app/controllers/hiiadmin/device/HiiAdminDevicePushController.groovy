package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import grails.gorm.transactions.Transactional
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.school.device.DeviceService
import timetabling.device.Device
import timetabling.device.DeviceTime

import java.sql.Time

@Transactional
class HiiAdminDevicePushController implements BaseExceptionHandler {

    DeviceService deviceService

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1 as byte
        String deviceIds = params.deviceIds
        Byte type = params.byte("pushType")
        String timeMap = params.timeMap
        String content = params.content
        Byte deviceType = params.byte("deviceType", ConstantEnum.DeviceType.CLASS_PAD.type)
        if (null != deviceIds) {
            List<Long> deviceIdList = deviceIds.split(',').collect { it.toLong() }
            if (deviceIdList && deviceIdList.size() > 0) {
                List<Device> deviceList = Device.findAllByIdInList(deviceIdList)
                // 设备类型
                if (deviceList) {
                    deviceType = deviceList[0].type
                }
                List<DeviceTime> deviceTimeList = []
                //设定开关机时间
                if (timeMap) {
                    deviceList.each {
                        Device device ->
                            Map<Integer, String> mapResult = (Map) JSON.parse(timeMap)
                            for (int i = 0; i < 7; i++) {
                                String times = mapResult.get(i)
                                DeviceTime deviceTime = deviceService.fetchOrCreateDeviceTimeViaDeviceIdAndDayOfWeek(device.id, i)
                                deviceTime.status = 0 as Byte
                                if (times) {
                                    deviceTime.startTime = new Date(times.split(",")[0].toLong())
                                    deviceTime.endTime = new Date(times.split(",")[1].toLong())
                                    deviceTime.powerOnTime = new Time(times.split(",")[0].toLong())
                                    deviceTime.shutDownTime = new Time(times.split(",")[1].toLong())
                                    deviceTime.status = 1 as Byte
                                }
                                deviceTimeList.add(deviceTime)
                            }
                    }
                    deviceTimeList*.save(failOnError: true, flush: true)
                }
                //推送
                if (type) {
                    if (deviceList && deviceList.size() > 0) {
                        if (type == ConstantEnum.LeanCloudType.RESTART.type) {
                            deviceList*.restartTime = new Date()
                            deviceList*.shutdownTime = null
                            deviceList*.save(failOnError: true, flush: true)
                        } else if (type == ConstantEnum.LeanCloudType.STOP.type) {
                            deviceList*.shutdownTime = new Date()
                            deviceList*.save(failOnError: true, flush: true)
                        }
                        // 班牌操作
                        if (deviceType == ConstantEnum.DeviceType.CLASS_PAD.type) {
                            deviceService.pushDeviceShell(deviceList, type)
                        }
                    }
                }
                deviceService.iotOperationPushByDeviceListAndType(deviceList, type, content, deviceType)
            }

            resultVO.result.put("success", true)
        } else {
            resultVO.code = 200001
            resultVO.status = 0
            resultVO.message = "未选择设备"
            resultVO.msg = "未选择设备"
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

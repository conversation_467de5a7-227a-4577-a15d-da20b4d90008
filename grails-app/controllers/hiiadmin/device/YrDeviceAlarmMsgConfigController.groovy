package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.google.common.collect.Multimap
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.biz.PositionService
import hiiadmin.docking.yunRui.DeviceAlarmMsgConfigService
import hiiadmin.docking.yunRui.DockingAlarmEnumService
import hiiadmin.module.docking.dhYr.DeviceAlarmMsgConfigVO
import hiiadmin.module.docking.dhYr.DeviceChannel4AlarmMsgVO
import hiiadmin.newMenu.BgbCampusRoleService
import hiiadmin.school.TeacherService
import hiiadmin.school.device.DeviceService
import hiiadmin.userEncodeInfo.UserEncodeInfoService
import hiiadmin.utils.PhenixCoder
import timetabling.Position
import timetabling.device.Device
import timetabling.docking.yunrui.DeviceAlarmMsgConfig
import timetabling.docking.yunrui.DockingAlarmEnum
import timetabling.newMenu.BgbCampusRole
import timetabling.user.Teacher

@Slf4j
class YrDeviceAlarmMsgConfigController implements BaseExceptionHandler {

    DeviceService deviceService

    TeacherService teacherService

    BgbCampusRoleService bgbCampusRoleService

    DeviceAlarmMsgConfigService deviceAlarmMsgConfigService

    DockingAlarmEnumService dockingAlarmEnumService

    UserEncodeInfoService userEncodeInfoService

    PositionService positionService

    def index() {
        Long campusId = request.getAttribute("campusId") as Long

        Integer alarmType = params.int("alarmType"),
                p = params.int("p", 1),
                s = params.int("s", 30)

        Integer firm = params.int("firm")
        Byte type = params.byte("type")
        String positionIdStr = params.positionId
        Long positionId = PhenixCoder.decodeId(positionIdStr)
        String searchValue = params.searchValue
        List<Device> deviceList = []
        Integer total = 0
        //设备和终端合并处理,查所有设备
        if (alarmType == ConstantEnum.TrackAlarmEventType.DEVICE_OFFLINE.event) {
//            List<Device> devices = deviceService.fetchAllDeviceByCampusIdAndFirmAndType(campusId, ConstantEnum.DeviceFirm.DH_YR.firm, ConstantEnum.DeviceType.EDGE_BOX.type) ?: []
//            Integer mergeTotal = deviceService.countDevice4ClientDeviceLevel(campusId, ConstantEnum.DeviceFirm.DH_YR.firm, ConstantEnum.DeviceType.EDGE_BOX.type) ?: 0
            deviceList = deviceService.fetchAllDevice(campusId,firm,null,type,null,searchValue,positionId, null,p, s)
            total = deviceService.countDevice(campusId,firm,null,type,null,searchValue,positionId,null)
        } else {
            deviceList = deviceService.fetchAllDevice(campusId,null,null,null,null,searchValue,positionId, 2,p, s)
            total = deviceService.countDevice(campusId,null,null,null,null,searchValue,positionId,2)
        }

        DockingAlarmEnum alarmEnum = dockingAlarmEnumService.fetchDockingAlarmEnumByType(alarmType)


        Multimap<Long, Position> deviceIdPositionMap = positionService.transformPositionDeviceMap(campusId)
        List<DeviceChannel4AlarmMsgVO> alarmMsgVOList = deviceList.collect { channel ->
            DeviceChannel4AlarmMsgVO channel4AlarmMsgVO = new DeviceChannel4AlarmMsgVO().buildVO(channel)
            channel4AlarmMsgVO.alarmName = alarmEnum?.name
            channel4AlarmMsgVO.type = channel.type
            List<Position> positionList = deviceIdPositionMap.get(channel.id).toList()
            channel4AlarmMsgVO.positionName = positionList*.name?.join(",")
            List<DeviceAlarmMsgConfig> msgConfigList = []
            if (alarmType == ConstantEnum.TrackAlarmEventType.DEVICE_OFFLINE.event) {
                //设备和终端合并处理
                msgConfigList = deviceAlarmMsgConfigService.fetchAllDeviceAlarmMsgConfigByDeviceIdAndAlarmTypeList(channel.id, [alarmType, ConstantEnum.TrackAlarmEventType.TERMINAL_OFFLINE.event])
            } else {
                msgConfigList = deviceAlarmMsgConfigService.fetchAllDeviceAlarmMsgConfigByDeviceIdAndAlarmType(channel.id, alarmType)
            }
            msgConfigList.each { msgConfig ->
                if (msgConfig.alarmUserId) {
                    Teacher teacher = teacherService.fetchTeacherById(msgConfig.alarmUserId)
                    if (teacher?.status == 1) {
                        String mobile = userEncodeInfoService.fetchDecodeInfoByTypeAndUserTypeAndUserId(ConstantEnum.UserEncodeInfoType.MOBILE.type,ConstantEnum.UserTypeEnum.TEACHER.type,teacher.id)
                        DeviceAlarmMsgConfigVO configVO = new DeviceAlarmMsgConfigVO(
                                alarmType: alarmType,
                                alarmUserId: PhenixCoder.encodeId(msgConfig.alarmUserId),
                                alarmUserName: teacher.name,
                                mobile: mobile,
                                type: msgConfig.type
                        )
                        
                        if (msgConfig.type == 1 || msgConfig.type == null) {
                            channel4AlarmMsgVO.alarm4users << configVO
                        } else {
                            channel4AlarmMsgVO.handleAlarm4users << configVO
                        }
                        
                    }
                } else if (msgConfig.alarmUserRoleId) {
                    BgbCampusRole bgbCampusRole = bgbCampusRoleService.fetchBgbCampusRoleById(msgConfig.alarmUserRoleId)
                    if (bgbCampusRole) {
                        DeviceAlarmMsgConfigVO configVO = new DeviceAlarmMsgConfigVO(
                                alarmType: alarmType,
                                alarmUserRoleId: msgConfig.alarmUserRoleId,
                                alarmUserRoleName: bgbCampusRole.name,
                                type: msgConfig.type
                        )
                        if (msgConfig.type == 1 || msgConfig.type == null) {
                            channel4AlarmMsgVO.alarm4roles << configVO
                        } else {
                            channel4AlarmMsgVO.handleAlarm4roles << configVO
                        }
                    }
                }
            }
            channel4AlarmMsgVO
        }
        ResultVO resultVO = ResultVO.success([list: alarmMsgVOList, total: total])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long campusId = request.getAttribute("campusId") as Long

        Integer alarmType = params.int("alarmType")
        String channelIds = params.channelIds ?: null,
               userIds = params.userIds ?: null,
               userRoleIds = params.userRoleIds ?: null,
               handleUserIds = params.handleUserIds ?: null,
               handleRoleIds = params.handleRoleIds ?: null

        List<Long> channelIdList = channelIds?.split(",")?.collect { it as Long },
                   userIdList = userIds?.split(",")?.collect { PhenixCoder.decodeId(it) },
                   userRoleIdList = userRoleIds?.split(",")?.collect { it as Long },
                   handleUserIdList = handleUserIds?.split(",")?.collect { PhenixCoder.decodeId(it) },
                   handleRoleIdList = handleRoleIds?.split(",")?.collect { PhenixCoder.decodeId(it) }
        deviceAlarmMsgConfigService.updateDeviceAlarmMsgConfig(campusId, alarmType, channelIdList, userIdList, userRoleIdList, handleUserIdList, handleRoleIdList)
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

}

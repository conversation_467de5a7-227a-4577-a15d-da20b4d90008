package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.RCConstantEnum
import hiiadmin.apiCloud.AntennaDhYrApi
import hiiadmin.apiCloud.BgbDockingApi
import hiiadmin.biz.PositionService
import hiiadmin.docking.yunRui.DockingDeviceAlarmControlService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.listen.event.device.DeviceChangeEvent
import hiiadmin.module.device.YrDeviceChannelVO
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.YrDeviceService
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import org.springframework.beans.factory.annotation.Autowired
import timetabling.Position
import timetabling.device.Device
import timetabling.docking.yunrui.DockingDeviceAlarmControl

import static hiiadmin.ConstantEnum.*
import static hiiadmin.utils.PatternUtils.*

class YrDeviceChannelController implements BaseExceptionHandler {

    @Autowired
    AntennaDhYrApi antennaDhYrApi

    DeviceService deviceService

    YrDeviceService yrDeviceService

    DockingDeviceAlarmControlService dockingDeviceAlarmControlService

    PositionService positionService

    @Autowired
    BgbDockingApi bgbDockingApi

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long

        Long deviceId = params.long("deviceId")
        String positionIdStr = params.positionId
        Long positionId = PhenixCoder.decodeId(positionIdStr)
        Byte onlineStatus = params.byte("onlineStatus")
        Byte controlStatus = params.byte("controlStatus")
        Integer calculationType = params.int("calculationType")
        String searchValue = params.searchName
        int p = params.int("p", 1),
            s = params.int("s", 30)
        Boolean allCheck = params.boolean("allCheck", false)

        List<Device> deviceList = []
        Integer total = 0
        if (deviceId) {
            if (allCheck) {
                def map = yrDeviceService.fetchAllChannelByDeviceId(campusId, deviceId, searchValue, positionId, onlineStatus, controlStatus, calculationType)
                deviceList = map.list
                List<YrDeviceChannelVO> vos = deviceList.collect { device ->
                    new YrDeviceChannelVO().buildVO(device)
                }
                resultVO.result.put("list", vos)
                render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
                return
            }
            def map = yrDeviceService.fetchChannelByDeviceIdLimit(campusId, deviceId, searchValue, positionId, onlineStatus, controlStatus, calculationType, p, s)
            deviceList = map.list
            total = map.total
        } else {
            deviceList = deviceService.fetchAllChannelByCampusId(campusId)
            total = deviceList?.size()
        }

        List<YrDeviceChannelVO> vos = deviceList.collect { device ->
            YrDeviceChannelVO channelVO = new YrDeviceChannelVO().buildVO(device)
            channelVO.controlStatus = 0
            List<DockingDeviceAlarmControl> controlList = dockingDeviceAlarmControlService.fetchAllDockingDeviceAlarmControlByDeviceId(device.id)
            controlList.each {
                if (it.dockingTaskId) {
                    channelVO.controlStatus = 1
                }
            }
            List<Position> positionList = positionService.fetchAllPositionByDeviceId(campusId, device.id)
            if (positionList) {
                List<Long> positionIdList = positionList*.id
                if (positionIdList) {
                    channelVO.positionIds = positionIdList.collect {PhenixCoder.encodeId(it)}.join(",")
                }
                channelVO.positionName = ToStringUnits.list2String(positionList*.name)
            }
            channelVO
        }
        resultVO = ResultVO.success([list: vos, total: total])
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def save() {
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = request.getAttribute("campusId") as Long

        Long deviceId = params.long("deviceId")
        //设备编号
        String serialNumber = params.serialNumber

        if (!deviceId) {
            throw new HiiAdminException("请选择设备终端")
        }

        String ip = params.ip,
               username = params.username,
               password = params.password,
               name = params.name

        Byte protocolType = params.byte("protocolType")
        String protocolName = params.protocolName
        if (!protocolType && !protocolName) {
            throw new HiiAdminException("请选择协议类型")
        }
        String protocol
        if (protocolName) {
            protocol = protocolName
            protocolType = ProtocolEnumType.getEnumByName(protocolName).type
        } else if (protocolType) {
            protocol = ProtocolEnumType.getEnumByType(protocolType).name
        }

        if (protocolType == ProtocolEnumType.GB28181.type && !serialNumber?.number && serialNumber.size() != 20) {
            throw new HiiAdminException("请输入正确的Ip或设备编码")
        } else if (protocolType == ProtocolEnumType.GB28181.type) {
            ip = serialNumber
        }

        if (ip && !isIp(ip) && protocolType != ProtocolEnumType.GB28181.type) {
            throw new HiiAdminException("请输入正确的Ip或设备编码")
        }

        if (name && !isSpecial(name)) {
            throw new HiiAdminException("设备名称仅支持汉字、字母、数字")
        }
        if (name && name.length() > 20) {
            throw new HiiAdminException("设备名称不能超过20个字符")
        }
        Device device = deviceService.fetchDeviceById(deviceId)
        if (device.firm != DeviceFirm.DH_YR.firm) {
            throw new HiiAdminException("平台不支持")
        }
        if (deviceService.exitDeviceName4CampusId4Yr(campusId, name, null)) {
            throw new HiiAdminException("设备名称已存在")
        }
        Integer port = params.int("port", 80),
                rtspPort = params.int("rtspPort"),
                channel = deviceService.getDefectChannel(deviceId)

        yrDeviceService.checkComputing(campusId, DeviceType.EDGE_BOX.type.intValue(), 1)
        JSONObject jsonObject = new JSONObject([deviceId    : device.serialNumber,
                                                username    : username,
                                                password    : password,
                                                protocolType: protocol,
                                                ip          : ip,
                                                port        : port,
                                                rtspPort    : rtspPort,
                                                channel     : channel])
        ResultVO resultVO = antennaDhYrApi.channelAdd(jsonObject)
        if (resultVO.status == 1) {
            if (!name) {
                name = device.name + channel
            }
            Device deviceChannel = new Device(
                    schoolId: schoolId,
                    campusId: campusId,
                    name: name,
                    serialNumber: device.serialNumber,
                    ip: ip,
                    port: port,
                    rtspPort: rtspPort,
                    protocolType: protocolType,
                    userName: username,
                    password: password,
                    firm: device.firm,
                    deviceLevel: 2,
                    channelNo: channel,
                    status: 1,
                    parentId: deviceId,
                    deviceType: Device.DEVICE_TYPE_FACE,
                    type: DeviceType.FACE_CAMERA.type
            )
            deviceService.saveDevice(deviceChannel)
            deviceService.updateDeviceDeviceUserCalculation(deviceId)
            try {
                antennaDhYrApi.syncDeviceInfoLine(deviceChannel.serialNumber)
            } catch (Exception e) {
                log.error("syncDeviceInfoLine ${deviceChannel.serialNumber}".toString(), e)
            }
            bgbDockingApi.refreshResourceComputing(campusId, new com.alibaba.fastjson2.JSONObject(computingType: RCConstantEnum.ComputingType.creamer.name()))
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        Long channelId = params.int("id")

        String ip = params.ip,
               username = params.username,
               password = params.password,
               indexCode = params.indexCode,
               name = params.name
        Byte protocolType = params.byte("protocolType")
        //设备编号
        String serialNumber = params.serialNumber
        if (!isName(name)) {
            throw new HiiAdminException("设备名称仅支持汉字、字母、数字")
        }

        if (ip && !isIp(ip) && protocolType != ProtocolEnumType.GB28181.type) {
            throw new HiiAdminException("请输入正确的Ip或设备编码")
        }

        if (protocolType == ProtocolEnumType.GB28181.type) {
            if (!serialNumber) {
                throw new HiiAdminException("请输入正确的设备编码")
            }
            ip = serialNumber
        }

        Integer port = params.int("port", 80),
                rtspPort = params.int("rtspPort")
        Device deviceChannel = deviceService.fetchDeviceById(channelId)
        String protocol = ProtocolEnumType.getEnumByType(protocolType).name()
        JSONObject jsonObject = new JSONObject([deviceId    : deviceChannel.serialNumber,
                                                username    : username,
                                                password    : password,
                                                protocolType: protocol,
                                                ip          : ip,
                                                port        : port,
                                                rtspPort    : rtspPort,
                                                channel     : deviceChannel.channelNo])
        ResultVO resultVO = antennaDhYrApi.channelAdd(jsonObject)
        if (resultVO.status == 1) {
            deviceChannel.name = name
            deviceChannel.ip = ip
            deviceChannel.userName = username
            deviceChannel.password = password
            deviceChannel.protocolType = protocolType
            deviceChannel.indexCode = indexCode
            deviceChannel.port = port
            deviceChannel.rtspPort = rtspPort
            deviceService.saveDevice(deviceChannel)

            deviceService.updateDeviceDeviceUserCalculation(deviceChannel.parentId)
            try {
                antennaDhYrApi.syncDeviceInfoLine(deviceChannel.serialNumber)
            } catch (Exception e) {
                log.error("syncDeviceInfoLine ${deviceChannel.serialNumber}".toString(), e)
            }
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        Long channelId = params.int("id")
        Device channel = deviceService.fetchDeviceById(channelId)
        YrDeviceChannelVO vo = new YrDeviceChannelVO().buildVO(channel)

        List<Position> positionList = positionService.fetchAllPositionByDeviceId(channel.campusId, channel.id)
        if (positionList) {
            vo.positionIds = ToStringUnits.list2String(positionList*.id)
            vo.positionName = ToStringUnits.list2String(positionList*.name)
        }
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success(vo)), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def patch() {
        Long channelId = params.long("id")
        Device device = deviceService.fetchDeviceById(channelId)
        ResultVO resultVO = antennaDhYrApi.deviceSnapshotPath(new JSONObject([deviceId: device.serialNumber,
                                                                              channel : device.channelNo]))

        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }


    def delete() {
        ResultVO resultVO = new ResultVO()
        Long campusId = request.getAttribute("campusId") as Long
        String deviceChannelIds = params.ids
        List<Long> deviceChannelIdList = ToStringUnits.idsString2LongList(deviceChannelIds)
        Set<Long> deviceIdList = []
        Set<Long> channelIdList = []
        try {
            deviceChannelIdList.each {
                Device deviceChannel = deviceService.fetchDeviceById(it)

                if (dockingDeviceAlarmControlService.existDockingTaskIdDockingDeviceAlarmConfig4Device(deviceChannel.id)) {
                    throw new HiiAdminException("请先撤回通道的已布控告警")
                }
                if (deviceChannel.firm != DeviceFirm.DH_YR.firm) {
                    throw new HiiAdminException("平台不支持")
                }
                resultVO = antennaDhYrApi.channelDel([deviceId: deviceChannel.serialNumber,
                                                      channel : deviceChannel.channelNo])
                if (resultVO.status != 1) {
                    throw new HiiAdminException(resultVO.message)
                }
                deviceChannel.status = 0
                deviceService.saveDevice(deviceChannel)
                deviceIdList << deviceChannel.parentId
                channelIdList << it
            }
        } catch (Exception e) {
            throw e
        } finally {
            deviceIdList.each {
                deviceService.updateDeviceDeviceUserCalculation(it)
            }
            channelIdList.each {
                DeviceChangeEvent changeEvent = new DeviceChangeEvent(this, it, OperateType.DEL.type)
                applicationContext.publishEvent(changeEvent)
            }
        }

        try {
            bgbDockingApi.refreshResourceComputing(campusId, new com.alibaba.fastjson2.JSONObject(computingType: RCConstantEnum.ComputingType.creamer.name()))
            bgbDockingApi.refreshResourceComputing(campusId, new com.alibaba.fastjson2.JSONObject(computingType: RCConstantEnum.ComputingType.guard.name()))
        } catch (Exception e) {
            log.error("refreshResourceComputing ${campusId}".toString(), e)
        }
        withFormat {
            json { render text: JSON.toJSONString(ResultVO.success()), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

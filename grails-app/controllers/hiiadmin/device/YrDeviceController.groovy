package hiiadmin.device

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.RCConstantEnum
import hiiadmin.apiCloud.AntennaDhYrApi
import hiiadmin.apiCloud.BgbDockingApi
import hiiadmin.biz.PositionService
import hiiadmin.docking.yunRui.DeviceAlarmMsgConfigService
import hiiadmin.docking.yunRui.DockingDeviceAlarmConfigService
import hiiadmin.docking.yunRui.DockingDeviceAlarmControlService
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.listen.event.device.DeviceChangeEvent
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.module.device.YrDeviceChannelVO
import hiiadmin.module.device.YrDeviceVO
import hiiadmin.school.device.DeviceService
import hiiadmin.school.device.YrDeviceService
import hiiadmin.utils.PatternUtils
import hiiadmin.utils.ToStringUnits
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import timetabling.Position
import timetabling.device.Device
import timetabling.docking.yunrui.DockingDeviceAlarmConfig

import static hiiadmin.ConstantEnum.DeviceType
import static hiiadmin.ConstantEnum.OperateType

@Slf4j
class YrDeviceController implements BaseExceptionHandler {

    DeviceService deviceService

    YrDeviceService yrDeviceService

    DockingDeviceAlarmConfigService dockingDeviceAlarmConfigService

    DockingDeviceAlarmControlService dockingDeviceAlarmControlService

    PositionService positionService

    DeviceAlarmMsgConfigService deviceAlarmMsgConfigService

    @Autowired
    ApplicationContext applicationContext

    @Autowired
    AntennaDhYrApi antennaDhYrApi

    @Autowired
    BgbDockingApi bgbDockingApi

    def index() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer p = params.int("p", 1),
                s = params.int("s", 30)

        Byte type = params.byte("type"),
             online = params.byte('onlineStatus')
        Byte controlStatus = params.byte("controlStatus")
        Integer firm = params.int("firm", ConstantEnum.DeviceFirm.DH_YR.firm)

        List<Device> deviceList = deviceService.fetchAllDeviceByCampusId4Yr(campusId, type, firm, null, p, s)
        Integer count = deviceService.countDeviceByCampusId4Yr(campusId, type, firm, null)

        List<YrDeviceVO> vos = deviceList.collect { device ->
            YrDeviceVO deviceVO = new YrDeviceVO().buildVO(device)
            if (device.type == DeviceType.EDGE_BOX.type) {

                def map = yrDeviceService.fetchAllChannelByDeviceId(campusId, device.id, null, null, online, controlStatus, null)
                List<Device> channelList = map.list
                deviceVO.channels = channelList.collect { channel ->

                    List<DockingDeviceAlarmConfig> configList = dockingDeviceAlarmConfigService.fetchAllDockingDeviceAlarmConfigByDeviceId(channel.id)
                    Integer configNum = configList.size()
                    Integer controlNum = configList.findAll { it.controlStatus }.size()
                    YrDeviceChannelVO channelVO = new YrDeviceChannelVO().buildVO(channel)
                    channelVO.configNum = configNum
                    channelVO.controlNum = controlNum
                    channelVO.controlStatus = 0
                    if (controlNum > 0) {
                        channelVO.controlStatus = 1
                    }

                    channelVO
                }
            }
            deviceVO
        }
        ResultVO resultVO = ResultVO.success([list: vos, total: count])

        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }


    def save() {
        Long campusId = request.getAttribute("campusId") as Long
        Integer type = params.int("type", DeviceType.GATE.type)

        String serialNumber = params.serialNumber,
               name = params.name,
               username = params.username,
               password = params.password
        if (!serialNumber) {
            throw new HiiAdminException("终端序列号不能为空")
        }
        if (!PatternUtils.isName(name)) {
            throw new HiiAdminException("终端名称仅支持汉字、字母、数字")
        }
        if (deviceService.exitDeviceName4CampusId4Yr(campusId, name, null)) {
            throw new HiiAdminException("设备名称已存在")
        }
        if (serialNumber && deviceService.exitDeviceSn4CampusId4Yr(serialNumber, null)) {
            throw new HiiAdminException("该序列号终端已被绑定")
        }
        yrDeviceService.checkComputing(campusId, type, 1)
        ResultVO resultVO = deviceService.saveDeviceAndSyncDHyunRui(campusId, name, serialNumber, username, password, type, 1)

        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.long("id")
        String name = params.name

        if (!PatternUtils.isName(name)) {
            throw new HiiAdminException("终端名称仅支持汉字、字母、数字")
        }
        if (deviceService.exitDeviceName4CampusId4Yr(campusId, name, deviceId)) {
            throw new HiiAdminException("设备名称已存在")
        }

        Device device = deviceService.fetchDeviceById(deviceId)
        device.name = name
        deviceService.saveDevice(device)
        DeviceVO vo = new DeviceVO().buildVO(device)
        resultVO.result = vo
        resultVO.status = 1

        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long deviceId = params.int("id")
        Device device = deviceService.fetchDeviceById(deviceId)
        YrDeviceVO vo = new YrDeviceVO().buildVO(device)
        if (device.type == DeviceType.EDGE_BOX.type) {
            List<Device> deviceList = deviceService.fetchAllChannelByDeviceId(deviceId)

            vo.channels = deviceList.collect {
                YrDeviceChannelVO channelVO = new YrDeviceChannelVO().buildVO(it)
                List<Position> positionList = positionService.fetchAllPositionByDeviceId(campusId, it.id)
                if (positionList) {
                    channelVO.positionIds = ToStringUnits.list2String(positionList*.id)
                    channelVO.positionName = ToStringUnits.list2String(positionList*.name)
                }

                channelVO
            }

        }
        resultVO.result = vo

        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        Long campusId = request.getAttribute("campusId") as Long
        String deviceIds = params.ids
        List<Long> deviceIdList = ToStringUnits.idsString2LongList(deviceIds)
        List<Device> deviceList = deviceService.fetchAllDeviceByIdInList(deviceIdList)

        Set<Long> parentDeviceIdSet = []
        for (Device device in deviceList) {
            if (device.type == DeviceType.EDGE_BOX.type) {
                if (dockingDeviceAlarmControlService.existDockingTaskIdDockingDeviceAlarmConfig4Device(device.id)) {
                    throw new HiiAdminException("请先撤回通道的已布控告警")
                }
            } else if (device.type == DeviceType.GPY.type) {
                if (device.action == 1) {
                    throw new HiiAdminException("设备正在使用中，不可删除")
                }
            }
            resultVO = antennaDhYrApi.deviceDel([deviceId: device.serialNumber])
            log.info(JSON.toJSONString(resultVO))
            if (resultVO.status != 1) {
                throw new HiiAdminException(resultVO.message)
            }
            deviceService.deleteDeviceByDeviceId(device.id)
            if (device.parentId) {
                parentDeviceIdSet.add(device.parentId)
            }

            DeviceChangeEvent changeEvent = new DeviceChangeEvent(this, device.id, OperateType.DEL.type)
            applicationContext.publishEvent(changeEvent)
        }

        parentDeviceIdSet.each {
            deviceService.updateDeviceDeviceUserCalculation(it)
        }
        try {
            bgbDockingApi.refreshResourceComputing(campusId, new JSONObject(computingType: RCConstantEnum.ComputingType.creamer.name()))
            bgbDockingApi.refreshResourceComputing(campusId, new JSONObject(computingType: RCConstantEnum.ComputingType.guard.name()))
        } catch (Exception e) {
            log.error("refreshResourceComputing ${campusId}".toString(), e)
        }
        deviceAlarmMsgConfigService.deleteDeviceAlarmMsgConfigByDeviceIds(deviceIdList)
        return render(text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8")
    }

}

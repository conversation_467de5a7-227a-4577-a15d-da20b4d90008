package hiiadmin.device

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import com.bugu.ServiceResult
import grails.async.Promises
import hiiadmin.BaseExceptionHandler
import hiiadmin.ConstantEnum
import hiiadmin.apiCloud.AntennaHkIsupApi
import hiiadmin.exceptions.AlreadyExistException
import hiiadmin.module.bugu.DeviceTimeVO
import hiiadmin.module.bugu.DeviceVO
import hiiadmin.school.device.DeviceService
import hiiadmin.utils.ObjectUtils
import io.swagger.annotations.Api
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import timetabling.device.Device
import timetabling.device.DeviceTime

@Deprecated
@Api(value = "设备管理", tags = "基础管理", consumes = "admin")
class HiiAdminDeviceController implements BaseExceptionHandler {

    DeviceService deviceService

    @Autowired
    AntennaHkIsupApi antennaHkIsupApi

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long("campusId")
        Byte type = params.byte('deviceType')
        Byte netStatus = params.byte('netStatus')
        int p = params.int('p', 1)
        int s = params.int('s', 30)
        def list
        Integer count = 0
        if (null == netStatus) {
            list = deviceService.fetchAllDeviceBySchoolIdAndCampusIdLimit(schoolId, campusId, type, p, s)
            count = list?.totalCount
        } else {
            list = deviceService.fetchAllDeviceBySchoolIdAndCampusId(schoolId, campusId, type)
        }
        List<DeviceVO> deviceVOList = []
        List<Long> deviceIdList = []
        list.each {
            Device device ->
                deviceIdList << device.id
                DeviceVO deviceVO = new DeviceVO().buildVO(device)
                Integer dayIndex = new DateTime().getDayOfWeek() - 1
                DeviceTime deviceTime = deviceService.fetchOrCreateDeviceTimeViaDeviceIdAndDayOfWeek(device.id, dayIndex)
                List<DeviceTime> deviceTimeList = deviceService.fetchAllDeviceTimeViaDeviceId(device.id)
                List<DeviceTimeVO> timeList = deviceTimeList.collect { new DeviceTimeVO().buildVO(it) }
                deviceVO.timeList = timeList
                deviceVOList.add(deviceVO)
        }
        if (null != netStatus) {
            count = deviceVOList.size() ?: 0
            if ((p - 1) * s > count) {
                deviceVOList = []
            } else {
                deviceVOList?.subList((p - 1) * s, count < p * s ? count : p * s)
            }
        }
        Map<Long, String> longStringMap = deviceService.getDeviceIdDoorFullNameByDeviceIdInList(deviceIdList)
        deviceVOList?.each {
            DeviceVO deviceVO ->
                deviceVO.doorPlateName = longStringMap.get(deviceVO.id)
        }
        resultVO.result.put('list', deviceVOList)
        resultVO.result.put('total', count)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long("id")
        Long campusId = request.getAttribute("campusId") as Long
        ServiceResult<DeviceVO> result = deviceService.getDeviceDetails(id, campusId)
        if (result.success) {
            resultVO.result.put("deviceVo", result.result)
        } else {
            resultVO.status = 0
        }
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }


    }


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long schoolId = request.getAttribute("schoolId") as Long
        Long campusId = params.long("campusId")
        Byte type = params.byte('type')
        String name = params.name
        String serialNumber = params.serialNumber
        String mac = params.mac
        String memo = params.memo
        Device device = deviceService.createAndSavePadDevice(schoolId, campusId, type, name, serialNumber, mac, memo)
        resultVO.result = ObjectUtils.resultVOObject2Map(new DeviceVO().buildVO(device))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        Byte type = params.byte('type')
        String name = params.name
        String serialNumber = params.serialNumber
        String mac = params.mac
        String memo = params.memo
        Long doorPlateId = params.long('doorPlateId')
        Device device = deviceService.fetchDeviceById(id)
        Long campusId = device.campusId
        if (name) {
            Device d = deviceService.fetchDeviceByCampusIdAndName(campusId, name)
            if (d && d.id != id) {
                throw new AlreadyExistException("校区内设备名称已存在")
            }
            device.name = name
        }
        if (serialNumber) {
            Device d = deviceService.fetchDeviceBySerialNumber(serialNumber)
            if (d && d.id != id) {
                throw new AlreadyExistException("设备编号已存在")
            }
            device.serialNumber = serialNumber
        }
        if (mac) {
            Device d = deviceService.fetchDeviceByMac(mac)
            if (d && d.id != id) {
                throw new AlreadyExistException("mac地址已存在")
            }
            device.mac = mac
        }
        if (memo) {
            device.memo = memo
        }
        if (type) {
            device.type = type
        }
        if (doorPlateId) {
            deviceService.fetchOrCreateByDeviceIdAndDoorPlateId(id, doorPlateId)
        }
        deviceService.saveDevice(device)
        
        if (device && device.firm == ConstantEnum.DeviceFirm.HK_ISUP.firm && name) {
            Promises.task {
                antennaHkIsupApi.syncDeviceName([campusId: campusId, serial: device.indexCode, name: name])
            }
        }
        
        resultVO.result = ObjectUtils.resultVOObject2Map(new DeviceVO().buildVO(device))
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long id = params.long('id')
        deviceService.deleteDeviceByDeviceId(id)
        resultVO.result.put("id", id)
        withFormat {
            json { render text: JSON.toJSONString(resultVO), contentType: 'application/json', encoding: "UTF-8" }
        }
    }
}

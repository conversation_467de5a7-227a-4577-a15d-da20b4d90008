package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.store.StoreClassMapper
import hiiadmin.mapstruct.store.StoreGoodsMapper
import hiiadmin.module.store.StoreClassVO
import hiiadmin.module.store.StoreGoodsVO
import hiiadmin.utils.PhenixCoder
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.StoreClass
import timetabling.store.StoreGoods


@Deprecated
class HiiAdminStoreGoodsController implements BaseExceptionHandler {


    StoreClassService storeClassService

    StoreGoodsService storeGoodsService

    StoreGoodsOperateService storeGoodsOperateService

    @Autowired
    StoreGoodsMapper storeGoodsMapper

    @Autowired
    StoreClassMapper storeClassMapper


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        // 未分组 传 v2FHT
        String classIdStr = params.classId
        Long classId = PhenixCoder.decodeId(classIdStr)
        String searchValue = params.searchValue

        int p = params.int('p', 1)
        int s = params.int('s', 30)
        Boolean all = params.boolean("all")
        if (all) {
            List<StoreClass> storeClassList = storeClassService.fetchAllStoreClass(campusId)
            List<StoreClassVO> storeClassVOS = []
            storeClassList.each {
                StoreClassVO storeClassVO = storeClassMapper.convert2StoreClassVO(it)
                List<StoreGoods> storeGoodsList = storeGoodsService.fetchAllGoodsByClassId(campusId, it.id)
                List<StoreGoodsVO> storeGoodsVOList = storeGoodsList.collect {
                    StoreGoodsVO storeGoodsVO = storeGoodsMapper.convert2StoreGoodsVO(it)
                    storeGoodsVO
                }
                storeClassVO.storeGoodsVOList = storeGoodsVOList
                storeClassVOS << storeClassVO
            }
            List<StoreGoods> storeGoodsList = storeGoodsService.fetchAllGoodsByClassId(campusId, -1)
            if (storeGoodsList && storeGoodsList.size() > 0) {
                StoreClassVO storeClassVO = new StoreClassVO(
                        id: PhenixCoder.encodeId(-1),
                        name: "未分组"
                )
                storeClassVO.storeGoodsVOList = storeGoodsList.collect {
                    storeGoodsMapper.convert2StoreGoodsVO(it)
                }
                storeClassVOS << storeClassVO
            }
            resultVO.result.put("list", storeClassVOS)
        } else {
            def map = storeGoodsService.fetchGoodsLimit(campusId, classId, searchValue, p, s)
            List<StoreGoodsVO> storeGoodsVOList = map.list.collect {
                storeGoodsMapper.convert2StoreGoodsVO(it)
            }
            resultVO.result.put("list", storeGoodsVOList)
            resultVO.result.put("total", map.total)
        }


        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(id)
        StoreGoodsVO storeGoodsVO = storeGoodsMapper.convert2StoreGoodsVO(storeGoods)
        resultVO.result = storeGoodsVO
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String classIdStr = params.classId
        Long classId = PhenixCoder.decodeId(classIdStr)
        String name = params.name
        String specification = params.specification
        Integer num = params.int("num")
        String unitOfMeasurement = params.unitOfMeasurement
        if(StringUtils.isEmpty(unitOfMeasurement)){
            unitOfMeasurement = "个"
        }
        String unitPriceStr = params.unitPrice
        BigDecimal unitPrice = new BigDecimal(unitPriceStr)
        String priceStr = params.price
        BigDecimal price = new BigDecimal(priceStr)

        if (classId == -1) {
            classId = null
        }

        StoreGoods storeGoods = storeGoodsService.createAndSaveStoreGoods(campusId, classId, name, specification, num, unitOfMeasurement, unitPrice, price)
        StoreGoodsVO storeGoodsVO = storeGoodsMapper.convert2StoreGoodsVO(storeGoods)
        resultVO.result = storeGoodsVO

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        String name = params.name
        String specification = params.specification
        Integer num = params.int("num")
        String unitOfMeasurement = params.unitOfMeasurement
        if(StringUtils.isEmpty(unitOfMeasurement)){
            unitOfMeasurement = "个"
        }
        String unitPriceStr = params.unitPrice
        BigDecimal unitPrice = new BigDecimal(unitPriceStr)
        String priceStr = params.price
        BigDecimal price = new BigDecimal(priceStr)
        StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(id)
        storeGoods.name = name
        storeGoods.specification = specification
        storeGoods.num = num
        storeGoods.unitOfMeasurement = unitOfMeasurement
        storeGoods.unitPrice = unitPrice
        storeGoods.price = price
        storeGoodsService.save(storeGoods)
        StoreGoodsVO storeGoodsVO = storeGoodsMapper.convert2StoreGoodsVO(storeGoods)
        resultVO.result = storeGoodsVO

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        if (storeGoodsOperateService.existGoodsWailOutById(id)) {
            throw new HiiAdminException("该物品部分待出库，无法移除")
        }
        if (storeGoodsOperateService.existGoodsWaitInById(id)) {
            throw new HiiAdminException("该物品部分待入库，无法移除")
        }
        storeGoodsService.deleteStoreGoods(id)
        resultVO.result.put("id", id)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    //领用
    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String idStr = params.id
        Long goodsId = PhenixCoder.decodeId(idStr)
        Integer num = params.int("num")
        Long userId = params.long("userId")
        StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(goodsId)
        if (num > storeGoods.num) {
            throw new HiiAdminException("领用数量超出库存，无法领用")
        }
        storeGoodsOperateService.receive(campusId, goodsId, num, userId)
        resultVO.result.put("id", goodsId)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

}

package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.PropertyListField

//已更换为通用的listField
@Deprecated
class HiiAdminPropertyListFieldController implements BaseExceptionHandler {

    PropertyListFieldService propertyListFieldService


    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        PropertyListField propertyListField = propertyListFieldService.fetchPropertyListFieldByCampusId(campusId)
        resultVO.result.put("json", propertyListField?.json)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String json = params.json
        PropertyListField propertyListField = propertyListFieldService.createOrUpdatePropertyListField(campusId, json)
        resultVO.result.put("json", propertyListField?.json)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


}

package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.mapstruct.store.FixMapper
import hiiadmin.module.store.FixVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.Fix

class HiiAdminFixController implements BaseExceptionHandler {

    FixService fixService

    @Autowired
    FixMapper fixMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        Integer fixStatus = params.int("fixStatus")
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        def map = fixService.fetchFixLimit(campusId, searchValue, fixStatus, p, s)
        List<Fix> fixList = map.list
        List<FixVO> fixVOList = fixList.collect {
            fixMapper.convert2FixVO(it)
        }
        resultVO.result.put("list", fixVOList)
        resultVO.result.put("total", map.total)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        Fix fix = fixService.fetchFixById(id)
        FixVO fixVO = fixMapper.convert2FixVO(fix)
        resultVO.result = fixVO
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long fixId = PhenixCoder.decodeId(idStr)
        String fixName = params.fixName
        String fixMobile = params.fixMobile
        String fixRemark = params.fixRemark
        Long fixDate = params.long("fixDate")
        fixService.fixProperty(fixId, fixName, fixMobile, fixRemark, fixDate)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


}

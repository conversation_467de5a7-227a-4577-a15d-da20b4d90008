package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import timetabling.store.StoreMessageConfig

//更换为通用messageCenter 方法
@Deprecated
class HiiAdminStoreMessageConfigController implements BaseExceptionHandler {

    StoreMessageConfigService storeMessageConfigService

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        StoreMessageConfig storeMessageConfig = storeMessageConfigService.fetchStoreMessageConfig(campusId)
        resultVO.result.put("teacherIds", storeMessageConfig?.teacherIds)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"

    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String teacherIds = params.teacherIds
        StoreMessageConfig storeMessageConfig = storeMessageConfigService.createOrUpdateStoreMessageConfig(campusId, teacherIds)
        resultVO.result.put("teacherIds", storeMessageConfig.teacherIds)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

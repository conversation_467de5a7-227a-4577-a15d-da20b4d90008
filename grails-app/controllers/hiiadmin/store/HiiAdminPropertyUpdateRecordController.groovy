package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.mapstruct.store.PropertyUpdateRecordMapper
import hiiadmin.module.store.PropertyUpdateRecordVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.PropertyUpdateRecord

@Deprecated
class HiiAdminPropertyUpdateRecordController implements BaseExceptionHandler {

    PropertyUpdateRecordService propertyUpdateRecordService

    @Autowired
    PropertyUpdateRecordMapper propertyStatusRecordMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String propertyIdStr = params.propertyId
        Long propertyId = PhenixCoder.decodeId(propertyIdStr)
        List<PropertyUpdateRecord> propertyStatusRecordList = propertyUpdateRecordService.fetchAllUpdateRecordByPropertyId(propertyId)
        List<PropertyUpdateRecordVO> propertyStatusRecordVOList = propertyStatusRecordList.collect {
            propertyStatusRecordMapper.convert2PropertyStatusRecordVO(it)
        }
        resultVO.result.put("list", propertyStatusRecordVOList)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


}

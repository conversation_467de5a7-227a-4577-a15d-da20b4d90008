package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.mapstruct.store.StoreClassMapper
import hiiadmin.module.store.StoreClassVO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.StoreClass


@Deprecated
class HiiAdminStoreClassController implements BaseExceptionHandler {

    StoreClassService storeClassService

    @Autowired
    StoreClassMapper storeClassMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        List<StoreClass> storeClasses = storeClassService.fetchAllStoreClass(campusId)
        List<StoreClassVO> storeClassVOList = storeClasses.collect {
            storeClassMapper.convert2StoreClassVO(it)
        }
        StoreClassVO classVO = new StoreClassVO(
                id: PhenixCoder.encodeId(-1),
                name: "未分组"
        )
        storeClassVOList << classVO
        resultVO.result.put("list", storeClassVOList)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String name = params.name
        Integer threshold = params.int("threshold")
        StoreClass storeClass = storeClassService.createAndSaveStoreClass(campusId, name, threshold)
        StoreClassVO vo = storeClassMapper.convert2StoreClassVO(storeClass)
        resultVO.result = vo
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        String name = params.name
        Integer threshold = params.int("threshold")
        StoreClass storeClass = storeClassService.updateStoreClass(id, name, threshold)
        StoreClassVO vo = storeClassMapper.convert2StoreClassVO(storeClass)
        resultVO.result = vo

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        storeClassService.deleteStoreClass(id)
        resultVO.result = id
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


}

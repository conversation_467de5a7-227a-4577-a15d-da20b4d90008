package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.exceptions.HiiAdminException
import hiiadmin.mapstruct.store.StorePropertyDtoMapper
import hiiadmin.mapstruct.store.PropertyMapper
import hiiadmin.module.store.StorePropertyVO
import hiiadmin.module.store.dto.StorePropertyDTO
import hiiadmin.module.store.dto.UpdateFieldDTO
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.StoreProperty


@Deprecated
class HiiAdminPropertyController implements BaseExceptionHandler {

    PropertyService propertyService

    PropertyUpdateRecordService propertyUpdateRecordService

    @Autowired
    PropertyMapper propertyMapper

    @Autowired
    StorePropertyDtoMapper propertyDtoMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String parentIdStr = params.parentId
        Long parentId = PhenixCoder.decodeId(parentIdStr)
        Long gainDate = params.long("gainDate")
        String useUserIdStr = params.useUserId
        Long useUserId = PhenixCoder.decodeId(useUserIdStr)
        String useDepartmentIdStr = params.useDepartmentId
        Long useDepartmentId = PhenixCoder.decodeId(useDepartmentIdStr)
        String searchValue = params.searchValue
        int p = params.int("p", 1)
        int s = params.int("s", 30)
        List<StoreProperty> propertyList =propertyService.fetchPropertyLimit(campusId, parentId,useUserId,useDepartmentId,gainDate, searchValue, p, s)
        List<StorePropertyVO> propertyVOList = propertyService.transformPropertyVOByList(campusId,propertyList)
        Integer total = propertyService.countPropertyLimit(campusId, parentId,useUserId,useDepartmentId,gainDate, searchValue)
        resultVO.result.put("list", propertyVOList)
        resultVO.result.put("total", total)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def show() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        StoreProperty property = propertyService.fetchPropertyById(id)
        StorePropertyVO propertyVO = propertyMapper.convert2PropertyVO(property)
        resultVO.result = propertyVO
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def save() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String idStr = params.parentId
        Long parentId = null
        if(idStr){
            parentId = PhenixCoder.decodeId(idStr)
        }

        String json = params.json
        StorePropertyDTO propertyDTO =  JSON.parseObject(json,StorePropertyDTO.class)

        propertyService.createAndSaveProperty(campusId,userId,parentId,propertyDTO)

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        String json = params.json
        StorePropertyDTO propertyDTO =  JSON.parseObject(json,StorePropertyDTO.class)
        StoreProperty property = propertyService.fetchPropertyById(id)
        if(propertyDTO.code){
            StoreProperty codeProperty = propertyService.fetchPropertyByCode(campusId,propertyDTO.code)
            if((codeProperty && codeProperty.id!=id && !property.parentId)){
                throw new HiiAdminException("资产编号已存在，请重新输入！")
            }
        }
        propertyDtoMapper.dtoIntoDomain(propertyDTO,property)
        propertyService.save(property)
        String updateJson = params.updateJson
        List<UpdateFieldDTO> updateFieldDTOList = JSON.parseArray(updateJson,UpdateFieldDTO.class)
        updateFieldDTOList.each {
            propertyUpdateRecordService.createAndSaveUpdateRecord(campusId,id,userId,it.field,it.fieldName,it.value)
        }
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    def delete() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Long userId = request.getAttribute("userId") as Long
        String idStr = params.id
        Long id = PhenixCoder.decodeId(idStr)
        propertyService.deleteProperty(id, campusId, userId)
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }


}

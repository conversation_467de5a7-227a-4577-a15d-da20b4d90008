package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.CacheService
import hiiadmin.mapstruct.store.StoreGoodsOperateMapper
import hiiadmin.module.store.StoreGoodsOperateVO
import hiiadmin.msg.MessageService
import hiiadmin.utils.PhenixCoder
import org.springframework.beans.factory.annotation.Autowired
import timetabling.store.StoreClass
import timetabling.store.StoreGoods
import timetabling.store.StoreGoodsOperate
import timetabling.store.StoreMessageConfig

import java.util.concurrent.TimeUnit


@Deprecated
class HiiAdminStoreGoodsOperateController implements BaseExceptionHandler {

    CacheService cacheService

    StoreGoodsService storeGoodsService

    StoreClassService storeClassService

    StoreGoodsOperateService storeGoodsOperateService

    StoreMessageConfigService storeMessageConfigService

    MessageService messageService

    @Autowired
    StoreGoodsOperateMapper storeGoodsOperateMapper

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        Integer status = params.int("status")
        String searchValue = params.searchValue

        int p = params.int('p', 1)
        int s = params.int('s', 30)

        Boolean allCheck = params.boolean("allCheck")

        if (allCheck) {
            List<StoreGoodsOperate> storeGoodsOperateList = storeGoodsOperateService.fetchAllStoreGoodsOperateByStatus(campusId, status)
            List<String> idList = storeGoodsOperateList*.id.collect {
                PhenixCoder.encodeId(it)
            }
            resultVO.result.put("list", idList)
        } else {
            def map = storeGoodsOperateService.fetchStoreGoodsOperateLimit(campusId, status, searchValue, p, s)
            List<StoreGoodsOperate> storeGoodsOperateList = map.list

            List<StoreGoodsOperateVO> storeGoodsOperateVOList = storeGoodsOperateList.collect {
                StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(it.goodsId)
                storeGoodsOperateMapper.convert2StoreGoodsOperateVO(storeGoods, it)
            }
            Integer total = map.total

            resultVO.result.put("list", storeGoodsOperateVOList)
            resultVO.result.put("total", total)
        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    //出库
    def update() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        String ids = params.ids
        List<Long> idList = ids.split(",").collect {
            PhenixCoder.decodeId(it)
        }
        List<StoreGoodsOperate> storeGoodsOperateList = storeGoodsOperateService.fetchAllStoreGoodsOperateByIdList(idList)
        List<StoreGoods> storeGoodsList = []
        storeGoodsOperateList.each {
            StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(it.goodsId)
            StoreClass storeClass = storeClassService.fetchStoreClassById(storeGoods.classId)
            Boolean alreadyUnderThreshold = false
            if(storeClass && storeGoods.num<=storeClass.threshold){
                alreadyUnderThreshold =true
            }

            String key = it.goodsId as String
            Long operateId = it.id
            Integer num = it.num
            cacheService.tryLockAndRun4lockCache(key, 30, TimeUnit.SECONDS, {
                storeGoods = storeGoodsOperateService.goodsOut(campusId, operateId, userId, storeGoods, num)
                if (storeClass) {
                    if (storeClass && (storeGoods.num <= storeClass.threshold) && !alreadyUnderThreshold) {
                        storeGoodsList << storeGoods

                    }
                }
            })
        }
        if (storeGoodsList.size() > 0) {
            storeGoodsList.unique {
                it.id
            }
            StoreMessageConfig storeMessageConfig = storeMessageConfigService.fetchStoreMessageConfig(campusId)
            storeGoodsList.each {
                messageService.sendStoreGoodsMessage(campusId, it, storeMessageConfig)
            }


        }

        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }

    //入库
    def patch() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long userId = request.getAttribute("userId") as Long
        Long campusId = request.getAttribute("campusId") as Long
        String ids = params.ids
        List<Long> idList = ids.split(",").collect {
            PhenixCoder.decodeId(it)
        }
        List<StoreGoodsOperate> storeGoodsOperateList = storeGoodsOperateService.fetchAllStoreGoodsOperateByIdList(idList)
        storeGoodsOperateList.each {

            StoreGoods storeGoods = storeGoodsService.fetchStoreGoodsById(it.goodsId)
            String key = it.goodsId as String
            Long operateId = it.id
            Integer num = it.num
            cacheService.tryLockAndRun4lockCache(key, 30, TimeUnit.SECONDS, {
                storeGoodsOperateService.goodsIn(campusId, operateId, userId, storeGoods, num)
            })
        }
        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

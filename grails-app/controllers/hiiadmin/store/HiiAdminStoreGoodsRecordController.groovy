package hiiadmin.store

import com.alibaba.fastjson.JSON
import com.bugu.ResultVO
import hiiadmin.BaseExceptionHandler
import hiiadmin.module.store.StoreGoodsRecordVO


@Deprecated
class HiiAdminStoreGoodsRecordController implements BaseExceptionHandler {

    StoreGoodsRecordService storeGoodsRecordService

    def index() {
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        Long campusId = request.getAttribute("campusId") as Long
        String searchValue = params.searchValue
        Long startTime = params.long("startTime")
        Long endTime = params.long("endTime")
        int p = params.int('p', 1)
        int s = params.int('s', 30)

        List<StoreGoodsRecordVO> storeGoodsRecordVOList = storeGoodsRecordService.fetchStoreGoodsRecordLimit(campusId, searchValue, startTime, endTime, p, s)
        Integer total = storeGoodsRecordService.countStoreGoodsRecordLimit(campusId, searchValue, startTime, endTime)
        resultVO.result.put("list", storeGoodsRecordVOList)
        resultVO.result.put("total", total)


        render text: JSON.toJSONString(resultVO), contentType: "application/json", encoding: "UTF-8"
    }
}

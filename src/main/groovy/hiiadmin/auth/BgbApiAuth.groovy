package hiiadmin.auth

import com.bugu.ResultVO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam

@FeignClient("bgb-auth")
interface BgbApiAuth {

    //新权限菜单
    @RequestMapping(path = "/auth/api/bgb/campus/menu", method = RequestMethod.POST, produces = "application/json")
    ResultVO fetchBgbCampusMenu(@RequestParam("campusId") Long campusId,
                                @RequestParam("staffId") Long staffId,
                                @RequestParam("terminal") Byte terminal,
                                @RequestParam("authorization") String authorization)

}
package hiiadmin.cloudApiServer

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.bugu.ResultVO
import groovy.util.logging.Slf4j
import hiiadmin.UserService
import hiiadmin.auth.LoginService
import hiiadmin.module.bugu.dto.CampusDTO
import hiiadmin.module.bugu.dto.CodeUserDTO
import hiiadmin.module.bugu.dto.SchoolDTO
import hiiadmin.module.bugu.dto.UserInfoDTO
import hiiadmin.school.CampusService
import hiiadmin.school.SchoolService
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import timetabling.org.Campus
import timetabling.org.School

import javax.annotation.Resource

@Slf4j
@ControllerAdvice
@RequestMapping("/api/hiiadmin/school")
@RestController
class SchoolRemoteDataController {

    @Resource
    CampusService campusService

    @Resource
    SchoolService schoolService

    @Resource
    UserService userService

    @Resource
    LoginService loginService
    /**
     * 添加年级
     * @param sectionId
     * @param jsonObject
     * @return
     */
    @PostMapping(path = "/idList", produces = "application/json")
    ResponseEntity<ResultVO> findCampusList(@RequestBody List<Long> campusIdList) {
        List<Campus> campusList = campusService.fetchAllByIdList(campusIdList)
        List<CampusDTO> campusDTOList = []
        campusList.each {
            Campus campus ->
                campusDTOList << new CampusDTO(id: campus.id, name: campus.name)
        }
        log.info("[findCampusList]campusIdList size@${campusIdList.size()},campus Size@${campusList.size()}".toString())

        HttpHeaders headers = new HttpHeaders()
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8)
        return new ResponseEntity<>(ResultVO.success(campusDTOList), headers, HttpStatus.OK)
    }

    /**
     * 添加年级
     * @param sectionId
     * @param jsonObject
     * @return
     */
    @PostMapping(path = "/school", produces = "application/json")
    ResponseEntity<ResultVO> findSchoolTree() {
        List<School> schoolList = campusService.fetchAllSchool()
        List<Campus> campusList = campusService.fetchALlCampus()
        Map<Long, List<Campus>> schoolIdKeyMap = campusList.groupBy { it.schoolId }
        List<SchoolDTO> schoolDtoList = []
        schoolList.each {
            School school ->
                SchoolDTO dto = new SchoolDTO(id: school.id, name: school.name)
                dto.campusList = []
                List<Campus> campusSchoolList = schoolIdKeyMap.get(school.id)
                if (campusSchoolList != null && campusSchoolList.size() > 0) {
                    campusSchoolList.each {
                        Campus campus ->
                            dto.campusList << new CampusDTO(id: campus.id, name: campus.name)
                    }
                }
                schoolDtoList << dto
        }
        log.info("[findSchoolTree]size@${schoolDtoList.size()},campusId@${campusList.size()}".toString())
        HttpHeaders headers = new HttpHeaders()
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8)
        ResultVO resultVO = new ResultVO()
        resultVO.result.put("list", schoolDtoList)
        resultVO.message = "获取成功"
        return new ResponseEntity<>(resultVO, headers, HttpStatus.OK)
    }

    /**
     * 获取用户
     * @param sectionId
     * @param jsonObject
     * @return
     */
    @PostMapping(path = "/userInfo", produces = "application/json")
    ResponseEntity<ResultVO> findUserInfo(@RequestBody CodeUserDTO codeUserDTO) {
        log.info("[findUserInfo]parem@${JSON.toJSONString(codeUserDTO)}".toString())
        UserInfoDTO userInfoDTO = userService.gerUserInfoDTO(codeUserDTO)
        log.info("[findUserInfo]parem@${JSON.toJSONString(userInfoDTO)}".toString())
        HttpHeaders headers = new HttpHeaders()
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8)
        ResultVO resultVO = new ResultVO()
        resultVO.status = 1
        if (userInfoDTO) {
            resultVO.result.put("body", userInfoDTO)
            resultVO.message = "获取成功"
        } else {
            resultVO.status = 0
            resultVO.message = "人员不存在"
        }
        return new ResponseEntity<>(resultVO, headers, HttpStatus.OK)
    }

    /**
     * 获取用户基础信息
     * @param jsonObject
     * @return
     */
    @PostMapping(path = "/login", produces = "application/json")
    ResponseEntity<ResultVO> login4UserInfo(@RequestBody JSONObject jsonObject) {
        log.info("[login4UserInfo]parem@${jsonObject.toString()}".toString())
        String userName = jsonObject.getString("userName")
        String password = jsonObject.getString("password")
        Long campusId = jsonObject.getLong("campusId")

        log.info("[login4UserInfo] userName@${userName} password@${password}".toString())
        HttpHeaders headers = new HttpHeaders()
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8)
        ResultVO resultVO =null
        try {
            resultVO = loginService.login4UserInfo(userName, password,campusId)
        } catch (Exception e) {
            resultVO = new ResultVO()
            resultVO.status = 0
            resultVO.code = 500
            resultVO.message = e.getMessage()
        }
        return new ResponseEntity<>(resultVO, headers, HttpStatus.OK)
    }
}

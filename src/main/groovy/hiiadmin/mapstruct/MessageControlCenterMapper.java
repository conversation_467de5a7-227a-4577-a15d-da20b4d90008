package hiiadmin.mapstruct;

import hiiadmin.module.messageControl.MessageControlCenterVO;
import hiiadmin.module.messageControl.MessageControlRoleVO;
import hiiadmin.module.messageControl.MessageControlTeacherVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import timetabling.messageControl.MessageControlCenter;
import timetabling.newMenu.BgbCampusRole;
import timetabling.user.Teacher;

/**
 * Created by sunnysails on 2023/2/15 10:00
 *
 * @version 1.0
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = MapstructCoverUtil.class)
public interface MessageControlCenterMapper {

    @Mapping(target = "id", source = "id", qualifiedByName = "idEncode")
    @Mapping(target = "pushOpt", source = "pushOptBit", qualifiedByName = "trackMessageFeatureOpt")
    @Mapping(target = "metaClass", ignore = true)
    MessageControlCenterVO convert2MessageControlCenterVO(MessageControlCenter center);


    //    @Mapping(target = "personId", source = "teacher.id", qualifiedByName = "idEncode")
    @Mapping(target = "id", source = "messagePersonId", qualifiedByName = "idEncode")
    @Mapping(target = "personType", ignore = true)
    @Mapping(target = "personId", source = "teacher.id")
    @Mapping(target = "mobile", source = "mobile")
    @Mapping(target = "metaClass", ignore = true)
    MessageControlTeacherVO convert2MessageControlTeacherVO(Teacher teacher, Long messagePersonId, String mobile);

    @Mapping(target = "id", source = "messagePersonId", qualifiedByName = "idEncode")
    @Mapping(target = "personType", ignore = true)
    @Mapping(target = "personId", source = "role.id")
    @Mapping(target = "metaClass", ignore = true)
    MessageControlRoleVO convert2MessageControlRoleVO(BgbCampusRole role, Long messagePersonId);


}

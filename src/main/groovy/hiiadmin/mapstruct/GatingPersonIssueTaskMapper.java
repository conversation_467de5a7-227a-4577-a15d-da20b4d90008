package hiiadmin.mapstruct;

import hiiadmin.module.docking.gating.GatingPersonIssueTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import timetabling.docking.gating.GatingPersonIssueTask;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = MapstructCoverUtil.class)
public interface GatingPersonIssueTaskMapper {

    @Mapping(target = "userType", ignore = true)
    @Mapping(target = "taskName", ignore = true)
    @Mapping(target = "deviceName", ignore = true)
    @Mapping(target = "planTemplateName", ignore = true)
    @Mapping(target = "id", source = "id", qualifiedByName = "idEncode")
    @Mapping(target = "metaClass", ignore = true)
    GatingPersonIssueTaskVO convert2GatingPersonIssueTaskVO(GatingPersonIssueTask issueTask);

    @Mapping(target = "id", source = "issueTask.id", qualifiedByName = "idEncode")
    @Mapping(target = "metaClass", ignore = true)
    GatingPersonIssueTaskVO convert2GatingPersonIssueTaskVO(GatingPersonIssueTask issueTask, String taskName, Byte userType, String planTemplateName, String deviceName);

    @Mapping(target = "userType", ignore = true)
    @Mapping(target = "taskName", ignore = true)
    @Mapping(target = "planTemplateName", ignore = true)
    @Mapping(target = "id", source = "issueTask.id", qualifiedByName = "idEncode")
    @Mapping(target = "metaClass", ignore = true)
    GatingPersonIssueTaskVO convert2GatingPersonIssueTaskVO(GatingPersonIssueTask issueTask, String deviceName);

}

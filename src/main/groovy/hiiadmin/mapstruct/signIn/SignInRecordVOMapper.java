package hiiadmin.mapstruct.signIn;

import hiiadmin.mapstruct.MapstructCoverUtil;
import hiiadmin.module.bugu.signIn.SignInRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import timetabling.sign.SignInRecord;

import java.util.List;

/**
 * Created by sunnysails on 2024/9/26 16:34
 *
 * @version 1.0
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, uses = MapstructCoverUtil.class)
public interface SignInRecordVOMapper {


    @Mapping(target = "unitId", source = "normalUnitId")
    @Mapping(target = "unitName", source = "normalUnitName")
    @Mapping(target = "metaClass", ignore = true)
    SignInRecordVO convert2SignInRecordVO(SignInRecord signInRecord);

    List<SignInRecordVO> cover2SignInRecordVOList(List<SignInRecord> listOfSignInRecords);
}

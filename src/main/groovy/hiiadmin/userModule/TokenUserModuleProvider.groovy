package hiiadmin.userModule

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import groovy.util.logging.Slf4j
import io.jsonwebtoken.Claims
import io.jsonwebtoken.JwtException
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
//import io.jsonwebtoken.security.Keys
//import io.jsonwebtoken.security.SecureDigestAlgorithm
import org.springframework.util.StringUtils

import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

@Slf4j
class TokenUserModuleProvider {

//    private final static SecureDigestAlgorithm<SecretKey, SecretKey> ALGORITHM = Jwts.SIG.HS256;
    private static final String SECRET = "bjkN^ci6p#C&uL86xh1bm&nhekGbAfGT";

//    public static final SecretKey KEY = Keys.hmacShaKeyFor(SECRET.getBytes());

    private static SecretKey keyInstance = null
    
    static {
        byte[] keyBytes = SECRET.getBytes();
        keyInstance =  new SecretKeySpec(keyBytes, SignatureAlgorithm.HS256.getJcaName())
    }
    
    //设置一周过期 （产品开会确定的）
    private static final Long expirationTime = 604800L;

    public static String generateToken(Map<String, Object> claims, String iss) {

        final Date createdDate = new Date();
        final Date expirationDate = new Date(createdDate.getTime() + expirationTime * 1000);
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject("user-module")
                .setIssuer(iss)
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS256, SECRET)
                .compact();
        return "Bearer " + token;
    }

    /**
     * 解析token
     * */
    public static UserTokenBO checkAuthentication(String token) throws Exception {
        UserTokenBO userTokenBO = new UserTokenBO()
        if (StringUtils.hasText(token)) {
            String[] jwtParts = token.split("\\.", 0);
            if (jwtParts.length >= 1) {
                token = jwtParts[1];
                String payLoadString = new String(Base64.getUrlDecoder().decode(token))
                JSONObject jsonObject = JSONObject.parseObject(payLoadString)
                Long userId = jsonObject.getJSONObject("iss").getLong("userId")
                Byte userType = jsonObject.getJSONObject("iss").getByte("userType")
                String userName = jsonObject.getJSONObject("iss").getString("userName")
                Long organizationId = jsonObject.getJSONObject("iss").getLong("organizationId")
                userTokenBO.userId = userId
                userTokenBO.userName = userName
                userTokenBO.userType = userType
                userTokenBO.organizationId = organizationId
            }
        }
        log.info("come from usermodule login:${JSON.toJSONString(userTokenBO)}, token:${token}".toString())
        return userTokenBO
    }

    public static boolean validateToken(String token) {
        try {
            final Date expiration = getAllClaimsFromToken(token).getExpiration();
            return expiration.before(new Date());
        } catch (JwtException | IllegalArgumentException e) {
            return true;
        }
    }

    public static String resolveToken(String bearerToken) {
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        } else if (StringUtils.hasText(bearerToken)) {
            return bearerToken;
        }
        return null;
    }


    private static Claims getAllClaimsFromToken(String token) {
        if (token.contains("Bearer")) {
            token = token.replace("Bearer ", "");
        }
//        return Jwts.parser()
//                .verifyWith(KEY)
//                .build()
//                .parseSignedClaims(token)
//                .getPayload();
        return Jwts.parser().setSigningKey(keyInstance).parseClaimsJws(token).getBody();
    }

    public static void main(String[] args) {
        Map<String, Object> claims = [userId: 1L]

        //String token = generateToken(claims, "user-module");
        String token = generateToken(claims, "user-module");
        token = token.replace("Bearer ", "");
        System.out.println(token);
        Claims claims1 = getAllClaimsFromToken(token);

        System.out.println("---");
    }
}

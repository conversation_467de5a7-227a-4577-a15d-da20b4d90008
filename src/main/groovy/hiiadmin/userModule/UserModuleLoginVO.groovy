package hiiadmin.userModule

import hiiadmin.module.SchoolLoginVO
import hiiadmin.module.bugu.DepartmentVO
import hiiadmin.module.newMenu.BgbCampusRoleVO

class UserModuleLoginVO implements Serializable {

    String username
    String token
    String id
    Long originId
    String jobNum
    boolean pswd = true
    String nick
    String schoolId
    Long originSchoolId
    String campusId
    Long originCampusId
    String teacherId
    Long originTeacherId
    String schoolName
    String campusName
    Byte campusType
    Integer orgSyncType
    String avatar
    String corpId
    List<DepartmentVO> department
    List<SchoolLoginVO> schoolCampusList
    List<BgbCampusRoleVO> list
    String supervisorIds
    String appId
}

package hiiadmin.config;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.Message;
import hiiadmin.rocket.LectureConsumerService;
import org.apache.rocketmq.client.apis.*;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: changzhaoliang
 * @date: 2020-11-25 09:04
 * @description:
 */
@Component
@Configuration
public class RocketV2Configuration {

    private final Logger logger = LoggerFactory.getLogger(RocketV2Configuration.class);


    @Value("${rocketmqV2.ak}")
    private String accessKey;

    @Value("${rocketmqV2.sk}")
    private String secretKey;

    @Value("${rocketmqV2.nameSrvAddr}")
    private String NAMESRVAddr;

    @Value("${rocketmqV2.groupId}")
    private String groupId;

    @Value("${rocketmqV2.topic.lecture}")
    private String lectureTopic;

    @Value("${rocketmqV2.topic.lectureChangeResult}")
    private String lectureChangeResultTopic;

    @Resource
    private LectureConsumerService lectureConsumerService;


    @Bean(destroyMethod = "close", name = "rocketV2Producer")
    public Producer producer() throws ClientException {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        SessionCredentialsProvider sessionCredentialsProvider =
                new StaticSessionCredentialsProvider(accessKey, secretKey);

        ClientConfiguration configuration = ClientConfiguration.newBuilder()
                .setEndpoints(NAMESRVAddr)
                .setCredentialProvider(sessionCredentialsProvider).build();

        return provider.newProducerBuilder()
                .setClientConfiguration(configuration)
                .setTopics(lectureTopic)
                .build();
    }

    @Bean(destroyMethod = "close", name = "rocketV2Consumer")
    public PushConsumer consumer() throws ClientException {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        SessionCredentialsProvider sessionCredentialsProvider =
                new StaticSessionCredentialsProvider(accessKey, secretKey);

        ClientConfiguration configuration = ClientConfiguration.newBuilder()
                .setEndpoints(NAMESRVAddr)
                .setCredentialProvider(sessionCredentialsProvider).build();

        FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
        Map<String, FilterExpression> subscriptionExpressions = new HashMap<>(4);
        subscriptionExpressions.put(lectureTopic, filterExpression);

        return provider.newPushConsumerBuilder()
                .setClientConfiguration(configuration)
                .setConsumerGroup(groupId)
                .setSubscriptionExpressions(subscriptionExpressions)
                .setMessageListener(messageView -> {
                    // 获取消息的 Topic
                    String topic = messageView.getTopic();
                    logger.info("Processing message for {}, messageId={}", topic, messageView.getMessageId());

                    // 获取 ByteBuffer 体
                    ByteBuffer bodyBuffer = messageView.getBody();
                    // 使用 remaining() 和 get() 方式读取
                    byte[] bodyBytes = new byte[bodyBuffer.remaining()];
                    bodyBuffer.get(bodyBytes); // 读取剩余的字节
                    // 转ali的message
                    Message message = new Message(topic, null, bodyBytes);

                    Action messageAction = Action.CommitMessage;
                    if (topic.equalsIgnoreCase(lectureTopic)) {
                        messageAction = lectureConsumerService.consume(message, null);
                    } else {
                        logger.warn("Received message from an unknown topic: {}", topic);
                    }

                    return messageAction == Action.CommitMessage ? ConsumeResult.SUCCESS : ConsumeResult.FAILURE;
                })
                .build();
    }
}

package hiiadmin.config;

import com.buguk12.logging.TracingLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> at 17/11/2021
 */
@Component
public class DefaultTracingLogger implements TracingLogger {

    static Logger logger = LoggerFactory.getLogger(DefaultTracingLogger.class);

    @Override
    public Logger getLogger() {
        return logger;
    }
}

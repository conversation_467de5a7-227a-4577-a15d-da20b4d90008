package hiiadmin.listen.event.device

import org.springframework.context.ApplicationEvent

import java.time.Clock

/**
 * Created by sunnysails on 2022/12/7 11:45
 * @version 1.0 
 */
class DeviceChangeEvent extends ApplicationEvent {

    long deviceId

    Integer operateType

    DeviceChangeEvent(Object source) {
        super(source)
    }

    DeviceChangeEvent(Object source, Clock clock) {
        super(source, clock)
    }

    DeviceChangeEvent(Object source, long deviceId, Integer operateType) {
        super(source)
        this.deviceId = deviceId
        this.operateType = operateType
    }
}

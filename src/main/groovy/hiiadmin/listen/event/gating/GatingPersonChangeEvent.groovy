package hiiadmin.listen.event.gating

import java.time.Clock

/**
 * Created by sunnysails on 2022/11/15 17:17
 * @version 1.0 
 */
class GatingPersonChangeEvent extends BaseGatingIssueTaskEvent {

    Long groupId

    String userIds

    int operateType

    GatingPersonChangeEvent(Object source) {
        super(source)
    }

    GatingPersonChangeEvent(Object source,Long groupId,String userIds,int operateType) {
        super(source)
        this.groupId = groupId
        this.userIds = userIds
        this.operateType = operateType
    }
}

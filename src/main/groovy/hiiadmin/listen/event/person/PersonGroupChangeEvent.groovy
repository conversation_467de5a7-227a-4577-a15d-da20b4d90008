package hiiadmin.listen.event.person


class PersonGroupChangeEvent extends Base<PERSON>ersonEvent {

    Long campusId

    Long groupId

    int operateType

    PersonGroupChangeEvent(Object source) {
        super(source)
    }

    PersonGroupChangeEvent(Object source,Long campusId,Long groupId,int operateType) {
        super(source)
        this.campusId = campusId
        this.groupId = groupId
        this.operateType = operateType
    }

}

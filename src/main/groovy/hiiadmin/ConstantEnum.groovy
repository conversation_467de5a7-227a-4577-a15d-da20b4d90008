package hiiadmin

import com.google.common.base.Joiner
import com.google.common.collect.ImmutableList
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.StringUtils
import org.joda.time.DateTime

class ConstantEnum {

    private final static byte b0 = 0
    private final static byte b1 = 1
    private final static byte b2 = 2
    private final static byte b3 = 3
    private final static byte b4 = 4
    private final static byte b5 = 5
    private final static byte b6 = 6
    private final static byte b7 = 7
    private final static byte b8 = 8
    private final static byte b9 = 9
    private final static byte b10 = 10
    private final static byte b11 = 11
    private final static byte b12 = 12
    private final static byte b13 = 13
    private final static byte b14 = 14


    @Deprecated
    static enum SignInType {

        UNREQUIRED(-1 as byte, "无需考勤"),

        NOSIGNIN(0 as byte, "未签到"),

        NORMAL(1 as byte, "正常"),

        LATE(2 as byte, "迟到"),

        ABSENT(3 as byte, "缺席"),

        LEAVE(4 as byte, "请假"),

        LEAVE_EARLY(5 as byte, "早退"),

        OTHER(6 as byte, "其他")

        public Byte type

        public String name

        SignInType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static SignInType getEnumByType(Byte type) {
            SignInType[] allEnums = values()
            for (SignInType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum PdUnitType {
        SCHOOL(0 as byte, "学校", "school"),

        CAMPUS(1 as byte, "校区", "campus"),

        PERIOD(2 as byte, "学段", "period"),

        GRADE(3 as byte, "年级", "grade"),

        CLASS(4 as byte, "班级", "class"),

        FACULTY(5 as byte, "教学院系", "faculty"),

        MAJOR(6 as byte, "专业", "major"),

        ADMINISTRATIVE(7 as byte, "党政机构", "administrative"),

        DEPT(8 as byte, "部门", "dept")

        Byte type
        String name
        String value

        PdUnitType(Byte type, String name, String value) {
            this.type = type
            this.name = name
            this.value = value
        }
    }

    static enum PdDataBizType {

        USER_CHANGE(13, "教师信息变更，包含用户添加、修改、删除"),
        DEPARTMENT_CHANGE(14, "部门变更，包含部门添加、修改、删除"),
        ROLE_CHANGE(15, "角色变更，包含角色添加、修改、删除"),
        ENTERPRISE_CHANGE(16, "学校变更，包含组织修改、删除"),


        CAMPUS_CHANGE(50, "家校通讯录2.0，校区信息变更"),
        CAMPUS_DIRECTORY_CHANGE(51, "家校通讯录2.0，班级结构，年级结构变更"),
        CAMPUS_USER_CHANGE(52, "家校通讯录2.0，人员信息（学生家长）变更")

        int type

        String name

        PdDataBizType(int type, String name) {
            this.type = type
            this.name = name
        }
    }

    static enum PdDataSyncAction {
        ROLE_ADD("role_add"),
        ROLE_UPDATE("role_update"),
        ROLE_DELETE("role_delete"),
        ORG_DEPT_CREATE("org_dept_create"),
        ORG_DEPT_MODIFY("org_dept_modify"),
        ORG_DEPT_REMOVE("org_dept_remove"),
        EDU_DEPT_INSERT("edu_dept_insert"),
        EDU_DEPT_UPDATE("edu_dept_update"),
        EDU_DEPT_DELETE("edu_dept_delete"),
        USER_ADD_ORG("user_add_org"),
        USER_MODIFY_ORG("user_modify_org"),
        USER_LEAVE_ORG("user_leave_org"),
        ORG_ADD("org_add"),
        ORG_CHANGE("org_change"),
        ORG_REMOVE("org_remove"),
        EDU_USER_INSERT("edu_user_insert"),
        EDU_USER_DELETE("edu_user_delete"),
        EDU_USER_UPDATE("edu_user_update")

        String action

        PdDataSyncAction(String action) {
            this.action = action
        }
    }

    static enum SyncType {

        Ding(1, "钉钉"),

        User_Module_Pd(2, "组织用户中心")

        int type
        String name

        SyncType(int type, String name) {
            this.type = type
            this.name = name
        }

        static String getDesc(Integer type) {
            if (!type) {
                return "系统自维护"
            }
            values().find { it.type == type }?.name
        }
    }

    static enum MessageControlCenterType {

        MORAL_RECALL(1, "德育记录撤销消息", 24),

        PLATFORM_STATUS(2, "平台状态告警", 29),

        PROPERTY_SCRAP(3, "资产报废提醒", 29)


        int type

        String name

        Integer messageType

        MessageControlCenterType(int type, String name, Integer messageType) {
            this.type = type
            this.name = name
            this.messageType = messageType
        }

        static MessageControlCenterType getEnumByType(int type) {
            values().find { it.type == type }
        }

        static List<Integer> allType() {
            values()*.type
        }
    }

    static enum TrackMessageFeatureType {

        INNER_DOOR(1, "学生到校", "学生在以下设备上通过并产生轨迹视为到校"),

        OUT_DOOR(2, "学生离校", "学生在以下设备上通过并产生轨迹视为离校"),

        HOSPITAL(3, "学生就医", "学生在以下设备上刷脸/刷卡视为到医务室就诊"),

        TRACK_ALARM(4, "通用学生轨迹提醒", "学生在以下设备上产生轨迹时提醒相关对象（建议选择少量重要点位设备）"),

        GOODS_BORROW_RETURN(5, "物品借还", "人员在以下设备上刷脸/刷卡视为到仓库借还物品")

        int type

        String name

        String memo

        TrackMessageFeatureType(int type, String name, String memo) {
            this.type = type
            this.name = name
            this.memo = memo
        }

        static TrackMessageFeatureType getEnumByType(int type) {
            values().find { it.type == type }
        }

        static List<Integer> allType() {
            values()*.type
        }
    }

    static enum WeekIndex {

        Monday(1, "周一"),
        Tuesday(2, "周二"),
        Wednesday(3, "周三"),
        Thursday(4, "周四"),
        Friday(5, "周五"),
        Saturday(6, "周六"),
        Sunday(7, "周日")

        Integer status

        String name

        WeekIndex(Integer status, String name) {
            this.status = status
            this.name = name
        }

        static WeekIndex getEnumByType(Integer status) {
            WeekIndex[] allEnums = values()
            for (WeekIndex s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Integer status) {
            return getEnumByType(status)?.name
        }
    }

    static enum WeekIndexV2 {

        Monday(1, "星期一"),
        Tuesday(2, "星期二"),
        Wednesday(3, "星期三"),
        Thursday(4, "星期四"),
        Friday(5, "星期五"),
        Saturday(6, "星期六"),
        Sunday(7, "星期天")

        Integer status

        String name

        WeekIndexV2(Integer status, String name) {
            this.status = status
            this.name = name
        }

        static WeekIndexV2 getEnumByType(Integer status) {
            WeekIndexV2[] allEnums = values()
            for (WeekIndexV2 s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Integer status) {
            return getEnumByType(status)?.name
        }
    }

    static enum EventAttendanceOpt {

        STUDENT(1, "学生自助考勤"),

        HEADMASTER(2, "教师手动登记")

        int opt

        String name

        EventAttendanceOpt(int opt, String name) {
            this.opt = opt
            this.name = name
        }

        static String bit2Types(int bit) {
            if (bit < 1) {
                return ""
            }
            List<Integer> list = []
            values().each { event ->
                Integer type = Math.pow(2, event.opt - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    list.add(event.opt)
                }
            }
            Joiner.on(",").join(list)
        }

        static int types2Bit(String types) {
            int bit = 0
            if (!types) {
                return bit
            }
            types.split(",").each {
                bit += Math.pow(2, it.toInteger() - 1).intValue()
            }
            return bit
        }
    }

    static enum TrackMessageFeatureOpt {

        PARENT(1, "家长"),

        HEADMASTER(2, "班主任"),

        TEACHER(3, "指定教师"),

        DEPARTMENT(4, "部门"),

        ROLE(5, "角色"),

        ADMIN_TEACHER(6, "管理教师"),

        ONESELF(7, "学生本人"),

        REGISTER_PERSON(8, "撤销人员")

        int opt

        String name

        TrackMessageFeatureOpt(int opt, String name) {
            this.opt = opt
            this.name = name
        }

        static String bit2Names(int bit) {
            if (bit < 1) {
                return "未知组合"
            }
            List<String> nameList = []
            values().each { task ->
                Integer type = Math.pow(2, task.opt - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    nameList.add(task.name)
                }
            }
            Joiner.on("+").join(nameList)
        }

        static String bit2Types(int bit) {
            if (bit < 1) {
                return "未知组合"
            }
            List<Integer> list = []
            values().each { task ->
                Integer type = Math.pow(2, task.opt - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    list.add(task.opt)
                }
            }
            Joiner.on(",").join(list)
        }

        static int types2Bit(String types) {
            int bit = 0
            if (!types) {
                return bit
            }
            types.split(",").each {
                bit += Math.pow(2, it.toInteger() - 1).intValue()
            }
            return bit
        }

        static boolean compareFieldBit(int opt, int bit) {
            Integer type = Math.pow(2, opt - 1).intValue()
            (Integer.compare(bit & type, type) == 0)
        }
    }

    static enum FacultyType {
        CLASS(1 as byte, "教学院系"),
        ADMINISTRATIVE(2 as byte, "党政机构")

        byte type
        String name

        FacultyType(byte type, String name) {
            this.type = type
            this.name = name
        }

        static FacultyType getEnumByType(Byte type) {
            values().find { it.type == type }
        }
    }


    static enum IssueStatus {

        FAILURE(0, "失败"),

        ISSUING(1, "任务提交"),

        SUCCESS(2, "成功")

        int status

        String name

        IssueStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static IssueStatus getEnumByStatus(int status) {
            values().find { it.status == status }
        }
    }

    static enum FaceLibIssueStatus {

        FAILURE(-1, "失败"),
        ISSUING(0, "下发中"),
        SUCCESS(1, "成功")

        int status
        String name

        FaceLibIssueStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static FaceLibIssueStatus getEnumByType(int status) {
            values().find { it.status == status }
        }
    }

    static enum OperateType {

        ADD(1, "添加"),
        UPDATE(2, "更新"),
        DEL(3, "删除")

        Integer type
        String name

        OperateType(int type, String name) {
            this.type = type
            this.name = name
        }

        static OperateType getEnumByType(int type) {
            values().find { it.type = type }
        }
    }

    static enum AuthGateTaskStatus {

        FAILURE(-1, "失败"),
        ISSUING(0, "处理中"),
        FINISH(1, "完成"),
        DELETE(2, "删除")

        int status
        String name

        AuthGateTaskStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static AuthGateTaskStatus getEnumByType(int status) {
            values().find { it.status == status }
        }
    }

    static enum CampusType {
        K12(1 as byte, "普校"),
        VOCATIONAL(2 as byte, "职校")

        byte type
        String name

        CampusType(byte type, String name) {
            this.type = type
            this.name = name
        }

        static CampusType getEnumByType(byte type) {
            values().find { it.type == type }
        }
    }

    static enum MoralRegisterPermissions {

        PUBLIC(0, "公开"),

        MANAGE(1, "管理"),

        HEAD_TEACHER(2, "班主任")

        int registerPermissions

        String name

        MoralRegisterPermissions(int registerPermissions, String name) {
            this.registerPermissions = registerPermissions
            this.name = name
        }

        static MoralRegisterPermissions getEnumByViewPermissions(int registerPermissions) {
            values().find { it.registerPermissions == registerPermissions }
        }
    }

    static enum AttendanceViewPermissions {

        PUBLIC(0, "公开"),

        MANAGE(1, "管理"),

        HEAD_TEACHER(2, "班主任"),

        Designated_Teacher(3, "指定教师")

        int viewPermissions

        String name

        AttendanceViewPermissions(int viewPermissions, String name) {
            this.viewPermissions = viewPermissions
            this.name = name
        }

        static AttendanceViewPermissions getEnumByViewPermissions(int viewPermissions) {
            values().find { it.viewPermissions == viewPermissions }
        }
    }

    static enum AttendanceViewMode {

        CLASS(0, "班级"),

        DORM(1, "宿舍")

        int viewMode

        String name

        AttendanceViewMode(int viewMode, String name) {
            this.viewMode = viewMode
            this.name = name
        }

        static AttendanceViewMode getEnumByViewMode(int viewMode) {
            values().find { it.viewMode == viewMode }
        }
    }

    // 德育 -- 评分场景
    static enum MoralMenuRegistrationOpt {

        STUDENT(1, "学生"),

        UNIT(2, "班级"),

        ROOM(3, "教室+学生位置"),

        DORMITORY(4, "宿舍")

        int type

        String name

        MoralMenuRegistrationOpt(int type, String name) {
            this.type = type
            this.name = name
        }

        static String bit2Names(int bit) {
            if (bit < 1) {
                return "未知组合"
            }
            List<String> nameList = []
            values().each { task ->
                Integer type = Math.pow(2, task.type - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    nameList.add(task.name)
                }
            }
            Joiner.on("+").join(nameList)
        }

        static String bit2Types(int bit) {
            if (bit < 1) {
                return "未知组合"
            }
            List<Integer> list = []
            values().each { task ->
                Integer type = Math.pow(2, task.type - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    list.add(task.type)
                }
            }
            Joiner.on(",").join(list)
        }

        static int types2Bit(String types) {
            int bit = 0
            if (!types) {
                return bit
            }
            types.split(",").each {
                bit += Math.pow(2, it.toInteger() - 1).intValue()
            }
            return bit
        }
    }

    static enum DingDing_APP_MAP {

        ZHIHUIXIAOYUAN_DAILY(36198l, 'zhxy_daily', "智慧校园测试版"),

        ZHIHUIXIAOYUAN_ONLINE(38232l, 'zhxy', "智慧校园线上")

        public Long appId
        public String app
        public String des

        DingDing_APP_MAP(Long appId, String app, String des) {
            this.appId = appId
            this.app = app
            this.des = des
        }

        static DingDing_APP_MAP getEnumByAppId(Long appId) {
            for (DingDing_APP_MAP s : values()) {
                if (s.appId == appId) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 菜单跳转类型
     */
    static enum MenuType {

        INNER(0 as Byte, '内部链接跳转'),

        OUTER(1 as Byte, '外部跳转')

        public Byte type

        public String name

        MenuType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MenuType getEnumByType(Byte type) {
            MenuType[] allEnums = MenuType.values()
            for (MenuType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum BgbMenuType {

        H5(1 as byte, "H5"),

        DING_MINI_APP(2 as byte, "钉钉小程序")

        Byte type

        String name

        BgbMenuType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BgbMenuType getEnumByType(Byte type) {
            BgbMenuType[] allEnums = values()
            for (BgbMenuType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum ApprovalExportRecordType {

        APPROVAL(1 as byte, "审批"),

        STUDENT_LEAVE(2 as byte, "学生请假"),

        TEACHER_LEAVE(3 as byte, "教师请假"),

        STUDENT_VIOLATION(4 as byte, "学生违纪"),

        TEACHER_LEAVE_STATISTICS(5 as byte, "教师请假人员统计")

        public Byte type

        public String name

        ApprovalExportRecordType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalExportRecordType getEnumByType(Byte type) {
            for (ApprovalExportRecordType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum IotType {
        /**
         * 电子班牌 = 1,
         * 教学一体机 = 2,
         * 微信端 = 3,
         * 学校后台 = 4,
         * 访客机 = 6,
         * 扫描仪 = 8,
         * 宿舍大屏 = 9
         */
        E_PAD("电子班牌", 1, "g7awWeRVLh5", "g7awhHTvATg"),

        SCAN_PRINTER("扫描仪", 8, "g7awydPF9gu", ""),
        SCHOOL_BOX("校园一体机（点点通）", 12, "g7awEVzG0tJ", "g7aw61giPzz")
        //班级名称
        public String name
        //类型
        public int type
        // 物联网产品key
        public String productKey

        public String productKeyOnline

        private IotType(String name, int type, String productKey, String productKeyOnline) {
            this.name = name
            this.type = type
            this.productKey = productKey
            this.productKeyOnline = productKeyOnline
        }

        static IotType getIotType(Integer t) {
            for (IotType each : values()) {
                if (each.type == t) {
                    return each
                }
            }
            return null
        }
    }

    static enum JobRecordType {

        SIGN_IN_REMIND(1 as byte, "上课考勤登记提醒"),

        CLASS_COMMENT_REMIND(2 as byte, "上课评价提醒"),

        ELECTIVE_COURSE_START_REMIND(3 as byte, "选课开始提醒"),

        ELECTIVE_COURSE_COMPLETE_REMIND(4 as byte, "选课完成提醒")

        public Byte type

        public String name

        JobRecordType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static JobRecordType getEnumByType(Byte type) {
            for (JobRecordType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum TerminalType {

        HII_ADMIN(1 as byte, "pc端"),

        HII(2 as byte, "移动端")

        public Byte type

        public String name

        TerminalType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static TerminalType getEnumByType(Byte type) {
            for (TerminalType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    //业务代码
    static enum BizCode {

        SHEN_PI(1, '审批'),

        XIAONEI_TONGZHI(2, '校内通知'),

        RENYUN_GUIJI(3, '人员轨迹'),//通过track表统计

        XUESHENG_JIESONG(4, '学生接送'),//通过track表统计

        JIUYI(5, '就医'),

        DAOXIAO_KAOQIN(6, '到校考勤'),//通过track表统计

        LIXIAO_KAOQIN(7, '离校考勤'),//通过track表统计

        SHISHENG_XUNHU(8, '师生寻呼'),

        PAD_JIAXIAO_LIUYAN(10, '家校留言'),

        PAD_XINWEN(11, '家校新闻'),//通过journalism表统计

        PAD_BANJI_FENGCAI(12, '班级风采'),

        PAD_JIAXIAO_TONGZHI(13, '家校通知')

        public Integer type

        public String name

        BizCode(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static BizCode getEnumByType(Integer type) {
            BizCode[] allEnums = BizCode.values()
            for (BizCode s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum LeaveRecordStatus {

        FAILURE(-2 as byte, "已撤销"),

        CANCELLED(-1 as byte, "已销假"),

        INIT(1 as byte, '待生效'),

        ASKING(2 as byte, "请假中"),

        END(3 as byte, "已结束")

        public Byte status

        public String name

        LeaveRecordStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static LeaveRecordStatus getEnumByType(Byte type) {
            for (LeaveRecordStatus s : values()) {
                if (s.status == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum ApprovalFormType {

        SINGLE_LINE_INPUT_BOX(10001, '单行输入框'),

        MULTILINE_INPUT_BOX(10002, "多行输入框"),

        DIGITAL_INPUT_BOX(10003, "数字输入框"),

        SINGLE_BOX(10004, "单选框"),

        WHETHER_TO_SELECT_BOX(10005, "是否选择框"),

        CHECKBOX(10006, "多选框"),

        DATE(10007, "日期"),

        DATE_RANGE(10008, "日期区间"),

        INSTRUCTIONS(10009, "说明文字"),

        ID_CARD(10010, "身份证"),

        PHONE(10011, "电话"),

        STUDENT_SELECTOR(20001, "学生选择器"),

        FACULTY_SELECTOR(20002, "教职工选择器"),

        DEPARTMENT(20003, "部门"),

        FACE_PHOTO(20004, "人脸照片"),

        PICTURE(20005, "图片"),

        DETAIL_TABLE(20006, "明细/表格"),

        AMOUNT(20007, "金额"),

        HANDWRITTEN_SIGNATURES(20008, "手写签名"),

        CALCULATION_FORMULA(20009, "计算公式"),

        ASSOCIATED_APPROVAL_FORM(20010, "关联审批单"),

        INVOICE(20011, "发票"),

        ENCLOSURE(20012, "附件"),

        UNIT_SELECTOR(20013, "行政班选择器"),

        SELECT_STAR(20014, "星级评分框"),

        STUDENT_LEAVE(30001, "学生请假套件"),

        VISITOR_APPOINTMENT(30002, "访客预约套件"),

        VISITOR_REGISTRATION(30003, "访客登记套件"),

        TEACHER_LEAVE(30004, "教师请假套件"),

        STUDENT_HONOR_DECLARATION(30005, "学生荣誉申报套件"),

        WEN_YIN(30006, "文印申请套件"),

        STUDENT_PERIODIC_LEAVE(30007, "学生周期性请假"),

        STUDENT_VIOLATION(30008, "学生违纪"),

        SUBSTITUTE_COURSE(30009, "临时调代课"),

        STUDENT_AVATAR(30010, "学生照片"),

        STUDENT_ACTIVITY(30011, "活动参与"),

        STUDENT_JOB(30012, "任职履历"),

        STORE_GOODS_BORROW(30013, "物品借用"),

        PROPERTY_FIX(30014, "报修申请"),

        RESUME_RETURN_SCHOOL(30015, "复课返校"),

        LEAVE_LATE_PICK_UP(30016, "迟接申请"),

        WORK_OVERTIME(30017, "加班申请"),

        ADJUST_OVER_WORK(30018, "调代班申请"),

        LEAVE_MODE(30019, "接送方式审批"),

        ADJUST_DUTY_WEEK(30020, "调代值日周申请"),

        REQUISITION(30021, '物品领用')



        public Integer type

        public String name

        ApprovalFormType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalFormType getEnumByType(Integer type) {
            for (ApprovalFormType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum PromoterChooseType {

        ONE_PERSON(1 as byte, "可选一人"),

        MANY_PERSON(2 as byte, "可选多人")

        Byte type

        String name

        PromoterChooseType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static PromoterChooseType getEnumByType(Byte type) {
            PromoterChooseType[] allEnums = PromoterChooseType.values()
            for (PromoterChooseType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum ResultVOStatus {

        NORMAL(1 as byte, "正常"),

        NOT_NORMAL(0 as byte, "不正常")

        Byte type

        String name

        ResultVOStatus(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ResultVOStatus getEnumByType(Byte type) {
            ResultVOStatus[] allEnums = values()
            for (ResultVOStatus s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ApprovalUserNullType {

        UNABLE_TO_SUBMIT(1 as byte, "无法提交"),

        AUTOMATIC_TRANSFER(2 as byte, "自动转交")

        Byte type

        String name

        ApprovalUserNullType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalUserNullType getEnumByType(Byte type) {
            ApprovalUserNullType[] allEnums = ApprovalUserNullType.values()
            for (ApprovalUserNullType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ApprovalProcessUserType {

        PERSON(1 as byte, "指定人员"),

        PROMOTER(2 as byte, "发起人自己"),

        STUDENT_PARENT(3 as byte, "所选学生家长"),

        HEAD_OF_DEPARTMENT(4 as byte, "部门主管"),

        PROMOTER_CHOOSE(5 as byte, "发起人自选"),

        CLASS_MASTER(6 as byte, "所选学生班主任"),

        DEPARTMENT(7 as byte, "指定部门"),
        ROLE(8 as byte, "指定角色"),

        ALL(101 as byte, "所有人")

        Byte type

        String name

        ApprovalProcessUserType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalProcessUserType getEnumByType(Byte type) {
            ApprovalProcessUserType[] allEnums = ApprovalProcessUserType.values()
            for (ApprovalProcessUserType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum ApprovalConditionType {

        PROMOTER(1 as byte, "发起人"),

        RADIO(2 as byte, "单选框"),

        SELECT_BOX(3 as byte, "是否选择框"),

        NUMBER_INPUT_BOX(4 as byte, "数字输入框"),

        TIME(5 as byte, "时长"),

        MONEY(6 as byte, "金额"),

        STUDENT_BOX(7 as byte, "学生选择器"),

        Calculation_formula(8 as byte, "计算公式")

        public Byte type

        public String name

        ApprovalConditionType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalConditionType getEnumByType(Byte type) {
            ApprovalConditionType[] allEnums = ApprovalConditionType.values()
            for (ApprovalConditionType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ApprovalPromoterType {

        ALL_PERSON(1 as byte, "所有人员"),

        DESIGNATED_PERSON(2 as byte, "指定人员")

        public Byte type

        public String name

        ApprovalPromoterType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalPromoterType getEnumByType(Byte type) {
            ApprovalPromoterType[] allEnums = ApprovalPromoterType.values()
            for (ApprovalPromoterType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    /**
     * 算法
     * */
    static enum Algorithm {

        FACE(1 as Byte, '人脸算法'),

        BODY(2 as Byte, '人体算法'),

        CAR(3 as Byte, '车算法')

        public Byte type

        public String name

        Algorithm(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static Algorithm getEnumByType(Byte type) {
            Algorithm[] allEnums = values()
            for (Algorithm s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum PadDataType {

        VISITOR(1, "访客列表"),

        LECTURE(2, "首页课表")

        public Integer type

        public String name

        PadDataType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static PadDataType getEnumByType(Integer type) {
            PadDataType[] allEnums = PadDataType.values()
            for (PadDataType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum HomeNoticeType {

        ASK_FOR_LEAVE(1 as byte, "请假"),

        SING_UP(2 as byte, "报名"),

        ELEGANCE(3 as byte, "风采"),

        NOTICE(4 as byte, "通知"),

        CLOCK_IN(5 as byte, "打卡"),

        ELECTIVE(6 as byte, "选科")

        public Byte type

        public String name

        HomeNoticeType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static HomeNoticeType getEnumByType(Byte type) {
            for (HomeNoticeType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ImpType {

        STUDENT_IMPORT(1 as byte, "批量导入学生"),

        BASE_INFO_EXPORT(2 as byte, "导出基本信息"),

        BASE_INFO_IMPORT(3 as byte, "修改基本信息"),

        UNIT_STUDENT_EXPORT(4 as byte, "学生重新分班导出"),

        UNIT_STUDENT_IMPORT(5 as byte, "学生重新分班导入"),

        CODE_EXPORT(6 as byte, "批量修改学号导出"),

        CODE_IMPORT(7 as byte, "批量修改学号导入"),

        CARD_NUM_EXPORT(8 as byte, "批量修改一卡通导出"),

        CARD_NUM_IMPORT(9 as byte, "批量修改一卡通导入"),

        ID_CARD_EXPORT(10 as byte, "批量修改身份证导出"),

        ID_CARD_IMPORT(11 as byte, "批量修改身份证导入"),

        STUDENT_EXPORT(12 as byte, "导出学生"),

        AVATAR_IMPORT(13 as byte, "导入照片"),

        AVATAR_EXPORT(14 as byte, "导出照片"),

        BED_EMPTY_EXPORT(15 as byte, "导出空床位"),

        BED_IMPORT(16 as byte, "导入床位表"),

        BED_EXPORT(17 as byte, "导出床位表"),

        TEACHER_BASE_INFO_EXPORT(18 as byte, "导出教师基本信息"),

        TEACHER_BASE_INFO_IMPORT(19 as byte, "导入教师基本信息"),

        STUDENT_SYMPTOM_RECORD_EXPORT(20 as byte, "学生就医记录导出"),

        ALI_DEVICE_MOLD_EXPORT(21 as byte, "阿里设备模板导出"),

        ALI_DEVICE_EXPORT_IMPORT(22 as byte, "阿里设备导入"),

        NONRESIDENT_EXPORT(23 as byte, "通校生导出"),

        NONRESIDENT_IMPORT(24 as byte, "通校生导入"),

        STUDENT_PARENT_IMPORT(25 as byte, "家长导入"),

        STUDNET_EVALUATION_EXPORT(26 as byte, "学生评教导出"),

        ATTENDANCE_EXPORT(27 as byte, "德育考勤导出"),

        TEACHING_EVALUATION_TEMPLATE_IMPORT(28 as byte, "教师评语批量导入"),

        AUTH_DOWNLOAD_RECORD_PERSON_4_HIK_EXPORT(29 as byte, "权限更新记录详情导出"),

        STUDY_REPORT_EXPORT(30 as byte, "学业报告导出"),

        MORAL_RESULT_EXPORT(31 as byte, "德育考勤结果导出"),

        DOCUMENT_EXPORT(32 as byte, "公文导出"),

        EVENT_ATTENDANCE_SIGN_IN_RECORD_EXPORT(33 as byte, "签到事件记录导出"),

        PLAN_GROUP_PERSON_EXPORT_TYPE(34 as byte, "人员通行组导出"),

        TRACK_RECORD_EXPORT(35 as byte, "人员通行记录导出"),

        BOX_STUDENT_DATA_EXPORT(36 as byte, "学生数据发布导出"),

        BOX_STUDENT_DATA_IMPORT(37 as byte, "导入学生数据"),

        PERSON_ISSUE_FAILURE_EXPORT(38 as byte, "下发失败人员名单导出"),

        DOCKING_FIRM_AUTH_PERSON_RESULT_EXPORT(39 as byte, "人员通行权限下发结果导出"),

        VOCATIONAL_SCHOOL_MAJOR_EXPORT(40 as byte, "职校专业模板导出"),
        VOCATIONAL_SCHOOL_MAJOR_IMPORT(41 as byte, "职校专业导入"),

        VOCATIONAL_CLASS_BATCH_EXPORT(42 as byte, "班级批量导入模板"),
        VOCATIONAL_CLASS_BATCH_IMPORT(43 as byte, "班级批量导入"),

        VOCATIONAL_STUDENT_EXPORT(44 as byte, "职校批量导入学生模板"),
        VOCATIONAL_STUDENT_IMPORT(45 as byte, "职校批量导入学生"),

        DOCKING_FIRM_SYNC_PERSON_RESULT_EXPORT(46 as byte, "人员同步状态结果导出"),

        TEACHER_ROLE_EXPORT(47 as byte, "批量修改教职工角色模板"),

        TEACHER_ROLE_IMPORT(48 as byte, "批量修改教职工角色"),

        PERSON_GROUP_EXPORT(49 as byte, "人员分组导出"),

        CHECK_FACE_RECORD_EXPORT(50 as byte, "人脸检测记录导出"),

        PERSON_CURRENT_PIC_STATUS_EXPORT(51 as byte, "所有人照片当前状态导出"),

        HONORDECLARATION_EXPORT(52 as byte, "批量导出荣誉记录"),

        ACTIVITY_EXPORT(53 as byte, "批量导出学生活动记录"),

        JOB_EXPORT(54 as byte, "批量导出任职履历记录"),

        GATING_CONFIG_ISSUE_TASK_EXPORT(55 as byte, "权限下发结果导出"),

        DEVICE_CHANNEL_EXPORT(56 as byte, "导出通道列表"),

        DEVICE_CHANNEL_IMPORT(57 as byte, "批量导入通道模板"),

        FACE_LIB_USER_CONTROL_EXPORT(58 as byte, "人脸库配置结果导出"),

        STUDNET_JOGGINGPLAN_DAILY_EXPORT(64 as byte, "学生跑操记录"),

        GRADUATE_STUDENT_EXPORT(65 as byte, "毕业学生导出"),

        ATTENDANCE2_EXPORT(66 as byte, "德育考勤导出(新)"),

        UNIT_JOGGING_COMPLETION_RATE(67 as byte, "班级跑操完成率"),
        STUDENT_JOGGING_COMPLETION_RATE(68 as byte, "学生跑操完成率"),

        MORAL_REGISTER_RECORD_EXPORT(69 as byte, "德育登记记录导出"),

        ELECTIVE_TASK_RESULT_EXPORT(70 as byte, "选课结果导出"),

        TEACHER_CARD_EXPORT(71 as byte, "教职工卡号导出"),

        TEACHER_CARD_IMPORT(72 as byte, "教职工卡号导入"),

        TEACHER_JOB_NUM_EXPORT(73 as byte, "教职工工号导出"),

        TEACHER_JOB_NUM_IMPORT(74 as byte, "教职工工号导入"),

        GATING_CONFIG_ISSUE_RECORD_EXPORT(75 as byte, "人员下发记录"),

        BOX_TEACHER_DATA_EXPORT(96 as byte, "教师数据发布导出"),

        BOX_TEACHER_DATA_IMPORT(97 as byte, "教师数据发布导入"),

        PAD_DEVICE_IMPORT(98 as byte, "班牌导入"),

        PAD_DEVICE_EXPORT(99 as byte, "班牌导出"),

        DOOR_NAME_DEVICE_IMPORT(100 as byte, "房间号导入"),

        DOOR_NAME_DEVICE_EXPORT(101 as byte, "房间号导出"),

        STUDENT_LIVE_PLACE_IMPORT(102 as byte, "批量修改家庭地址导入"),
        STUDENT_LIVE_PLACE_EXPORT(103 as byte, "批量修改家庭地址导入"),

        TEACHER_DEPARTMENT_EXPORT(105 as byte, "批量修改教职工部门模板导出"),

        TEACHER_DEPARTMENT_IMPORT(106 as byte, "批量修改教职工部门导入"),

        VISITOR_EXPORT(107 as byte, "访客导出"),
        //导出沟通导师
        GROWTH_TEACHER_EXPORT(108 as byte, "沟通导师导出"),
        //导出沟通学生
        GROWTH_STUDENT_EXPORT(109 as byte, "沟通学生导出"),
        //导出全校记录
        GROWTH_CAMPUS_RECORD_EXPORT(110 as byte, "全校记录导出");

        //导入导出相关枚举 增加时需要看下bgb项目中枚举 以免重复


        Integer type

        String name

        ImpType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static ImpType getEnumByType(Integer type) {
            values().find {
                impType ->
                    impType.type == type
            }
        }


    }

    static enum ImpStatus {

        DEFAULT(0 as byte, "未处理"),

        IMPORTING(1 as byte, "处理中"),

        IMPORT_FAILED(-1 as byte, "失败"),

        IMPORT_SUCCESS(2 as byte, "成功")

        Byte status

        String name

        ImpStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static ImpStatus getEnumByStatus(Byte status) {
            values().find {
                impStatus ->
                    impStatus.status == status
            }
        }
    }

    static enum Screen_Type {

        BUGU(0 as byte, "布谷"),

        DATAV(1 as byte, "dataV"),

        EASYV(2 as byte, "easyV")

        public Byte type

        public String name

        Screen_Type(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static Screen_Type getEnumByType(Byte type) {
            for (Screen_Type s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum TrackAlarmEventType {
        A_LI_CROSS_LINE("", "CrossLine", 1000, "越界告警"),

        A_LI_CROWD("", "Crowd", 1001, "拥挤告警"),

        A_LI_WANDERING("", "Wandering", 1002, "徘徊检测"),

        A_LI_BLACKLIST("", "Blacklist", 1003, "人员轨迹"),

        A_LI_STRANGER("", "Stranger", 1004, "陌生人告警"),

        A_LI_FORBIDDEN_AREA("", "ForbiddenArea", 1005, "闯禁告警"),

        A_LI_LEAVE_SEAT("", "LeaveSeat", 1006, "离岗"),

        LINGER_EVENT("307", "131590", 2001, "徘徊事件"),

        GATHERING_EVENT("311", "131593", 2002, "聚众事件"),

        RUNNING_EVENT("", "", 2003, "异常奔跑事件"),

        OVER_THE_FENCE_EVENT("", "", 2004, "翻越围栏事件"),

        BLACKLIST_EVENT("", "", 2005, "黑名单"),

        COERCION_EVENT("41", "", 2006, "胁迫"),

        INTRUSION_EVENT("303", "131588", 2007, "区域入侵"),

        ILLEGAL_CARD_EVENT("1446", "", 2008, "非法卡超次"),

        FIRE_ALARM_EVENT("5121", "", 2009, "火警事件"),

        CROSS_BORDER_EVENT("", "131585", 2010, "越界侦测事件"),

        EMERGENCY_EVENT("", "327687", 2011, "紧急报警事件"),

        PARKING_DETECTION_EVENT("", "131591", 2012, "停车侦测"),

        LEAVING_POST_EVENT("", "131603", 2013, "离岗"),

        MONITORING_POINT_OFFLINE_EVENT("", "889196545", 2014, "监控点离线"),

        GAS_ALARM_EVENT("", "254069", 2015, "燃气报警"),

        SMOKE_ALARM_EVENT("", "254075", 2016, "烟雾报警"),

        PEOPLE_CROSSING_THE_CORDON_ALARM_EVENT("962", "", 2017, "人穿越警戒线"),

        EMERGENCY_ASSISTANCE_EVENT("1907", "", 2018, "紧急求助"),

        INTRUSION_ALARM("13110", "", 2019, "入侵报警"),

        PLAY_MOBILE_EVENT("", "", 2020, "玩手机"),

        SLEEP_EVENT("", "", 2021, "睡岗"),

        MAN_NUM_EVENT("", "", 2022, "人数异常"),

        SMOKING_EVENT("", "", 2023, "吸烟"),

        STUDENT_FACE_ANALYSIS("", "", 2025, "学生轨迹提醒"),

        TEACHER_FACE_ANALYSIS("", "", 2026, "教师轨迹提醒"),

        TERMINAL_OFFLINE("", "", 3001, "终端离线"),

        TERMINAL_ONLINE("", "", 3002, "终端在线"),

        DEVICE_OFFLINE("", "", 3003, "设备离线"),

        DEVICE_ONLINE("", "", 3004, "设备在线")

        String alarmCode

        String eventType

        int event

        String eventName

        TrackAlarmEventType(String alarmCode, String eventType, int event, String eventName) {
            this.alarmCode = alarmCode
            this.eventType = eventType
            this.event = event
            this.eventName = eventName
        }

        static TrackAlarmEventType getEnumByAlarmCode(String alarmCode) {
            values().find {
                alarmEventType ->
                    alarmEventType.alarmCode == alarmCode
            }
        }

        static TrackAlarmEventType getEnumByEvent(int event) {
            values().find {
                alarmEventType ->
                    alarmEventType.event == event
            }
        }

        static TrackAlarmEventType getEnumByEventName(String eventName) {
            values().find {
                alarmEventType ->
                    alarmEventType.eventName == eventName
            }
        }

        static TrackAlarmEventType getEnumByEventType(String eventType) {
            values().find {
                alarmEventType ->
                    alarmEventType.eventType == eventType
            }
        }
    }

    static enum DataCenterCourseType {

        ALL(0 as byte, "全部"),

        SCHOOL(1 as byte, "本校课程"),

        ER_YA(2 as byte, "尔雅课程"),

        XUE_YIN(3 as byte, "学银课程")

        public Byte type

        public String name

        DataCenterCourseType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DataCenterCourseType getEnumByType(Byte type) {
            DataCenterCourseType[] allEnums = DataCenterCourseType.values()
            for (DataCenterCourseType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum EventEffectiveType {
        LONG_TIME(1 as byte, "长期有效"),

        EVERY_DAY_TIMING(2 as byte, "每天定时有效"),

        CUSTOMIZE(3 as byte, "自定义")

        public Byte status

        public String name

        EventEffectiveType(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static EventEffectiveType getEnumByType(Byte status) {
            for (EventEffectiveType s : values()) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    static enum BookBorrowTimeType {

        ONE_MONTH(1 as byte, "近1个月"),

        THREE_MONTH(2 as byte, "近3个月"),

        SIX_MONTH(3 as byte, "近6个月"),

        ONE_YEAR(4 as byte, "近12个月")

        public Byte type

        public String name

        BookBorrowTimeType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BookBorrowTimeType getEnumByType(Byte type) {
            BookBorrowTimeType[] allEnums = BookBorrowTimeType.values()
            for (BookBorrowTimeType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum CourseOptionTaskStatus {

        DELETE(0 as byte, "已删除"),

        NOT_START(1 as byte, "未开始"),

        PROGRESS(2 as byte, "进行中"),

        END(3 as byte, "已截止")

        public Byte status

        public String name

        CourseOptionTaskStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static CourseOptionTaskStatus getEnumByType(Byte status) {
            CourseOptionTaskStatus[] allEnums = CourseOptionTaskStatus.values()
            for (CourseOptionTaskStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    static enum MessageCatesTest {

        HOME_SCHOOL_CONNECT(2 as Byte, '家校通消息', 'vZnL6nF5WVoA3eIv0arY9GV6Nm00ruz26Msr3PuHjyY', 'https://res.yunzhiyuan100.com/we_chat/<EMAIL>'),

        STUDENT_PARENT_CHAT(3 as Byte, "学生动态", "", ''),

        COURSE_CHOICE_CONFIRM(102 as Byte, "课程确认", 'g1unDuk0VjLU-E1Do2vQ9W_kwIVETKEMMEtbiB6EfHg', ''),

        LEAVE_SCHOOL_NOTICE(4 as byte, "学生离校通知", "eP6W61nNPI8c6tdzMJLbz5pTWrjhT5JexQAME-UaT4E", ""),

        LEAVE_SCHOOL_REMIND(5 as byte, "学生离校提醒", "oiW2JnmzC7T6D188emxaWaF3yhR_0W8A0hLOMV1ZoUI", ""),

        ASK_FOR_LEAVE_REMIND(6 as byte, "学生请假提醒", "Q6WAJqELE-ykkrtW0oQIi6IuZdBJJe2qLKBys4LfLiM", ""),

        ENTER_SCHOOL_REMIND(7 as byte, "学生通过闸机进门提醒", "8D4WkuLmXI8nUWkNGYelQSU8vD5MyebRJTfr62oIFjQ", ""),

        SCHOOL_MORAL_NOTICE(8 as byte, "学校德育统计结果通知", "vZnL6nF5WVoA3eIv0arY9GV6Nm00ruz26Msr3PuHjyY", ""),

        SYMPTOM_CHECKING_PARENT_TEST(9 as byte, "检测结果通知", "HvLhgTtAKy-RL3idbummMr2UyW7Lko2lxRbn_aLFPAk", ""),

        SYMPTOM_CHECKING_MASTER_TEST(10 as byte, "检测结果通知", "HvLhgTtAKy-RL3idbummMr2UyW7Lko2lxRbn_aLFPAk", ""),

        ILLNESS_NOTIFY_DOCTOR_TEST(11 as byte, "学生请假提醒", "DnCyLlYHK4c2xGDdv8TgghqHFWMHPl0emm_1RMCiD88", ""),

        EXAMINATION_NOTICE(12 as byte, "考务安排通知", "cAvvoVsd4r3uEDGm5DBFUW5VKCZz5lZ4cinZPKXFd10", ""),

        ILLNESS_NOTIFY_DOCTOR_TO_RECORD_TEST(13 as byte, "就医申请登记提醒", "NS-XTxeOTGrU2C8SNmAiFnT8RdG5N2UH10N5asNmDdI", ""),

        CLOCK_NOTICE_TEST(14 as byte, "打卡任务发布成功后的即时通知", "EXg-2UtGlCI7CfBdwJ_5wVuIm7SgKRsl1LHmzNlrSH0", ""),

        CLOCK_REMIND_TEST(15 as byte, "打卡日每日提醒时间提醒", "BTC0a1ISzf3RfAMT6e8lMFnDlz11FVjXyZRd0y2KJK4", ""),

        SIGN_UP_CHECKING_PARENT_TEST(105 as byte, "活动报名通知", "EXg-2UtGlCI7CfBdwJ_5wVuIm7SgKRsl1LHmzNlrSH0", "")


        public Byte cate

        public String name

        public String templateId

        public String logo

        MessageCatesTest(Byte cate, String name, String templateId, String logo) {
            this.cate = cate
            this.name = name
            this.templateId = templateId
            this.logo = logo
        }
        //家校通知归类
        static List<MessageCatesTest> getSchoolHomeMessageCates() {
            return [HOME_SCHOOL_CONNECT, COURSE_CHOICE_CONFIRM]
        }

        static MessageCatesTest getEnumByCate(Byte cate) {
            MessageCatesTest[] allEnums = MessageCatesTest.values()
            for (MessageCatesTest s : allEnums) {
                if (s.cate == cate) {
                    return s
                }
            }
            return null
        }
    }

    static enum MessageCatesDaily {

        HOME_SCHOOL_CONNECT_DAILY(2 as Byte, '家校通消息', 'FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4', 'https://res.yunzhiyuan100.com/we_chat/<EMAIL>'),

        STUDENT_PARENT_CHAT_DAILY(3 as Byte, "学生动态", "", ''),

        COURSE_CHOICE_CONFIRM_DAILY(102 as Byte, "课程确认", 'uAClFovAy_CybOg-9UJvjGZ3LNJQXSvcM6vrngH7_gw', ''),

        LEAVE_SCHOOL_NOTICE_DAILY(4 as byte, "学生离校通知", "LXFaVsYGqVyjTkWcEp0y0oj7YtueHqTzDft4cdDxjHo", ""),

        LEAVE_SCHOOL_REMIND_DAILY(5 as byte, "学生离校提醒", "cgG59XpU3oApx-ElWmfc51vj7REZCH-w-PbYVeDIaik", ""),

        ASK_FOR_LEAVE_REMIND_DAILY(6 as byte, "学生请假提醒", "mp-lxdMgj7xc3eSTY7TUyhzzpTZNwEabKz-PwMY_KV8", ""),

        ENTER_SCHOOL_REMIND_DAILY(7 as byte, "学生通过闸机进门提醒", "4tGr4hf9mBKzO-G5pWgFrJWjIaho3jXZoYvLboV7IKE", ""),

        SCHOOL_MORAL_NOTICE_DAILY(8 as byte, "学校德育统计结果通知", "FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4", ""),

        SYMPTOM_CHECKING_PARENT_DAILY(9 as byte, "检测结果通知", "KAw8VUveUYhTOmmChJ3AYPJ963JjJlLOo0yC3BEAWTA", ""),

        SYMPTOM_CHECKING_MASTER_DAILY(10 as byte, "检测结果通知", "KAw8VUveUYhTOmmChJ3AYPJ963JjJlLOo0yC3BEAWTA", ""),

        ILLNESS_NOTIFY_DOCTOR_DAILY(11 as byte, "学生请假提醒", "mp-lxdMgj7xc3eSTY7TUyhzzpTZNwEabKz-PwMY_KV8", ""),

        EXAMINATION_NOTICE_DAILY(12 as byte, "考务安排通知", "KqpGNlqzjngEodkLb3Ae8GXQ6Toh_KczTK3m94mp5Qo", ""),

        ILLNESS_NOTIFY_DOCTOR_TO_RECORD_DAILY(13 as byte, "就医申请登记提醒", "C5IClhYnxgtvJIkKy38vjy0QXV3CuXjr8yFvh_AiUCU", ""),

        CLOCK_NOTICE_DAILY(14 as byte, "打卡任务发布成功后的即时通知", "FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4", ""),

        CLOCK_REMIND_DAILY(15 as byte, "打卡日每日提醒时间提醒", "LuoMxts9lP_o9F1SQ9ujRZwXK3Qz8x4D9-DPSvqy4XE", ""),

        SIGN_UP_CHECKING_PARENT_DAILY(105 as byte, "活动报名通知", "FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4", "")

        public Byte cate

        public String name

        public String templateId

        public String logo

        MessageCatesDaily(Byte cate, String name, String templateId, String logo) {
            this.cate = cate
            this.name = name
            this.templateId = templateId
            this.logo = logo
        }

        static MessageCatesDaily getEnumByCate(Byte cate) {
            MessageCatesDaily[] allEnums = MessageCatesDaily.values()
            for (MessageCatesDaily s : allEnums) {
                if (s.cate == cate) {
                    return s
                }
            }
            return null
        }
    }

    static enum InputStatus {

        FAILURE(-1 as byte, "处理失败"),

        NON(0 as byte, '未导入'),

        DAORUING(1 as byte, '导入中'),

        DAORU_DONE(2 as byte, '导入完成'),

        JISUANING(3 as byte, '计算中'),

        JISUAN_DONE(4 as byte, '计算完成'),

        FINISH(5 as byte, "处理完成")

        public Byte status

        public String name

        InputStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static InputStatus getEnumByStatus(Byte status) {
            for (InputStatus s : values()) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    static enum UnitType {

        ADMINISTRATIVE_CLASS(1 as Byte, '行政教学班', "本班教学"),

        XUAN_XIU(2 as byte, "选修班", ""),

        ELECTIVE_CLASS(3 as Byte, '选考班', "走班教学"),

        NATURAL_CLASS(4 as Byte, "学考班", "走班教学"),

        public Byte type

        public String name

        public String memo

        UnitType(Byte type, String name, String memo) {
            this.type = type
            this.name = name
            this.memo = memo
        }

        static List<Byte> getAllUnitTypeList() {
            return values()*.type
        }

        static UnitType getEnumByType(Byte type) {
            for (UnitType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static UnitType getEnumByName(String name) {
            values().find {
                it.name == name
            }
        }
    }

    static enum AllocationType {

        MORAL(1 as byte, "考勤规则配置"),

        TEMPERATURE(2 as byte, "测温规则配置"),

        public Byte type

        public String name

        AllocationType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AllocationType getEnumByType(Byte type) {
            AllocationType[] allEnums = AllocationType.values()
            for (AllocationType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum SignInMethod {

        FACE(1 as byte, "人脸"),

        CARD(2 as byte, "刷卡"),

        PAD(3 as byte, "班牌手动签到"),

        PC(4 as byte, "PC手动签到"),

        MOBILE(5 as byte, "移动端手动签到")

        Byte type

        String name

        SignInMethod(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static SignInMethod getEnumByType(Byte type) {
            values().find { signInMethod -> signInMethod.type == type
            }
        }
    }

    static enum DeviceFirm {
        HK_EDU(101, "海康edu平台设备"),
        HK_YM(102, "海康云眸平台设备"),
        HK_ISUP(103, "海康ISUP直连设备"),

        DH_DSS(201, "大华DSS平台设备"),
        DH_YR(202, "大华云睿平台"),
        DH_ICC(203, "大华icc平台"),

        DH_SDK(204, "大华SDK平台设备"),

        FS_CAR(301, "富士平台车牌识别设备"),

        ALY_DEVICE(410, "阿里云一体机"),

        ALY_EDGE_DEVICE(411, "阿里云边缘一体机"),

        ANDROID_DEVICE(501, "安卓门禁"),

        YS_DEVICE(601, "宇视门禁"),

        MEGVII(701, "旷视")

        int firm
        String name

        DeviceFirm(int firm, String name) {
            this.firm = firm
            this.name = name
        }

        static DeviceFirm getEnumByFirm(Integer firm) {
            values().find {
                deviceFirm ->
                    deviceFirm.firm == firm
            }
        }
    }

    static enum CarCurrentType {
        CAR_IN(1, '进'),

        CAR_OUT(2, '出')

        Integer type
        String name

        CarCurrentType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static CarCurrentType getEnumByType(Byte type) {
            for (CarCurrentType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            null
        }
    }

    static enum DeviceType {

        OTHER(b0, "其他"),

        CLASS_PAD(b1, "班牌"),//横板

        TEACH_AIO(b2, "教学一体机"),
        @Deprecated
        FACE_GATE(b3, "人脸闸机"),

        FACE_CAMERA(b4, "摄像头"),

        GATE(b5, "门禁设备"),

        VISITOR_MACHINE(b6, "访客机"),

        @Deprecated
        CAR_GATE(b7, "车辆闸机"),

        SCANNER(b8, "扫描仪"),

        BOX_SCREEN(b9, "宿舍考勤发布屏幕"),

        EDGE_BOX(b10, "智能一体机"),

        PICK_SCREEN(b11, "智慧校园接送大屏"),
        SCHOOL_BOX(b12, "校园一体机"),//点点通

        GPY(b13, "高拍仪")

        Byte type

        String name

        DeviceType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DeviceType getEnumByType(Byte type) {
            for (DeviceType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            null
        }
    }

    static enum AskLeaveType {

        STAY_BEDROOM(5 as byte, "回寝室"),

        LEAVE_SCHOOL(6 as byte, "离校"),

        STAY_CLASS_ROOM(7 as byte, "留教室"),

        DOCTOR_ROOM(8 as byte, "回寝室")

        public Byte type

        public String name

        AskLeaveType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AskLeaveType getEnumByType(Byte type) {
            AskLeaveType[] allEnums = AskLeaveType.values()
            for (AskLeaveType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    //评分选项类别参数
    static enum MoralMenuType {

        CLASS_SCORE(1L, "班级评分"),

        DORMITORY_SCORE(2L, "宿舍评分")

        public Long id

        public String name

        MoralMenuType(Long id, String name) {
            this.id = id
            this.name = name
        }

        static MoralMenuType getEnumByType(Long id) {
            MoralMenuType[] allEnums = MoralMenuType.values()
            for (MoralMenuType s : allEnums) {
                if (s.id == id) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }

        static String bit2Types(int bit) {
            if (bit < 1) {
                return null
            }
            List<Long> list = []
            values().each { menuType ->
                Integer type = Math.pow(2, menuType.id - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    list.add(menuType.id)
                }
            }
            Joiner.on(",").join(list)
        }

        static int types2Bit(String types) {
            int bit = 0
            if (!types) {
                return bit
            }
            types.split(",").each {
                bit += Math.pow(2, it.toInteger() - 1).intValue()
            }
            return bit
        }
    }

    //评分项状态
    static enum MoralResultStatus {

        IN_FORCE(1 as byte, "正常"),

        CANCELLED(0 as byte, "已撤销")

        public Byte status

        public String name

        MoralResultStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static MoralResultStatus getEnumByType(Byte status) {
            MoralResultStatus[] allEnums = MoralResultStatus.values()
            for (MoralResultStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte status) {
            return getEnumByType(status)?.name
        }
    }

    static enum AttendanceGroupType {

        ALL(1 as Byte, '所有学生'),

        RESIDENT(2 as Byte, '住校生'),

        NO_RESIDENT(3 as Byte, '通校生'),

        CUSTOM(4 as Byte, '自定义'),

        public Byte type

        public String name

        AttendanceGroupType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AttendanceGroupType getEnumByType(Byte type) {
            for (AttendanceGroupType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 设配种类
     * */
    static enum DeviceKind {
        IPC(1 as Byte, 'IPC'),

        NVR(2 as Byte, 'NVR'),

        public Byte type

        public String name

        DeviceKind(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DeviceKind getEnumByType(Byte type) {
            DeviceKind[] allEnums = values()
            for (DeviceKind s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ProtocolEnumType {
        RTSP(1 as byte, 'RTSP'),

        GB(2 as byte, 'GB'),

        HK(3 as byte, 'HK'),

        Private(4 as byte, "大华私有"),

        Onvif(5 as byte, "Onvif"),

        Onvifs(6 as byte, "Onvifs"),

        GB28181(7 as byte, "GB28181"),

        HIKVISION(8 as byte, "海康"),

        BSCP(9 as byte, "RTSP")

        public Byte type

        public String name

        ProtocolEnumType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static YR_LIST = [1 as byte, 4 as byte, 5 as byte, 6 as byte, 7 as byte, 8 as byte, 9 as byte]

        static ProtocolEnumType getEnumByType(Byte type) {
            ProtocolEnumType[] allEnums = values()
            for (ProtocolEnumType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static ProtocolEnumType getEnumByName(String name) {
            ProtocolEnumType[] allEnums = values()
            for (ProtocolEnumType s : allEnums) {
                if (s.name == name) {
                    return s
                }
            }
            return null
        }
    }

    //德育统计汇总目录分类
    static enum MoralScoreType {

        OF_All(0 as byte, "全部"),

        OF_UNIT(1 as byte, "班级"),

        OF_DORMITORY(2 as byte, "寝室"),

        OF_STUDENT(3 as byte, "个人"),

        OF_UNIT_OPTION(4 as byte, "班级分项"),

        OF_DORMITORY_OPTION(5 as byte, "寝室分项"),

        OF_PUBLISHER_OPTION(6 as byte, "根据提交人分项"),

        public Byte type

        public String name


        MoralScoreType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MoralScoreType getEnumByType(Byte type) {
            MoralScoreType[] allEnums = values()
            for (MoralScoreType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }
    //德育统计 周报，日报
    static enum MoralCountType {

        BY_DAY(1 as byte, "日报"),

        BY_WEEK(2 as byte, "周报"),

        BY_MONTH(3 as byte, "月报"),

        BY_SEMESTER(4 as byte, "学期报")

        public Byte type

        public String name


        MoralCountType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MoralCountType getEnumByType(Byte type) {
            MoralCountType[] allEnums = values()
            for (MoralCountType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    //德育统计 - 评分项
    static enum MoralOptionType {

        NORMAL(1 as byte, "正常"),

        LATE(2 as byte, "迟到"),

        ASK_FOR_LEAVE(6 as byte, "请假"),

        ABNORMAL(-1 as byte, "异常"),

        NON(5 as byte, "未考勤")

        public Byte type

        public String name

        MoralOptionType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MoralOptionType getEnumByType(Byte type) {
            MoralOptionType[] allEnums = MoralOptionType.values()
            for (MoralOptionType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    //德育统计 - 登记项 - 通知人员类型
    static enum MoralOptionNoticeType {

        NOT_NOTICE(0 as byte, "不通知"),

        HEADMASTER(1 as byte, "班主任"),

        PARENT(2 as byte, "家长"),

        HEADMASTER_PARENT(3 as byte, "班主任和家长")

        public Byte type

        public String name

        MoralOptionNoticeType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MoralOptionNoticeType getEnumByType(Byte type) {
            MoralOptionNoticeType[] allEnums = MoralOptionNoticeType.values()
            for (MoralOptionNoticeType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    static enum MoralPushType {

        EVERY_DAY(1 as byte, "每日"),

        EVERY_WEEK(2 as byte, "每周"),

        public Byte type

        public String name

        MoralPushType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MoralPushType getEnumByType(Byte type) {
            MoralPushType[] allEnums = MoralPushType.values()
            for (MoralPushType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    /**
     * 海康ISC的事件类型枚举
     */
    static enum HikIscEventType {

        //十四中关注3个事件 大门医务室 人脸对比通过，轨迹用重点人员比对事件  寝室用 人脸比对事件
        CARD_FACE(196893, "一卡通人脸认证通过"),//刷卡人脸认证通过，有刷卡场景的

        FAIL_DOWN(131605, "倒地检测"),

        PERSON_COUNT_EXCEPTION(131664, "人数异常"),

        REN_LIAN_ZHUA_PAI(131614, "人脸抓拍事件"),//抓拍不一定对比成功，故做轨迹和考勤可不处理抓拍事件

        REN_LIAN_BI_DUI(131659, "人脸比对事件"),//寝室用这个事件

        REN_ZHENG_DUI_BI_TONG_GUO(197162, "人证对比通过"),//医务室 大门

        ZHONG_DIAN_REN_YUAN_BI_DUI(1644175361, "重点人员比对事件"),//相机都是重点人员事件

        MO_SHENG_REN_SHI_BIE(1644171265, "陌生人识别事件"),

        GAO_PIN_REN_YUAN_SHI_BIE(132865, "高频人员识别事件")

        public Integer type

        public String name

        HikIscEventType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static HikIscEventType getEnumByType(Integer type) {
            HikIscEventType[] allEnums = HikIscEventType.values()
            for (HikIscEventType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum CourseType {

        CHINESE(1 as Byte, '语文', 1),

        ENGLISH(2 as Byte, '英语', 3),

        MATH(3 as Byte, '数学', 2),

        PHY(4 as Byte, '物理', 4),

        HISTORY(5 as Byte, '历史', 8),

        BIO(6 as Byte, '生物', 6),

        CHEM(7 as Byte, '化学', 5),

        GEO(8 as Byte, '地理', 9),

        POLITICS(9 as Byte, '政治', 7),

        TECH(10 as Byte, '技术', 9),

        GENERAL(11 as Byte, '综合', 0)

        public Byte type

        public String name

        public Integer index

        CourseType(Byte type, String name, Integer index) {
            this.type = type
            this.name = name
            this.index = index
        }

        static List<CourseType> getAllOption() {
            ImmutableList.builder()
                    .add(PHY)
                    .add(CHEM)
                    .add(BIO)
                    .add(POLITICS)
                    .add(HISTORY)
                    .add(GEO)
                    .add(TECH)
                    .build() as List<CourseType>
        }

        static List<Byte> getAllOptionType() {
            getAllOption()*.type
        }

        static CourseType getEnumByType(Byte type) {
            CourseType[] allEnums = CourseType.values()
            for (CourseType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static Byte getEnumTypeByName(String name) {
            CourseType[] allEnums = CourseType.values()
            for (CourseType s : allEnums) {
                if (StringUtils.equalsIgnoreCase(s.name, name)) {
                    return s.type
                }
            }
            return null
        }
    }

    static enum DataTemplateEnum {

        BUREAU(1 as byte, '局端')

        public Byte type

        public String name

        DataTemplateEnum(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DataTemplateEnum getEnumById(Byte type) {
            DataTemplateEnum[] allEnums = DataTemplateEnum.values()
            for (DataTemplateEnum s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum WenYinStatus {

        APPLY(0 as byte, "待处理"),

        EXECUTE(1 as byte, "已打印"),

        NOTICE(2 as byte, "已通知"),

        PICK_UP(3 as byte, "已取件"),

        DELETE(4 as byte, "已撤销")


        public Byte status

        public String name


        WenYinStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static WenYinStatus getEnumByType(Byte status) {
            WenYinStatus[] allEnums = WenYinStatus.values()
            for (WenYinStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    static enum YrCheckFaceResultStatus {

        FAILURE(-1, "检测失败"),

        CHECKING(0, "检测中"),

        QUALIFIED(1, "合格"),

        UNQUALIFIED(2, "不合格"),

        NOCHECK(3, "未检测"),

        NOPIC(4, "无照片")

        int status

        String name

        YrCheckFaceResultStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static YrCheckFaceResultStatus getEnumByStatus(int status) {
            YrCheckFaceResultStatus[] allEnums = YrCheckFaceResultStatus.values()
            for (YrCheckFaceResultStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static String getValueByStatus(int status) {
            return getEnumByStatus(status)?.name
        }
    }

    /**
     * 考勤状态 记录中只有 正常 晚归 缺勤
     */
    static enum AttendanceStatus {

        ABNORMAL(-1 as Byte, "异常"),

        NORMAL(1 as Byte, "正常"),

        LATE(2 as Byte, "晚归"),

        LOST(4 as Byte, '缺勤'),

        NON(5 as Byte, '未考勤'),

        ASK_FOR_LEAVE(6 as Byte, '请假'),

        NONRESIDENT(7 as Byte, '通校')

        public Byte status

        public String name

        AttendanceStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static AttendanceStatus getEnumByStatus(Byte status) {
            AttendanceStatus[] allEnums = AttendanceStatus.values()
            for (AttendanceStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    //房间的使用方式
    static enum DoorPlateType {

        TEACHING(1 as byte, "教学区"),

        DORMITORY(2 as byte, "宿舍区"),

        LIFE(3 as byte, "生活区")

        public Byte type

        public String name


        DoorPlateType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DoorPlateType getEnumByType(Byte type) {
            DoorPlateType[] allEnums = DoorPlateType.values()
            for (DoorPlateType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    static enum TeacherType {

        TEACH(0 as byte, "普通教学老师"),

        UNIT_DIRECTOR(1 as byte, "班主任"),

        DOCTOR(9 as byte, "医生")

        public Byte type

        public String name


        TeacherType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static TeacherType getEnumByType(Byte type) {
            TeacherType[] allEnums = TeacherType.values()
            for (TeacherType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum UnitTeacherType {

        TEACH(0 as byte, "教学"),

        ADMINISTRATION(1 as byte, "管理"),

        public Byte type

        public String name


        UnitTeacherType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static UnitTeacherType getEnumByType(Byte type) {
            UnitTeacherType[] allEnums = UnitTeacherType.values()
            for (UnitTeacherType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum SemesterType {
        LAST_S(b1, "上学期"),
        NEXT_S(b2, "下学期")

        public Byte type
        public String name
        public DateTime startDate = new DateTime()
        public DateTime endDate = new DateTime()
        public String years

        SemesterType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static SemesterType getEnumByType(Byte type) {
            SemesterType[] allEnums = SemesterType.values()
            for (SemesterType s : allEnums) {
                if (s.type == type) {
                    DateTime dateTime = new DateTime()
                    int year = dateTime.year
                    int month = dateTime.monthOfYear
                    switch (type) {
                        case LAST_S.type:
                            //TODO 升级时间魔法值去除
                            if (month >= 7) {
                                s.years = """${year}~${year + 1}"""
                                s.startDate = new DateTime().withMonthOfYear(8).withDayOfMonth(1)
                                s.endDate = new DateTime().withYear(year + 1).withMonthOfYear(1).withDayOfMonth(31)
                            } else {
                                s.years = """${year - 1}~${year}"""
                                s.startDate = new DateTime().withYear(year - 1).withMonthOfYear(8).withDayOfMonth(1)
                                s.endDate = new DateTime().withMonthOfYear(1).withDayOfMonth(31)
                            }
                            break
                        case NEXT_S.type:
                            //TODO 升级时间魔法值去除
                            if (month >= 7) {
                                s.years = """${year}~${year + 1}"""
                                s.startDate = new DateTime().withYear(year + 1).withMonthOfYear(2).withDayOfMonth(1)
                                s.endDate = new DateTime().withYear(year + 1).withMonthOfYear(7).withDayOfMonth(31)
                            } else {
                                s.years = """${year - 1}~${year}"""
                                s.startDate = new DateTime().withYear(year).withMonthOfYear(2).withDayOfMonth(1)
                                s.endDate = new DateTime().withYear(year).withMonthOfYear(7).withDayOfMonth(31)
                            }
                            break
                    }
                    return s
                }
            }
            return null
        }

    }

    static enum YearAndGradeAndUnitStatus {

        DELETE(0 as byte, "删除"),

        NORMAL(1 as byte, "正常"),

        UPGRADE(4 as byte, "升级"),

        FINISH(6 as byte, "毕业"),

        REMOVE(-1 as byte, "学期变动")

        public Byte status

        public String name

        YearAndGradeAndUnitStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }
    }

    static enum ApprovalPersonStatus {

        NO_NEED(-2 as byte, '无需操作'),//当上一个审批人操作时发现审批已失效时更改后面审批人的状态

        PUBLISH(-1 as byte, '发起'),

        FAILURE(0 as byte, '已失效'),

        APPROVAL(1 as byte, '已通过'),

        WAIT(2 as byte, '等待中'),

        PROGRESS(3 as byte, '审批中'),

        REFUSE(4 as byte, '已拒绝')

        public Byte status

        public String name

        ApprovalPersonStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static ApprovalPersonStatus getEnumByType(Byte type) {
            ApprovalPersonStatus[] allEnums = ApprovalPersonStatus.values()
            for (ApprovalPersonStatus s : allEnums) {
                if (s.status == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum HumanApprovalStatus {

        INCOMPLETE(0 as byte, "信息未完善，待提交"),

        TEACHER_OFFICE(1 as byte, "待教研室审核"),

        RE_SUBMIT_THREE(2 as byte, "教研室退回，待重新提交"),

        DEPARTMENT_AUDIT_ONE(3 as byte, "待系部审核"),

        RE_SUBMIT_ONE(4 as byte, "系部退回，待重新提交"),

        PERSONNEL_AUDIT(5 as byte, "待人事处审核"),

        DEPARTMENT_AUDIT_TWO(6 as byte, "人事退回，待系部审核"),

        RE_SUBMIT_TWO(7 as byte, "人事退回，待重新提交"),

        APPROVED(8 as byte, "审核通过"),

        REFUSE(9 as byte, "审核不通过"),


        public Byte status

        public String name

        HumanApprovalStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static HumanApprovalStatus getEnumByType(Byte status) {
            HumanApprovalStatus[] allEnums = HumanApprovalStatus.values()
            for (HumanApprovalStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }


    /**
     * 审批主体状态
     */
    static enum ApprovalStatus {

        FAILURE(-2 as byte, '已撤销'),

        DELETE(-1 as byte, '已删除'),

        INIT(0 as byte, '初始化状态，待流转'),

        APPROVALING(1 as Byte, '审批中'),

        DELIVER(2 as byte, '已转交'),

        APPROVALDENY(4 as Byte, '审批拒绝'),

        APPROVALED(6 as byte, '审批通过')

        public Byte status

        public String name

        ApprovalStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static ApprovalStatus getEnumByType(Byte type) {
            ApprovalStatus[] allEnums = ApprovalStatus.values()
            for (ApprovalStatus s : allEnums) {
                if (s.status == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum HumanMessageContentEnum {
        JOB_TITLE(1 as Byte, '职称变动'),

        JOB_CHANGE(2 as Byte, '岗位变动'),

        WORKING_STATUS(3 as Byte, "在职状态"),

        DEPARTMENT_CHANGE(4 as Byte, "部门调动"),
        ENTRY(5 as Byte, "入职录入")


        public Byte type

        public String name

        HumanMessageContentEnum(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static HumanMessageContentEnum getEnumById(Byte type) {
            HumanMessageContentEnum[] allEnums = HumanMessageContentEnum.values()
            for (HumanMessageContentEnum s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 疫情管控系统处理建议枚举
     */
    static enum VirusAdviceEnum {

        WATCH(1l, '继续观察'),

        SEEMINGLY(2l, '疑似'),

        DEFINITE(3, "确诊"),

        REMOVED(0l, "解除观察")

        public Long id

        public String name

        VirusAdviceEnum(Long id, String name) {
            this.id = id
            this.name = name
        }

        static VirusAdviceEnum getEnumById(Long id) {
            VirusAdviceEnum[] allEnums = VirusAdviceEnum.values()
            for (VirusAdviceEnum s : allEnums) {
                if (s.id == id) {
                    return s
                }
            }
            return null
        }
    }

    static enum Temperature {

        DANGER("重点关注", 38D, 40D),

        UNUSUAL("异常", 37.3D, 38D),

        NORMAL("正常", 35D, 37.3D)

        public String name

        public Double min

        public Double max

        Temperature(String name, Double min, Double max) {
            this.name = name
            this.min = min
            this.max = max
        }

        static boolean isNormal(Double temperature) {
            return (NORMAL.min <= temperature && temperature < NORMAL.max)
        }
    }

    static enum UserTypeEnum {

        STUDENT(1 as Byte, '学生'),

        PARENT(2 as Byte, '家长'),

        TEACHER(6 as Byte, "教职工"),

        DOCTOR(9 as Byte, "医生"),

        SCHOOL_MASTER(18 as Byte, '校长'),

        FERRIES(30 as byte, "接送人"),

        VISITORS(31 as byte, "访客"),

        STAFF(50 as byte, "工作人员")

        public Byte type

        public String name

        UserTypeEnum(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static UserTypeEnum getEnumByType(Byte type) {
            values().find {
                it.type == type
            }
        }

        static List<UserTypeEnum> getAllEnumByTypes(String types) {
            List<UserTypeEnum> userTypeEnumList = []
            if (types) {
                types.split(',').each {
                    UserTypeEnum userTypeEnum = getEnumByType(it as byte)
                    if (userTypeEnum) {
                        userTypeEnumList << userTypeEnum
                    }
                }
            }
            userTypeEnumList
        }

        static String getValueByType(Byte type) {
            return getEnumByType(type)?.name
        }

    }

    /**
     * 审批人类型，和用户类型有重叠
     */
    static enum ApprovalUserType {

        STUDENT(1 as Byte, '学生'),

        PARENT(2 as Byte, '家长'),

        TEACHER(6 as Byte, "老师"),

        CLASS_MASTER(13 as Byte, '班主任'),

        PERSON(100 as Byte, '个人'),//个人

        ALL(101 as Byte, '所有人'),

        PROMOTER(102 as Byte, '发起人'),

        PROMOTER_CHOOSE(104 as Byte, '发起人自选'),

        HEAD_OF_DEPARTMENT(105 as Byte, '部门主管')

        public Byte type

        public String name

        ApprovalUserType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalUserType getEnumByType(Byte type) {
            for (ApprovalUserType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }
    /**
     * 审批流程阶段记录的类型
     */
    static enum ApprovalStepType {

        PROMOTE(1 as Byte, '发起'),

        APPROVAL(2 as Byte, '审批'),

        COPY_TO(3 as Byte, "抄送"),

        FAILURE(4 as Byte, "撤销")

        public Byte type

        public String name

        ApprovalStepType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalStepType getEnumByType(Byte type) {
            for (ApprovalStepType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 审批流程阶段记录的类型
     */
    static enum ApprovalTemplateStepType {

        PROMOTE(1 as Byte, '发起'),

        APPROVAL(2 as Byte, '审批'),

        COPY_TO(3 as Byte, "抄送"),

        CONDITION(4 as Byte, "条件")

        public Byte type

        public String name

        ApprovalTemplateStepType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalTemplateStepType getEnumByType(Byte type) {
            for (ApprovalTemplateStepType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ApprovalMethodType {

        OR_SIGN(1 as byte, '或签'),

        JOINTLY_SIGN(2 as byte, '会签')

        public Byte type

        public String name

        ApprovalMethodType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalMethodType getEnumByType(Byte type) {
            ApprovalMethodType[] allEnums = values()
            for (ApprovalMethodType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 审批类型
     */
    static enum ApprovalType {

        ASK_FOR_LEAVE_ILL(1 as byte, '病假申请'),

        RESUME_CLASSES_ABSENCE(2 as byte, '事假审批'),

        RESUME_CLASSES_MORNING_EXERCISES(3 as byte, '早操假审批'),

        RESUME_CLASSES(4 as byte, '复课审批'),

        REPAIR(5 as byte, '报修审批'),

        TEMPORARY_VISITOR(6 as byte, '临时访客登记'),

        VISITOR_APPOINTMENT(7 as byte, '访客预约申请'),

        PARENT_VISIT(8 as byte, '家长入校申请'),

        TEACHER_LEAVE(9 as byte, '教师请假'),

        CHANGE_COURSE(10 as byte, '临时代调课'),

        ADMIN_DUTY(11 as byte, '行政值日'),

        GENERAL_SCHOOL(12 as byte, '通校生离校'),

        REQUISITION(13 as byte, '物品领用')

        public Byte type

        public String name

        ApprovalType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ApprovalType getEnumByType(Byte type) {
            ApprovalType[] allEnums = values()
            for (ApprovalType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum DingRelationCode {

        F('F', '爸爸', 1 as Byte),

        M('M', '妈妈', 2 as Byte),

        GF('GF', '爷爷', 3 as Byte),

        GM('GM', '奶奶', 4 as Byte),

        GFA('GFA', '外公', 5 as Byte),

        GMA('GMA', '外婆', 6 as Byte),

        U('U', '叔叔', 7 as Byte),

        A('A', '阿姨', 8 as Byte),

        B('B', '哥哥', 9 as Byte),

        S('S', '姐姐', 10 as Byte),

        O('O', '家长', 0 as Byte)

        String code

        String name

        Byte type

        DingRelationCode(String code, String name, Byte type) {
            this.code = code
            this.name = name
            this.type = type
        }

        static DingRelationCode getEnumByCode(String code) {
            values().find { dingRelationCode -> dingRelationCode.code == code
            }
        }

        static DingRelationCode getEnumByName(String name) {
            values().find { dingRelationCode -> dingRelationCode.name == name
            }
        }
    }

    static enum AppellationType {

        OTHER(0 as Byte, "家人"),

        PAPA(1 as Byte, '爸爸'),

        MAMA(2 as Byte, '妈妈'),

        GF(3 as Byte, '爷爷'),

        GM(4 as Byte, '奶奶'),

        GFA(5 as Byte, '外公'),

        GMA(6 as Byte, '外婆'),

        U(7 as Byte, '叔叔'),

        A(8 as Byte, '阿姨'),

        B(9 as Byte, '哥哥'),

        S(10 as Byte, '姐姐')

        public Byte type

        public String name

        AppellationType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AppellationType getEnumByType(Byte type) {
            AppellationType[] allEnums = AppellationType.values()
            for (AppellationType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 状态涉及表 unitStudent、unitTeacher、
     */
    static enum RelationStatus {

        CHANGE(-9 as byte, "换班"),

        DELETE(0 as byte, "删除"),

        NORMAL(1 as byte, "正常"),

        UPGRADE(4 as byte, "升级"),

        STAY_DOWN(5 as byte, "留级"),

        FINISH(6 as byte, "毕业"),

        SUSPEND(7 as byte, "休学"),

        RESUME(8 as byte, "复学")

        public Byte status

        public String name

        RelationStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static RelationStatus getEnumByType(Byte status) {
            RelationStatus[] allEnums = RelationStatus.values()
            for (RelationStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }


    static enum SectionType {

        KINDERGARTEN(1L, "幼儿园"),

        PRIMARY_SCHOOL(3L, "小学"),

        MIDDLE_SCHOOL(5L, "初中"),

        SPECIALIZED_SCHOOL(6L, "中专"),

        HIGH_SCHOOL(7L, "高中"),

        COLLEGE_SCHOOL(8L, "大专"),

        UNIVERSITY(9L, "大学"),

        VOCATIONAL_SCHOOL(10L, "中职")

        public Long id

        public String name

        SectionType(Long id, String name) {
            this.id = id
            this.name = name
        }

        static List<SectionType> getAllEnumByTypes(String types) {
            List<SectionType> sectionTypeList = []
            if (types) {
                types.split(',').each {
                    SectionType sectionType = getEnumByType(it as Long)
                    if (sectionType) {
                        sectionTypeList << sectionType
                    }
                }
            }
            sectionTypeList
        }

        static String getNameBySectionIdAndCodeAndSectionType(Long sectionId, Integer code, Byte sectionType) {
            String name = ToStringUnits.getChinese(code.toString()) + "年级"
            if (sectionType == 1 as byte) {
                switch (sectionId) {
                    case KINDERGARTEN.id:
                        if (sectionType == 1 as byte && code < 4) {
                            String[] names = ["小班", "中班", "大班"]
                            name = names[code - 1]
                        }
                        break
                    case PRIMARY_SCHOOL.id:
                        break
                    case MIDDLE_SCHOOL.id:
                        name = "初" + ToStringUnits.getChinese(code.toString())
                        break
                    case SPECIALIZED_SCHOOL.id:
                        name = "专" + ToStringUnits.getChinese(code.toString())
                        break
                    case HIGH_SCHOOL.id:
                        name = "高" + ToStringUnits.getChinese(code.toString())
                        break
                    case COLLEGE_SCHOOL.id:
                        name = "专" + ToStringUnits.getChinese(code.toString())
                        break
                    case UNIVERSITY.id:
                        name = "大" + ToStringUnits.getChinese(code.toString())
                        break
                    case VOCATIONAL_SCHOOL.id:
                        name = "职" + ToStringUnits.getChinese(code.toString())
                        break
                }
            }
            name
        }

        static SectionType getEnumByType(Long id) {
            for (SectionType s : values()) {
                if (s.id == id) {
                    return s
                }
            }
            return null
        }

        static SectionType getEnumByName(String name) {
            for (SectionType s : values()) {
                if (s.name == name) {
                    return s
                }
            }
            return null
        }

    }

    static enum EventPlatform {

        YUN_MOU(1 as byte, '云眸'),

        ISC(2 as byte, 'ISC')

        public Byte type

        public String name

        EventPlatform(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static EventPlatform getEnumByType(Byte type) {
            EventPlatform[] allEnums = values()
            for (EventPlatform s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ProjectType {

        SCHOOL(1 as byte, '学校'),

        COMPANY(2 as byte, '企业')

        public Byte type

        public String name

        ProjectType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ProjectType getEnumByType(Byte type) {
            ProjectType[] allEnums = ProjectType.values()
            for (ProjectType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum AppIdEnum {

        PC('rejmgk8s', 'PC管理端用'),

        WX('jz1ftek5', '微信权限菜单用'),

        SYSTEM_ADMIN('HIIADMIN', '超级管理员用'),

        BUREAU("BUREAU", "局端使用")

        public String appId

        public String name

        AppIdEnum(String appId, String name) {
            this.appId = appId
            this.name = name
        }

        static AppIdEnum getEnumByAppId(String appId) {
            AppIdEnum[] allEnums = AppIdEnum.values()
            for (AppIdEnum s : allEnums) {
                if (s.appId.equalsIgnoreCase(appId)) {
                    return s
                }
            }
            return null
        }
    }

    static enum JournalismType {

        SCHOOL_NEWS(1 as Byte, '校园新闻'),

        SCHOOL_NOTICE(2 as Byte, '通知公告')//原校园公告

        public Byte type

        public String name

        JournalismType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static JournalismType getEnumByStatus(Byte type) {
            JournalismType[] allEnums = JournalismType.values()
            for (JournalismType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum LeanCloudType {

        NEWS(1 as Byte, "校园新闻"),

        SCHOOL(2 as Byte, '通知公告'),//原校园公告

        GRADE(3 as Byte, '班级公告'),

        MESSAGE(4 as Byte, '家长推送新消息'),

        SWEEP_CODE(5 as Byte, '微信已扫码'),

        LOGIN(6 as Byte, '确认登录'),

        UPDATE(11 as Byte, '版本更新'),

        RESTART(12 as Byte, '重启班牌'),

        PAGING(13 as Byte, '师生寻呼'),

        WELCOME(14 as Byte, '欢迎词'),

        DELETE_WELCOME(15 as Byte, '删除欢迎词'),

        ELEGANCE(16 as Byte, '班级风采'),

        APPROVAL_PROCEDURE(17 as Byte, '请假回复'),

        STOP(18 as Byte, '关机'),

        OPERATE_SHELL(20 as Byte, 'shell命令'),

        ON_OFF_TIME(26 as Byte, "开关机时间"),

        LECTURE(27 as Byte, '首页课表'),

        EVENT_ATTENDANCE_SIGN_IN(30 as Byte, '事件考勤签到'),



        HOME_MODULE(33 as Byte, '首页模块'),

        HOME_CARD(34 as Byte, '班训'),

        HOME_CLASS_CADRE(35 as Byte, '班级干部'),

        HOME_TEACHER_MSG(36 as Byte, '教师寄语'),

        HOME_DUTY(37 as Byte, '值日生'),

        DELETE_DEVICE(38 as Byte, '删除设备'),

        DEVICE_BIND(39 as Byte, '设备绑定'),

        LEAVE_SCHOOL(28 as Byte, "放学班局部刷新"),

        LEAVE_SCHOOL_DATA(29 as Byte, "放学班全部刷新"),

        SCREEN_DEVICE_CHANGE_USE(47 as Byte, "更新设备用途"),

        PARENT_ENTER_SCHOOL(48 as Byte, "家长到校"),

        PARENT_LATE(49 as Byte, "迟接申请通过")

        public Byte type

        public String name

        LeanCloudType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static LeanCloudType getEnumByStatus(Byte type) {
            LeanCloudType[] allEnums = LeanCloudType.values()
            for (LeanCloudType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum PushMessageStatus {

        DELETE_P(-1 as Byte, "发送人删除"),

        DELETE(0 as Byte, '已删除'),

        SET(1 as Byte, '新建待发Push'),

        START(2 as Byte, '开始Push'),

        FILE(3 as Byte, 'Push失败'),

        SUCCESS(4 as Byte, 'Push成功'),

        DELETE_FAIL(5 as Byte, '取消定时任务失败')

        public Byte status

        public String name

        PushMessageStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static PushMessageStatus getEnumByStatus(Byte status) {
            PushMessageStatus[] allEnums = PushMessageStatus.values()
            for (PushMessageStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 消息状态
     */
    static enum MessageStatus {

        OUT_OF_DATE(-1 as Byte, "已过期"),

        DELETE(0 as Byte, "已删除"),

        SENT(1 as Byte, '信息已发送'),

        READ_NOT_CONFIRMED(2 as Byte, '已读未确认'),

        CONFIRMED(3 as Byte, "已确认")


        public Byte status

        public String name

        MessageStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static MessageStatus getEnumByCate(Byte status) {
            MessageStatus[] allEnums = MessageStatus.values()
            for (MessageStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    static enum NetStatus {

        ONLINE(1 as Byte, '在线'),

        OFFLINE(0 as Byte, '离线')

        public Byte status

        public String name

        NetStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static NetStatus getEnumByStatus(Byte status) {
            NetStatus[] allEnums = NetStatus.values()
            for (NetStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 选科家长是否已确认
     * */
    static enum ConfirmState {

        CONFIRMED(1 as byte, "已确认"),

        UNCONFIRMED(0 as byte, "未确认"),

        public Byte type

        public String name

        ConfirmState(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ConfirmState getEnumByConfirm(Byte type) {
            ConfirmState[] allEnums = ConfirmState.values()
            for (ConfirmState s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }
    /**
     * 消息分类
     */
    static enum MessageCates {

        HOME_SCHOOL_CONNECT(2 as Byte, '家校通消息', 'TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs', 'https://res.yunzhiyuan100.com/we_chat/<EMAIL>'),

        STUDENT_PARENT_CHAT(3 as Byte, "学生动态", "", ''),

        COURSE_CHOICE_CONFIRM(102 as Byte, "课程确认", '5N1KpFDkFCa1-vm239Ma-IhdGtEzAzxJqv4-qG0Lw9w', ''),

        LEAVE_SCHOOL_NOTICE(4 as byte, "学生离校通知", "48jOGZo6wOXC5hNPi2JvsOOMVEcQ9gnpOmS2LLPWNmA", ""),

        LEAVE_SCHOOL_REMIND(5 as byte, "学生离校提醒", "0qKoRPTe-easqFI0ZQQG2T_J0UXvhmefaVHEO4ev-EE", ""),

        ASK_FOR_LEAVE_REMIND_Online(6 as byte, "学生请假提醒", "7jWv-yID-t8MJzx2tXSk94mXorOiNz6QSlai8KNw64Q", ""),

        ENTER_SCHOOL_REMIND(7 as byte, "学生通过闸机进门提醒", "34TyOUJ-us5wEZOd2nb3doc0ewsSPdcdQApD-pk1zWY", ""),

        SCHOOL_MORAL_NOTICE(8 as byte, "学校德育统计结果通知", "TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs", ""),

        SYMPTOM_CHECKING_PARENT_ONLINE(9 as byte, "检测结果通知", "GwbKk2bW1Kb2HRdfI43LFHuX2YlZFU6jHnDfMwP0vOY", ""),

        SYMPTOM_CHECKING_MASTER_ONLINE(10 as byte, "检测结果通知", "GwbKk2bW1Kb2HRdfI43LFHuX2YlZFU6jHnDfMwP0vOY", ""),

        ILLNESS_NOTIFY_DOCTOR_ONLINE(11 as byte, "学生请病假提醒医务室", "7jWv-yID-t8MJzx2tXSk94mXorOiNz6QSlai8KNw64Q", ""),

        EXAMINATION_NOTICE(12 as byte, "考务安排通知", "bgvGNPtoAGPwTHY1N5uOsLqmax1SdO5T12cBhfHH2-M", ""),

        ILLNESS_NOTIFY_DOCTOR_TO_RECORD_ONLINE(13 as byte, "就医登记申请提醒", "lOy6TebhNWgoDub9T235m5rFQeisr8h9gIxnGNMtkQM", ""),

        CLOCK_NOTICE_ONLINE(14 as byte, "打卡任务发布成功后的即时通知", "TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs", ""),

        CLOCK_REMIND_ONLINE(15 as byte, "打卡日每日提醒时间提醒", "1NT5QHMP79pwrguyqSY94SsqqeQqLWAKVRC5DQQ8nOw", ""),

        SIGN_UP_CHECKING_PARENT_ONLINE(105 as byte, "活动报名通知", "TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs", ""),

        LEAN_CLOUD_SEND_FROM_PAD_ONLINE(106 as byte, "学校通知", "TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs", ""),

        EDUCATIONAL_ADMINISTRATION_NOTICE_ONLINE(107 as byte, "教务通知", "TCQ71uanCZTYA7nKZt08ol3YaG4LZalGvYQ1uzdJIRs", ""),

        ASK_FOR_LEAVE_NEW_ONLINE(108 as byte, "请假申请提醒", "ZV_8spqUEuDkeemPbNh1XctMJby0PQWOI_-cwVz1BWc", ""),

        ASK_FOR_LEAVE_APPROVAL_PROCEDURE_ONLINE(109 as byte, "请假审核通知", "Yhi-el-ps1iqAn5u-jdxhGjOCSxUXid3vL3alXT0Szs", ""),

        ADJUST_COURSE_ONLINE(110 as byte, "调课通知", "iQRMVscTqGO8XYI_V6R7mJBDvlCTfiEebz4-t0rE-A8", ""),

        HEALTH_CLOCK_REMIND_ONLINE(201 as byte, "每日健康打卡提醒", "1NT5QHMP79pwrguyqSY94SsqqeQqLWAKVRC5DQQ8nOw", ""),

        VIRUS_HEALTH_NOTIFY_ONLINE(900 as byte, "健康提醒", "crvJXR-bPrZPdaTFkeVsg3iAqFns2Aem74Q23ci-70s", ""),

        VIRUS_BODY_TEMPERATURE_TAKE_NOTIFY_ONLINE(901 as byte, "健康提醒", "9hwrwGND3dVTtL0-4ZVzo9Dim442xtXRBSeseDVz6JM", ""),

        HOME_WORK_ONLINE(301 as byte, "作业提醒", "CFfgLqIot_pz6tEDHuw3PG11H7RKk4q8kC559GQPxGo", ""),

        APPROVAL_ONLINE(601 as byte, "待审批通知", "1KZ39wVbBdBthF13uPO-XidIjXhJ0XTvtmjIt5k6BpQ", ""),

        APPROVAL_RESULT_ONLINE(602 as byte, "审批提醒", "vl2U-l_SJRm7kmwWspmlpUINWAxgxjjuQE_NMbPHOAA", ""),

        FERRIES_ONLINE(603 as byte, "待接送提醒", "lXr9mbns2x8xLvxVnXvMxvZwGMTdw74x2vURmDTTu1w", ""),

//        OPTION_START_ALL(706 as byte, "待选科通知", "1NT5QHMP79pwrguyqSY94SsqqeQqLWAKVRC5DQQ8nOw", ""),
//
//        NOT_OPTION_SUBJECT(707 as byte, "未选科通知", "FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4", "")

        OPTION_START_ALL(20 as byte, "待选科通知", "VZ7nnI0gVhtG-S2Vj3VF-xhnYESqvWkXJYCrXso_9JY", ""),

        NOT_OPTION_SUBJECT(21 as byte, "未选科通知", "FkZKTE0daB4ZiMM1ulMHN9tjRh6NiqIRI1GCJUTnhu4", "")

        public Byte cate

        public String name

        public String templateId

        public String logo

        MessageCates(Byte cate, String name, String templateId, String logo) {
            this.cate = cate
            this.name = name
            this.templateId = templateId
            this.logo = logo
        }
        //家校通知归类
        static List<MessageCates> getSchoolHomeMessageCates() {
            return [HOME_SCHOOL_CONNECT, COURSE_CHOICE_CONFIRM]
        }

        static MessageCates getEnumByCate(Byte cate) {
            MessageCates[] allEnums = MessageCates.values()
            for (MessageCates s : allEnums) {
                if (s.cate == cate) {
                    return s
                }
            }
            return null
        }

        static MessageCates getEnumByTemplateId(String tid) {
            MessageCates[] allEnums = MessageCates.values()
            for (MessageCates s : allEnums) {
                if (s.templateId.equals(tid)) {
                    return s
                }
            }
            return null
        }
    }

    //消息通知类型
    static enum MessageNotifyType {

        PERSONAL(1 as Byte, "个人通知"),

        DEPT(2 as Byte, "部门通知"),

        CAMPUS(3 as Byte, '学校通知')

        public Byte type

        public String name

        MessageNotifyType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MessageNotifyType getEnumByType(Byte type) {
            MessageNotifyType[] allEnums = values()
            for (MessageNotifyType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 扫描仪平台类型
     */
    static enum ScannerPlatFormEnum {

        SO(1 as byte, '解决方案'),

        DING_ISV(2 as byte, '钉钉')

        public Byte type

        public String name

        ScannerPlatFormEnum(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static ScannerPlatFormEnum getEnumByType(Byte type) {
            ScannerPlatFormEnum[] allEnums = ScannerPlatFormEnum.values()
            for (ScannerPlatFormEnum s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum MenuCollectionType {
        CANCEL(-1 as Byte, '取消'),
        DELETE(0 as Byte, '删除'),
        COLLECTION(1 as Byte, '收藏')

        Byte type
        String name

        MenuCollectionType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static MenuCollectionType getEnumByType(Byte type) {
            for (MenuCollectionType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            null
        }
    }

    /**
     * 操作类型
     * */
    static enum OperationType {

        SINGLE(1 as Byte, '单个'),

        BATCH(2 as Byte, '批量')

        Byte type
        String name

        OperationType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static OperationType getEnumByType(Byte type) {
            OperationType[] allEnums = values()
            for (OperationType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 操作端
     * */
    static enum OperationTerminal {

        PC(1 as Byte, 'PC端'),

        MOBILE(2 as Byte, '移动端')

        Byte type
        String name

        OperationTerminal(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static OperationTerminal getEnumByType(Byte type) {
            OperationTerminal[] allEnums = values()
            for (OperationTerminal s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }


    static enum FerriesType {

        ROUTINE(1 as Byte, '常规接送人'),

        TEMPORARY(2 as Byte, '临时接送人')

        Byte type
        String name

        FerriesType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static FerriesType getEnumByType(Byte type) {
            FerriesType[] allEnums = FerriesType.values()
            for (FerriesType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum TimeType {

        WEEK(1 as byte, "本周"),
        MONTH(2 as byte, "本月"),
        SEMESTER(3 as byte, "学期"),
        SCHOOL_YEAR(4 as byte, "学年")

        Byte type
        String name

        TimeType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static TimeType getEnumByType(Byte type) {
            TimeType[] allEnums = values()
            for (TimeType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum StudentFileTaskStatus {

        SUCCESS(1 as byte, "成功"),
        FAILURE(-1 as byte, "失败"),
        PARTIAL_FAILURE(3 as byte, "部分成功"),
        UPDATE(4 as byte, "更新中"),
        CREATE(5 as byte, "创建中")

        Byte type
        String name

        StudentFileTaskStatus(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static StudentFileTaskStatus getEnumByType(Byte type) {
            StudentFileTaskStatus[] allEnums = values()
            for (StudentFileTaskStatus s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum ClockType {

        HEALTH("健康打卡", 1 as byte)

        public String name

        public Byte type

        ClockType(String name, Byte type) {
            this.name = name
            this.type = type
        }

        static ClockType getEnumByType(Byte type) {
            for (ClockType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }
    //评教任务状态
    static enum StudentEvaluationTaskStatus {

        DELETE(0 as byte, "已删除"),

        NOTPUBLISH(1 as byte, "未发布"),

        GATHER(2 as byte, "收集中"),

        public Byte type

        public String name

        StudentEvaluationTaskStatus(Byte type, String name) {
            this.name = name
            this.type = type
        }

        static StudentEvaluationTaskStatus getEnumByType(Byte type) {
            for (StudentEvaluationTaskStatus s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    //问卷问题类型
    static enum StudentEvaluationQuestionType {

        MCQ(1 as byte, "选择题"),

        QA(2 as byte, "问答题")


        public Byte type

        public String name

        StudentEvaluationQuestionType(Byte type, String name) {
            this.name = name
            this.type = type
        }

        static StudentEvaluationQuestionType getEnumByType(Byte type) {
            for (StudentEvaluationQuestionType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    //评语类型
    static enum TeachingEvaluationType {

        MASTER(1 as byte, "班主任评语"),

        TEACHINT(2 as byte, "任课教师评语")


        public Byte type

        public String name

        TeachingEvaluationType(Byte type, String name) {
            this.name = name
            this.type = type
        }

        static TeachingEvaluationType getEnumByType(Byte type) {
            for (TeachingEvaluationType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 公文模版状态
     */
    static enum DocumentTemplateStatus {

        ENABLE(1 as byte, '已启用'),

        DISABLE(-1 as byte, '已禁用'),

        DELETE(0 as byte, '已删除')

        public Byte status

        public String name

        DocumentTemplateStatus(Byte status, String name) {
            this.name = name
            this.status = status
        }

        static DocumentTemplateStatus getEnumByType(Byte status) {
            for (DocumentTemplateStatus s : values()) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 公文模版可见人员
     */
    static enum DocumentTemplateVisibleType {

        ALL(1 as Byte, '所有人'),

        DESIGNATED_DEPARTMENT(2 as byte, '指定部门'),

        DESIGNATED_PERSONNEL(3 as byte, '指定人员')

        Byte type

        String name

        DocumentTemplateVisibleType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DocumentTemplateVisibleType getEnumByType(Byte type) {
            for (DocumentTemplateVisibleType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 公文缓急
     */
    static enum DocumentDegreeOfUrgency {

        EXTRA_URGENT(1 as byte, '特急'),

        URGENT(2 as byte, '加急'),

        CALM_AND_URGENT(3 as byte, '平急')

        Byte type

        String name

        DocumentDegreeOfUrgency(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DocumentDegreeOfUrgency getEnumByType(Byte type) {
            for (DocumentDegreeOfUrgency s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 公文状态
     */
    static enum DocumentStatus {

        DELETE(0 as byte, "已废除"),

        RECEIVE(1 as byte, '收发中'),

        PROPOSED(2 as byte, '拟办中'),

        APPROVE(3 as byte, '批办中'),

        UNDERTAKE(4 as byte, '承办中'),

        TO_BE_FINISH(5 as byte, '办结中'),

        FINISH(6 as byte, '已办结')

        Byte status

        String name

        DocumentStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static DocumentStatus getEnumByType(Byte status) {
            for (DocumentStatus s : values()) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 公文流转节点
     */
    static enum DocumentNodeType {

        SUBMIT(0 as byte, '提交登记'),

        RECEIVE(1 as byte, '收发'),

        PROPOSED(2 as byte, '拟办'),

        APPROVE(3 as byte, '批办'),

        UNDERTAKE(4 as byte, '承办'),

        DISTRIBUTE_AND_CIRCULATE(5 as byte, '分发传阅'),

        FINISH(6 as byte, '办结')

        Byte type

        String name

        DocumentNodeType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DocumentNodeType getEnumByType(Byte type) {
            for (DocumentNodeType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 公文密级
     */
    static enum DocumentDegreeOfSecrets {

        TOP_SECRET(1 as byte, '绝密'),

        SECOND_SECRET(2 as byte, '机密'),

        SECRET(3 as byte, '秘密'),

        ORDINARY(4 as byte, '普通')

        Byte type

        String name

        DocumentDegreeOfSecrets(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DocumentDegreeOfSecrets getEnumByType(Byte type) {
            for (DocumentDegreeOfSecrets s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 公文流转状态
     */
    static enum DocumentStepStatus {

        DELETE(-1 as byte, '删除'),

        PROCESSING(0 as byte, '处理中'),

        DISTRIBUTE(1 as byte, '派发'),

        AGREE(2 as byte, '同意'),

        TRANSFER(3 as byte, '转办'),

        BACK_OFF(4 as byte, '回退'),

        FINISH(5 as byte, '已办结'),

        CIRCULATE(6 as byte, '已分发'),

        UNREAD(7 as byte, '未读'),

        READ(8 as byte, '已读')

        Byte status

        String name

        DocumentStepStatus(Byte status, String name) {
            this.status = status
            this.name = name
        }

        static DocumentStepStatus getEnumByType(Byte status) {
            for (DocumentStepStatus s : values()) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }
    }

    /**
     * 巡更类型
     */
    static enum KeepWatchType {

        PAPER(1 as byte, '纸质点位'),

        PAD(2 as byte, '班牌点位')

        Byte type

        String name

        KeepWatchType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static KeepWatchType getEnumByType(Byte type) {
            for (KeepWatchType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 签到方式
     */
    static enum KeepWatchSignInType {

        PHOTO(1 as byte, '照片'),

        QRCODE(2 as byte, '二维码')

        Byte type

        String name

        KeepWatchSignInType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static KeepWatchSignInType getEnumByType(Byte type) {
            for (KeepWatchSignInType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 导出方式
     */
    static enum TrackExportType {

        SINGLE(1 as byte, '指定个人'),

        USERTYPE(2 as byte, '指定人员类型')

        Byte type

        String name

        TrackExportType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static TrackExportType getEnumByType(Byte type) {
            for (TrackExportType t : values()) {
                if (t.type == type) {
                    return t
                }
            }
            return null
        }

    }

    static enum LoginPlatFormEnum {

        WX(1 as byte, '微信'),

        DING(2 as byte, '钉钉')

        public Byte type

        public String name

        LoginPlatFormEnum(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static LoginPlatFormEnum getEnumByType(Byte type) {
            LoginPlatFormEnum[] allEnums = LoginPlatFormEnum.values()
            for (LoginPlatFormEnum s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum MessageConfirmEnum {

        CONFIRMED('Confirmed', "确认"),

        UN_CONFIRMED('UnConfirmed', "未确认"),

        ALL('all', "全部")

        public String key

        public String name


        MessageConfirmEnum(String key, String name) {
            this.key = key
            this.name = name
        }

        static MessageConfirmEnum getEnumByKey(String key) {
            MessageConfirmEnum[] allEnums = MessageConfirmEnum.values()
            for (MessageConfirmEnum s : allEnums) {
                if (s.key == key) {
                    return s
                }
            }
            return null
        }
    }

    static enum BgbMenuTerminal {

        PC(1 as byte, "后台菜单端"),

        TEACHER(2 as byte, "老师菜单"),

        PARENT(3 as byte, "家长菜单"),

        STUDENT(4 as byte, "学生菜单"),

        GLAD(5 as byte, "权限后台")

        Byte type

        String name

        BgbMenuTerminal(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BgbMenuTerminal getEnumByType(Byte type) {
            BgbMenuTerminal[] allEnums = values()
            for (BgbMenuTerminal s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    //Ai一体机版本号
    static enum AiBoxVersion {

        VERSION_ONE(1, "V1.2.1.R.2"),

        VERSION_TWO(2, "V1.4.2.R.2")

        public Integer type

        public String name

        AiBoxVersion(Integer type, String name) {
            this.name = name
            this.type = type
        }

        static AiBoxVersion getEnumByType(Integer type) {
            for (AiBoxVersion s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum DevicePlanTimeType {

        WEEK(1, "周计划"),
        SHUT_DOWN(2, "节假日关机"),
        POWER_ON(3, "休息日补课")

        Integer type

        String name

        DevicePlanTimeType(int type, String name) {
            this.type = type
            this.name = name
        }

        static DevicePlanTimeType getEnumByType(Integer type) {
            DevicePlanTimeType[] allEnums = values()
            for (DevicePlanTimeType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum PadType {

        ACROSS(1 as Byte, "横板"),

        VERTICAL(12 as Byte, "竖版")

        Byte type

        String name

        PadType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static PadType getEnumByType(Byte type) {
            PadType[] allEnums = values()
            for (PadType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum TrackAlarmOperationStatus {

        IGNORE(-1, "已忽略"),

        PROCESSED(1, "已处理"),

        UNTREATED(0, "待处理")

        int status

        String name

        TrackAlarmOperationStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static TrackAlarmOperationStatus getEnumByStatus(Integer status) {
            values().find { trackAlarmOperationStatus -> trackAlarmOperationStatus.status == status
            }
        }

    }

    static enum DeviceCalculation {

        FaceCompare(1, 1, "人脸识别"),

        Normal(2, 4, "通用行为"),

        ControlMonitor(3, 1, "岗位分析")

        int type

        Integer calculation

        String name

        DeviceCalculation(int type, Integer calculation, String name) {
            this.type = type
            this.calculation = calculation
            this.name = name
        }

        static String bit2Names(int bit) {
            if (bit < 1) {
                return "未知组合"
            }
            List<String> nameList = []
            values().each { task ->
                Integer type = Math.pow(2, task.type - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    nameList.add(task.name)
                }
            }
            Joiner.on("+").join(nameList)
        }

        static Integer bit2calculation(Integer bit) {
            Integer calculation = 0
            if (bit < 1) {
                return calculation
            }
            values().each { task ->
                Integer type = Math.pow(2, task.type - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    calculation += task.calculation
                }
            }
            return calculation
        }

        static String bit2Types(int bit) {
            if (bit < 1) {
                return null
            }
            List<Integer> list = []
            values().each { task ->
                Integer type = Math.pow(2, task.type - 1).intValue()
                if (Integer.compare(bit & type, type) == 0) {
                    list.add(task.type)
                }
            }
            Joiner.on(",").join(list)
        }

        static int types2Bit(String types) {
            int bit = 0
            if (!types) {
                return bit
            }
            types.split(",").each {
                bit += Math.pow(2, it.toInteger() - 1).intValue()
            }
            return bit
        }
    }

    static enum UserEncodeInfoType {

        ID_CARD(1 as byte, "身份证件号"),

        MOBILE(2 as byte, "手机号")

        Byte type

        String name

        UserEncodeInfoType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static UserEncodeInfoType getEnumByType(Byte type) {
            values().find { userEncodeInfoType -> userEncodeInfoType.type == type
            }
        }
    }

    static enum BgbBaseMenuType {

        CATEGORY(1 as byte, "分组"),

        MENU(2 as byte, "菜单")

        Byte type

        String name

        BgbBaseMenuType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BgbBaseMenuType getEnumByType(Byte type) {
            BgbBaseMenuType[] allEnums = values()
            for (BgbBaseMenuType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum BgbCampusMenuSourceType {

        ATTENDANCE(1 as byte, "考勤配置"),

        APPROVAL(2 as byte, "审批配置"),

        MORAL(3 as byte, "德育配置")

        Byte type

        String name

        BgbCampusMenuSourceType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BgbCampusMenuSourceType getEnumByType(Byte type) {
            BgbCampusMenuSourceType[] allEnums = values()
            for (BgbCampusMenuSourceType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum BgbCampusRoleStatus {

        DELETE(0 as byte, "删除"),

        UN_USED(-1 as byte, "未启用"),

        USED(1 as byte, "启用")

        Byte type

        String name

        BgbCampusRoleStatus(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static BgbCampusRoleStatus getEnumByType(Byte type) {
            BgbCampusRoleStatus[] allEnums = values()
            for (BgbCampusRoleStatus s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum DockingFirmStatus {

        NORMAL(1, "正常"),

        ABNORMAL(3, "异常"),

        CHECKING(4, "检测中"),

        UNKNOWN(5, "未知")

        int status

        String name

        DockingFirmStatus(int status, String name) {
            this.status = status
            this.name = name
        }
    }

    static enum StoreOperateStatus {

        DEL(0, "删除"),

        NORMAL(1, "正常"),

        BORROW(2, "待借用"),

        BACK(3, "待归还"),

        RECEIVE(4, "待领取")

        int status

        String name

        StoreOperateStatus(int status, String name) {
            this.status = status
            this.name = name
        }
    }

    static enum JoggingStatus {

        NO_JOGGING(0 as byte, "未跑操"),

        NOT_FINISH(-1 as byte, "未完成"),

        FINISH(1 as byte, "已完成")

        Byte type

        String name

        JoggingStatus(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static JoggingStatus getEnumByType(Byte type) {
            JoggingStatus[] allEnums = values()
            for (JoggingStatus s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

        static String getValueByStatus(Byte type) {
            return getEnumByType(type)?.name
        }
    }

    static enum JoggingCompletionRateType {

        STUDENT_COUNT(1 as byte, "学生完成率"),

        UNIT_COUNT(2 as byte, "班级完成率")

        Byte type

        String name

        JoggingCompletionRateType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static JoggingCompletionRateType getEnumByType(Byte type) {
            JoggingCompletionRateType[] allEnums = values()
            for (JoggingCompletionRateType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum PropertyStatus {

        NORMAL(1, "未使用"),

        USING(2, "使用中"),

        BORROWING(3, "领用"),

        SCRAP(4, "报废")

        int status

        String name

        PropertyStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static PropertyStatus getEnumByStatus(int status) {
            PropertyStatus[] allEnums = values()
            for (PropertyStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static PropertyStatus getEnumByName(String name) {
            PropertyStatus[] allEnums = values()
            for (PropertyStatus s : allEnums) {
                if (s.name == name) {
                    return s
                }
            }
            return null
        }
    }

    static enum BoxGroupRecordPublishStatus {

        NOPUBLISH(0, "未发布"),

        PUBLISHED(1, "已发布"),

        PUBLISHING(2, "定时中")

        int status

        String name

        BoxGroupRecordPublishStatus(int status, String name) {
            this.status = status
            this.name = name
        }

        static BoxGroupRecordPublishStatus getEnumByStatus(int status) {
            BoxGroupRecordPublishStatus[] allEnums = values()
            for (BoxGroupRecordPublishStatus s : allEnums) {
                if (s.status == status) {
                    return s
                }
            }
            return null
        }

        static BoxGroupRecordPublishStatus getEnumByName(String name) {
            BoxGroupRecordPublishStatus[] allEnums = values()
            for (BoxGroupRecordPublishStatus s : allEnums) {
                if (s.name == name) {
                    return s
                }
            }
            return null
        }
    }

    static enum BoxGroupRecordShowType {

        ALL(1, "全部"),

        MANAGER_ME(2, "我管理的"),

        PUBLISHING_ME(3, "我发布的")

        int type

        String name

        BoxGroupRecordShowType(int type, String name) {
            this.type = type
            this.name = name
        }

        static BoxGroupRecordShowType getEnumByStatus(int type) {
            BoxGroupRecordShowType[] allEnums = values()
            for (BoxGroupRecordShowType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum PadMacType {

        DH(1 as byte, "大华"),

        HK(2 as byte, "海康")

        Byte type

        String name

        PadMacType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static PadMacType getEnumByType(Byte type) {
            values().find { PadMacType -> PadMacType.type == type
            }
        }
    }

    static enum SilencePeriod {

        ONE_DAY(0 as byte, "hour", 24, "24小时"),
        TWO_DAY(1 as byte, "hour", 48, "48小时"),
        THREE_DAY(2 as byte, "hour", 72, "72小时"),
        ONE_WEEK(3 as byte, "week", 1, "1周"),
        TWO_WEEK(4 as byte, "week", 2, "2周"),
        ONE_MONTH(5 as byte, "month", 1, "1月"),
        TWO_MONTH(6 as byte, "month", 2, "2月"),
        THREE_MONTH(7 as byte, "month", 3, "3月"),
        HALF_YEAR(8 as byte, "month", 6, "半年"),
        ONE_YEAR(9 as byte, "year", 1, "1年")

        byte type

        //hour week month year
        String dayType

        Integer num

        String name

        SilencePeriod(byte type, String dayType, Integer num, String name) {
            this.type = type
            this.dayType = dayType
            this.num = num
            this.name = name
        }

        static SilencePeriod getEnumByStatus(int type) {
            SilencePeriod[] allEnums = values()
            for (SilencePeriod s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }

    static enum LeaveUnitType {

        HOUR(1 as byte, "按小时请假"),

        HALF_DAY(2 as byte, "按半天请假"),

        DAY(3 as byte, "按天请假")

        Byte type

        String name

        LeaveUnitType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static LeaveUnitType getEnumByType(Byte type) {
            values().find { leaveUnitType -> leaveUnitType.type == type
            }
        }
    }

    static enum ParentType {

        PARENT(1, "家长"),

        PICK_UP_PERSON(2, "接送人")

        Integer type
        String name

        ParentType(Integer type, String name) {
            this.type = type
            this.name = name
        }
    }

    static enum YunXueType {

        HOME_SCHOOL(1 as byte, "家校服务"),

        ADMINISTRATION(2 as byte, "管理服务"),

        WORK(3 as byte, "办事服务"),

        TEACHING(4 as byte, "教学服务")

        Byte type

        String name

        YunXueType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static YunXueType getEnumByType(Byte type) {
            values().find { yunXueType -> yunXueType.type == type
            }
        }
    }

    static enum ClientModulesType {

        SXA(1, "数小安"),

        ZHJS(2, "智慧接送"),

        HBB(3, "黑板报"),

        XYYTJ(4, "校园一体机"),

        HII(5, "智慧校园微信端"),

        HII_ADMIN(6, "智慧校园pc端"),

        GLAD(7, "智慧校园权限系统")

        Integer type

        String name

        ClientModulesType(int type, String name) {
            this.type = type
            this.name = name
        }

        static ClientModulesType getEnumByType(int type) {
            values().find { it.type == type }
        }
    }

    static enum DhVersion {
        V1("V1", "普通版", ""),
        V2("V2.0", "定制下发版", "")


        String version
        String name
        String remark

        DhVersion(String version, String name, String remark) {
            this.version = version
            this.name = name
            this.remark = remark
        }

    }

    static enum GatingType {

        GATING_TASK_TIME(1 as byte, "常态化通行"),

        LEAVE_TIME(2 as byte, "请假通行")

        Byte type

        String name

        GatingType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static GatingType getEnumByType(Byte type) {
            GatingType[] allEnums = values()
            for (GatingType g : allEnums) {
                if (g.type == type) {
                    return g
                }
            }
            return null
        }
    }


    static enum YunPianErrorEnum {

        ErrorCode10(10, "手机号防骚扰名单过滤，详情请联系客服"),

        ErrorCode11(11, "用户号码防骚扰"),

        ErrorCode20(20, "暂不支持的国家地区"),

        ErrorCode23(23, "号码归属地不在模板可发送的地区内"),

        ErrorCode53(53, "手机号接收超过频率限制，详情请联系客服"),

        ErrorCode55(55, "服务已下线")


        Integer code

        String message

        YunPianErrorEnum(Integer code, String message) {
            this.code = code
            this.message = message
        }

        static YunPianErrorEnum getEnumByType(Integer code) {
            YunPianErrorEnum[] allEnums = values()
            for (YunPianErrorEnum y : allEnums) {
                if (y.code == code) {
                    return y
                }
            }
            return null
        }
    }

    /**
     * 信息发布类型
     * */
    static enum PublicationBandType {

        DEVICE(1, "设备"),
        UNIT(2, "班级")

        Integer type

        String name

        PublicationBandType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static PublicationBandType getEnumByStatus(Integer type) {
            values().find {
                listFieldType ->
                    listFieldType.type == type
            }
        }
    }

    /**
     * 信息发布类型
     * */
    static enum LoginUrlType {

        DEFAULT(1, "默认类型"),
        HIK_CLOUD(2, "海康云眸")

        Integer type

        String name

        LoginUrlType(Integer type, String name) {
            this.type = type
            this.name = name
        }

        static LoginUrlType getEnumByStatus(Integer type) {
            values().find {
                listFieldType ->
                    listFieldType.type == type
            }
        }
    }

    static enum AuditLogEventObject {

        APPROVAL(1 as byte, "OA审批"),

        MORAL(2 as byte, "德育评分")

        public Byte type

        public String name

        AuditLogEventObject(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AuditLogEventObject getEnumByType(Byte type) {
            for (AuditLogEventObject s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    static enum AuditLogEventType {

        APPROVAL_SAVE(1 as byte, "提交审批"),

        MORAL_SAVE(2 as byte, "德育登记")

        public Byte type

        public String name

        AuditLogEventType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static AuditLogEventType getEnumByType(Byte type) {
            for (AuditLogEventType s : values()) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }

    }

    /**
     * 杭州市民卡推送请假记录校区信息
     */
    static enum CampusCitizenInfo {

        HGQJ(143L, "杭高钱江", "8017ed693e16bd6286395cc04a5cf266", 190L, 189L),
        HGQY(102L, "杭高贡院", "8017ed693e16bd6286395cc04a5cf266", 93L, 94L),

        HGLP(222L, "杭高临平", "2fb3b1d86433eceacde34957be210e3e", 461L, 460L),

        HQZT(182L, "杭七转塘", "51c52edaa3f993fb1a054f92a16cf9ed", 327L, 326L),
        HQJF(195L, "杭七解放", "51c52edaa3f993fb1a054f92a16cf9ed", 391L, 392L),

        HSYDS(187L, "杭十一德胜", "5a00660d8b768b40fbb24fc944ee86df", 358L, 357L),
        HSYDG(19L, "杭十一大关", "5a00660d8b768b40fbb24fc944ee86df", 345L, 346L),

        HSSKQ(1L, "杭十四康桥", "145481f550e7af9aa1f0bddfe3f27beb", 44L, 45L),
        HSSFQ(86L, "杭十四凤起", "145481f550e7af9aa1f0bddfe3f27beb", 82L, 83L),

        HSSQSH(183L, "杭十四青山湖", "928b0884ebadffdd065b3373f43c3d63", 337L, 336L),

        HZKYSM(223L, "杭州市开元商贸学校", "9919870b10fd40ad8996c82410b2d01b", 470L, 471L),

        HZZC(128L, "杭州中策", "c06ba279c4c74a998e106692c8a6d291", 0L, 0L) //未使用智慧校园请假

        Long campusId

        String campusName

        String citizenCode

        Long absenceLeaveCategoryId //事假

        Long sickLeaveCategoryId //病假

        CampusCitizenInfo(Long campusId, String campusName, String citizenCode, Long absenceLeaveCategoryId, Long sickLeaveCategoryId) {
            this.campusId = campusId
            this.campusName = campusName
            this.citizenCode = citizenCode
            this.absenceLeaveCategoryId = absenceLeaveCategoryId
            this.sickLeaveCategoryId = sickLeaveCategoryId
        }

        static CampusCitizenInfo getEnumByCampusId(Long campusId) {
            CampusCitizenInfo[] allEnums = values()
            for (CampusCitizenInfo s : allEnums) {
                if (s.campusId == campusId) {
                    return s
                }
            }
            return null
        }

        static List<Long> getAllCampusIds() {
            List<Long> campusIds = new ArrayList<>()
            CampusCitizenInfo[] allEnums = values()
            for (CampusCitizenInfo info : allEnums) {
                campusIds.add(info.getCampusId())
            }
            return campusIds
        }
    }

    static enum GrantType {

        CODE("code", "授权码"),

        IMPLICIT("implicit", "隐藏式"),

        PASSWORD("password", "密码式"),

        CLIENT_CREDENTIALS("client_credentials", "客户端凭证式")

        String type

        String name

        GrantType(String type, String name) {
            this.type = type
            this.name = name
        }

        static GrantType getEnumByType(String type) {
            return values().find { it.type == type }
        }

    }

    static enum SingleLoginFirm {
        BASE_UNIFIED_PORTAL(1, "工作台门户")

        int firm
        String name

        SingleLoginFirm(int firm, String name) {
            this.firm = firm
            this.name = name
        }

        static SingleLoginFirm getEnumByFirm(Integer firm) {
            values().find {
                deviceFirm ->
                    deviceFirm.firm == firm
            }
        }
    }

    static enum DataStatus {

        FAIL(-1, "失败"),
        NONE(0, "未处理"),
        NORMAL(1, "已处理"),
        PROCESSING(2, "处理中")

        Integer status
        String name

        DataStatus(Integer status, String name) {
            this.status = status
            this.name = name
        }

        static DataStatus getEnumByStatus(Integer status) {
            values().find {
                dataStatus ->
                    dataStatus.status == status
            }
        }
    }

    static enum DutyBusinessType {
        DUTY_WEEK(1 as byte, "值日值周"),
        DUTY_WORK(2 as byte, "值班加班")

        Byte type
        String name

        DutyBusinessType(Byte type, String name) {
            this.type = type
            this.name = name
        }

        static DutyBusinessType getEnumByType(Byte type) {
            DutyBusinessType[] allEnums = values()
            for (DutyBusinessType s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }


    static enum SsoODockingPlatform {
        DEFAULT_HOST(1, "默认", "uth-server.yunzhiyuan100.com", "5299479759", "YmMzMDZhMWFkNWFhM2FjMjNkMGIxN2E4YTVjYzI0NTE=", 0L),
        ZHONG_CE(2, "中策", "http://*************:30000", "5299479759", "YmMzMDZhMWFkNWFhM2FjMjNkMGIxN2E4YTVjYzI0NTE=", 128L),
        TAI_ZHOU_HS(3, "台州护士", " https://data-center.zjtzhsxx.cn/edu-base/app-auth", "7867995549", "Yjc1ZmY0ZjY4YzhkMmFmMjU5OTQ1ZTgxNmEyYWEzMmY=", 208),
        DAILY_USER_MODULE(4, "日常组织基座", "https://daily.buguk12.com:8443/bd/app-auth", "9654897474", "ZDNhMDdkZThlM2JjM2RmYzBiMTVkODVjZGQxYzY3NTI=", 209)

        String name
        Integer type
        String host
        String appKey
        String appSecret
        Long orgId

        SsoODockingPlatform(Integer type, String name, String host, String appKey, String appSecret, Long orgId) {
            this.type = type
            this.name = name
            this.host = host
            this.appKey = appKey
            this.appSecret = appSecret
            this.orgId = orgId
        }

        static SsoODockingPlatform getEnumByType(Integer type) {
            SsoODockingPlatform[] allEnums = values()
            for (SsoODockingPlatform s : allEnums) {
                if (s.type == type) {
                    return s
                }
            }
            return null
        }
    }
}
    
package hiiadmin.handler

import hiiadmin.handler.approval.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static hiiadmin.ConstantEnum.ApprovalFormType.*

/**
 * Created by sunnysails on 2024/9/2 15:41
 * @version 1.0 
 */
@Component
class HandlerFactory {

    @Autowired
    InspectionHandler inspectionHandler

    @Autowired
    StudentViolationHandler studentViolationHandler

    @Autowired
    StudentAvatarHandler studentAvatarHandler

    @Autowired
    StudentActivityHandler studentActivityHandler

    @Autowired
    DefaultEmptyApprovalHandler defaultApprovalHandler

    @Autowired
    LeaveModeHandler leaveModeHandler

    @Autowired
    WorkOverTimeHandler workOverTimeHandler

    @Autowired
    AdjustOverWorkHandler adjustOverWorkHandler

    @Autowired
    RequisitionHandler requisitionHandler

    // 工厂方法用于创建具体的处理器实例
    ApprovalHandler createApprovalHandler(Integer formType) {
        switch (formType) {
            case PROPERTY_FIX.type:
                return inspectionHandler
            case STUDENT_VIOLATION.type:
                return studentViolationHandler
                //学生照片
            case STUDENT_AVATAR.type:
                return studentAvatarHandler
//            学生活动参与
            case STUDENT_ACTIVITY.type:
                return studentActivityHandler
            case WORK_OVERTIME.type:
                return workOverTimeHandler
            case ADJUST_OVER_WORK.type:
            case ADJUST_DUTY_WEEK.type:
                return adjustOverWorkHandler
            case LEAVE_MODE.type:
                return leaveModeHandler
            case REQUISITION.type:
                return requisitionHandler
            default:
                return defaultApprovalHandler
//                throw new IllegalArgumentException("Unsupported form type: ${formType}")
        }
    }

}

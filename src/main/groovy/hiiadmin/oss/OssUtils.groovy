package hiiadmin.oss

import com.alibaba.fastjson.JSON
import com.aliyun.oss.*
import com.aliyun.oss.common.comm.Protocol
import com.aliyuncs.DefaultAcsClient
import com.aliyuncs.IAcsClient
import com.aliyuncs.auth.sts.AssumeRoleRequest
import com.aliyuncs.auth.sts.AssumeRoleResponse
import com.aliyuncs.exceptions.ServerException
import com.aliyuncs.profile.DefaultProfile
import groovy.util.logging.Slf4j
import org.joda.time.DateTime

import javax.sql.rowset.serial.SerialBlob

@Slf4j
class OssUtils {

    /**
     *
     * @param base64
     * @param objectName 文件路径
     * @return
     */
    static String uploadPicture(String base64, String fileName) {
        String content = base64.replaceAll("^data:image/\\w+;base64,", "")
        byte[] bytes = Base64.getDecoder().decode(content)
        SerialBlob serialBlob = new SerialBlob(bytes)
        String url = upload(serialBlob.getBinaryStream(), fileName)
        return url
    }

    /**
     *
     * @param is
     * @param fileName
     * @param bucketName
     * @return 私有存储下不能使用return 的值，只需要保存 fileName ,图片查看
     */
    static String upload(InputStream is, String fileName, String bucketName = OssConfig.bucketName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(OssConfig.endpoint, OssConfig.accessKeyId, OssConfig.accessKeySecret)
        if (!ossClient.doesBucketExist(bucketName)) {
            log.error("该Bucket不存在, {}", bucketName)
            return ""
        }
        try {
            ossClient.putObject(bucketName, fileName, is)
        } catch (Exception oe) {
            log.error("上传阿里云oss异常 ${oe.getMessage()}".toString(), oe)
        } finally {
            // 关闭client
            ossClient.shutdown()
        }
        String src = "https://" + bucketName + "." + OssConfig.endpoint + "/" + fileName
        return src
    }

    /**
     *
     * @param fileName
     * @param expirationTime 有效时间 （秒）
     * @param bucketName
     * @return
     */
    static String transformFilePath(String fileName, int expirationTime, String bucketName = OssConfig.bucketName) {
        // 从STS服务获取临时访问凭证后，您可以通过临时访问密钥和安全令牌生成OSSClient。
        // 创建OSSClient实例。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration()
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS)
        OSS ossClient = new OSSClientBuilder().build(OssConfig.endpoint, OssConfig.accessKeyId, OssConfig.accessKeySecret, clientBuilderConfiguration)

        try {
            // 设置签名URL过期时间，单位为毫秒。
            Date expiration = new DateTime().plusSeconds(expirationTime).toDate()
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucketName, fileName, expiration)
//            System.out.println(url)
            return url.toString()
        } catch (OSSException oe) {
            log.warn("Error Message:${oe.getErrorMessage()}".toString())
            log.warn("Error Code:${oe.getErrorCode()}".toString())
            log.warn("Request ID:${oe.getRequestId()}".toString())
            log.warn("Host ID:${oe.getHostId()}".toString())
            log.warn("Caught an OSSException, which means your request made it to OSS, but was rejected with an error response for some reason.", oe)
            return null
        } catch (ClientException ce) {
            log.warn("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.", ce)
            log.warn("Error Message:${ce.getErrorMessage()}".toString())
            return null
        } finally {
            if (ossClient != null) {
                ossClient.shutdown()
            }
        }
    }

    /**
     *
     * @param fileName
     * @param expirationTime 有效时间 （秒）
     * @param bucketName
     * @return
     */
    static List<String> transformFilePathList(List<String> fileNameList, int expirationTime, String bucketName = OssConfig.bucketName) {
        List<String> urlList = []
        if (!fileNameList || fileNameList?.size() == 0) {
            return []
        }
        fileNameList.each { String fileName ->
            String url
            if (fileName != null && fileName != "" && !fileName.startsWith("http:") && !fileName.startsWith("https:")) {
                url = transformFilePath(fileName, expirationTime, bucketName) ?: ""
            } else {
                url = fileName
            }
            urlList.add(url)
        }
        return urlList
    }


    private static String temporaryAccessKeyId = 'LTAI4G1CoaZk2j5LoDMuVnXd'
    private static String temporaryAccessKeySecret = '******************************'
    /**
     * 获取临时STS
     *
     * @return
     */
    static AssumeRoleResponse.Credentials STS() {
        String roleArn = "acs:ram::1250402692105284:role/ramosstemporary"
        String roleSessionName = "bugu"
        //构建一个阿里云客户端，用于发起请求。
        //构建阿里云客户端时需要设置AccessKey ID和AccessKey Secret。
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", temporaryAccessKeyId, temporaryAccessKeySecret)
        IAcsClient client = new DefaultAcsClient(profile)
        //构造请求，设置参数。
        AssumeRoleRequest request = new AssumeRoleRequest()
        request.setRegionId("cn-hangzhou")
        request.setRoleArn(roleArn)
//        request.setPolicy(policy)
        request.setRoleSessionName(roleSessionName)
        request.setDurationSeconds(60L * 60)

        //发起请求，并得到响应。
        try {
            AssumeRoleResponse response = client.getAcsResponse(request)
            AssumeRoleResponse.Credentials c = response.getCredentials()
            log.info("sts response ${JSON.toJSONString(response)}".toString())
            return c
        } catch (ServerException e) {
            log.info("sts ServerException error code:${e.errCode} msg:${e.message}".toString(), e)
        } catch (com.aliyuncs.exceptions.ClientException e) {
            log.info("sts ClientException error code:${e.errCode} msg:${e.message} RequestId:${e.requestId}".toString(), e)
        }
        return null
    }
}

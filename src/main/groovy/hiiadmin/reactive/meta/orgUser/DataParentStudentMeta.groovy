package hiiadmin.reactive.meta.orgUser

import groovy.transform.CompileStatic
import hiiadmin.reactive.meta.DataMeta
import hiiadmin.reactive.modle.orgUser.PdDataParentStudentDTO
import timetabling.user.Student

@CompileStatic
class DataParentStudentMeta extends DataMeta<PdDataParentStudentDTO> {
    
    Student student
    
    List<PdDataParentStudentDTO> parentStudentDTOList
    
    Long campusId
    
    DataParentStudentMeta(PdDataParentStudentDTO response, boolean continued, boolean update) {
        super(response, continued, update)
    }
}

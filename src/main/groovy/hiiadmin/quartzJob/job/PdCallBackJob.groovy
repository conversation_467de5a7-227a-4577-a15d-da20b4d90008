package hiiadmin.quartzJob.job

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.aliyun.odps.utils.StringUtils
import groovy.util.logging.Slf4j
import hiiadmin.ConstantEnum
import hiiadmin.pd.RepositoryPdCallBackDataService
import hiiadmin.reactive.DataMetaEventbusService
import hiiadmin.reactive.meta.org.DataCampusMeta
import hiiadmin.reactive.meta.org.DataDepartmentMeta
import hiiadmin.reactive.meta.org.DataGradeMeta
import hiiadmin.reactive.meta.org.DataSchoolMeta
import hiiadmin.reactive.meta.org.DataUnitMeta
import hiiadmin.reactive.meta.user.DataRoleMeta
import hiiadmin.reactive.meta.user.DataStudentMeta
import hiiadmin.reactive.meta.user.DataTeacherMeta
import hiiadmin.reactive.modle.org.PdDataCampusDTO
import hiiadmin.reactive.modle.org.PdDataDepartmentDTO
import hiiadmin.reactive.modle.org.PdDataGradeDTO
import hiiadmin.reactive.modle.org.PdDataSchoolDTO
import hiiadmin.reactive.modle.org.PdDataUnitDTO
import hiiadmin.reactive.modle.user.PdDataRoleDTO
import hiiadmin.reactive.modle.user.PdDataStudentDTO
import hiiadmin.reactive.modle.user.PdDataTeacherDTO
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.quartz.JobExecutionException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.quartz.QuartzJobBean
import org.springframework.stereotype.Component
import timetabling.pd.PdCallBackData

@Component
@DisallowConcurrentExecution
@Slf4j
class PdCallBackJob extends QuartzJobBean {

    @Autowired
    RepositoryPdCallBackDataService repositoryPdCallBackDataService

    @Autowired
    DataMetaEventbusService dataMetaEventbusService
    
    
    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        execute()
    }

    def execute() {
        List<PdCallBackData> pdCallBackDataList = repositoryPdCallBackDataService.fetchCallBackDataListByOrder()

        if (pdCallBackDataList) {
            log.info("start sync pdCallBackData size:${pdCallBackDataList?.size()}".toString())
        }
        pdCallBackDataList.each {
            PdCallBackData pdCallBackData ->

                boolean update = true

                try {
                    if (StringUtils.isNotBlank(pdCallBackData.callBackJson)) {
                        switch (pdCallBackData.bizType) {
                            case ConstantEnum.PdDataBizType.USER_CHANGE.type:

                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.USER_ADD_ORG.action:
                                    case ConstantEnum.PdDataSyncAction.USER_MODIFY_ORG.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.USER_LEAVE_ORG.action:
                                        update = false
                                        break
                                }

                                List<PdDataTeacherDTO> pdDataTeacherDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataTeacherDTO.class)
                                pdDataTeacherDTOList.each {
                                    PdDataTeacherDTO pdDataTeacherDTO ->
                                        DataTeacherMeta dataTeacherMeta = new DataTeacherMeta(pdDataTeacherDTO, true, update)
                                        dataMetaEventbusService.onNext(dataTeacherMeta)
                                }

                                break
                            case ConstantEnum.PdDataBizType.DEPARTMENT_CHANGE.type:

                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.ORG_DEPT_CREATE.action:
                                    case ConstantEnum.PdDataSyncAction.ORG_DEPT_MODIFY.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.ORG_DEPT_REMOVE.action:
                                        update = false
                                        break
                                }

                                List<PdDataDepartmentDTO> pdDataDepartmentDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataDepartmentDTO.class)
                                pdDataDepartmentDTOList.each {
                                    PdDataDepartmentDTO pdDataDepartmentDTO ->
                                        DataDepartmentMeta dataDepartmentMeta = new DataDepartmentMeta(pdDataDepartmentDTO, true, update)
                                        dataDepartmentMeta.organizationId = pdCallBackData.organizationId
                                        dataMetaEventbusService.onNext(dataDepartmentMeta)
                                }
                                break
                            case ConstantEnum.PdDataBizType.ROLE_CHANGE.type:

                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.ROLE_ADD.action:
                                    case ConstantEnum.PdDataSyncAction.ROLE_UPDATE.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.ROLE_DELETE.action:
                                        update = false
                                        break
                                }

                                List<PdDataRoleDTO> pdDataRoleDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataRoleDTO.class)
                                pdDataRoleDTOList.each {
                                    PdDataRoleDTO pdDataRoleDTO ->
                                        pdDataRoleDTO.organizationId = pdCallBackData.organizationId
                                        DataRoleMeta dataRoleMeta = new DataRoleMeta(pdDataRoleDTO, true, update)
                                        dataMetaEventbusService.onNext(dataRoleMeta)
                                }
                                break
                            case ConstantEnum.PdDataBizType.ENTERPRISE_CHANGE.type:

                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.ORG_ADD.action:
                                    case ConstantEnum.PdDataSyncAction.ORG_CHANGE.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.ORG_REMOVE.action:
                                        update = false
                                        break
                                }

                                List<PdDataSchoolDTO> pdDataSchoolDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataSchoolDTO.class)
                                pdDataSchoolDTOList.each {
                                    PdDataSchoolDTO pdDataSchoolDTO ->
                                        DataSchoolMeta dataSchoolMeta = new DataSchoolMeta(pdDataSchoolDTO, true, update)
                                        dataMetaEventbusService.onNext(dataSchoolMeta)
                                }

                                break
                            case ConstantEnum.PdDataBizType.CAMPUS_CHANGE.type:

                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.ORG_ADD.action:
                                    case ConstantEnum.PdDataSyncAction.ORG_CHANGE.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.ORG_REMOVE.action:
                                        update = false
                                        break
                                }

                                List<PdDataCampusDTO> pdDataCampusDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataCampusDTO.class)
                                pdDataCampusDTOList.each {
                                    PdDataCampusDTO pdDataCampusDTO ->
                                        DataCampusMeta dataCampusMeta = new DataCampusMeta(pdDataCampusDTO, true, update)
                                        dataMetaEventbusService.onNext(dataCampusMeta)
                                }

                                break
                            case ConstantEnum.PdDataBizType.CAMPUS_DIRECTORY_CHANGE.type:
                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.EDU_DEPT_INSERT.action:
                                    case ConstantEnum.PdDataSyncAction.EDU_DEPT_UPDATE.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.EDU_DEPT_DELETE.action:
                                        update = false
                                        break
                                }

                                List<JSONObject> jsonArray = JSON.parseArray(pdCallBackData.callBackJson, JSONObject.class)

                                jsonArray.each {
                                    JSONObject jsonObject ->
                                        Byte type = jsonObject.getByte("type")
                                        switch (type) {
                                            case ConstantEnum.PdUnitType.GRADE.type:

                                                PdDataGradeDTO pdDataGradeDTO = jsonObject.to(PdDataGradeDTO.class)
                                                DataGradeMeta dataGradeMeta = new DataGradeMeta(pdDataGradeDTO, true, update)
                                                dataMetaEventbusService.onNext(dataGradeMeta)
                                                break
                                            case ConstantEnum.PdUnitType.CLASS.type:

                                                PdDataUnitDTO pdDataUnitDTO = jsonObject.to(PdDataUnitDTO.class)
                                                DataUnitMeta dataClassMeta = new DataUnitMeta(pdDataUnitDTO, true, update)
                                                dataMetaEventbusService.onNext(dataClassMeta)
                                                break
                                        }
                                }

                                break
                            case ConstantEnum.PdDataBizType.CAMPUS_USER_CHANGE.type:
                                switch (pdCallBackData.syncAction) {
                                    case ConstantEnum.PdDataSyncAction.EDU_USER_INSERT.action:
                                    case ConstantEnum.PdDataSyncAction.EDU_USER_UPDATE.action:
                                        break
                                    case ConstantEnum.PdDataSyncAction.EDU_USER_DELETE.action:
                                        update = false
                                        break
                                }

                                List<PdDataStudentDTO> pdDataStudentDTOList = JSON.parseArray(pdCallBackData.callBackJson, PdDataStudentDTO.class)
                                pdDataStudentDTOList.each {
                                    PdDataStudentDTO pdDataStudentDTO ->
                                        DataStudentMeta dataStudentMeta = new DataStudentMeta(pdDataStudentDTO, true, update)
                                        dataMetaEventbusService.onNext(dataStudentMeta)
                                }

                                break
                        }
                    }
                    pdCallBackData.dataStatus = ConstantEnum.DataStatus.NORMAL.status
                    repositoryPdCallBackDataService.saveData(pdCallBackData)
                } catch (Exception e) {
                    log.error("pd call back data process error", e)
                    pdCallBackData.dataStatus = ConstantEnum.DataStatus.FAIL.status
                    repositoryPdCallBackDataService.saveData(pdCallBackData)
                }
        }
    }
}

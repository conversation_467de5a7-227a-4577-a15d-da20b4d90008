package hiiadmin.utils

import groovy.util.logging.Slf4j
import hiiadmin.EnvService

import java.util.regex.Matcher
import java.util.regex.Pattern

@Slf4j
class PatternUtils {

    public static final String IP_PATTERN = """^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\$"""

    public static final String EXCEL_SHEET = "([*/:\\\\\\[\\]?])"

    static String excelSheetName(String name) {
        name = name.replaceAll(EXCEL_SHEET, "|")
        name.substring(0, name.length() < 31 ? name.length() : 31)
    }

    static boolean isIp(String ip) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile(IP_PATTERN)
            Matcher m = p.matcher(ip)
            flag = m.matches()
        } catch (Exception e) {
            log.error("IP@${ip} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    static boolean isEName(String eName) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^[A-Za-z0-9 ·.]+\$")
            Matcher m = p.matcher(eName)
            flag = m.matches()
        } catch (Exception e) {
            flag = false
        }
        return flag
    }

    /**
     * 是否是合法的电话号码
     * @param mobile
     * @return
     */
    static boolean isMobile(String mobile, EnvService envService) {
        if (envService.isDev() && mobile.startsWith("177")) {
            return true
        }
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^((13[0-9])|(14[0-9])|(15[0-9])|(17[0-9])|(18[0-9])|(16[0-9])|(19[0-9]))\\d{8}\$")
            Matcher m = p.matcher(mobile)
            flag = m.matches()
        } catch (Exception e) {
            flag = false
        }
        return flag
    }

    static boolean isMobile(String mobile) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^((13[0-9])|(14[0-9])|(15[0-9])|(17[0-9])|(18[0-9])|(16[0-9])|(19[0-9]))\\d{8}\$")
            Matcher m = p.matcher(mobile)
            flag = m.matches()
        } catch (Exception e) {
            flag = false
        }
        return flag
    }

    /**
     * 是否是合法的邮件地址
     * @param email
     * @return
     */
    static Boolean validEmail(String email) {
        //电子邮件
        String check = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}\$"
        Pattern regex = Pattern.compile(check)
        Matcher matcher = regex.matcher(email)
        boolean isMatched = matcher.matches()
        return isMatched
    }
    
    static Boolean validCardNum(String cardNum) {
        boolean flag = false
        try {
            Pattern pattern = Pattern.compile("^[A-Za-z0-9]+\$")
            Matcher m = pattern.matcher(cardNum)
            flag = m.matches()
        } catch (Exception e) {
            log.error("cardNum@${cardNum} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    // 仅支持汉字，字母，数字
    static boolean isName(String name) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^[\\u4e00-\\u9fa5A-Za-z0-9]+\$")
            Matcher m = p.matcher(name)
            flag = m.matches()
        } catch (Exception e) {
            log.error("name@${name} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    static boolean isChineseOrWord(String str) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^[\\u4e00-\\u9fa5A-Za-z]+\$")
            Matcher m = p.matcher(str)
            flag = m.matches()
        } catch (Exception e) {
            log.error("name@${str} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    static boolean isNumeric(String str) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("^[0-9]+\$")
            Matcher m = p.matcher(str)
            flag = m.matches()
        } catch (Exception e) {
            log.error("name@${str} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    static boolean isNumericV2(String str) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("-?\\d+(\\.\\d+)?")
            Matcher m = p.matcher(str)
            flag = m.matches()
        } catch (Exception e) {
            log.error("name@${str} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    /**
     * 是否包含特殊字符串
     * @param name
     * @return
     */

    static boolean isSpecial(String str) {
        boolean flag = false
        try {
            Pattern p = Pattern.compile("[^\\ / : ? “” ‘’ \" \'\\[ \\]< > |]+")
            Matcher m = p.matcher(str)
            flag = m.matches()
        } catch (Exception e) {
            log.error("str@${str} pattern error".toString(), e)
            flag = false
        }
        return flag
    }

    /**
     * 去除字符串首尾的空格键
     * @param str
     * @return
     */
    static String delStartOrEndWithSpace(String str) {
        while (str.length() > 0) {
            if (str.startsWith(" ")) {
                str = str.substring(1, str.length())
            } else {
                break
            }
        }
        while (str.length() > 0) {
            if (str.endsWith(" ")) {
                str = str.substring(0, str.length() - 1)
            } else {
                break
            }
        }

        str
    }

    /**
     * 判断各类name是否小于等于50字符，默认做50字符限制，特殊情况另外处理
     * @param name
     * @return
     */
    static boolean isNameLe50(String name) {
        if (name?.length() > 50) {
            return false
        }

        true
    }

    /**
     * 判断各类eName(英文名)是否小于等于100字符，默认做100字符限制，特殊情况另外处理
     * @param eName
     * @return
     */
    static boolean isENameLe100(String eName) {
        if (eName?.length() > 100) {
            return false
        }

        true
    }

    public static final String IMG_PATTERN = "([a-zA-z]+://[^\\s]*.com/)|(\\?Expires=[^\\s]*)"

    public static final String IMG_PATTERN_PAD = "([a-zA-z]+://[^\\s]*.com/)|(\\.[^\\s]*)"

    static String getFileName(String url) {
        return url.replaceAll(IMG_PATTERN, "")
    }

    static String getFileName4Face(String url) {
        return url.replaceAll(IMG_PATTERN_PAD, "")
    }

//    public final static String regex = """'|%|--|and|or|not|use|insert|delete|update|select|count|group|union|create|drop|truncate|alter|grant|execute|exec|xp_cmdshell|call|declare|source|sql"""

    static String filter(String param) {
        if (param == null) {
            return param
        }
        return param.replaceAll("%", /\\%/)//(?i)不区分大小写替换
    }
}

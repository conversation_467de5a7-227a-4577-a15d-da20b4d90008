package hiiadmin.utils


import java.util.regex.Matcher
import java.util.regex.Pattern

class ImageUtil {
    static String image2Base64(String imgUrl) {
        URL url
        HttpURLConnection urlConnection = null
        InputStream inputStream = null
        ByteArrayOutputStream baos = null
        try {
            url = new URL(imgUrl)
            urlConnection = (HttpURLConnection) url.openConnection()
            urlConnection.connect()
            inputStream = urlConnection.getInputStream()

            baos = new ByteArrayOutputStream()

            byte[] buffer = new byte[1024]
            int len = 0
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inputStream.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                baos.write(buffer, 0, len)
            }
            // 对字节数组Base64编码
            return encode(baos.toByteArray())

        } catch (Exception e) {
            e.printStackTrace()
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (IOException e) {
                    e.printStackTrace()
                }
            }

            if (baos != null) {
                try {
                    baos.close()
                } catch (IOException e) {
                    e.printStackTrace()
                }
            }

            if (urlConnection != null) {
                urlConnection.disconnect()
            }
        }

        return imgUrl
    }

    private static String encode(byte[] image) {
        return image.encodeAsBase64()
    }

    private static String replaceEnter(String str) {
        String reg = "[\n-\r]"
        Pattern p = Pattern.compile(reg)
        Matcher m = p.matcher(str)
        return m.replaceAll("")
    }
}

package hiiadmin.utils

class MapUtils {
    /**
     * latitude:纬度
     * longitude: 经度
     * "39.904030","116.407526"
     * 注意：仅支持省份匹配经纬度 不能加省，直辖市，自治区等，如黑龙江省 需传如黑龙江
     * */
    static AddressAims provinceChangeLatAndLon(String address) {
        Map<String, AddressAims> map = new HashMap<>(16)
        map.put("北京市", new AddressAims(latitude: "39.904030", longitude: "116.407526"))
        map.put("天津市", new AddressAims(latitude: "39.084158", longitude: "117.200983"))
        map.put("河北省", new AddressAims(latitude: "38.037057", longitude: "114.468664"))
        map.put("山西省", new AddressAims(latitude: "37.873531", longitude: "112.562398"))
        map.put("内蒙古自治区", new AddressAims(latitude: "40.817498", longitude: "111.765617"))
        map.put("辽宁省", new AddressAims(latitude: "41.835441", longitude: "123.429440"))
        map.put("吉林省", new AddressAims(latitude: "43.896536", longitude: "125.325990"))
        map.put("黑龙江", new AddressAims(latitude: "45.742347", longitude: "126.661669"))
        map.put("上海市", new AddressAims(latitude: "31.230416", longitude: "121.473701"))
        map.put("江苏省", new AddressAims(latitude: "39.904030", longitude: "118.763232"))
        map.put("浙江省", new AddressAims(latitude: "30.267446", longitude: "120.152791"))
        map.put("安徽省", new AddressAims(latitude: "31.861184", longitude: "117.284922"))
        map.put("福建省", new AddressAims(latitude: "26.100779", longitude: "119.295144"))
        map.put("江西省", new AddressAims(latitude: "28.675696", longitude: "115.909228"))
        map.put("山东省", new AddressAims(latitude: "36.668530", longitude: "117.020359"))
        map.put("河南省", new AddressAims(latitude: "34.765515", longitude: "113.753602"))
        map.put("湖北省", new AddressAims(latitude: "30.546498", longitude: "114.341861"))
        map.put("湖南省", new AddressAims(latitude: "28.112444", longitude: "112.983810"))
        map.put("广东省", new AddressAims(latitude: "23.132191", longitude: "113.266530"))
        map.put("广西壮族自治区", new AddressAims(latitude: "22.815478", longitude: "108.327546"))
        map.put("海南省", new AddressAims(latitude: "20.017377", longitude: "110.349228"))
        map.put("重庆市", new AddressAims(latitude: "29.563009", longitude: "106.551556"))
        map.put("四川省", new AddressAims(latitude: "30.651651", longitude: "104.075931"))
        map.put("贵州省", new AddressAims(latitude: "26.598194", longitude: "106.707410"))
        map.put("云南省", new AddressAims(latitude: "25.045806", longitude: "102.710002"))
        map.put("西藏自治区", new AddressAims(latitude: "29.646922", longitude: "91.117212"))
        map.put("陕西省", new AddressAims(latitude: "34.265472", longitude: "108.954239"))
        map.put("甘肃省", new AddressAims(latitude: "36.059421", longitude: "103.826308"))
        map.put("青海省", new AddressAims(latitude: "36.620901", longitude: "101.780199"))
        map.put("宁夏省", new AddressAims(latitude: "38.471317", longitude: "106.258754"))
        map.put("新疆维吾尔自治区", new AddressAims(latitude: "43.793026", longitude: "87.627704"))
        map.put("香港特别行政区", new AddressAims(latitude: "22.320048", longitude: "114.173355"))
        map.put("澳门特别行政区", new AddressAims(latitude: "22.198951", longitude: "113.549090"))
        map.put("台湾省", new AddressAims(latitude: "25.030724", longitude: "121.520076"))
        map.put("空", new AddressAims(latitude: "00.0", longitude: "00.0"))
        AddressAims addressAims = map.get(address)
        if (addressAims != null) {
            return addressAims
        } else {
            addressAims = map.get("空")
            return addressAims
        }
    }

    static class AddressAims implements Serializable {
        String latitude
        String longitude
    }
}

package hiiadmin.utils

/**
 * <AUTHOR> @date 2020/12/23 上午10:56
 */
class ActivationCodeUtils {

    final static List<String> codeList = [
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
            'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
            'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    ]

    static getActivationCode(int digit) {
        Random random = new Random()
        String activationCode = ""
        for (int i = 0; i < digit; i++) {
            activationCode = activationCode + codeList.get(random.nextInt(codeList.size()))
        }
        activationCode
    }

    static String genShortUUId() {
        List<String> list = codeList
        //打乱元素排序，增加反推难度
        Collections.shuffle(list)
        StringBuffer randomStr = new StringBuffer()
        for (int i = 0; i < codeList.size(); i++) {
            System.out.println(i)
            randomStr.append(list.get(i))
        }
        //更改下面两个数字可以渠道不同位数的随机数
        return randomStr.substring(4, 10)
    }

    static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            System.out.println(genShortUUId())
        }
    }
}

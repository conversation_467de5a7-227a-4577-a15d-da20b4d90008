package hiiadmin.utils

import com.google.common.base.Joiner
import com.google.common.base.Splitter
import groovy.util.logging.Slf4j
import net.sourceforge.pinyin4j.PinyinHelper
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination

import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.text.DecimalFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

@Slf4j
class ToStringUnits {
    //富士 组织Rid
    public static final String ORGANIZATION_RID = "********************************"
    //富士企业id
    public static final String GID = "105BCEF9-A734-4CCE-B380-4F0A445A3AA3"
    //千亿
    public static final Long MYRIADS = 100000000000
    //十亿
    public static final Long BILLION = 1000000000

    /**
     * 月租卡A  开门卡 BBB817CE-E6A9-4F9C-8C2D-BDFD2431C053
     * */
    public static final String TCMA = "C97611CE-C6EA-4582-9680-55934B79F9F7"

    /**
     * 千位时会出现结果异常，移步bugu-common.jar内方法
     * @param str
     * @return
     */
    static String getChinese(String str) {
        String[] s1 = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]
        String[] s2 = ["十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"]
        String result = ""
        int n = str.length()
        for (int i = 0; i < n; i++) {
            int num = str.bytes[i] - '0'.chars[0]
            if (i != n - 1 && num != 0) {
                result += s1[num] + s2[n - 2 - i]
            } else {
                result += s1[num]
            }
        }
        Integer i = str as Integer
        if (i >= 10 && i < 20) {
            result = result.replace("一十", "十")
        }
        StringBuffer s = new StringBuffer(result)
        chineseNumRemoveLastZero(s)
        s.toString()
    }

    static String longList2String(List idList) {
        try {
            return Joiner.on(",").join(idList)
        } catch (RuntimeException e) {
            println(e.message)
            log.error("to id List error str@{} ,e@{}", idList.toString(), e.message)
        }
        ""
    }

    static void chineseNumRemoveLastZero(StringBuffer str) {
        if (str[-1] == "零" && str.length() > 1) {
            str.deleteCharAt(str.length() - 1)
        }
        if (str[-1] == "零" && str.length() > 1) {
            chineseNumRemoveLastZero(str)
        }
    }
    /**
     * 根据ids字符串生成List<Long>
     * @param ids
     * @return
     */
    static List<Long> idsString2LongList(String ids) {

        try {
            Iterable li = Splitter.on(",").trimResults().omitEmptyStrings().split(ids)
            return li.toSet().collect {
                it as Long
            }
        } catch (RuntimeException e) {
            log.error("to id List error str@{} ,e@{}", ids, e.message)
        }
        []
    }

    static List<Long> idsString2LongListDecode(String ids) {
        Iterable li = Splitter.on(",").trimResults().omitEmptyStrings().split(ids)
        List<Long> decodeIdList = []
        try {
            List<String> strList = li.toSet().collect {
                it as String
            }
            strList.each {
                String str ->
                    decodeIdList <<PhenixCoder.decodeId(str)
            }
        } catch (RuntimeException e) {
            log.error("to id List error str@{} ,e@{}", ids, e.message)
        }
        return decodeIdList
    }

    /**
     * 根据userType字符串生成List<Byte>
     * @param ids
     * @return
     */
    static List<Byte> userTypeString2ByteList(String userType) {

        try {
            Iterable li = Splitter.on(",").trimResults().omitEmptyStrings().split(userType)
            return li.toSet().collect {
                it as Byte
            }
        } catch (RuntimeException e) {
            log.error("to type List error str@{} ,e@{}", userType, e.message)
        }
        []
    }


    /**
     * 根据userType字符串生成List<Int>
     * @param ids
     * @return
     */
    static List<Integer> strString2IntegerList(String str) {
        try {
            Iterable li = Splitter.on(",").trimResults().omitEmptyStrings().split(str)
            return li.toSet().collect {
                it as Integer
            }
        } catch (RuntimeException e) {
            log.error("to type List error str@{} ,e@{}", str, e.message)
        }
        []
    }

    static String list2String(List idList) {
        try {
            return Joiner.on(",").join(idList)
        } catch (RuntimeException e) {
            println(e.message)
            log.error("to id List error str@{} ,e@{}", idList.toString(), e.message)
        }
        ""
    }

    static void sortChinesList(List<String> list, boolean asc = true) {
        try {
            list.sort {
                a, b ->
                    char a1 = a.charAt(0)
                    char b1 = b.charAt(0)
                    int i = (PinyinHelper.toHanyuPinyinStringArray(a1).toString() <=> PinyinHelper.toHanyuPinyinStringArray(b1).toString())
                    if (!asc) {
                        return -i
                    }
                    i
            }
        } catch (RuntimeException e) {
            log.error("sort chinese error ,@{}, asc@{}", list.toListString(), asc)
        }
    }

    private static HanyuPinyinOutputFormat hanYuPinOutputFormat = new HanyuPinyinOutputFormat()
    private static Map<String, String> staticChars = new HashMap<String, String>()

    static {
        //输出设置，大小写，音标方式等
        hanYuPinOutputFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE)
        hanYuPinOutputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE)
        hanYuPinOutputFormat.setVCharType(HanyuPinyinVCharType.WITH_V)

        staticChars.put("长", "chang")
    }

    static String getChinesPinyin(String code) {
        if (code == null) return null
        char[] codeChar = code.toCharArray()
        StringBuilder sb = new StringBuilder()
        for (char c : codeChar) {
            // 是中文或者a-z或者A-Z转换拼音
            if (String.valueOf(c).matches("[\\u4E00-\\u9FA5]+")) {
                try {
                    String key = String.valueOf(c)
                    if (staticChars.containsKey(key)) {
                        sb.append(staticChars.get(key))
                        continue
                    }
                    String[] pins = PinyinHelper.toHanyuPinyinStringArray(c, hanYuPinOutputFormat)
                    if (pins != null && pins.length > 0) {
                        sb.append(pins[0])
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error(e.getMessage(), e)
                }
            } else if (((int) c >= 65 && (int) c <= 90)
                    || ((int) c >= 97 && (int) c <= 122)) {
                sb.append(String.valueOf(c))
            } else {
                sb.append(String.valueOf(c))
            }
        }
        return sb.toString()
    }

    static String getChinesPinyinFirst(String code) {
        if (code == null) return null
        char[] codeChar = code.toCharArray()
        StringBuilder sb = new StringBuilder()
        for (char c : codeChar) {
            // 是中文或者a-z或者A-Z转换拼音
            if (String.valueOf(c).matches("[\\u4E00-\\u9FA5]+")) {
                try {
                    String key = String.valueOf(c)
                    if (staticChars.containsKey(key)) {
                        sb.append(staticChars.get(key).substring(0, 1))
                        continue
                    }
                    String[] pins = PinyinHelper.toHanyuPinyinStringArray(c, hanYuPinOutputFormat)
                    if (pins != null && pins.length > 0) {
                        sb.append(pins[0].substring(0, 1))
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error(e.getMessage(), e)
                }
            } else if (((int) c >= 65 && (int) c <= 90)
                    || ((int) c >= 97 && (int) c <= 122)) {
                sb.append(String.valueOf(c))
            } else {
                sb.append(String.valueOf(c))
            }
        }
        return sb.toString()
    }

    /**
     * 获取两个集合的差集
     * @return listA*            */
    static List<Long> getListRemove(List<Long> listA, List<Long> listB) {
        listA.removeAll(listB)
        return listA
    }

    static String getUUIdToString() {
        return UUID.randomUUID().toString().trim().replaceAll("-", "")
    }
    /**
     * 根据接送人id生成staffNo
     * */
    static String idChangeStaffNo(Long id, Byte userType) {
        String no = (MYRIADS + id) + userType * BILLION + ""
        String staffNo = no.replaceFirst("1", "SI")
        return staffNo
    }

    /**
     * 根据 staffNo 转换成 long id
     * */
    static Long noStrChangeIdLong(String staffNo, Byte userType) {
        if (staffNo) {
            String idStr = staffNo.replaceFirst("SI", "1")
            String id = Long.parseLong(idStr) - userType * BILLION + ""
            String idStr2 = id.replaceFirst("1", "0")
            return Long.parseLong(idStr2)
        } else {
            return null
        }
    }

    static Float divisionFloat(int a, int b, String digits) {
        DecimalFormat df = new DecimalFormat(digits)
        return df.format((float) (a / b) * 100) as Float
    }

    static boolean haveChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5
    }
// 判断一个字符串是bai否含有中文du
    static boolean haveChinese(String str) {
        if (str == null) return false
        for (char c : str.toCharArray()) {
            if (haveChinese(c)) return true
        }
        return false
    }

    static String phoneEncode(String mobile) {
        if (mobile.length() == 11) {
            String s = mobile.substring(3, 7)
            String m = mobile.replaceFirst(s, "****")
            return m
        }
        return mobile
    }

    /**
     * 判断字符串是否有特殊字符
     * */
    static boolean haveSpecialChar(String str) {
        String regEx = """[_!\$^&*+=|{}';'",<>/?~！#￥%……&*|{}【】‘；：”“'。，、？]"""
        Pattern p = Pattern.compile(regEx)
        Matcher m = p.matcher(str)
        return m.find()
    }

    /**
     * 判断字符串是否有特殊字符
     * 与上面相比，可使用"_"和"#"
     * */
    static boolean haveSpecialCharV2(String str) {
        String regEx = """[!\$^&*+=|{}';'",<>/?~！￥%……&*|{}【】‘；：”“'。，、？]"""
        Pattern p = Pattern.compile(regEx)
        Matcher m = p.matcher(str)
        return m.find()
    }

    /**
     * 判断字符串是否包含某个字符
     * 包含：true 不包含：false
     * */
    static boolean containsCharacter(String str, String character) {
        return str.contains(character)
    }

    /**
     * 身份证合法性校验
     * */
    static boolean idCardLegalVerification(String idCard) {
        boolean isId = false
        // 判断号码的长度 15位或18位
        if (idCard.length() != 15 && idCard.length() != 18) {
            isId = true
            return isId
        }
        return isId
    }


    /**
     * 判断时间是否属于历史时间
     * @param timeStamp 时间搓
     * @param days 几天,前为负数，后为正数
     * @return 属于：true 不属于：false
     */
    static Boolean belongsToHistoricalData(long timeStamp, Integer days = 30) {
        Long daysAge = TimeUtils.getDateBeforeOrAfter(days).time
        if (timeStamp > daysAge) {
            return false
        } else {
            true
        }
    }


    static String urlTransformationReferer(String url) {
        URI uri = new URI(url)
        URI effectiveURI = null
        String urlStr = ""
        try {
            effectiveURI = new URI(uri.getScheme(), uri.getUserInfo(), uri.getHost(), uri.getPort(), null, null, null)
            urlStr = effectiveURI.toString()
        } catch (Exception e) {
            log.error("[urlTransformationReferer]transformation error".toString(), e)

        }
        return urlStr
    }

    /**
     * 获取两个集合的交集
     * */
    static List<Long> getListIntersection(List<Long> list1, List<Long> list2) {
        //保证长度小的在左边
        if (list1.size() > list2.size()) {
            return getListIntersection(list2, list1);
        }
        Map<Long, Integer> map = new HashMap<Long, Integer>(list1.size());
        list1.each {
            Long id ->
                int count = map.getOrDefault(id, 0) + 1
                map.put(id, count)
        }
        List<Long> newList = []
        list2.each {
            Long id ->
                int count = map.getOrDefault(id, 0)
                if (count > 0) {
                    newList.add(id)
                    map.remove(id);
                }
        }
        return newList
    }
    /**
     * hmacSHA256加密
     *
     * @param data
     * @param secret
     * @return
     */
    public static String hmacSHA256(String data, String secret) {
        StringBuilder sb = new StringBuilder();
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100), 1, 3);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString().toUpperCase();
    }
}

package hiiadmin.module.attendance.dto

import hiiadmin.utils.TimeUtils

import java.sql.Time

class EventAttendanceConfigRuleDTO {

    String id

    String configId

    /**
     * 周几
     * */
    Integer weekDay

    /**
     * 签到打开状态
     * */
    Boolean sinInOpenStatus


    Long signInTime


    Long startTime


    Long endTime

    Integer timeIdx

    /**
     * 座位类型是的座位id
     * */
    String pewTypeId

    Long signInTimeIdx

    String ignoreGroupIds

    Long getSignInTimeIdx() {
        if (!this.signInTime) {
            return -1
        }
        String timeStr = TimeUtils.getFormatDate(System.currentTimeMillis(), TimeUtils.DATE_FORMAT)
        this.signInTimeIdx = TimeUtils.getFormatDateTime(timeStr + " " + new Time(this.signInTime).toString())?.time
        return signInTimeIdx
    }

    Long getSignInTime() {
        if (!this.signInTime) {
            return -1
        }
        String timeStr = TimeUtils.getFormatDate(System.currentTimeMillis(), TimeUtils.DATE_FORMAT)
        this.signInTime = TimeUtils.getFormatDateTime(timeStr + " " + new Time(this.signInTime).toString())?.time
        return signInTime
    }

    Long getStartTime() {
        if (!this.startTime) {
            return -1
        }
        String timeStr = TimeUtils.getFormatDate(System.currentTimeMillis(), TimeUtils.DATE_FORMAT)
        this.startTime = TimeUtils.getFormatDateTime(timeStr + " " + new Time(this.startTime).toString())?.time
        return startTime
    }

    Long getEndTime() {
        if (!this.endTime) {
            return -1
        }
        String timeStr = TimeUtils.getFormatDate(System.currentTimeMillis(), TimeUtils.DATE_FORMAT)
        this.endTime = TimeUtils.getFormatDateTime(timeStr + " " + new Time(this.endTime).toString())?.time
        return endTime
    }
}

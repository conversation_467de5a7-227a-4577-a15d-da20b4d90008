package hiiadmin.module.attendance

class Attendance2AllocationVO {

    Long id

    Long ruleId

    String ruleName

    String groupId

    String groupName

    String name

    Long startDate

    Long endDate

    String messageSend

    Integer parentMessageContent

    Integer dormViewPermission

    Integer classViewPermission

    Integer viewMode

    String permissionObjIds

    Long startTime

    Long endTime

    Long closeTime

    Integer personCount

    Byte userType

    private int realCount

    private int nonCount = 0

    private int lateCount = 0

    private int normalCount = 0

    private int standardCount = 0

    private int askForLeaveCount = 0

    int getNonCount() {
        return nonCount
    }

    void setNonCount(int nonCount) {
        this.nonCount = nonCount
    }

    int getLateCount() {
        return lateCount
    }

    void setLateCount(int lateCount) {
        this.lateCount = lateCount
    }

    int getNormalCount() {
        return normalCount
    }

    void setNormalCount(int normalCount) {
        this.normalCount = normalCount
    }

    int getStandardCount() {
        return standardCount
    }

    void setStandardCount(int standardCount) {
        this.standardCount = standardCount
    }

    int getAskForLeaveCount() {
        return askForLeaveCount
    }

    void setAskForLeaveCount(int askForLeaveCount) {
        this.askForLeaveCount = askForLeaveCount
    }

    int getRealCount() {
        return normalCount + lateCount
    }
}
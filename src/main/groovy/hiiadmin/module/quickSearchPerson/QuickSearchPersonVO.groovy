package hiiadmin.module.quickSearchPerson

import hiiadmin.ConstantEnum
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.gated.TrackVO
import hiiadmin.utils.PhenixCoder
import hiiadmin.utils.ToStringUnits
import org.apache.commons.lang3.StringUtils
import timetabling.user.Student


/**
 * @Author: sjl
 * @CreateTime: 2025-08-25
 * @Description: ${description}
 * @Version: 1.0
 */
class QuickSearchPersonVO implements Serializable{

    static QuickSearchPersonVO setFaceGroupVoToTeacherVO(TeacherVO teacher) {
        if (teacher == null) {
            throw new RuntimeException("该用户不存在")
        }
        QuickSearchPersonVO vo = new QuickSearchPersonVO()
        vo.userType = ConstantEnum.UserTypeEnum.TEACHER.type
        vo.userId = teacher.id
        vo.name = teacher?.name
        vo.gender = teacher?.gender
        vo.pic = teacher?.avatar
        vo.code = teacher?.code
        return vo
    }

    static QuickSearchPersonVO setFaceGroupVoToStudent(Student student, String gradeName, String unitName, String sectionName, String dormRoom, String facultyName, String majorName, Byte campusType, int resultStatus = 99, String checkMessage = null) {
        if (student == null) {
            throw new RuntimeException("该用户不存在")
        }
        QuickSearchPersonVO vo = new QuickSearchPersonVO()
        vo.userId = PhenixCoder.encodeId(student.id)
        vo.userType = ConstantEnum.UserTypeEnum.STUDENT.type
        vo.name = student?.name
        vo.pic = student?.pic
        vo.code = student?.code
        vo.gender = student?.gender
        vo.code = student?.code
        vo.facultyName = facultyName
        vo.majorName = majorName
        if (StringUtils.isNotBlank(student?.mobile)) {
            vo.mobile = ToStringUnits.phoneEncode(student.mobile)
        }
        StringBuilder sb = new StringBuilder()
        if (StringUtils.isNotBlank(sectionName)) {
            sb.append(sectionName)
            vo.sectionName = sectionName
        }
        if (StringUtils.isNotBlank(gradeName)) {
            sb.append(gradeName)
            vo.gradeName = gradeName
        }
        if (StringUtils.isNotBlank(unitName)) {
            sb.append(unitName)
            vo.className = unitName
        }
        if (resultStatus != 99) {
            vo.resultStatus = resultStatus
        }
        if (checkMessage != null && resultStatus != 99) {
            vo.checkMessage = checkMessage
        }
        vo.dormRoom = dormRoom
        if (campusType == 2 as byte) {
            vo.unitName = unitName
        } else {
            vo.unitName = sb.toString()
        }
        return vo
    }
    Long id

    String faceGroupName

    //用户id
    String userId

    Byte userType
    //姓名,也可能是老师、家长、学生姓名
    String name

    String studentName

    String pic

    String code

    //性别
    Boolean gender

    String mobile

    String sectionName

    String gradeName

    String className //班级名

    String unitName //可能是学段+年级+班级名

    String facultyName

    String majorName

    String dormRoom

    String departmentName

    int resultStatus

    String checkMessage

    List<String> relation

    List<String> unitNameList

    List<TrackVO> trackList

    String mapPic

    Integer trackCount //抓拍记录数

}

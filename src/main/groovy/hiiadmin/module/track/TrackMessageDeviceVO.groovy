package hiiadmin.module.track

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

@ToString(includeNames = true)
@EqualsAndHashCode
class TrackMessageDeviceVO {

    String id

    String featureId

    long deviceId

    String deviceName

    String placeNames

    private String ip

    private String deviceIp

    String serialNumber

    Byte type

    String typeName

    Integer firm

    String getIp() {
        if (ip) {
            return ip
        } else {
            return deviceIp
        }
    }

    void setIp(String ip) {
        this.ip = ip
    }

    void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp
    }
}

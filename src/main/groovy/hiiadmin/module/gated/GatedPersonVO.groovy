package hiiadmin.module.gated

import com.google.common.base.Converter
import groovy.util.logging.Slf4j
import hiiadmin.oss.OssUtils
import hiiadmin.utils.PhenixCoder
import timetabling.user.Parent
import timetabling.user.Student
import timetabling.user.Teacher
import timetabling.visitor.Visitor

import static hiiadmin.ConstantEnum.UserTypeEnum

/**
 * <AUTHOR> @date 2020/7/22 17:43
 * @version 1.0* @description
 */
@Slf4j
class GatedPersonVO {

    Long id

    String userId

    Byte userType

    String code

    String name

    Boolean gender

    /**
     * 家长老师相关
     */
    String mobile

    /**
     * 学生相关字段
     */
    String unitId

    String unitName

    String buildingId

    String layerId

    String doorPlateName

    String alias

    String gradeId

    String gradeName
    //是否在控制列表
    Boolean gated

    String avatar

    GatedPersonVO buildVO(Visitor visitor, Long personId) {
        GatedPersonVO vo = new BuildVO4visitor().convert(visitor)
        vo.id = personId
        vo
    }

    GatedPersonVO buildVO(Parent parent, Long personId) {
        GatedPersonVO vo = new BuildVO4parent().convert(parent)
        vo.id = personId
        vo
    }

    GatedPersonVO buildVO4Teacher(Teacher teacher, Long personId, Boolean gated) {
        GatedPersonVO vo = new BuildVO4teacher().convert(teacher)
        vo.id = personId
        vo.gated = gated
        vo
    }

    GatedPersonVO buildVO(Student student, Long personId, Boolean gated) {
        GatedPersonVO vo = new BuildVO4student().convert(student)
        vo.id = personId
        vo.gated = gated
        vo
    }

    private class BuildVO4visitor extends Converter<Visitor, GatedPersonVO> {
        @Override
        protected GatedPersonVO doForward(Visitor visitor) {
            GatedPersonVO gatedPersonVO = new GatedPersonVO()

            gatedPersonVO.setUserId(PhenixCoder.encodeId(visitor.getId()))
            gatedPersonVO.setUserType(UserTypeEnum.VISITORS.type)
            gatedPersonVO.setName(visitor.visitorName)
            String url = visitor.pic
            if (!url.startsWith("http")) {
                url = OssUtils.transformFilePath(url, 300)
            }
            gatedPersonVO.avatar = url
            return gatedPersonVO
        }

        @Override
        protected Visitor doBackward(GatedPersonVO gatedPersonVO) {
            return null
        }
    }

    private class BuildVO4parent extends Converter<Parent, GatedPersonVO> {
        @Override
        protected GatedPersonVO doForward(Parent parent) {
            GatedPersonVO gatedPersonVO = new GatedPersonVO()

            gatedPersonVO.setUserId(PhenixCoder.encodeId(parent.id))
            gatedPersonVO.setUserType(UserTypeEnum.PARENT.type)
            gatedPersonVO.setName(parent.getName())
            gatedPersonVO.setGender(parent.getGender())
            gatedPersonVO.avatar = parent.avatar
            return gatedPersonVO
        }

        @Override
        protected Parent doBackward(GatedPersonVO gatedPersonVO) {
            return null
        }
    }

    private class BuildVO4teacher extends Converter<Teacher, GatedPersonVO> {

        @Override
        protected GatedPersonVO doForward(Teacher teacher) {
            GatedPersonVO gatedPersonVO = new GatedPersonVO()

            gatedPersonVO.setUserId(PhenixCoder.encodeId(teacher.id))
            gatedPersonVO.setUserType(UserTypeEnum.TEACHER.type)
            gatedPersonVO.setCode(teacher.getCode())
            gatedPersonVO.setName(teacher.getName())
            gatedPersonVO.gender = teacher.gender
            gatedPersonVO.avatar = teacher.avatar
            return gatedPersonVO
        }

        @Override
        protected Teacher doBackward(GatedPersonVO gatedPersonVO) {
            return null
        }
    }

    private class BuildVO4student extends Converter<Student, GatedPersonVO> {
        @Override
        protected GatedPersonVO doForward(Student student) {
            GatedPersonVO gatedPersonVO = new GatedPersonVO()

            gatedPersonVO.setUserId(PhenixCoder.encodeId(student.getId()))
            gatedPersonVO.setUserType(UserTypeEnum.STUDENT.type)
            gatedPersonVO.setCode(student.getCode())
            gatedPersonVO.setName(student.getName())
            gatedPersonVO.gender = student.gender
            gatedPersonVO.avatar = student.pic
            return gatedPersonVO
        }

        @Override
        protected Student doBackward(GatedPersonVO gatedPersonVO) {
            return null
        }
    }

}
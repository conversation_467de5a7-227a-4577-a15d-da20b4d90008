package hiiadmin.module.gated

import com.google.common.base.Converter
import hiiadmin.module.humanface.TrackImageVO
import timetabling.track.Track

class TrackVO implements Serializable {
    Long id

    String name

    String code

    String mobile

    String avatar

    String userId

    String campusId

    /*
    班级/部门/学生关系
     */
    String organizeName

    String facultyName

    String majorName

    /**
     * 学段，学生使用
     */
    String sectionName

    /**
     * 年级
     */
    String gradeName

    /**
     * 班级
     */
    String unitName

    /**
     * 人员类型名称
     */
    String userTypeName

    /*
    通行牌证
     */
    String licensePlate

    /*
    通行设备
     */
    String viaName

    /*
    通行时间
     */
    Long viaTime

    /*
    身份列表
     */
    String identities

    Byte userType

    /*
   消息推送时间
       事件发生时间
    */
    Long pushTime

    /*
    抓拍开始时间
        富士进入设备
     */
    Date startTime
    /*
    抓拍结束时间
        富士出设备时间
     */
    Date endTime

    /*
   告警事件位置
       默认设备名称
    */
    String location

    /*
       对接系统告警设备（类似） 名称
        */
    String objName

    String faceImg

    String originalParameters

    List<TrackImageVO> trackImageVOList

    //坐标
    String coordinate

    //真实电话
    String realMobile

    Long deviceId

    String buildingId

    String layerId

    String doorplateId

    Double rowIndex

    Double colIndex

    Integer idx

    String getMobile() {
        if (this.mobile?.length() == 11) {
            String s = this.mobile.substring(3, 7)
            String m = this.mobile.replaceFirst(s, "****")
            return m
        }
        null
    }

    TrackVO buildVO(Track track) {
        TrackVO vo = new BuildVO().convert(track)
        vo.viaName = track.location

        vo
    }

    private class BuildVO extends Converter<Track, TrackVO> {

        @Override
        protected TrackVO doForward(Track track) {
            TrackVO trackVO = new TrackVO()
            trackVO.setId(track.getId())
            trackVO.setLicensePlate(track.getLicensePlate())
            trackVO.setPushTime(track.getPushTime())
            trackVO.setStartTime(track.getStartTime())
            trackVO.setEndTime(track.getEndTime())
            trackVO.setLocation(track.getLocation() ?: track.getObjName())
            trackVO.setObjName(track.getObjName())
            trackVO.setOriginalParameters(track.getOriginalParameters())
            return trackVO
        }

        @Override
        protected Track doBackward(TrackVO trackVO) {
            return null
        }
    }
}



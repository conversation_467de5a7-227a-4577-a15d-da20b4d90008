package hiiadmin.module.device

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.device.Device

/**
 * Created by sunnysails on 2022/8/24 10:15
 * @version 1.0 
 */
class YrDeviceVO extends AbstractDeviceVO {

    String username

    String password

    Integer maxCalculation

    Integer maxChannel

    Integer calculationBit

    Integer useCalculation

    List<YrDeviceChannelVO> channels

    Integer operateType

    /**
     * 同步人脸库失败原因
     */
    String failMsg

    YrDeviceVO buildVO(Device device) {
        new BuildVO().convert(device)
    }

    private class BuildVO extends Converter<Device, YrDeviceVO> {
        @Override
        protected YrDeviceVO doForward(Device device) {
            YrDeviceVO yrDeviceVO = new YrDeviceVO()
            yrDeviceVO.setMaxCalculation(device.getMaxCalculation())
            yrDeviceVO.setMaxChannel(device.getMaxChannel())
            yrDeviceVO.setCalculationBit(device.getCalculationBit())
            yrDeviceVO.setUseCalculation(device.getUseCalculation())
            yrDeviceVO.setUsername(device.getUserName())
            yrDeviceVO.setPassword(device.getPassword())

            yrDeviceVO.setId(device.getId())
            yrDeviceVO.setSerialNumber(device.getSerialNumber())
            yrDeviceVO.setMac(device.getMac())
            yrDeviceVO.setName(device.getName())
            yrDeviceVO.setMemo(device.getMemo())
            yrDeviceVO.setIp(device.getIp())
            yrDeviceVO.setSchoolId(PhenixCoder.encodeId(device.getSchoolId()))
            yrDeviceVO.setCampusId(PhenixCoder.encodeId(device.getCampusId()))
            yrDeviceVO.setType(device.getType())
            yrDeviceVO.setFirm(device.getFirm())
            yrDeviceVO.setConnectStatus(device.getConnectStatus())
            yrDeviceVO.setOnlineStatus(device.getOnlineStatus())
            return yrDeviceVO
        }

        @Override
        protected Device doBackward(YrDeviceVO yrDeviceVO) {
            return null
        }
    }
}

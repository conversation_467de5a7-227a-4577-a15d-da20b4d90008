package hiiadmin.module.approval

/**
 * <AUTHOR> @date 2021/1/20 下午4:57
 */
class ApprovalProcessUserVO implements Serializable {
    Byte approvalUserType//发起人、审批人、抄送人类型
    String approvalUserIds//指定人员id
    String approvalDepartmentIds

    String approvalRoleIds
    Byte approvalMethodType//审批方式
    Byte approvalUserNull//审批人、抄送人为空时
    String transferIds//转交人id
    String batchStudentTransferIds//多个学生时的转交人id
    Byte promoterChooseType//发起人自选
    Byte copyToParent//是否抄送给家长
    //条件
    String conditions
    String editCardJson
}

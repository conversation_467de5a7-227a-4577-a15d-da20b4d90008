package hiiadmin.module.approval

class ViolationRecordVO implements Serializable {

    Long id

    Long studentId

    String studentName

    String avatar

    String gradeName

    String unitName

    String sectionName

    String code

    Long violateTime

    String punishmentResult
    
    Long punishmentResultId

    String memo

    String proofPhoto

    String publishTeacherName//登记人

    Long publishTime

    String demotionResult
    
    Long demotionResultId

    Long demoteTime

    String demoteTeacherName

    Long demoteRegisterTime

    Integer violationNum//违纪数量

    Integer unDemotionNum//未降级数量
    
    BigDecimal score 
    
    List<BigDecimal> scoreList = []
    
    List<ViolationVO> violationVOS = []
    
    List<ViolationDemoteRecordVO> demotes = []
}

package hiiadmin.module.docking.gating

import hiiadmin.module.docking.hk.PersonDownloadDetailVO

class GatingPersonIssueRecordVO implements Serializable {

    String id

    Long issueTaskId

    String issueTaskCode

    Long startTime

    Long endTime

    Integer firm

    Long planTemplateId

    String planTemplateName

    Long personGroupId

    String timeIndex

    Long configTaskId

    Long deviceId

    String deviceName

    Long userId

    Byte userType

    String userName

    String userCode

    String facultyName

    String majorName

    String sectionName

    String gradeName

    String unitName

    Integer issueStatus

    String issueMessage

    Long issueStartTime

    Long issueEndTime

    Integer operateType

    //通行类型
    Byte gatingType

    //通行时段
    String times

    Long dateCreated

    Long lastUpdated

    PersonDownloadDetailVO personDownloadDetail
}

package hiiadmin.module.docking

/**
 * 对接平台状态统计VO
 * Created by AI Assistant on 2025/08/15
 * @version 1.0 
 */
class DockingPlatformStatusStatisticsVO {

    /**
     * 日在线率
     */
    String onlineRate

    /**
     * 24小时异常时长
     */
    String abnormalDuration

    /**
     * 异常次数
     */
    Integer abnormalCount

    /**
     * 时间轴数据
     */
    List<TimelineSegmentVO> timeline

    /**
     * 构建统计VO
     */
    static DockingPlatformStatusStatisticsVO buildVO(Map<String, Object> statistics, List<Map<String, Object>> timelineData) {
        DockingPlatformStatusStatisticsVO vo = new DockingPlatformStatusStatisticsVO()
        
        vo.onlineRate = statistics.onlineRate as String
        vo.abnormalDuration = statistics.abnormalDuration as String
        vo.abnormalCount = statistics.abnormalCount as Integer
        
        vo.timeline = timelineData.collect { data ->
            new TimelineSegmentVO(
                startTime: data.startTime as Long,
                endTime: data.endTime as Long,
                status: data.status as Integer,
                statusText: data.statusText as String,
                statusClass: data.statusClass as String,
                message: data.message as String
            )
        }
        
        return vo
    }
}

/**
 * 时间轴段VO
 */
class TimelineSegmentVO {
    /**
     * 开始时间戳
     */
    Long startTime

    /**
     * 结束时间戳
     */
    Long endTime

    /**
     * 状态值
     */
    Integer status

    /**
     * 状态文本
     */
    String statusText

    /**
     * 状态样式类
     */
    String statusClass

    /**
     * 状态消息
     */
    String message
}

package hiiadmin.module.pickUp

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.device.Device

class LeavePlanLevelParentAuthDeviceVO extends PlanDeviceVO {
    LeavePlanLevelParentAuthDeviceVO buildVO(Device device) {
        new BuildVO().convert(device)
    }

    private class BuildVO extends Converter<Device, LeavePlanLevelParentAuthDeviceVO> {
        @Override
        protected LeavePlanLevelParentAuthDeviceVO doForward(Device device) {
            LeavePlanLevelParentAuthDeviceVO leavePlanLevelParentAuthDeviceVO = new LeavePlanLevelParentAuthDeviceVO()

            leavePlanLevelParentAuthDeviceVO.setId(device.getId())
            leavePlanLevelParentAuthDeviceVO.setCampusId(PhenixCoder.encodeId(device.getCampusId()))
            leavePlanLevelParentAuthDeviceVO.setName(device.getName())
            leavePlanLevelParentAuthDeviceVO.setOnline(device.getConnectStatus() == 1)
            leavePlanLevelParentAuthDeviceVO.setFirm(device.getFirm())
            return leavePlanLevelParentAuthDeviceVO
        }

        @Override
        protected Device doBackward(LeavePlanLevelParentAuthDeviceVO leavePlanLevelParentAuthDeviceVO) {
            return null
        }
    }
}

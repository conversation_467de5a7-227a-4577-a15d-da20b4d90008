package hiiadmin.module.evaluation

import com.google.common.base.Converter
import timetabling.evaluation.StudyReport

class StudyReportVO implements Serializable {

    Long id
    //报告名称
    String name

    Long semesterId//学期Id

    String semesterName//学期名称

    Long gradeId//年级id
    
    String gradeCode

    String gradeName//年级名称

    Long creatorId//创建人id

    String creatorName//创建人姓名

    Date dateCreated//创建时间

    Date doneTime//完成时间

    Byte status//状态(含生成状态和发布状态)see:StudyReportStatus

    String description

    Long campusId

    Date publishTime

    Date lastUpdated

    Long version

    StudyReportVO buildVO(StudyReport studyReport) {
        return new BuildVO().convert(studyReport)
    }

    private class BuildVO extends Converter<StudyReport, StudyReportVO> {
        @Override
        protected StudyReportVO doForward(StudyReport studyReport) {
            StudyReportVO studyReportVO = new StudyReportVO()
            studyReportVO.id = studyReport.id
            studyReportVO.gradeId = studyReport.gradeId
            studyReportVO.gradeCode = studyReport.gradeCode
            studyReportVO.name = studyReport.name
            studyReportVO.status = studyReport.status
            studyReportVO.semesterId = studyReport.semesterId
            studyReportVO.semesterName = studyReport.semesterName
            studyReportVO.gradeName = studyReport.gradeName
            studyReportVO.campusId = studyReport.campusId
            studyReportVO.creatorId = studyReport.creatorId
            studyReportVO.creatorName = studyReport.creatorName
            studyReportVO.dateCreated = studyReport.dateCreated
            studyReportVO.lastUpdated = studyReport.lastUpdated
            studyReportVO.publishTime = studyReport.publishTime
            studyReportVO.description = studyReport.description
            return studyReportVO
        }

        @Override
        protected StudyReport doBackward(StudyReportVO studyReportVO) {
            throw new AssertionError("不支持逆向转化方法!")
        }
    }
}

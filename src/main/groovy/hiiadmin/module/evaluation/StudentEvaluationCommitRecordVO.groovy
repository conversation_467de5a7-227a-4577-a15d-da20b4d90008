package hiiadmin.module.evaluation

class StudentEvaluationCommitRecordVO implements Serializable {

    Long id
    Long studentId
    Long teacherId
    Long studentEvaluationTaskId
    String studentCode
    String formJson

    String valueJson

    Long campusId

    Date dateCreated
    Date lastUpdated
    // "0: 未提交  1:已提交")
    Byte status
    String teacherName

    String studentName

    String commitTime

    Long version

    String courseId

    String courseName

    Long classUnitId

    Long gradeId

    String unitName

    String gradeName
    //评教对象，导出报表用
    String evaluationObject

    List<EvaluationAnswerJsonVO> answerJsonVOList

}

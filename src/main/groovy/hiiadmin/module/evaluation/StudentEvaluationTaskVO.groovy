package hiiadmin.module.evaluation

import com.google.common.base.Converter
import hiiadmin.ConstantEnum
import hiiadmin.utils.PhenixCoder
import timetabling.evaluation.StudentEvaluationTask

class StudentEvaluationTaskVO implements Serializable {

    Long id

    String title

    String createUserId

    String createUserName

    Date dateCreated

    Date lastUpdated

    String semesterId

    String semesterName
    //参考枚举类StudentEvaluationTaskStatus
    Byte status

    String campusId

    String statusDesc

    String gradeIds

    String courseIds

    String publishUsername

    String publishUserId

    Date publishTime

    String questionJson

    String formJson

    Integer commitCount

    Integer mustCommitCount

    StudentEvaluationTaskVO buildVO(StudentEvaluationTask studentEvaluationTask) {
        new BuildVO().convert(studentEvaluationTask)
    }

    private class BuildVO extends Converter<StudentEvaluationTask, StudentEvaluationTaskVO> {

        @Override
        protected StudentEvaluationTaskVO doForward(StudentEvaluationTask template) {
            StudentEvaluationTaskVO templateVO = new StudentEvaluationTaskVO()
            templateVO.id = template.id
            templateVO.campusId = PhenixCoder.encodeId(template.campusId)
            templateVO.dateCreated = template.dateCreated
            templateVO.lastUpdated = template.lastUpdated
            templateVO.createUserId = PhenixCoder.encodeId(template.createUserId)
            templateVO.title = template.title
            templateVO.createUserName = template.createUserName
            templateVO.questionJson = template.questionJson
            templateVO.status = template.status
            templateVO.gradeIds = PhenixCoder.encodeIds(template.gradeIds)
            templateVO.courseIds = template.courseIds
            templateVO.semesterId = PhenixCoder.encodeId(template.semesterId)
            templateVO.mustCommitCount = template.mustCommitCount
            templateVO.formJson = template.formJson
            templateVO.statusDesc = ConstantEnum.StudentEvaluationTaskStatus.getEnumByType(templateVO.status).name
            if (ConstantEnum.StudentEvaluationTaskStatus.GATHER.type.equals(template.status)) {
                templateVO.publishUserId = template.publishUserId
                templateVO.publishUsername = template.publishUsername
                templateVO.publishTime = template.publishTime
            }
            templateVO.semesterName = template.semesterName
            return templateVO
        }

        @Override
        protected StudentEvaluationTask doBackward(StudentEvaluationTaskVO templateVO) {
            throw new AssertionError("不支持逆向转化方法!")
        }
    }
}

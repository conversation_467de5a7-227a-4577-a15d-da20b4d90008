package hiiadmin.module.studentFiles

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.studentFile.StudentFileTaskRecord

class FileTaskRecordVO {

    Long id

    String studentId

    String studentName

    String studentCode

    //任务状态 1 成功 -1失败
    Byte taskStatus

    Long fileTaskId

    FileTaskRecordVO buildVO(StudentFileTaskRecord studentFileTaskRecord) {
        new BuildVO().convert(studentFileTaskRecord)
    }

    private class BuildVO extends Converter<StudentFileTaskRecord, FileTaskRecordVO> {
        @Override
        protected FileTaskRecordVO doForward(StudentFileTaskRecord studentFileTaskRecord) {
            FileTaskRecordVO fileTaskRecordVO = new FileTaskRecordVO()
            fileTaskRecordVO.setId(studentFileTaskRecord.getId())
            fileTaskRecordVO.setStudentId(PhenixCoder.encodeId(studentFileTaskRecord.getStudentId()))
            fileTaskRecordVO.setStudentName(studentFileTaskRecord.getStudentName())
            fileTaskRecordVO.setStudentCode(studentFileTaskRecord.getStudentCode())
            fileTaskRecordVO.setTaskStatus(studentFileTaskRecord.getTaskStatus())
            fileTaskRecordVO.setFileTaskId(studentFileTaskRecord.getFileTaskId())
            return fileTaskRecordVO
        }

        @Override
        protected StudentFileTaskRecord doBackward(FileTaskRecordVO fileTaskRecordVO) {
            return null
        }
    }


}

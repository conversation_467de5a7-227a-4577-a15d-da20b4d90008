package hiiadmin.module.humanface.dto

import hiiadmin.module.humanface.CameraConfigVO

/**
 *
 * <AUTHOR> @version 1.0
 * @date 2021-01-27 10:54
 * *   */
class EventConfigurationDTO {

    Long id

    //对方平台唯一标识
    String synId

    String name

    /**
     * ⽣效类型, 1:⻓期有效 2:每天定时有效 3:⾃
     * 定义
     * */
    Integer effectiveType

    /**
     * 告警类型
     * 越界:CrossLine 拥挤:Crowd
     * 徘徊:Wandering 布控:Blacklist
     * 陌⽣⼈:stranger 闯禁：ForbiddenArea
     * */
    String algoModel

    Integer eventType

    Long startTime

    Long stopTime

    List<CameraConfigVO> cameraConfigs = []

}

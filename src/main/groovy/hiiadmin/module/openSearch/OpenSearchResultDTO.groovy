package hiiadmin.module.openSearch

import com.alibaba.fastjson.annotation.JSONField

/**
 * Created by sunnysails on 2022/12/16 9:57
 * @version 1.0 
 */
class OpenSearchResultDTO {

    @JSONField(name = "searchtime")
    double searchtime
    @JSONField(name = "total")
    int total
    @JSONField(name = "compute_cost")
    List<ComputeCostDTO> computeCost
    @JSONField(name = "num")
    int num
    @JSONField(name = "viewtotal")
    int viewtotal
    @JSONField(name = "items")
    List<OpenSearchResultItemsDTO> items
    @JSONField(name = "facet")
    List<?> facet

    static class ComputeCostDTO {
        @JSONField(name = "index_name")
        String indexName
        @JSONField(name = "value")
        double value
    }

}

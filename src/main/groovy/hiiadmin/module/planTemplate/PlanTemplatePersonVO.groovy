package hiiadmin.module.planTemplate

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.planTemplate.PlanTemplatePerson

class PlanTemplatePersonVO {

    Long groupId

    Long campusId

    String userId

    /*
    学生时 userId == studentId
    接送人时 userId == studentId
    老师时 userId == teacherId
    家长  userId == parentId
    访客  userId == visitorId
     */
    Byte userType
    //学生Id时附加字段
    String sectionId
    String sectionName
    String gradeId
    String gradeName
    String unitId
    String unitName
    String alias
    String buildingId
    String buildingName
    String layerId
    String layerName
    String doorPlateId
    String doorPlateName
    String bedId
    String bedName
    String building2BedName
    String unitCode

    Boolean gender

    String code

    String name

    String cardNum

    String avatar

    List<String> relationList

    String mobile

    String noHideMobile

    String departmentName

    String licensePlate

    Boolean isSelect


    PlanTemplatePersonVO buildVO(PlanTemplatePerson person) {
        new BuildVO().convert(person)
    }

    private class BuildVO extends Converter<PlanTemplatePerson, PlanTemplatePersonVO> {
        @Override
        protected PlanTemplatePersonVO doForward(PlanTemplatePerson planTemplatePerson) {
            PlanTemplatePersonVO planTemplatePersonVO = new PlanTemplatePersonVO()

            planTemplatePersonVO.setGroupId(planTemplatePerson.getGroupId())
//            planTemplatePersonVO.setCampusId(planTemplatePerson.getCampusId())
            planTemplatePersonVO.setUserId(PhenixCoder.encodeId(planTemplatePerson.getUserId()))
            planTemplatePersonVO.setUserType(planTemplatePerson.getUserType())
            planTemplatePersonVO.setGradeId(PhenixCoder.encodeId(planTemplatePerson.getGradeId()))
            planTemplatePersonVO.setUnitId(PhenixCoder.encodeId(planTemplatePerson.getUnitId()))
            planTemplatePersonVO.setBuildingId(PhenixCoder.encodeId(planTemplatePerson.getBuildingId()))
            planTemplatePersonVO.setLayerId(PhenixCoder.encodeId(planTemplatePerson.getLayerId()))
            planTemplatePersonVO.setDoorPlateId(PhenixCoder.encodeId(planTemplatePerson.getDoorPlateId()))
            planTemplatePersonVO.setBedId(PhenixCoder.encodeId(planTemplatePerson.getBedId()))
            planTemplatePersonVO.setGender(planTemplatePerson.getGender())
            return planTemplatePersonVO
        }

        @Override
        protected PlanTemplatePerson doBackward(PlanTemplatePersonVO planTemplatePersonVO) {
            return null
        }
    }
}

package hiiadmin.module.bugu

import com.google.common.base.Converter
import org.joda.time.DateTime
import timetabling.org.Semester

class SemesterVO implements Serializable {
    private final static String PATTERN = "yyyy-MM-dd"
    Long id
    String academicYear
    Byte semesterType
    Date startDate
    Date endDate
    Long schoolId
    Long campusId
    Byte currentType
    String campusName
    Date originDate
    private String weekStart
    private String weekEnd

    String getWeekStart() {
        if (this.originDate) {
            return new DateTime(this.originDate).toString(PATTERN)
        }
        return null
    }

    String getWeekEnd() {
        DateTime dateTime = new DateTime(this.originDate)
        return dateTime.plusDays(6).toString(PATTERN)
    }

    Long getSemesterId() {
        return this.id
    }

    SemesterVO buildVO(Semester semester) {
        new BuildVO().convert(semester)
    }

    private class BuildVO extends Converter<Semester, SemesterVO> {
        @Override
        protected SemesterVO doForward(Semester semester) {
            SemesterVO semesterVO = new SemesterVO()

            semesterVO.setId(semester.getId())
            semesterVO.setAcademicYear(semester.getAcademicYear())
            semesterVO.setSemesterType(semester.getSemesterType())
            semesterVO.setStartDate(semester.getStartDate())
            semesterVO.setEndDate(semester.getEndDate())
            semesterVO.setSchoolId(semester.getSchoolId())
            semesterVO.setCampusId(semester.getCampusId())
            semesterVO.setCurrentType(semester.getCurrentType())
            semesterVO.setOriginDate(semester.getOriginDate())
            return semesterVO
        }

        @Override
        protected Semester doBackward(SemesterVO semesterVO) {
            return null
        }
    }
}
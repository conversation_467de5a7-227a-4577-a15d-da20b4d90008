package hiiadmin.module.bugu

class SymptomRecordVO implements Serializable {

    Long id

    String studentId

    String studentName

    String studentCode

    String className

    String schoolId

    String pic

    String creatorId//创建者ID

    String creatorName //登记人

    String symptomMemo//症状备注

    String symptomSuggestMemo//症状建议备注

    Float temperature//体温

    Integer sphygmus//脉搏

    Integer diastolicBloodPressure//舒张压

    Integer systolicBloodPressure//收缩压

    String illnessMemo//病情描述

    String handleMoreMemo//处理更多建议

    Byte notifyMaster//通知老师

    Byte notifyParent//通知家长

    Long dateOnset //发病日期

    Long dateCreated

    Long lastUpdated

    Byte status

    List<SymptomVO> symptomVOList

    List<SymptomSuggestVO> symptomSuggestVOList

    String gradeName

    String sectionName

}

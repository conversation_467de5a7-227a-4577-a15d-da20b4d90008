package hiiadmin.module.bugu

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.CampusTeachingWeek

/**
 * Created by sunnysails on 2021/8/20 10:41
 * @version 1.0 
 */
class CampusTeachingWeekVO implements Serializable {
    Long id
    String campusId
    String semesterId
    Integer teachingWeekIdx
    Long dayStamp
    Byte status

    CampusTeachingWeekVO buildVO(CampusTeachingWeek week) {
        new BuildVO().convert(week)
    }

    private class BuildVO extends Converter<CampusTeachingWeek, CampusTeachingWeekVO> {

        @Override
        protected CampusTeachingWeekVO doForward(CampusTeachingWeek campusTeachingWeek) {
            CampusTeachingWeekVO campusTeachingWeekVO = new CampusTeachingWeekVO()

            campusTeachingWeekVO.setId(campusTeachingWeek.getId())
            campusTeachingWeekVO.setCampusId(PhenixCoder.encodeId(campusTeachingWeek.getCampusId()))
            campusTeachingWeekVO.setSemesterId(PhenixCoder.encodeId(campusTeachingWeek.getSemesterId()))
            campusTeachingWeekVO.setTeachingWeekIdx(campusTeachingWeek.getTeachingWeekIdx())
            campusTeachingWeekVO.setDayStamp(campusTeachingWeek.getDayStamp())
            campusTeachingWeekVO.setStatus(campusTeachingWeek.getStatus())
            return campusTeachingWeekVO
        }

        @Override
        protected CampusTeachingWeek doBackward(CampusTeachingWeekVO campusTeachingWeekVO) {
            return null
        }
    }
}

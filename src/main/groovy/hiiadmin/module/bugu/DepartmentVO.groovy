package hiiadmin.module.bugu

import hiiadmin.module.gated.GatedPersonVO

class DepartmentVO implements Serializable {

    String id

    String name

    Integer count
    
    // 院系使用字段
    Byte type

    String supervisors

    List<TeacherVO> teachers

    List<GatedPersonVO> persons

    Integer weight

    Integer getCount() {
        if (count) {
            return count
        } else if (teachers) {
            return teachers.size()
        }
        return null
    }
}

package hiiadmin.module.bugu

import com.google.common.base.Converter
import timetabling.option.OptionCourse

class PlanVO implements Serializable {

    Integer course

    String choiceIds

    String choiceName

    PlanVO buildPlanVO(OptionCourse optionCourse) {
        new BuildVO().convert(optionCourse)
    }

    private class BuildVO extends Converter<OptionCourse, PlanVO> {

        @Override
        protected PlanVO doForward(OptionCourse optionCourse) {
            PlanVO planVO = new PlanVO()

            planVO.setCourse(optionCourse.getCourse())
            planVO.setChoiceIds(optionCourse.getChoiceIds())
            planVO.setChoiceName(optionCourse.getChoiceName())
            return planVO
        }

        @Override
        protected OptionCourse doBackward(PlanVO planVO) {
            return null
        }
    }
}
package hiiadmin.module.bugu

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.org.Grade

import static hiiadmin.ConstantEnum.SectionType.getEnumByType

class GradeVO implements Serializable {
    String id
    String name
    private String code
    Integer schoolYear
    String campusId
    String campusName
    String sectionId
    String sectionName
    List<UnitVO> units
    List<SubjectVO> subjects

    Integer unitTotal
    Integer studentTotal
    Integer parentTotal

    Boolean maxGrade
    
    Byte status
    
    String facultyId
    
    String facultyName
    
    Long graduateTime
    
    Boolean unitFlag

    void setCode(String code) {
        this.code = code
    }
    //code 排序使用
    Integer getCode() {
        if (code?.integer) {
            return code.toInteger()
        } else {
            return code?.hashCode()
        }
    }

    void setName(String name) {
        this.name = name
    }

    GradeVO buildVO(Grade grade) {
        new BuildVO().convert(grade)
    }

    private class BuildVO extends Converter<Grade, GradeVO> {

        @Override
        protected GradeVO doForward(Grade grade) {
            GradeVO gradeVO = new GradeVO()
            gradeVO.setCode(grade.getCode())
            gradeVO.setName(grade.getName())

            gradeVO.setId(PhenixCoder.encodeId(grade.getId()))
            gradeVO.setSchoolYear(grade.getSchoolYear())
            gradeVO.setCampusId(PhenixCoder.encodeId(grade.getCampusId()))
            gradeVO.setSectionId(PhenixCoder.encodeId(grade.getSectionId()))
            gradeVO.setSectionName(getEnumByType(grade?.sectionId)?.name)
            gradeVO.setStatus(grade.status)
            return gradeVO
        }

        @Override
        protected Grade doBackward(GradeVO gradeVO) {
            return null
        }
    }

}
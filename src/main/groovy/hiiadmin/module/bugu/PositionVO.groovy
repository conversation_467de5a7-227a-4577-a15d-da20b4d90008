package hiiadmin.module.bugu

import com.google.common.base.Converter
import hiiadmin.utils.PhenixCoder
import timetabling.Position

class PositionVO implements Serializable {
    String id

    String name

    String nameInitials

    String campusName

    List<DeviceVO> devices

    PositionVO buildVO(Position position) {
        new BuildVO().convert(position)
    }

    private class BuildVO extends Converter<Position, PositionVO> {
        @Override
        protected PositionVO doForward(Position position) {
            PositionVO positionVO = new PositionVO()

            positionVO.setId(PhenixCoder.encodeId(position.getId()))
            positionVO.setName(position.getName())
            positionVO.setNameInitials(position.getNameInitials())
            return positionVO
        }

        @Override
        protected Position doBackward(PositionVO positionVO) {
            return null
        }
    }
}

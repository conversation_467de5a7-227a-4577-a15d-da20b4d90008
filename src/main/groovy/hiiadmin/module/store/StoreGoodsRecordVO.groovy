package hiiadmin.module.store

class StoreGoodsRecordVO implements Serializable {

    String id

    Integer type

    String goodsId

    String name

    //规格
    String specification

    //物品数量
    Integer num

    //计量单位
    String unitOfMeasurement

    //单价
    BigDecimal unitPrice

    //金额
    BigDecimal price

    String operateUserId

    String operateUserName

    Long dateCreated

    Long lastUpdated

}

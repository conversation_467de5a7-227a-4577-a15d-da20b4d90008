package hiiadmin.module.group


import hiiadmin.module.bugu.StudentSearchVO
import hiiadmin.module.bugu.TeacherVO
import hiiadmin.module.docking.gating.GatingConfigTaskVO

class PersonGroupVO implements Serializable {

    String id

    String name

    Byte type

    Byte userType

    Integer userCount

    //特征条件 使用json存储
    String conditionJson

    //关联海康状态
    Integer hkStatus

    //海康edu人数
    Integer hkCount

    String hkMessage

    //关联权限配置单
    List<GatingConfigTaskVO> gatingConfigTaskList

    Long dateCreated

    Long lastUpdated

    List<StudentSearchVO> studentVOS

    List<TeacherVO> teacherVOS

}

package hiiadmin.module.dataCenter

import com.google.common.base.Converter
import timetabling.data.DataField

class DataFieldVO implements Serializable {
    Integer idx
    String name
    String dataName
    String key

    DataFieldVO buildVO(DataField dataField) {
        new BuildVO().convert(dataField)
    }

    private class BuildVO extends Converter<DataField, DataFieldVO> {

        @Override
        protected DataFieldVO doForward(DataField dataField) {
            DataFieldVO dataFieldVO = new DataFieldVO()
            dataFieldVO.setIdx(dataField.getFieldIndex())
            dataFieldVO.setName(dataField.getName())
            dataFieldVO.setDataName(dataField.getDataName())
            dataFieldVO.setKey("index" + dataField.getFieldIndex())
            return dataFieldVO
        }

        @Override
        protected DataField doBackward(DataFieldVO dataFieldVO) {
            return null
        }
    }
}

package hiiadmin.module.dataCenter

import com.google.common.base.Converter
import hiiadmin.module.humanface.TrackImageVO
import timetabling.track.TrackAlarm

import java.math.RoundingMode

/**
 * <AUTHOR> @date 2020/8/19 10:58
 */
class TrackAlarmVO implements Serializable {

    Long id

    Long pushTime

    String positionName

    Integer event

    Long deviceId

    String deviceName

    String objName

    String eventName

    String name

    String eventType

    /**
     * 抓拍图
     * */
    String snapImg

    //相关平台的原始参数
    String originalParameters

    List<TrackImageVO> trackList

    Integer operationStatus

    AlarmHandleVO handle

    String campusMap

    String coordinate

    String targetType
    String targetArea
    String targetScore


    String buildingId

    String layerId

    String doorplateId

    Double rowIndex

    Double colIndex

    String relatedPersons

    TrackAlarmVO buildVO(TrackAlarm trackAlarm) {
        new BuildVO().convert(trackAlarm)
    }

    TrackAlarmVO buildVO(TrackAlarm trackAlarm, String positionName) {
        TrackAlarmVO trackAlarmVO = new BuildVO().convert(trackAlarm)
        trackAlarmVO.positionName = positionName
        trackAlarmVO
    }

    private class BuildVO extends Converter<TrackAlarm, TrackAlarmVO> {

        @Override
        protected TrackAlarmVO doForward(TrackAlarm trackAlarm) {
            TrackAlarmVO trackAlarmVO = new TrackAlarmVO()
            trackAlarmVO.setId(trackAlarm.getId())
            trackAlarmVO.setDeviceId(trackAlarm.getDeviceId())
            trackAlarmVO.setDeviceName(trackAlarm.getObjName())
            trackAlarmVO.setPushTime(trackAlarm.getPushTime())
            trackAlarmVO.setEvent(trackAlarm.getEvent())
            trackAlarmVO.setEventName(trackAlarm.getEventName())
            trackAlarmVO.setEventType(trackAlarm.getEventType())
            trackAlarmVO.setObjName(trackAlarm.getObjName())
            trackAlarmVO.setSnapImg(trackAlarm.getSnapImg())
            trackAlarmVO.setOriginalParameters(trackAlarm.getOriginalParameters())
            trackAlarmVO.setTargetType(trackAlarm.getTargetType())
            trackAlarmVO.setTargetArea(trackAlarm.getTargetArea())
            if (trackAlarm.getTargetScore()) {
                trackAlarmVO.setTargetScore(new BigDecimal(trackAlarm.getTargetScore()).setScale(3, RoundingMode.HALF_DOWN).toString())
            }
            return trackAlarmVO
        }

        @Override
        protected TrackAlarm doBackward(TrackAlarmVO trackAlarmVO) {
            return null
        }
    }

}

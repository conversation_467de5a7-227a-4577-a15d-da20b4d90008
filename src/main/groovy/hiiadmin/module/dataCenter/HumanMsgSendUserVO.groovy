package hiiadmin.module.dataCenter

import com.google.common.base.Converter
import timetabling.data.HumanMsgSendUser

class HumanMsgSendUserVO implements Serializable {
    Long id

    Long campusId

    Long humanMessageId

    Long userType

    Long userId

    String userName

    String dingUserId

    Byte status

    HumanMsgSendUserVO buildVO(HumanMsgSendUser humanMsgSendUser) {
        new BuildVO().convert(humanMsgSendUser)
    }

    private class BuildVO extends Converter<HumanMsgSendUser, HumanMsgSendUserVO> {
        @Override
        protected HumanMsgSendUserVO doForward(HumanMsgSendUser humanMsgSendUser) {
            HumanMsgSendUserVO humanMsgSendUserVO = new HumanMsgSendUserVO()
            humanMsgSendUserVO.setId(humanMsgSendUser.getId())
            humanMsgSendUserVO.setCampusId(humanMsgSendUser.getCampusId())
            humanMsgSendUserVO.setHumanMessageId(humanMsgSendUser.getHumanMessageId())
            humanMsgSendUserVO.setUserType(humanMsgSendUser.getUserType())
            humanMsgSendUserVO.setUserId(humanMsgSendUser.getUserId())
            humanMsgSendUserVO.setUserName(humanMsgSendUser.getUserName())
            humanMsgSendUserVO.setDingUserId(humanMsgSendUser.getDingUserId())
            humanMsgSendUserVO.setStatus(humanMsgSendUser.getStatus())
            return humanMsgSendUserVO
        }

        @Override
        protected HumanMsgSendUser doBackward(HumanMsgSendUserVO humanMsgSendUserVO) {
            return null
        }
    }
}


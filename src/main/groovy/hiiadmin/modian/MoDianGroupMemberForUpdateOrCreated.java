package hiiadmin.modian;

public class MoDianGroupMemberForUpdateOrCreated {

    Long deptId;//部门ID

    String deptName;//部门名称

    Long memberId;//成员ID

    String memberJobNum;//工号，成员ID为空时

    String memberName;//成员名称

    Byte confirmFlag;//必须填1

    Byte type;//组成员类型：1-部门，2-成员

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getMemberJobNum() {
        return memberJobNum;
    }

    public void setMemberJobNum(String memberJobNum) {
        this.memberJobNum = memberJobNum;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Byte getConfirmFlag() {
        return confirmFlag;
    }

    public void setConfirmFlag(Byte confirmFlag) {
        this.confirmFlag = confirmFlag;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }
}
package hiiadmin.apiCloud

import com.bugu.ResultVO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod

@FeignClient(name = "bgb-core")
interface BgbCoreApi {
    
    @RequestMapping(path = "/api/bgb/1.0/job/core/sendLeaveMessage", method = RequestMethod.POST, consumes = "application/json")
    ResultVO sendLeaveMessage(@RequestBody Map map)
}
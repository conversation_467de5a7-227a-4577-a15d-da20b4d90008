package hiiadmin.apiCloud

import com.bugu.ResultVO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod

@FeignClient(name = "edge")
interface EdgeALiYunApiInvoker {
    public static final int SUCCESS_CODE = 1000

    //同步更新告警事件配置
    @RequestMapping(path = "/api/aLiYun/syn/save/event", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synSaveAlarmEventToALiYun(@RequestBody Map map)

    //同步删除告警配置
    @RequestMapping(path = "/api/aLiYun/syn/delete/event", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synDeleteAlarmEventToALiYun(@RequestBody Map map)

    //同步更新人脸库
    @RequestMapping(path = "/api/aLiYun/syn/create/face/group", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synCreateFaceGroupToALiYun(@RequestBody Map map)

    //同步更新人脸
    @RequestMapping(path = "/api/aLiYun/syn/createOrUpdate/face", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synCreateOrUpdateFaceToALiYun(@RequestBody Map map)

    //添加人脸
    @RequestMapping(path = "/api/aLiYun/syn/save/face", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synCreateFaceToALiYun(@RequestBody Map map)

    //修改人脸
    @RequestMapping(path = "/api/aLiYun/syn/update/face", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synUpdateFaceToALiYun(@RequestBody Map map)

    //同步删除人脸
    @RequestMapping(path = "/api/aLiYun/syn/delete/face", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synDeleteFaceToALiYun(@RequestBody Map map)


    //同步单个人脸到缓存中
    @RequestMapping(path = "/api/aLiYun/syn/synDetails/face", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synDetailsFaceCacheToALiYun(@RequestBody Map map)

    //同步更新设备
    @RequestMapping(path = "/api/aLiYun/syn/save/device", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synSaveDeviceToALiYun(@RequestBody Map map)

    //同步更新设备
    @RequestMapping(path = "/api/aLiYun/syn/update/device", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synUpdateDeviceToALiYun(@RequestBody Map map)

    //同步更新设备
    @RequestMapping(path = "/api/aLiYun/syn/delete/device", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synDeleteDeviceToALiYun(@RequestBody Map map)

    //同步更新设备
    @RequestMapping(path = "/api/aLiYun/syn/query/device", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synFindDeviceToALiYun(@RequestBody Map map)

    //获取大屏摄像头报警列表
    @RequestMapping(path = "/api/aLiYun/getBigScreen/deviceList", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getBigScreenDeviceListToALiYun(@RequestBody Map map)

    //⼤屏摄像头报警统计
    @RequestMapping(path = "/api/aLiYun/getBigScreen/deviceAlert/count", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getBigScreenDeviceAlertToALiYun(@RequestBody Map map)

    //⼤屏历史事件总数与当天事件总数展示
    @RequestMapping(path = "/api/aLiYun/getBigScreen/getEventTotalData", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getBigScreenEventTotalDataToALiYun(@RequestBody Map map)

    //⼤屏历史事件总数与当天事件总数展示
    @RequestMapping(path = "/api/aLiYun/getBigScreen/eventReport", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getBigScreenEventReportToALiYun(@RequestBody Map map)

    //获取设备直播地址
    @RequestMapping(path = "/api/aLiYun/camera/getStreamingUrl", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getStreamingUrlToALiYun(@RequestBody Map map)

    //获取节点集合
    @RequestMapping(path = "/api/aLiYun/node/getList", method = RequestMethod.POST, consumes = "application/json")
    ResultVO getNodeListToALiYun(@RequestBody Map map)


    //以图搜图
    @RequestMapping(path = "/api/aLiYun/imageSearch/imageDetection", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synImageDetectionToALiYun(@RequestBody Map map)

    ///创建人脸库类型
    @RequestMapping(path = "/api/aLiYun/syn/create/face/GroupType", method = RequestMethod.POST, consumes = "application/json")
    ResultVO updateBizConfigToALiYun(@RequestBody Map map)


    //------------------------------------------------边缘一体机调用------------------------------------------------------------
    //创建设备库
    @RequestMapping(path = "/api/edge/save/deviceGroup", method = RequestMethod.POST, consumes = "application/json")
    ResultVO saveDeviceGroupToYun(@RequestBody Map map)

    //将设备加入设备组中
    @RequestMapping(path = "/api/edge/save/deviceJoinDeviceGroup", method = RequestMethod.POST, consumes = "application/json")
    ResultVO deviceJoinDeviceGroup(@RequestBody Map map)

    //添加用户组
    @RequestMapping(path = "/api/edge/save/userGroup", method = RequestMethod.POST, consumes = "application/json")
    ResultVO saveUserGroupToYun(@RequestBody Map map)


    //添加用户
    @RequestMapping(path = "/api/edge/save/user", method = RequestMethod.POST, consumes = "application/json")
    ResultVO addUserToYun(@RequestBody Map map)


    //更新用户
    @RequestMapping(path = "/api/edge/update/user", method = RequestMethod.POST, consumes = "application/json")
    ResultVO updateUserToYun(@RequestBody Map map)

    //同步用户到缓存中
    @RequestMapping(path = "/api/edge/syn/detailsUser", method = RequestMethod.POST, consumes = "application/json")
    ResultVO synUserDetailsCache(@RequestBody Map map)

    ///删除用户
    @RequestMapping(path = "/api/edge/delete/user", method = RequestMethod.POST, consumes = "application/json")
    ResultVO deleteUserToYun(@RequestBody Map map) ///删除用户用户

    //将用户加入用户分组
    @RequestMapping(path = "/api/edge/user/joinGroup", method = RequestMethod.POST, consumes = "application/json")
    ResultVO userJoinUserGroupToYun(@RequestBody Map map)


    //将用户加入用户分组
    @RequestMapping(path = "/api/edge/save/userAndDeviceGroup/relation", method = RequestMethod.POST, consumes = "application/json")
    ResultVO AddFaceUserGroupAndDeviceGroupRelation(@RequestBody Map map)

}

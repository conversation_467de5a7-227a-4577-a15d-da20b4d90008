package hiiadmin.apiCloud

import com.bugu.ResultVO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import timetabling.device.Device

@FeignClient(name = "biot")
interface ApiBiot {
    @RequestMapping(path = "/api/biot/1.0/biot/bind", method = RequestMethod.POST, produces = "application/json")
    ResultVO bind(@RequestBody Map map)

    @RequestMapping(path = "/api/biot/1.0/biot/bindV2", method = RequestMethod.POST, produces = "application/json")
    ResultVO bindV2(@RequestBody Map map)

    @RequestMapping(path = "/api/biot/1.0/biot/delete", method = RequestMethod.POST, produces = "application/json")
    ResultVO delete(@RequestBody Device device)

    @RequestMapping(path = "/api/biot/1.0/biot/msg/bpub", method = RequestMethod.POST, produces = "application/json")
    ResultVO batchPubMsg(@RequestBody Map map)

    @RequestMapping(path = "/api/biot/1.0/biot/device/status/batch/fetch", method = RequestMethod.POST, produces = "application/json")
    ResultVO batchGetDeviceState(@RequestBody Map map)

    @RequestMapping(path = "/api/biot/1.0/biot/screenshot", method = RequestMethod.POST, produces = "application/json")
    ResultVO deviceScreenshot(@RequestBody Map map)
}
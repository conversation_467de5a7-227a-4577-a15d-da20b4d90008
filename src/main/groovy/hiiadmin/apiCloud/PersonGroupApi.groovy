package hiiadmin.apiCloud

import com.bugu.ResultVO
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(name = "bgb-core")
interface PersonGroupApi {

    @RequestMapping(path = "/api/bgb/1.0/personGroup/syncUpdatePersonByCampusIdAndUserType", method = RequestMethod.POST, produces = "application/json")
    ResultVO syncUpdatePersonByCampusIdAndUserType(@RequestParam("campusId") Long campusId, @RequestParam("userType") Byte userType)

    @RequestMapping(path = "/api/bgb/1.0/personGroup/syncChangePersonGroup", method = RequestMethod.POST, produces = "application/json")
    ResultVO syncChangePersonGroup(@RequestParam("campusId") Long campusId, @RequestParam("groupId") Long groupId,
                                   @RequestParam("operateType") Integer operateType)

    @RequestMapping(path = "/api/bgb/1.0/personGroup/syncUpdatePersonGroupDetail", method = RequestMethod.POST, produces = "application/json")
    ResultVO syncUpdatePersonGroupDetail(@RequestParam("campusId") Long campusId, @RequestParam("userIds") String userIds,
                                         @RequestParam("userType") Byte userType,@RequestParam("userInfoChange") Integer userInfoChange)
}
# 设备状态色块图功能优化

## 概述

根据设计要求，优化了设备状态监控功能，新增了状态色块图展示功能。该功能能够根据所选时间范围，展示对应的在离线状态色块图，蓝色代表在线区间，红色代表离线区间。当鼠标悬浮某区间时，浮窗展示对应的区间状态及对应开始及结束的时刻。

## 新增功能特性

### 1. 状态色块图数据结构

每个状态块包含以下信息：
- **时间范围**: 开始时间和结束时间（时间戳和格式化时间）
- **状态信息**: 状态值、状态文本、状态样式类
- **持续时长**: 该状态持续的时间长度
- **时间占比**: 该状态在总时间中的百分比

### 2. 智能状态推断

- 当没有历史记录时，默认整个时间段为在线状态
- 当第一条记录不是从开始时间开始时，推断初始状态为在线
- 自动计算每个状态区间的持续时间和占比

### 3. 精确的时间计算

- 支持任意时间范围的状态统计
- 精确计算每个状态区间的开始和结束时间
- 自动处理状态切换的时间点

## API数据结构

### 统计数据响应示例

```json
{
  "status": 1,
  "result": {
    "onlineRate": "85.5%",
    "offlineDuration": "3小时30分钟",
    "statusChangeCount": 8,
    "statusBlocks": [
      {
        "startTime": 1692057600000,
        "endTime": 1692061200000,
        "status": 1,
        "statusText": "在线",
        "statusClass": "online",
        "duration": "1小时0分钟",
        "percentage": 4.17,
        "startTimeFormatted": "00:00:00",
        "endTimeFormatted": "01:00:00"
      },
      {
        "startTime": 1692061200000,
        "endTime": 1692064800000,
        "status": 0,
        "statusText": "离线",
        "statusClass": "offline",
        "duration": "1小时0分钟",
        "percentage": 4.17,
        "startTimeFormatted": "01:00:00",
        "endTimeFormatted": "02:00:00"
      }
    ],
    "timeline": [
      // 时间轴数据...
    ]
  }
}
```

### 状态块字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| startTime | Long | 开始时间戳（毫秒） |
| endTime | Long | 结束时间戳（毫秒） |
| status | Integer | 状态值（1=在线, 0=离线） |
| statusText | String | 状态文本（"在线"/"离线"） |
| statusClass | String | CSS样式类（"online"/"offline"） |
| duration | String | 持续时长文本 |
| percentage | Double | 时间占比百分比 |
| startTimeFormatted | String | 格式化开始时间（HH:mm:ss） |
| endTimeFormatted | String | 格式化结束时间（HH:mm:ss） |

## 前端集成指南

### 1. 色块图渲染

```javascript
function renderStatusBlocks(statusBlocks) {
    const container = document.getElementById('status-blocks-container');
    container.innerHTML = '';
    
    statusBlocks.forEach(block => {
        const blockElement = document.createElement('div');
        blockElement.className = `status-block ${block.statusClass}`;
        blockElement.style.width = `${block.percentage}%`;
        
        // 添加悬浮提示
        blockElement.title = `
            状态: ${block.statusText}
            时间: ${block.startTimeFormatted} - ${block.endTimeFormatted}
            持续: ${block.duration}
            占比: ${block.percentage.toFixed(2)}%
        `;
        
        container.appendChild(blockElement);
    });
}
```

### 2. CSS样式定义

```css
.status-blocks-container {
    display: flex;
    width: 100%;
    height: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: hidden;
}

.status-block {
    height: 100%;
    cursor: pointer;
    transition: opacity 0.2s;
}

.status-block:hover {
    opacity: 0.8;
}

/* 在线状态 - 蓝色 */
.status-block.online {
    background-color: #1890ff;
}

/* 离线状态 - 红色 */
.status-block.offline {
    background-color: #ff4d4f;
}

/* 未知状态 - 灰色 */
.status-block.unknown {
    background-color: #d9d9d9;
}
```

### 3. 悬浮提示增强

```javascript
function createTooltip(block) {
    return `
        <div class="status-tooltip">
            <div class="tooltip-header">
                <span class="status-indicator ${block.statusClass}"></span>
                <strong>${block.statusText}</strong>
            </div>
            <div class="tooltip-content">
                <p>开始时间: ${block.startTimeFormatted}</p>
                <p>结束时间: ${block.endTimeFormatted}</p>
                <p>持续时长: ${block.duration}</p>
                <p>时间占比: ${block.percentage.toFixed(2)}%</p>
            </div>
        </div>
    `;
}
```

### 4. 响应式设计

```css
@media (max-width: 768px) {
    .status-blocks-container {
        height: 30px;
    }
    
    .status-block {
        min-width: 2px; /* 确保小区间也能显示 */
    }
}
```

## 算法优化

### 1. 状态区间计算算法

```groovy
// 伪代码
for each statusHistory in historyList:
    blockStart = statusHistory.dateCreated
    blockEnd = nextStatusHistory?.dateCreated ?: endDate
    
    createStatusBlock(
        start: blockStart,
        end: blockEnd,
        status: statusHistory.onlineStatus,
        duration: blockEnd - blockStart
    )
```

### 2. 时间占比计算

```groovy
percentage = (blockDuration / totalDuration) * 100.0
```

### 3. 边界情况处理

- **无历史记录**: 默认整个时间段为在线状态
- **记录不从开始时间开始**: 推断初始状态为在线
- **记录不到结束时间**: 最后一个状态持续到结束时间

## 性能优化建议

1. **数据缓存**: 对于相同时间范围的查询结果进行缓存
2. **分页处理**: 对于长时间范围，考虑分页加载
3. **前端优化**: 使用虚拟滚动处理大量状态块
4. **数据压缩**: 合并相邻的相同状态区间

## 测试用例

### 1. 基本功能测试

```javascript
// 测试正常状态切换
const testData = [
    { dateCreated: '2023-08-15 08:00:00', onlineStatus: 1 },
    { dateCreated: '2023-08-15 10:00:00', onlineStatus: 0 },
    { dateCreated: '2023-08-15 14:00:00', onlineStatus: 1 }
];

// 期望结果: 3个状态块
// 1. 08:00-10:00 在线 (8.33%)
// 2. 10:00-14:00 离线 (16.67%)
// 3. 14:00-24:00 在线 (41.67%)
```

### 2. 边界情况测试

```javascript
// 测试无历史记录
const emptyData = [];
// 期望结果: 1个状态块，整天在线 (100%)

// 测试单条记录
const singleRecord = [
    { dateCreated: '2023-08-15 12:00:00', onlineStatus: 0 }
];
// 期望结果: 2个状态块
// 1. 00:00-12:00 在线 (50%)
// 2. 12:00-24:00 离线 (50%)
```

## 注意事项

1. **时区处理**: 确保前后端时间处理的一致性
2. **精度控制**: 百分比计算保留适当的小数位数
3. **颜色对比**: 确保色块颜色有足够的对比度
4. **可访问性**: 为色盲用户提供文字标识
5. **性能监控**: 监控大时间范围查询的性能表现

## 后续扩展

1. **自定义颜色**: 允许用户自定义状态颜色
2. **状态过滤**: 支持隐藏特定状态的色块
3. **时间缩放**: 支持时间轴的缩放功能
4. **数据导出**: 支持色块数据的导出功能
5. **实时更新**: 支持状态变化的实时推送更新

# 对接平台状态监控功能优化

## 概述

根据设计图要求，对对接平台状态监控功能进行了全面优化，新增了统计数据展示、时间轴可视化等功能。

## 新增功能

### 1. 状态统计数据

提供以下统计指标：
- **日在线率**：计算当天平台正常运行时间占比
- **24小时异常时长**：统计当天异常状态持续时间
- **异常次数**：统计当天发生异常的次数

### 2. 时间轴可视化

- 显示平台状态在时间轴上的变化
- 支持不同状态的颜色区分
- 提供状态切换的详细信息

### 3. 增强的历史记录查询

- 改进了参数验证
- 增加了错误处理
- 优化了数据返回格式

## API接口

### 1. 获取统计数据

```
GET /api/hiiadmin/1.0/docking/platformStatusStatistics
```

**参数：**
- `firm` (必需): 平台类型
- `time` (可选): 查询日期时间戳，默认为当前时间

**返回示例：**
```json
{
  "status": 1,
  "result": {
    "onlineRate": "95.5%",
    "abnormalDuration": "1小时5分",
    "abnormalCount": 3,
    "timeline": [
      {
        "startTime": 1692057600000,
        "endTime": 1692061200000,
        "status": 1,
        "statusText": "正常",
        "statusClass": "normal",
        "message": ""
      }
    ]
  }
}
```

### 2. 获取时间轴数据

```
GET /api/hiiadmin/1.0/docking/platformStatusTimeline
```

**参数：**
- `firm` (必需): 平台类型
- `time` (可选): 查询日期时间戳

### 3. 历史记录查询（优化）

```
GET /api/hiiadmin/1.0/docking/platformStatusHistory
```

**参数：**
- `firm` (必需): 平台类型
- `time` (可选): 查询日期时间戳
- `firmStatus` (可选): 状态筛选
- `p` (可选): 页码，默认1
- `s` (可选): 页大小，默认30

## 状态码说明

| 状态码 | 状态文本 | CSS类名 | 说明 |
|--------|----------|---------|------|
| 1 | 正常 | normal | 平台运行正常 |
| 3 | 异常 | abnormal | 平台运行异常 |
| 4 | 检测中 | checking | 正在检测平台状态 |
| 5 | 未知 | unknown | 状态未知 |

## 文件结构

### 新增文件

1. **控制器**
   - `grails-app/controllers/hiiadmin/docking/DockingPlatformStatusStatisticsController.groovy`

2. **VO类**
   - `src/main/groovy/hiiadmin/module/docking/DockingPlatformStatusStatisticsVO.groovy`

3. **测试文件**
   - `src/test/groovy/hiiadmin/docking/DockingPlatformHistoryServiceSpec.groovy`

### 修改文件

1. **服务层**
   - `grails-app/services/hiiadmin/docking/DockingPlatformHistoryService.groovy`
   - 新增统计计算方法
   - 新增时间轴数据生成方法

2. **控制器**
   - `grails-app/controllers/hiiadmin/docking/DockingPlatformStatusHistoryController.groovy`
   - 增强参数验证和错误处理

3. **URL映射**
   - `grails-app/controllers/hiiadmin/UrlMappings.groovy`
   - 新增统计接口路由

## 前端集成建议

### 1. 统计数据展示

```javascript
// 获取统计数据
fetch('/api/hiiadmin/1.0/docking/platformStatusStatistics?firm=1&time=' + Date.now())
  .then(response => response.json())
  .then(data => {
    if (data.status === 1) {
      // 更新统计显示
      document.getElementById('onlineRate').textContent = data.result.onlineRate;
      document.getElementById('abnormalDuration').textContent = data.result.abnormalDuration;
      document.getElementById('abnormalCount').textContent = data.result.abnormalCount;
    }
  });
```

### 2. 时间轴可视化

建议使用以下CSS类名来区分不同状态：

```css
.timeline-segment.normal {
  background-color: #52c41a; /* 绿色 - 正常 */
}

.timeline-segment.abnormal {
  background-color: #ff4d4f; /* 红色 - 异常 */
}

.timeline-segment.checking {
  background-color: #1890ff; /* 蓝色 - 检测中 */
}

.timeline-segment.unknown {
  background-color: #d9d9d9; /* 灰色 - 未知 */
}
```

## 测试

运行单元测试：
```bash
./gradlew test --tests "hiiadmin.docking.DockingPlatformHistoryServiceSpec"
```

## 注意事项

1. 统计计算基于当天0点到23:59:59的数据
2. 时间轴数据按状态变更时间排序
3. 异常时长计算考虑了状态持续时间
4. 所有时间戳均为毫秒级Unix时间戳
5. 建议前端实现定时刷新功能以获取最新状态

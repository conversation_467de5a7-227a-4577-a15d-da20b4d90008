create index teacher_uc_st_index on teacher (union_code, sync_type);

create index student_uc_st_index on student (union_code, sync_type);

create index parent_uc_st_index on t_parent (union_code, sync_type);

create index unit_student_st_u_index on unit_student (student_id, class_unit_id);
create index unit_student_st_s_index on unit_student (student_id, status);
create index unit_student_u_id_index on unit_student (class_unit_id, status);

create index grade_uc_st_index on grade (union_code, sync_type);
create index unit_uc_st_index on unit (union_code, sync_type);
create index campus_uc_st_index on campus (union_code, sync_type);

create index unit_g_id_index on unit (grade_id, status);

create index parent_student_s_id_index on t_parent_student (student_id, status);
create index parent_student_p_id_index on t_parent_student (parent_id, status);

create index user_u_id_type_index on t_user (u_id, type);
create index t_user_wx_ding_u_id_type_c_id_status_index on t_user_wx_ding (user_id, user_type, campus_id, status);

create index staff_department_c_id_t_id_index on staff_department (campus_id, teacher_id);
create index department_c_id_index on department (campus_id, status);

create index teacher_school_c_id_t_id_index on teacher_school (campus_id, teacher_id);
create index hii_user_c_id_t_id_index on hii_user (campus_id, teacher_id);
create index bgb_staff_role_s_id_index on bgb_staff_role (staff_id);
create index bgb_campus_role_c_id_index on bgb_campus_role (campus_id);
